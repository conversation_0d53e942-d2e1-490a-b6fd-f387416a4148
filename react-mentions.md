# React Mentions Integration Plan

This document outlines the step-by-step plan to integrate the `react-mentions` library into the `components/quickCreateButton.tsx` component to enable @mentions functionality for users in the "people" table.

## 1. Project Setup (Prerequisites)
- The package is already installed
- **Styling:** The project uses a combination of Tailwind CSS classes and shadcn/ui component styling. `react-mentions` components will be styled to match this design system.

## 2. Data Fetching (Convex)

-   **Implement Optimized Query:**
    -   Create a new Convex query in `convex/people.ts` named `searchPeople` that follows the repository pattern:

    ```typescript
    // convex/people.ts
    import { zQuery } from '../utils/zod-query';
    import { z } from 'zod';
    import { v } from 'convex/values';

    export const searchPeople = zQuery({
      args: {
        search: z.string().optional(),
      },
      handler: async (ctx, args) => {
        // Use the built-in search index from the schema
        let peopleQuery = ctx.db.query('people');
        
        if (args.search) {
          // Use the search_name index defined in schema.ts
          peopleQuery = peopleQuery.withSearchIndex('search_name', (q) => 
            q.search('name', args.search)
          );
        }
        
        // Limit results for performance
        const people = await peopleQuery.take(10);
        return people;
      },
    });
    ```

## 3. Data Transformation

-   **Create Utility Function:** Implement a utility function that follows the project's patterns:

    ```typescript
    // Within components/quickCreateButton.tsx or in a separate utils file
    import { Id } from '@/convex/_generated/dataModel';

    interface Person {
      _id: Id<'people'>;
      name: string;
      email?: string;
      image?: string;
    }

    function formatPeopleForMentions(people: Person[]): { id: string; display: string; image?: string }[] {
      return people.map((person) => ({
        id: person._id, // Convex ID as string
        display: person.name, 
        image: person.image // Include image for avatar in suggestion dropdown
      }));
    }
    ```

## 4. Component Integration (`quickCreateButton.tsx`)

-   **Imports:**
    ```typescript
    import { MentionsInput, Mention } from 'react-mentions';
    import { useQuery } from 'convex/react';
    import { api } from '@/convex/_generated/api';
    import { useCallback } from 'react';
    import { debounce } from 'lodash'; // Add if not already imported
    ```

-   **Query Hook Integration:**
    ```typescript
    // Add this near other state variables
    const [mentionSearch, setMentionSearch] = useState('');
    
    // Optimized query with debounce  
    const debouncedSetSearch = useCallback(
      debounce((search: string) => setMentionSearch(search), 300),
      []
    );
    
    // Use the Convex query hook 
    const people = useQuery(api.people.searchPeople, { 
      search: mentionSearch 
    });
    ```

-   **Replace Input with MentionsInput:**
    ```typescript
    <MentionsInput
      value={name}
      onChange={(e) => setName(e.target.value)}
      singleLine
      className="w-full h-8 text-xs px-2 py-1 rounded"
      placeholder={`${getDomainText(selectedDomain)}`}
      autoFocus
      autoComplete="off"
      style={{
        control: {
          backgroundColor: 'transparent',
          fontSize: 12,
          fontWeight: 'normal',
        },
        highlighter: {
          padding: 0,
          border: '1px solid transparent',
        },
        input: {
          padding: 0,
          border: 'none',
          outline: 'none', 
          background: 'transparent',
        },
        suggestions: {
          list: {
            backgroundColor: 'white',
            border: '1px solid rgba(0,0,0,0.15)',
            fontSize: 12,
            borderRadius: '0.375rem',
          },
          item: {
            padding: '5px 12px',
            borderBottom: '1px solid rgba(0,0,0,0.05)',
            '&focused': {
              backgroundColor: 'var(--primary-color-light, #f3f4f6)', // Matches shadcn/ui
            },
          },
        },
      }}
    >
      <Mention
        trigger="@"
        data={people ? formatPeopleForMentions(people) : []}
        onSearch={debouncedSetSearch}
        renderSuggestion={(suggestion, search, highlightedDisplay, index, focused) => (
          <div className={`flex items-center p-1 ${focused ? 'bg-primary/10' : ''}`}>
            {suggestion.image ? (
              <img src={suggestion.image} alt={suggestion.display} className="w-5 h-5 rounded-full mr-2" />
            ) : (
              <User size={14} className="mr-2" />
            )}
            <span>{suggestion.display}</span>
          </div>
        )}
        appendSpaceOnAdd
        displayTransform={(id, display) => `@${display}`}
      />
    </MentionsInput>
    ```

## 5. Convex Mutation Updates

-   **Create a Utility Function for Mentions Extraction:**

    ```typescript
    // convex/utils/mentions.ts
    import { Id } from '../_generated/dataModel';
    
    /**
     * Extracts mentioned person IDs from text containing react-mentions markup
     * @param text Text containing mentions with format @[display](id)
     * @returns Array of person IDs extracted from mentions
     */
    export function extractMentionsFromText(text: string): Id<'people'>[] {
      const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
      const mentions: Id<'people'>[] = [];
      
      let match;
      while ((match = mentionRegex.exec(text)) !== null) {
        mentions.push(match[2] as Id<'people'>);
      }
      
      return mentions;
    }
    ```

-   **Modify Mutation Implementation:**

    ```typescript
    // convex/tasks.ts 
    import { zMutation } from '../utils/zod-mutation';
    import { z } from 'zod';
    import { v } from 'convex/values';
    import { extractMentionsFromText } from '../utils/mentions';
    import { ConvexError } from "convex/values";

    export const createTasks = zMutation({
      args: {
        input: z.object({
          name: z.string(),
          status: z.string(),
          assigned_to: z.string().optional(),
        }),
      },
      handler: async (ctx, args) => {
        try {
          // Create the task first (same as before)
          const taskId = await ctx.db.insert('tasks', {
            name: args.input.name,
            status: args.input.status,
            assigned_to: args.input.assigned_to as Id<'users'> | undefined,
            updated_at: Date.now(),
          });
          
          // Extract and store mentions
          const mentions = extractMentionsFromText(args.input.name);
          
          // Store mentions in a relationship table using Promise.all for concurrency
          if (mentions.length > 0) {
            await Promise.all(mentions.map(personId => 
              ctx.db.insert('file_relationships', {
                // Using the existing relationship table 
                file_id: taskId as unknown as Id<'files'>, // Type casting for polymorphic relationship
                subject_type: 'people',
                subject_id: personId as unknown as Id<any>, // Type casting for polymorphic relationship
                linked_at: Date.now()
              })
            ));
          }
          
          return { ids: [taskId] };
        } catch (error: unknown) {
          // Follow error handling pattern from development guidelines
          console.error("Error creating task with mentions:", error);
          throw new ConvexError("Failed to create task with mentions");
        }
      },
    });
    ```

-   **Apply to Other Mutations:**
    - Update `createDecision`, `createProject`, and `createBill` using the same pattern
    - Ensure each implementation follows FOJO's established repository pattern

## 6. Testing

-   **Manual Testing:**
    -   Trigger mentions with `@` and confirm dropdown appears
    -   Verify search functionality using the search index works properly
    -   Confirm people selection from dropdown works
    -   Create items with mentions across all entity types
    -   Verify mentions are correctly stored in the database
    -   Check rendering of mentions in task names

-   **Edge Cases to Test:**
    -   Empty input validation
    -   No matching users behavior
    -   Long names rendering
    -   Special characters in names 
    -   Multiple mentions in a single input
    -   Performance with larger datasets

## 7. Documentation Update

-   **Update `activeContext.md`:**
    - Document the integration of `react-mentions` for @mentions functionality
    - Detail how mentions are stored in `file_relationships` table
    - Reference the new `searchPeople` query with search index

-   **Update `systemPatterns.md`:**
    - Add mention extraction pattern to the data processing section
    - Document the polymorphic relationship approach for storing mentions

-   **Update `techContext.md`:**
    - Add `react-mentions` to the dependency list
    - Note any additional utility packages added for this feature

-   **Update `.clinerules`:**
    - Add mention extraction patterns
    - Document styling conventions for mention components

## 8. Future Considerations

-   **Real-time Updates:**
    - The implementation already uses Convex's `useQuery` hook for real-time updates
    - Consider adding notification system for when a user is mentioned

-   **Performance Optimizations:**
    - The current implementation includes result limiting (take(10))
    - If needed, implement virtual scrolling for very large directories

-   **Integration with TipTap:**
    - Since FOJO already uses TipTap in some areas, consider extending this implementation to support mentions in rich text fields

-   **Additional Features:**
    - Add auto-notifications when users are mentioned
    - Implement mention highlighting in task/decision lists
    - Add support for team mentions (e.g., @engineering) in addition to people

This implementation plan aligns with FOJO's established patterns and architecture, using Convex's search index capabilities, proper error handling, and TypeScript best practices.
