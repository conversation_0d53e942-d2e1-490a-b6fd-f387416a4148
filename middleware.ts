import {
  convexAuthNextjsMiddleware,
  createRouteMatcher,
  nextjsMiddlewareRedirect
} from '@convex-dev/auth/nextjs/server';

// Define routes that should be protected (require authentication)
const isLoginPage = createRouteMatcher(['/login']);
// Removed signup page reference as it's no longer needed

// Check if the request is for the root path
const isRootPath = createRouteMatcher(['/']);

// Protect all routes in the (dashboard) directory and their sub-routes
// Using a more comprehensive pattern to ensure all sub-routes are protected
const isProtectedRoute = createRouteMatcher([
  // Dashboard routes
  '/(dashboard)/:path*',
  // Main section routes with all possible sub-paths
  '/admin',
  '/admin/:path+',
  '/bills',
  '/bills/:path+',
  '/directory',
  '/directory/:path+',
  '/documents',
  '/documents/:path+',
  '/home',
  '/home/<USER>',
  '/projects',
  '/projects/:path+',
  '/reports',
  '/reports/:path+',
  '/tasks',
  '/tasks/:path+',
  '/decisions',
  '/decisions/:path+'
]);

export default convexAuthNextjsMiddleware(
  async (request, { convexAuth }) => {
    // Check if authentication is disabled via environment variable
    const isAuthDisabled = process.env.DISABLE_AUTH === "TRUE";

    // Log the current request for debugging
    console.log(
      `Middleware processing request for: ${request.nextUrl.pathname}`
    );

    // Debug: Log if the current route is considered protected
    console.log(
      `Is protected route check for ${request.nextUrl.pathname}: ${isProtectedRoute(request)}`
    );

    // If authentication is disabled, allow all requests to proceed
    if (isAuthDisabled) {
      console.log('Middleware: Authentication checks bypassed due to DISABLE_AUTH=TRUE');
      return;
    }


    // We'll let the client-side handle redirection from login page when already authenticated
    // This allows for visual feedback and controlled timing
    // if (isLoginPage(request) && (await convexAuth.isAuthenticated())) {
    //   return nextjsMiddlewareRedirect(request, "/(dashboard)/home");
    // }

    // Middleware will focus on protecting routes from unauthenticated access
    if (isProtectedRoute(request) && !(await convexAuth.isAuthenticated())) {
      console.log(
        `Middleware: Unauthenticated user attempting to access protected route: ${request.nextUrl.pathname}`
      );
      return nextjsMiddlewareRedirect(request, '/login');
    }

    // If authenticated and trying to access a protected route, allow it
    if (isProtectedRoute(request) && (await convexAuth.isAuthenticated())) {
      console.log(
        `Middleware: Authenticated user accessing protected route: ${request.nextUrl.pathname}`
      );
      // Allow the request to proceed
      return;
    }
  },
  // Set cookie to expire after 30 days for better user experience
  { cookieConfig: { maxAge: 60 * 60 * 24 * 30 } }
);

export const config = {
  // The following matcher runs middleware on all routes
  // except static assets.
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)']
};
