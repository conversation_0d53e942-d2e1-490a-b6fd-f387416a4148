// ---- Investment Decision Seeding Variables ----
export const DECISION_CATEGORY_ID = "s970gxjbgbjw7g639zt5g883gd7dzzf1"; // Update to your Convex investment decision category ID

// Theme Tag IDs (replace with your real tag IDs)
export const PLANET_TAG_ID = "n5766a15kpcjjfz11dxmavk1xx7e1q9b";
export const RENEWABLE_ENERGY_TAG_ID = "n57des63tmx4scgxh8pv1zs7757e04sn";
export const CLEAN_TECHNOLOGY_TAG_ID = "n572be3adb44e8bnsx95vnrqmn7e1308";

// Utility functions for randomization
export function getRandomElement<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}
export function getRandomDate(start: Date, end: Date): number {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).getTime();
}

// Default values
export const DEFAULT_PRIORITIES = ["low", "medium", "high", "urgent"];
export const DEFAULT_STATUS_OPTIONS = [
  "draft", "in_review", "approved", "rejected", "escalated", "cancelled", "on_hold"
];
export const DEFAULT_UPDATED_AT = Date.now();

// ---- Investment Decisions Seed Data ----
export const investmentDecisions = [
  {
    // Core decision data
    title: "Horizon Growth Fund IV",
    decision_category_id: DECISION_CATEGORY_ID,
    status: getRandomElement(DEFAULT_STATUS_OPTIONS),
    priority: getRandomElement(DEFAULT_PRIORITIES),
    decision_type: "selection",
    has_financial_implications: true,
    financial_amount: 12500000,
    financial_currency: "USD",
    financial_time_period: "Multi-year",
    financial_years: 8,
    updated_at: DEFAULT_UPDATED_AT,

    // Theme tags (for tagging after seeding)
    theme_tag_ids: [PLANET_TAG_ID, RENEWABLE_ENERGY_TAG_ID, CLEAN_TECHNOLOGY_TAG_ID],

    // Investment-specific data
    is_active: true,
    proposed_investment: "Series B funding for emerging fintech companies",
    total_fundraise: 350000000,
    asset_class: "VC",
    structure: "Closed Ended/Committed",
    investment_entity: "Main Investment Vehicle",
    market: "Developed",
    geography: "North America, Europe",
    target_close_date: getRandomDate(new Date("2025-05-01"), new Date("2025-08-01")),
    close_timing_log: "Initial conversations began in January. Term sheet expected next month.",
    firm_name: "Horizon Capital Partners",
    notes: "Strong track record with previous funds, consistent returns above benchmark.",
    due_source: "Direct outreach from GP relationship",
    description: `<h2>Investment Overview</h2>
<p><strong>Horizon Capital</strong> has established itself as a premier venture capital firm, delivering top-quartile returns across three previous funds. The team has significantly expanded their operational support capabilities, implementing a comprehensive platform that includes <em>talent acquisition</em>, <em>go-to-market strategy</em>, and <em>technical advisory services</em>. This enhanced support system positions them uniquely to help portfolio companies navigate the current market challenges while accelerating their growth trajectories.</p>

<h2>Strategic Advantages & Track Record</h2>
<p>The firm's investment thesis focuses on three key areas:</p>
<ol>
  <li><strong>Enterprise SaaS Transformation</strong> - Supporting companies modernizing traditional industries</li>
  <li><strong>Financial Infrastructure</strong> - Backing next-generation payment and banking platforms</li>
  <li><strong>Sustainable Technology</strong> - Investing in companies driving environmental innovation</li>
</ol>
<p>Their previous fund (Fund III) has already achieved 3 successful exits with an aggregate IRR of 32%, demonstrating their ability to identify and scale promising companies. The team's deep network in both Silicon Valley and European tech hubs provides exceptional deal flow and partnership opportunities for portfolio companies.</p>`
  },
  // ...repeat for all remaining investments, each with a unique, rich, formatted description...
];

/*
  After seeding, use the theme_tag_ids for each decision to call your tag mutation, e.g.:
    setTagsForTaggable({
      taggable_type: "investment_decision",
      taggable_id: "<decision_id>",
      tagIds: investment.theme_tag_ids
    })
  This ensures each decision is tagged with the correct theme tags.
*/
