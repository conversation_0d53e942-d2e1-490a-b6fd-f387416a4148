#!/usr/bin/env node
import fs from 'fs';
import path from 'path';

// Configuration: Specify items to remove and the target file
export const cleanupConfig = {
  // Fields to remove (either line items or objects)
  items: [
    '_creationTime',  // Will remove lines like "_creationTime: 1234,"
    '_id',           // Will remove lines like "_id: 'abc123',"
    'badges',         // Will remove arrays like "badges: ["abc", "def"],"
    // Add more items to remove
  ],
  
  // Target file path relative to project root
  filePath: 'seedTeams.md'
};

/**
 * Removes specified fields from a seed file
 * Handles both single line items and arrays (with objects or simple values)
 */
export async function cleanupSeedFile(): Promise<void> {
  try {
    // Read the file
    const filePath = path.join(process.cwd(), cleanupConfig.filePath);
    let content = await fs.promises.readFile(filePath, 'utf8');

    // Process each item to remove
    for (const item of cleanupConfig.items) {
      // Match and remove arrays with any content (objects or simple values)
      // This pattern matches:
      // - Arrays containing objects: "badges: [{...}, {...}],"
      // - Arrays containing strings: "badges: ["abc", "def"],"
      // - Arrays containing numbers: "badges: [1, 2, 3],"
      const arrayPattern = new RegExp(`\\s*"?${item}"?:\\s*\\[(([^\\[\\]]|\\[[^\\[\\]]*\\])*?)\\],?\\n?`, 'g');
      content = content.replace(arrayPattern, '\n');

      // Then remove single line items (e.g., "_creationTime: 1234,")
      const linePattern = new RegExp(`\\s*"?${item}"?:.*?,\\n?`, 'g');
      content = content.replace(linePattern, '\n');
    }

    // Clean up extra whitespace and empty lines
    content = content
      .replace(/\n\s*\n\s*\n/g, '\n\n')  // Replace multiple empty lines with single empty line
      .replace(/,(\s*})/g, '$1')          // Remove trailing commas before closing braces
      .replace(/,(\s*\])/g, '$1')         // Remove trailing commas before closing brackets
      .trim();

    // Write back to file
    await fs.promises.writeFile(filePath, content, 'utf8');
    
    console.log(`Successfully cleaned up ${cleanupConfig.filePath}`);
  } catch (error) {
    console.error(`Error cleaning up ${cleanupConfig.filePath}:`, error);
    process.exit(1);
  }
}

// Run the cleanup when executed directly
cleanupSeedFile()
  .then(() => console.log('Cleanup completed successfully'))
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
