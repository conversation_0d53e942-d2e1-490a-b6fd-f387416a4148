 [
    {
      badgeDomain: "general",
      color: "#10B981",
      description:
        "Item is currently active and in good standing",
      icon: "Activity",
      name: "Active",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Unusual behavior or data detected",
      icon: "Activity",
      name: "Anomaly",
    },
    {
      badgeDomain: "decision",
      color: "#10B981",
      description: "Has received necessary approval",
      icon: "ThumbsUp",
      name: "Approved",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description: "Item is assigned to this person",
      icon: "UserCog",
      name: "Assigned",
    },
    {
      badgeDomain: "general",
      color: "#EF4444",
      description: "Predicted to miss deadline/goal",
      icon: "AlertTriangle",
      name: "At Risk",
    },
    {
      badgeDomain: "general",
      color: "#9CA3AF",
      description: "General file attachment",
      icon: "Paperclip",
      name: "Attachment",
    },
    {
      badgeDomain: "bills",
      color: "#10B981",
      description: "Set for automatic payment",
      icon: "Repeat",
      name: "Auto-Pay Enabled",
    },
    {
      badgeDomain: "general",
      color: "#F59E0B",
      description:
        "Needs information/approval from this person",
      icon: "UserClock",
      name: "Awaiting Input",
    },
    {
      badgeDomain: "bills",
      color: "#10B981",
      description: "Approved for payment",
      icon: "ThumbsUp",
      name: "Bill Approved",
    },
    {
      badgeDomain: "general",
      color: "#EF4444",
      description:
        "Progress is blocked by an external dependency",
      icon: "AlertCircle",
      name: "Blocked",
    },
    {
      badgeDomain: "general",
      color: "#6B7280",
      description: "Work has been permanently stopped",
      icon: "XCircle",
      name: "Cancelled",
    },
    {
      badgeDomain: "organization",
      color: "#3B82F6",
      description: "Client organization",
      icon: "Briefcase",
      name: "Client",
    },
    {
      badgeDomain: "general",
      color: "#10B981",
      description: "Work has been successfully finished",
      icon: "CheckCircle",
      name: "Completed",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Legal agreement document",
      icon: "FileSignature",
      name: "Contract",
    },
    {
      badgeDomain: "bills",
      color: "#8B5CF6",
      description: "Bill subject to contract terms",
      icon: "FileText",
      name: "Contract Pricing",
    },
    {
      badgeDomain: "bills",
      color: "#991B1B",
      description: "Significantly past due (30+ days)",
      icon: "AlertTriangle",
      name: "Critical Overdue",
    },
    {
      badgeDomain: "general",
      color: "#EF4444",
      description:
        "On the critical path for project completion",
      icon: "GitMerge",
      name: "Critical Path",
    },
    {
      badgeDomain: "decision",
      color: "#9CA3AF",
      description: "Decision postponed to a later date",
      icon: "Clock",
      name: "Deferred",
    },
    {
      badgeDomain: "bills",
      color: "#EF4444",
      description: "Payment approval denied",
      icon: "ThumbsDown",
      name: "Denied",
    },
    {
      badgeDomain: "bills",
      color: "#10B981",
      description: "Eligible for early payment discount",
      icon: "Percent",
      name: "Discount Available",
    },
    {
      badgeDomain: "bills",
      color: "#EF4444",
      description: "Bill amount or terms in dispute",
      icon: "AlertOctagon",
      name: "Disputed",
    },
    {
      badgeDomain: "bills",
      color: "#FCD34D",
      description: "Due in 8-14 days",
      icon: "CalendarRange",
      name: "Due Next Week",
    },
    {
      badgeDomain: "bills",
      color: "#A3E635",
      description: "Due within the current month",
      icon: "CalendarClock",
      name: "Due This Month",
    },
    {
      badgeDomain: "general",
      color: "#F59E0B",
      description: "Due within the current week",
      icon: "CalendarDays",
      name: "Due This Week",
    },
    {
      badgeDomain: "bills",
      color: "#F59E0B",
      description: "Due within the next 7 days",
      icon: "CalendarDays",
      name: "Due This Week Bill",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Due within the current day",
      icon: "Calendar",
      name: "Due Today",
    },
    {
      badgeDomain: "bills",
      color: "#F97316",
      description: "Payment due within 24 hours",
      icon: "Calendar",
      name: "Due Today Bill",
    },
    {
      badgeDomain: "bills",
      color: "#10B981",
      description: "Paid before due date",
      icon: "Award",
      name: "Early Payment",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Contract nearing expiration",
      icon: "Clock",
      name: "Expiring Soon",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description: "Contains linked files or documents",
      icon: "Paperclip",
      name: "Has Attachments",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description: "Contains discussion comments",
      icon: "MessageSquare",
      name: "Has Comments",
    },
    {
      badgeDomain: "general",
      color: "#F59E0B",
      description: "Depends on other items to progress",
      icon: "Link",
      name: "Has Dependencies",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description: "Contains child tasks",
      icon: "ListTree",
      name: "Has Subtasks",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Important and time-sensitive",
      icon: "ArrowUp",
      name: "High",
    },
    {
      badgeDomain: "bills",
      color: "#8B5CF6",
      description: "Bill amount in top 10% of expenses",
      icon: "TrendingUp",
      name: "High Value",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Flagged as important",
      icon: "Star",
      name: "Important",
    },
    {
      badgeDomain: "bills",
      color: "#3B82F6",
      description: "Payment is being processed",
      icon: "Loader",
      name: "In Processing",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description:
        "Work is actively being performed on this item",
      icon: "Clock",
      name: "In Progress",
    },
    {
      badgeDomain: "organization",
      color: "#9CA3AF",
      description: "Internal organization/department",
      icon: "Home",
      name: "Internal",
    },
    {
      badgeDomain: "general",
      color: "#EF4444",
      description: "Blocking progress on other items",
      icon: "Ban",
      name: "Is Blocking",
    },
    {
      badgeDomain: "general",
      color: "#8B5CF6",
      description: "Reference document",
      icon: "Book",
      name: "Knowledge Base",
    },
    {
      badgeDomain: "bills",
      color: "#EF4444",
      description: "Late payment fee applied",
      icon: "AlertCircle",
      name: "Late Fee",
    },
    {
      badgeDomain: "general",
      color: "#10B981",
      description: "Can be addressed when time permits",
      icon: "ArrowDown",
      name: "Low",
    },
    {
      badgeDomain: "bills",
      color: "#F97316",
      description:
        "Bill exceeds typical spending threshold",
      icon: "DollarSign",
      name: "Major Expense",
    },
    {
      badgeDomain: "general",
      color: "#F59E0B",
      description: "Standard priority level",
      icon: "Minus",
      name: "Medium",
    },
    {
      badgeDomain: "general",
      color: "#3B82F6",
      description: "Record of discussions",
      icon: "FileText",
      name: "Meeting Notes",
    },
    {
      badgeDomain: "bills",
      color: "#F59E0B",
      description: "Requires approval before payment",
      icon: "FileCheck",
      name: "Needs Approval",
    },
    {
      badgeDomain: "general",
      color: "#F97316",
      description: "Requires review or intervention",
      icon: "Eye",
      name: "Needs Attention",
    },
    {
      badgeDomain: "bills",
      color: "#F59E0B",
      description: "Requires additional documentation",
      icon: "FileQuestion",
      name: "Needs Documentation",
    },
    {
      badgeDomain: "bills",
      color: "#F59E0B",
      description: "Terms may be negotiable",
      icon: "MessageSquare",
      name: "Negotiable",
    },
  ]