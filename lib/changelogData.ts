// Shared changelog data to be used across components
import { ChangelogEntry } from "@/types/changelog";

export const changelogData: ChangelogEntry[] = [
  {
    date: "Apr 13",
    entries: [
      {
        headline: "Money Talks, Fojo Listens: Decision Dollars Get Real!",
        items: [
          "Ever wish your decisions could come with a built-in calculator and a reality check? Now, on the Decisions page, you can track the financial impact of every choice—Fojo instantly breaks down your big, bold amounts into monthly and annual costs. No more napkin math or “wait, how much is that per year?” moments. Make smarter moves, impress your team, and let <PERSON><PERSON><PERSON> do the number crunching (with zero judgment and a dash of charm). Check it out on the Decisions page!"
        ],
        link: "/decisions",
        image: "/images/changelog/20250413-decisiontype.png",
        imageStyle: { maxWidth: "100%", height: "auto" }
      }
    ]
  },
  {
    date: "Apr 12",
    entries: [
      {
        headline: "More easily add people to Organizations",
        items: [
          "Now, adding relationships is a breeze with the zippy new popovers on the 'People' tab (Org page) and 'Relations' tab (Person page)—link existing contacts or create+link new ones instantly, faster than making instant coffee."
        ],
        image: "/images/changelog/20250412-directorypopover.png",
        imageStyle: { maxWidth: "100%", height: "auto" }
      }
    ]
  },
  {
    date: "Apr 11",
    entries: [
      {
        headline: "Your Digital Sidekick Just Got a Voice!",
        items: [
          "Meet Fojo's new real-time voice assistant on project pages! Just click the friendly robot icon and start talking - your AI assistant will help you update project details, find team members, and organize your work through natural conversation. It's like having a helpful colleague who's always ready to lend a hand (and never needs coffee breaks)."
        ],
        image: "/images/changelog/20250411-fojoassistant.gif",
        imageStyle: { maxWidth: "100%", height: "auto" },
        link: "/projects"
      }
    ]
  },
  {
    date: "Apr 10",
    entries: [
      {
        headline: "Admin Dashboard Ditches the Crystal Ball for Real Numbers!",
        items: [
          "Remember those suspiciously round numbers on your Admin Dashboard? Well, they've packed their bags! We've hooked up the dashboard to the live Fojo feed, so now you see actual, real-time counts for Total Users, People, Orgs, Documents, Tags, Active Integrations, and Teams. No more guesswork, just the facts, ma'am (or sir!). Check out the newly enlightened stats right on the main /admin page."
        ],
        link: "/admin"
      }
    ]
  },
  {
    date: "Apr 9",
    entries: [
      {
        headline: "Meeting Notes Just Got Context! Meet the Details Card.",
        items: [
          "Ever opened a meeting note and thought, \"Wait, when was this again? And who was there?\" Wonder no more! We've added a nifty \"Meeting Details\" card right on the Meeting Note page (under Documents) where you can now easily set the date and add attendees (both people and organizations!), keeping all that crucial context right where you need it."
        ],
        image: "/images/changelog/20250409-meetingcard.png",
        imageStyle: { maxWidth: "50%", height: "auto" },
        link: "/documents/meeting-notes"
      },
      {
        headline: "Projects page just got a tag filter makeover!",
        items: [
          "Now you can easily find projects by selecting tags, with real-time filtering as you click. Like having a personal assistant who instantly knows which projects you're looking for based on their tags. Check it out on the Projects page and watch your project hunting efficiency skyrocket!"
        ],
        link: "/projects"
      }
    ]
  },
  {
    date: "April 8",
    entries: [
      {
        headline: "Peek-a-Boo! Your Nav Bar Just Got Sneaky.",
        items: [
          "We've taught the main navigation bar some serious hide-and-seek skills! It now stays tucked away by default, then POW – expands instantly when you hover, giving you more screen space without sacrificing access. It's like having a secret agent toolkit that only appears when summoned."
        ],
        image: "/images/changelog/20250408-navBar.gif",
        imageStyle: { maxWidth: "100%", height: "auto" }
      },
      {
        headline: "Your Command Center Just Got a Makeover!",
        items: [
          "The Home page has been completely redesigned with a sleek dashboard that puts everything you need at your fingertips. Now you can instantly see Recently Updated items across your workspace, Your Tasks, Your Decisions, and items where you're Informed—all in one beautiful, glanceable view. No more hunting through different sections to find what needs your attention today!"
        ],
        link: "/home"
      },

    ]
  },
  {
    date: "April 7",
    entries: [

          {
            headline: "Celebrate Success with Style!",
            items: [
              "We've added confetti celebrations to Tasks and Decisions Kanban boards! Now when you drag a task to \"Completed\" or a decision to \"Approved,\" confetti shoots upward from the card like a tiny digital standing ovation. Because completing things should feel as good as it looks on your performance review."
            ],
            image: "/images/changelog/20250407-confetti.gif",
            imageStyle: { maxWidth: "100%", height: "auto" },
            link: "/tasks"
          },
          {
            headline: "Decisions Just Got More Personal!",
            items: [
              "Added \"Show only my decisions\" filter to the Decisions page that lets you focus exclusively on decisions where you're a Driver or Contributor - available in both Card and Kanban views, with your preference saved so it's always just how you like it"
            ],
            image: "/images/changelog/20250407-decisions.png",
            imageStyle: { maxWidth: "100%", height: "auto" },
            link: "/decisions"
          },

    ]
  },
  {
    date: "April 6",
    entries: [
      {
        headline: "Project Timeline Gets a Memory Boost!",
        items: [
          "The Project Timeline now lets you add both Manual Updates and Meeting Notes with a single click! Keep track of important project milestones, decisions, and meeting outcomes all in one chronological view. Each update shows a handy icon so you can quickly distinguish between update types. Your project's story just got a whole lot easier to tell (and remember) on the Projects page."
        ],
        image: "/images/changelog/20250406-projectupdates.png",
        imageStyle: { maxWidth: "100%", height: "auto" }
      }
    ]
  },
  {
    date: "April 5",
    entries: [
      {
        headline: "Your Meetings Just Got a Personal Secretary!",
        items: [
          "Meeting Notes now feature real-time transcription that turns your spoken words into text as you talk! Just click 'Start Recording' and watch as your conversation magically appears on screen. Even better, our AI automatically transforms that transcript into beautifully organized meeting notes while you focus on the conversation. No more frantic typing or missing important details—it's like having a personal secretary who never asks for coffee breaks!"
        ],
        link: "/documents/meeting-notes"
      }
    ]
  },
  {
    date: "April 4",
    entries: [
      {
        headline: "Your Documents Just Got a Whole Lot Smarter!",
        items: [
          "Introducing Document Chat, our new AI assistant that lets you have conversations with all your documents! Just ask a question, and our AI will search across your knowledge base, meeting notes, contracts, and more to find the perfect answer. Filter by document type to narrow your search, or let the AI search everything at once. No more digging through files or struggling to remember where you saw that important detail—now your documents talk back!"
        ],
        image: "/images/changelog/20250405-rag.gif",
        imageStyle: { maxWidth: "100%", height: "auto" },
        link: "/documents"
      }
    ]
  },
  {
    date: "April 2",
    entries: [
      {
        headline: "Team Assignments Just Got a Drag-and-Drop Makeover!",
        items: [
          "We've completely redesigned how you assign people to Tasks, Projects, and Decisions with our new DCI Team Assignment cards! Now you can simply drag and drop team members between Driver, Contributor, and Informed roles. Even better, you can now assign entire teams as Contributors or Informed (because sometimes you need the whole squad). No more tedious clicking through menus—just grab, drag, and done!"
        ],
        image: "/images/changelog/20250403-teamAssignments.gif",
        imageStyle: { maxWidth: "50%", height: "auto" },
        link: "/tasks"
      }
    ]
  },
  {
    date: "April 1",
    entries: [

      {
        headline: "Breaking News: We Now Track Our Own Breaking News!",
        items: [
          "Introducing the Changelog page (yes, the very one you're reading right now!) where you can track all the exciting updates we're rolling out. No more wondering what's new—it's like having a personal news anchor for your FOJO experience, minus the perfect hair and dramatic pauses."
        ],
        link: "/changelog"
      }
    ]
  }
];

// Helper function to check if the most recent changelog entry is from today
export function isChangelogUpdatedToday(): boolean {
  if (changelogData.length === 0) return false;

  // Get the most recent changelog date
  const mostRecentDate = changelogData[0].date; // e.g., "April 7"

  // Parse the date (this is simplified and assumes current year)
  const currentYear = new Date().getFullYear();
  const dateObj = new Date(`${mostRecentDate}, ${currentYear}`);

  // Get today's date
  const today = new Date();

  // Compare the dates (only year, month, and day)
  return (
    dateObj.getFullYear() === today.getFullYear() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getDate() === today.getDate()
  );
}
