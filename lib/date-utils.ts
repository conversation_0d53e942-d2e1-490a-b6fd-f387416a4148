import { format } from 'date-fns';

// Define date quick filter types
export type DateQuickFilterType =
  | 'last30days'
  | 'last60days'
  | 'last90days'
  | 'q1'
  | 'q2'
  | 'q3'
  | 'q4'
  | 'ytd'
  | 'custom'
  | null;

/**
 * Time zone related utilities
 */

/**
 * Converts a local date to UTC
 * @param date The date in local time
 * @returns The same date in UTC
 */
export function toUTC(date: Date): Date {
  return new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds()
    )
  );
}

/**
 * Creates a UTC date from components
 * @param year Full year
 * @param month Month index (0-11)
 * @param day Day of month
 * @param hour Hour (optional)
 * @param minute Minute (optional)
 * @param second Second (optional)
 * @param millisecond Millisecond (optional)
 * @returns A new Date object in UTC
 */
export function createUTCDate(
  year: number,
  month: number,
  day: number,
  hour = 0,
  minute = 0,
  second = 0,
  millisecond = 0
): Date {
  return new Date(
    Date.UTC(year, month, day, hour, minute, second, millisecond)
  );
}

/**
 * Gets the user's current time zone
 * @returns The user's time zone string (e.g., "America/New_York")
 */
export function getUserTimeZone(): string {
  // Only run this on the client side to prevent hydration errors
  if (typeof window === 'undefined') {
    return 'UTC'; // Default to UTC on the server
  }
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Formats a date considering the user's time zone
 * @param date The date to format
 * @param format The format to use (defaults to 'short')
 * @returns Formatted date string
 */
export function formatDateWithTimeZone(
  date: Date,
  format: 'short' | 'medium' | 'long' = 'short'
): string {
  // Only use client-specific formatting on the client side
  if (typeof window === 'undefined') {
    // Server-side rendering - use a consistent format
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: format,
      timeZone: 'UTC'
    }).format(date);
  }

  // Client-side rendering - use the user's timezone
  return new Intl.DateTimeFormat('en-US', {
    dateStyle: format,
    timeZone: getUserTimeZone()
  }).format(date);
}

/**
 * Sets a date to the start of the day in UTC
 * @param date The date to modify
 * @returns The same date object set to the start of the day in UTC
 */
export function setToStartOfDayUTC(date: Date): Date {
  // Create a new UTC date with the same year, month, day, but with time set to 00:00:00.000
  return createUTCDate(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate()
  );
}

/**
 * Sets a date to the end of the day in UTC
 * @param date The date to modify
 * @returns The same date object set to the end of the day in UTC
 */
export function setToEndOfDayUTC(date: Date): Date {
  // Create a new UTC date with the same year, month, day, but with time set to 23:59:59.999
  return createUTCDate(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    23,
    59,
    59,
    999
  );
}

/**
 * Calculates the current quarter (1-4) based on the current month
 * @returns The current quarter number (1-4)
 */
export function getCurrentQuarter(): number {
  const currentMonth = new Date().getMonth();
  return Math.floor(currentMonth / 3) + 1; // 1-4
}

/**
 * Gets the current year
 * @returns The current year as a number
 */
export function getCurrentYear(): number {
  return new Date().getFullYear();
}

/**
 * Formats a year number to a 2-digit string (e.g., 2025 -> "25")
 * @param year The year to format
 * @returns The formatted 2-digit year string
 */
export function formatYearTwoDigits(year: number): string {
  return year.toString().slice(-2);
}

/**
 * Determines the year to use for a specific quarter based on the current date
 * @param quarterNumber The quarter number (1-4)
 * @returns The appropriate year for the specified quarter
 */
export function getYearForQuarter(quarterNumber: number): number {
  const currentQuarter = getCurrentQuarter();
  const currentYear = getCurrentYear();

  switch (quarterNumber) {
    case 1:
      // Q1 always uses current year
      return currentYear;
    case 2:
      // Current year's Q2 if we're in or past Q2, otherwise last year's Q2
      return currentQuarter > 2 ? currentYear : currentYear - 1;
    case 3:
      // Current year's Q3 if we're in or past Q3, otherwise last year's Q3
      return currentQuarter > 3 ? currentYear : currentYear - 1;
    case 4:
      // Current year's Q4 if we're in Q4, otherwise last year's Q4
      return currentQuarter === 4 ? currentYear : currentYear - 1;
    default:
      return currentYear;
  }
}

/**
 * Formats a quarter and its year for display
 * @param quarterNumber The quarter number (1-4)
 * @returns Formatted string like "Q1 '25"
 */
export function formatQuarterLabel(quarterNumber: number): string {
  const year = getYearForQuarter(quarterNumber);
  return `Q${quarterNumber} '${formatYearTwoDigits(year)}`;
}

/**
 * Calculates date ranges for quick filters, using UTC dates
 * @param filterType The quick filter type
 * @returns An object containing start and end dates in UTC
 */
export function getDateRangeForQuickFilter(filterType: DateQuickFilterType): {
  start?: Date;
  end?: Date;
} {
  // This is important: all date calculations should be done in UTC for consistent results
  const today = new Date();
  const utcToday = toUTC(today);

  let start: Date | undefined;
  let end: Date | undefined;

  // Get current quarter and year
  const currentMonth = today.getMonth();
  const currentYear = getCurrentYear();
  const currentQuarter = getCurrentQuarter();

  switch (filterType) {
    case 'last30days':
      return getRelativeDateRangeUTC(30);

    case 'last60days':
      return getRelativeDateRangeUTC(60);

    case 'last90days':
      return getRelativeDateRangeUTC(90);

    case 'ytd': {
      // Year to date - from January 1st of current year to today
      start = createUTCDate(currentYear, 0, 1); // Jan 1 of current year in UTC
      end = setToEndOfDayUTC(new Date(utcToday));
      return { start, end };
    }

    case 'q1': {
      // Q1 should always use the current year's Q1
      const year = currentYear;
      start = createUTCDate(year, 0, 1); // Jan 1
      end = createUTCDate(year, 2, 31, 23, 59, 59, 999); // Mar 31
      return { start, end };
    }

    case 'q2': {
      // Current year's Q2 if we're past Q2, otherwise last year's Q2
      const year = getYearForQuarter(2);
      start = createUTCDate(year, 3, 1); // Apr 1
      end = createUTCDate(year, 5, 30, 23, 59, 59, 999); // Jun 30
      return { start, end };
    }

    case 'q3': {
      // Current year's Q3 if we're past Q3, otherwise last year's Q3
      const year = getYearForQuarter(3);
      start = createUTCDate(year, 6, 1); // Jul 1
      end = createUTCDate(year, 8, 30, 23, 59, 59, 999); // Sep 30
      return { start, end };
    }

    case 'q4': {
      // Current year's Q4 if we're in Q4, otherwise last year's Q4
      const year = getYearForQuarter(4);
      start = createUTCDate(year, 9, 1); // Oct 1
      end = createUTCDate(year, 11, 31, 23, 59, 59, 999); // Dec 31
      return { start, end };
    }

    case 'custom':
    default:
      return {};
  }
}

/**
 * Gets a descriptive label for a date quick filter
 * @param filterType The quick filter type
 * @returns A human-readable label for the filter
 */
export function getDateQuickFilterLabel(
  filterType: DateQuickFilterType
): string {
  const currentYear = getCurrentYear();

  switch (filterType) {
    case 'last30days':
      return 'Last 30 Days';
    case 'last60days':
      return 'Last 60 Days';
    case 'last90days':
      return 'Last 90 Days';
    case 'ytd':
      return `Year to Date ${currentYear}`;
    case 'q1':
      return formatQuarterLabel(1);
    case 'q2':
      return formatQuarterLabel(2);
    case 'q3':
      return formatQuarterLabel(3);
    case 'q4':
      return formatQuarterLabel(4);
    case 'custom':
      return 'Custom';
    default:
      return 'Date Range';
  }
}

/**
 * Sets a date to the start of the day (00:00:00.000) in local time
 * @param date The date to modify
 * @returns The same date object set to the start of the day
 */
export function setToStartOfDay(date: Date): Date {
  date.setHours(0, 0, 0, 0);
  return date;
}

/**
 * Sets a date to the end of the day (23:59:59.999) in local time
 * @param date The date to modify
 * @returns The same date object set to the end of the day
 */
export function setToEndOfDay(date: Date): Date {
  date.setHours(23, 59, 59, 999);
  return date;
}

/**
 * Returns a date range that spans the specified number of days before today in local time
 * @param days Number of days before today
 * @returns Object with start and end dates
 */
export function getRelativeDateRange(days: number): { start: Date; end: Date } {
  const today = new Date();
  const start = new Date(today);
  start.setDate(today.getDate() - days);

  return {
    start: setToStartOfDay(start),
    end: setToEndOfDay(new Date(today))
  };
}

/**
 * Returns a date range that spans the specified number of days before today in UTC
 * @param days Number of days before today
 * @returns Object with start and end dates in UTC
 */
export function getRelativeDateRangeUTC(days: number): {
  start: Date;
  end: Date;
} {
  const today = new Date();
  const utcToday = toUTC(today);

  // Calculate the date 'days' days ago
  const start = new Date(utcToday);
  start.setUTCDate(utcToday.getUTCDate() - days);

  return {
    start: setToStartOfDayUTC(start),
    end: setToEndOfDayUTC(new Date(utcToday))
  };
}

/**
 * Converts a Date object to UTC with time set to midnight (00:00:00.000)
 * @param date The date to convert
 * @returns A new Date object in UTC with time set to midnight
 */
export function toUTCDateMidnight(date: Date): Date {
  return new Date(
    Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0)
  );
}

/**
 * Converts a Date object to a UTC timestamp (milliseconds since epoch)
 * @param date The date to convert
 * @returns The UTC timestamp or undefined if date is undefined
 */
export function toUTCTimestamp(date: Date | undefined): number | undefined {
  if (!date) return undefined;
  return toUTCDateMidnight(date).getTime();
}

/**
 * Converts a UTC timestamp to a Date object
 * @param timestamp The UTC timestamp (milliseconds since epoch)
 * @returns A new Date object
 */
export function fromUTCTimestamp(timestamp: number): Date {
  return new Date(timestamp);
}

/**
 * Parses a date string in "YYYY-MM-DD" format, preserving date fields
 * @param dateStr The date string to parse
 * @returns A new Date object
 */
export function parsePreservingDate(dateStr: string): Date {
  const [year, month, day] = dateStr.split('-').map(Number);
  const date = new Date();
  date.setFullYear(year);
  date.setMonth(month - 1);
  date.setDate(day);
  date.setHours(12, 0, 0, 0); // set to noon to avoid boundary issues
  return date;
}

/**
 * Formats a date for display in MM-dd-yyyy format
 * @param date The date to format
 * @returns Formatted date string or "No date" if date is undefined or null
 */
export function formatDisplayDate(date: Date | undefined | null): string {
  if (!date) return 'No date';
  // Use date-fns format to match the original implementation
  return format(date, 'MM-dd-yyyy');
}

/**
 * Formats a time portion of a date using date-fns.
 * @param date The date to format.
 * @param timeFormat The format pattern to use, defaults to 'hh:mm a'.
 * @returns A formatted time string.
 */
export function formatTime(date: Date, timeFormat: string = 'hh:mm a'): string {
  return format(date, timeFormat);
}

/**
 * Formats a timestamp as a relative time string (e.g., "Just now", "5 min ago", "2 hours ago")
 * @param timestamp The timestamp in milliseconds
 * @returns A human-readable relative time string
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  // Less than a minute
  if (diff < 60000) {
    return 'Just now';
  }
  
  // Less than an hour
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} min ago`;
  }
  
  // Less than a day
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)} hours ago`;
  }
  
  // Less than a week
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)} days ago`;
  }
  
  // Default to formatted date
  return formatDisplayDate(new Date(timestamp));
}
