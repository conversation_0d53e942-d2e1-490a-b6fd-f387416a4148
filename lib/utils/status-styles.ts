// Utility functions for handling status styles and formatting

// Helper function to get status styling
export const getStatusStyle = (status: string | undefined) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'in_review':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'rejected':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'escalated':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'cancelled':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    case 'on_hold':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'draft':
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

// Helper function to format status text
export const formatStatus = (status: string | undefined) => {
  return (status || 'draft').replace('_', ' ');
}; 