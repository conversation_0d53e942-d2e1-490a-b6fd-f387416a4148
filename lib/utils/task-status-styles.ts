import { TaskStatusEnum } from "@/zod/tasks-schema";

// Helper function to get Tailwind classes based on status
export const getStatusStyle = (status?: string) => {
  switch (status) {
    case 'todo':
      return 'bg-gray-100 text-gray-800 border-gray-300';
    case 'in_progress':
      return 'bg-blue-100 text-blue-800 border-blue-300';
    case 'blocked':
      return 'bg-red-100 text-red-800 border-red-300';
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-300';
    case 'cancelled':
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300';
  }
};

// Helper function to format status for display
export const formatStatus = (status?: string) => {
  switch (status) {
    case 'todo':
      return 'To Do';
    case 'in_progress':
      return 'In Progress';
    case 'blocked':
      return 'Blocked';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return 'To Do'; // Default to "To Do" if status is undefined
  }
};
