import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL}${path}`;
}

/**
 * Strips HTML tags from a string and returns plain text
 */
export function stripHtml(html: string) {
  const tmp = document.createElement('DIV');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
}

/**
 * Formats a phone number string into (XXX) XXX-XXXX format
 * If the phone number doesn't match the expected format, returns the original string
 */
export function formatPhoneNumber(phoneNumber: string) {
  const cleaned = phoneNumber.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return '(' + match[1] + ') ' + match[2] + '-' + match[3];
  }
  return phoneNumber;
}

/**
 * Converts an OpenAI tool JSON schema to the format expected by the OpenAI API
 * 
 * @param schema The schema object imported from a JSON file
 * @returns An object formatted for use as an OpenAI function tool
 */
export function createOpenAITool(schema: any) {
  // Format for the OpenAI API following the documentation for realtime API
  return {
    type: "function",
    name: schema.name,
    description: schema.description,
    parameters: schema.inputSchema
  };
}
