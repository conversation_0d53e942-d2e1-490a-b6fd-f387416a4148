import { DATE_FILTER_OPTIONS, DateQuickFilterType } from '@/zod/reports-schema';

type DateFilterOption = DateQuickFilterType;

export interface DateFilterParams {
  dateFilter: DateFilterOption;
  startDate?: Date;
  endDate?: Date;
}

export function buildFilterParams(params: DateFilterParams): URLSearchParams {
  const urlParams = new URLSearchParams();

  if (params.dateFilter === DATE_FILTER_OPTIONS.CUSTOM) {
    if (
      params.startDate instanceof Date &&
      !isNaN(params.startDate.getTime())
    ) {
      urlParams.append('startDate', params.startDate.toISOString());
    }
    if (params.endDate instanceof Date && !isNaN(params.endDate.getTime())) {
      urlParams.append('endDate', params.endDate.toISOString());
    }
  }

  urlParams.append('dateFilter', params.dateFilter || '');

  return urlParams;
}

/**
 * Returns a human-readable label for the selected date filter period
 * @param dateFilter - The selected date filter option
 * @returns A string describing the time period
 */
export function getFilterPeriodLabel(dateFilter: DateFilterOption): string {
  if (!dateFilter) {
    return 'period';
  }

  switch (dateFilter) {
    case DATE_FILTER_OPTIONS.LAST_30_DAYS:
      return '30 days';
    case DATE_FILTER_OPTIONS.LAST_60_DAYS:
      return '60 days';
    case DATE_FILTER_OPTIONS.LAST_90_DAYS:
      return '90 days';
    case DATE_FILTER_OPTIONS.Q1:
    case DATE_FILTER_OPTIONS.Q2:
    case DATE_FILTER_OPTIONS.Q3:
    case DATE_FILTER_OPTIONS.Q4:
      return 'quarter';
    case DATE_FILTER_OPTIONS.YEAR_TO_DATE:
      return 'year';
    case DATE_FILTER_OPTIONS.CUSTOM:
      return 'custom period';
    default:
      return 'period';
  }
}
