import { createOpenAITool } from './utils';

/**
 * Project update tool schema
 * This schema is designed to match the structure expected by the Convex
 * updateProjectFromAITool mutation in openai_tools/openai_projects.ts
 *
 * Note: For OpenAI Realtime API, we need to ensure the schema follows strict mode requirements:
 * - additionalProperties must be set to false for each object
 * - All fields in properties should be marked as required or use ["type", "null"] for optional fields
 */
export const updateProjectSchema = {
  "name": "update_project",
  "description": "Update a project after each piece of information is provided. IMPORTANT:\n• Users typically mention project by name, not ID - resolve name to ID first\n• Users use casual references ('make Sarah the driver', 'change status to on hold')\n• Handle informal terms ('on hold'→'paused', 'done'→'completed', 'started'→'in_progress')\n• For ambiguous person references, consider recent context and team structure",
  "inputSchema": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "The project ID. This is required."
      },
      "name": {
        "type": ["string", "null"],
        "description": "New project name - Concise title that captures the essence of the project - Plain text Only."
      },
      "description": {
        "type": ["string", "null"],
        "description": "New project description - Should include: • Executive Summary (1-2 sentence overview) • Desired Outcomes (specific results to achieve) • Timeline & Milestones (critical dates) • Concerns & Risks (potential challenges) • Success Criteria (how to measure success). Format using html tags with bullet points for readability."
      },
      "status": {
        "type": ["string", "null"],
        "enum": ["in_progress", "perpetual", "not_started", "completed", "paused", "cancelled"],
        "description": "Project status - map casual terms ('on hold'→'paused', 'done'→'completed')"
      },
      "priority": {
        "type": ["string", "null"],
        "enum": ["low", "medium", "high", "urgent"],
        "description": "Priority level - map casual terms ('critical'→'urgent', 'important'→'high')"
      },
      "driver": {
        "type": ["string", "null"],
        "description": "Only assign 1 Person that has a user_id. IMPORTANT: Must be a valid user_id. First search directory using directory_search_people_and_teams to find the correct user_id. Do NOT use the _id, email or name directly."
      },
      "contributors": {
        "type": ["array", "null"],
        "description": "Array of People/teams contributing to project. IMPORTANT: Array must contain ONLY valid IDs. For users: use 'user_id' (not _id). For teams: use user.teams (team_id). Always search directory first using directory_search_people_and_teams to get correct IDs. Never guess IDs.",
        "items": {
          "type": "string"
        }
      },
      "informed": {
        "type": ["array", "null"],
        "description": "People/teams to keep informed - handle casual references ('keep leadership updated'). You must use user id and team id",
        "items": {
          "type": "string"
        }
      }
      // Note: 'short_description' is in the backend mutation but not explicitly here.
      // OpenAI might still include it if instructed, and the backend handles optional fields.
    },
    "required": ["id"], // Only ID is strictly required to identify the project
    "additionalProperties": false,
    "description": "Specifies the project ID and the fields to update. Include only the fields you want to change."
  },
  "strict": true
};

/**
 * Create the OpenAI tool object for updating projects
 */
export const updateProjectTool = createOpenAITool(updateProjectSchema);

/**
 * Directory search tool schema
 * This schema is designed to match the structure expected by the Convex
 * searchPeopleAndTeams query in convex/directory/directory.ts
 */
export const searchPeopleAndTeamsSchema = {
  "name": "directory_search_people_and_teams",
  "description": "Run this tool whenever someone is mentioned for the first time to understand who they are. IMPORTANT:\n\n• Trim honorifics before searching (mr, ms, mrs, etc). Use incomplete or partial names or team names and then identify the best match from the results. When searching for a team, omit the 'team' keyword.\n• No need to search again for people already identified earlier in conversation\n• For first mentions, run search to disambiguate who user is talking about\n• Truncate search terms to 1 word initially to get more context about people/teams\n\n • This tool helps you understand WHO the user is talking about before taking action on their requests.",
  "inputSchema": {
    "type": "object",
    "properties": {
      "search": {
        "type": ["string", "null"],
        "description": "The search term to find matching people and teams."
      },
      "type": {
        "type": ["string", "null"],
        "enum": ["user", "team", "all"],
        "description": "Filter results by type: 'user' for people only, 'team' for teams only, or 'all' (default) for both."
      },
      "limit": {
        "type": ["number", "null"],
        "description": "Maximum number of results to return (default: 50, max: 100)."
      }
    },
    "required": [], // search and type are optional
    "additionalProperties": false
  },
  "strict": true
};

/**
 * Create the OpenAI tool object for searching people and teams
 */
export const searchPeopleAndTeamsTool = createOpenAITool(searchPeopleAndTeamsSchema);

/**
 * Export all tools for easy access
 */
export const openAITools = {
  updateProject: updateProjectTool,
  searchPeopleAndTeams: searchPeopleAndTeamsTool
};
