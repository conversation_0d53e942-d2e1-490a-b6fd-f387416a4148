# Zod Usage in This Vision Project

## Table of Contents
- [1. Introduction](#1-introduction)
- [2. Folder Structure & Naming](#2-folder-structure--naming)
- [3. Schema Definition Patterns (Zod 4)](#3-schema-definition-patterns-zod-4)
- [4. Object Schema Modifiers](#4-object-schema-modifiers)
- [5. Numbers & Coercion](#5-numbers--coercion)
- [6. Functions & Promises](#6-functions--promises)
- [7. <PERSON><PERSON><PERSON>](#7-error-handling)
- [8. Convex Integration](#8-convex-integration)
- [9. Common Best Practices](#9-common-best-practices)
- [10. Zod 4 Migration Essentials](#10-zod-4-migration-essentials)
- [11. <PERSON>](#11-quick-reference)
- [12. Example: Bills Domain](#12-example-bills-domain)
- [13. Advanced Patterns](#13-advanced-patterns)
- [14. References](#14-references)
- [15. <PERSON>mmary](#15-summary)

## Quick Reference
- Object: `z.object({ /* fields */ })`
- Enum: `z.enum([...])`
- Array: `z.array(z.string())`
- Tuple: `z.tuple([z.string(), z.number()])`
- Partial: `.partial()`
- Merge: `.merge()`
- Union: `z.union([z.string(), z.number()])`
- Discriminated Union: `z.discriminatedUnion('type', [...])`
- Record: `z.record(z.string(), z.any())`
- Refinement: `.refine(val => condition, { message })`
- Preprocess: `z.preprocess(val => /* */, z.string())`
- Transform: `.transform(val => /* */)`
- Strict Object: `z.strictObject({ ... })`
- Loose Object: `z.looseObject({ ... })`
- Nullable: `.nullable()`
- Optional: `.optional()`
- Default Value: `.default(value)`
- Error Map: `z.string({ error: issue => issue.message })`

## 1. Introduction

This project uses Zod (v4) for schema validation across backend (Convex) and frontend. Zod ensures:
- **Runtime security**: All inputs/outputs validated.
- **Type safety**: Types inferred directly from schemas.
- **Single source of truth**: One definition, many usages.

---

## 2. Folder Structure & Naming

All Zod schemas reside in `zod/`:
```
zod/
  bills-schema.ts
  decisions-schema.ts
  file-management-schema.ts
  ...
```
- **File name**: `<domain>-schema.ts`
- **Exports**:
  - Input schemas (`CreateXInputSchema`)
  - Domain/output schemas (`XSchema`)
  - Inferred types (`export type X = z.infer<typeof XSchema>`)

---

## 3. Schema Definition Patterns (Zod 4)

### a. Objects & Types
```ts
import { z } from 'zod';
export const BillSchema = z.object({
  _id: zid('bills'),
  amount: z.number(),
  billDate: z.number(),
  vendor_id: zid('organizations'),
});
export type Bill = z.infer<typeof BillSchema>;
```

### b. Enums
```ts
export const BillStatus = z.enum([
  'PAID', 'UNPAID', 'PARTIALLY_PAID'
]);
```

### c. Top-Level Validators
```ts
const email = z.email();         // ✅
const uuid = z.uuid();           // ✅
const url  = z.url();            // ✅
const ipv4 = z.ipv4();           // ✅
const emoji = z.emoji();         // ✅
```
> Deprecated: `z.string().email()`, `z.string().uuid()`.

### d. Records & Maps
```ts
// Key/value schemas required in Zod 4
const map1 = z.record(z.string(), z.number());
```

### e. Arrays & Tuples
```ts
const arr = z.array(z.string());       // string[]
const nonempty = z.array(z.string()).min(1);  // nonempty string[]
const tuple = z.tuple([z.string(), z.number()]);
```
> `.nonempty()` is alias for `.min(1)`.

### f. Refinements & Preprocessing
```ts
// Custom validation
z.string().min(5, { error: 'Too short' });
// Pre-parse/coercion
z.preprocess(val => String(val), z.string());
z.transform(val => val.trim());
```
> `.refine()` no longer narrows types. Use `z.transform()` and `z.preprocess()`.

---

## 4. Object Schema Modifiers

- `z.strictObject({ ... })` ⇒ no unknown keys  
- `z.looseObject({ ... })` ⇒ allow extra keys  
- Default `z.object()` ⇒ strip unknown keys  
> Deprecated: `.strict()`, `.passthrough()`, `.strip()`, `.nonstrict()`

---

## 5. Numbers & Coercion

- `z.number()` rejects `Infinity`/`-Infinity`  
- `.int()` enforces safe integer range  
- `z.coerce.boolean()` and other `z.coerce.*` take `unknown` input  

---

## 6. Functions & Promises

- `z.function({ input: [...], output: ... }).implement()`  
- `.implementAsync()` for async functions  
> Deprecated: `z.promise()`, old `.args()` / `.returns()` API  

---

## 7. Error Handling

- **Unified `error` param** for messages and maps  
  ```ts
  z.string({ 
    error: (issue) => `Invalid: ${issue.message}` 
  });
  ```
- **Drop**: `invalid_type_error`, `required_error`  
- **Drop**: `.format()`, `.flatten()`, `.formErrors`  
- **Use**: `z.treeifyError()` for human-readable error trees  
- **Note**: `ZodError` no longer extends `Error`; use `instanceof z.ZodError`

---

## 8. Convex Integration

Use custom wrappers to enforce schemas on queries/mutations:
```ts
export const createBill = zMutation({
  args: { bill: BillInputSchema },
  output: z.string(),
  handler: async (ctx, args) => { ... }
});
```
- Input and output schemas declared in `zod/`
- IDs validated with `zid('tableName')`

---

## 9. Common Best Practices

1. **Partial schemas** for updates:  
   `const UpdateBill = BaseBillSchema.partial();`
2. **Descriptive `.describe()`** for auto-docs.  
3. **Centralize refinements** (e.g., timestamp rules).  
4. **Reuse building blocks**, not copy/paste.

---

## 10. Zod 4 Migration Essentials

A quick checklist of Zod 4 breaking changes:

- **Error API**:  
  - Replace `message` param & `errorMap` with unified `error` function.  
  - Remove `invalid_type_error` & `required_error`.  
- **Error Formatting**:  
  - Replace `.format()` / `.flatten()` with `z.treeifyError()`.  
  - `ZodError` no longer extends `Error`.  
- **Object Modifiers**:  
  - `.strict()` → `z.strictObject()`  
  - `.passthrough()` → `z.looseObject()`  
  - `.strip()`, `.nonstrict()` removed.  
- **String Validators**:  
  - `z.string().email()` deprecated → `z.email()`  
  - `.ip()` / `.cidr()` split into `.ipv4()` / `.ipv6()`, `.cidrv4()` / `.cidrv6()`  
- **Enums & Records**:  
  - `z.nativeEnum()` → `z.enum()`  
  - `z.record()` now requires two args.  
- **Numbers**:  
  - No `Infinity` / `-Infinity`.  
  - `.int()` only safe integers.  
- **Functions & Promises**:  
  - `z.promise()` removed.  
  - `z.function()` uses new factory API.  
- **Advanced Internals** (for library authors):  
  - `._def` moved to `._zod.def`  
  - `ZodEffects` → unified “checks” on schemas.  
  - `.deepPartial()`, `.nonstrict()` removed.  

For complete details, consult the official [Zod 4 migration guide](https://github.com/colinhacks/zod/blob/main/CHANGELOG.md) and the introductory post on performance improvements and new APIs.

---

## 11. References

- Zod Documentation: https://zod.dev/  
- Zod 4 Migration Guide: https://github.com/colinhacks/zod/blob/main/CHANGELOG.md  
- Convex Zod Integration: https://docs.convex.dev/typescript/zod

### b. Updating Schemas

- When the data model changes (e.g., Convex schema update), update the Zod schema to match.
- Update all usages of the schema in Convex functions and frontend code.

---

## 8. Troubleshooting & Tips

- **Parsing errors:**  
  Use `safeParse` for error handling:
  ```ts
  const result = MySchema.safeParse(data);
  if (!result.success) { ... }
  ```

- **Auto-formatting:**  
  When editing schemas, be aware that the codebase may auto-format files (e.g., line breaks, indentation). Always use the final, formatted file as the reference for further edits.

- **TypeScript code completion:**  
  Use inferred types for full code completion and type safety in both backend and frontend.

---

## 9a. Error Handling and User-Friendly Error Reporting

### Using `treeifyError` for Human-Readable Errors

When using Zod's `safeParse`, the error object can be deeply nested and hard to read. The `z.treeifyError()` utility (from [zod-tree](https://github.com/colinhacks/zod-tree) or similar helpers) converts Zod errors into a readable tree structure, ideal for API responses and debugging.

**Example:**
```ts
const result = userCreatedWebhookSchema.safeParse(payload);
if (!result.success) {
  console.error(z.treeifyError(result.error));
  return { success: false, error: z.treeifyError(result.error) };
}
```

This pattern is especially useful for returning validation errors to clients in a structured, user-friendly way.

---

## 9b. Webhook Validation Patterns

When validating external data (e.g., webhooks), combine type guards, discriminated unions, and centralized validation for robust runtime type narrowing.

**Example:**
```ts
export function validateWebhook(payload: unknown) {
  if (!payload || typeof payload !== "object" || !("event" in payload)) {
    return { success: false, error: { message: "Missing event" } };
  }
  switch ((payload as any).event) {
    case "user.created": return validateUserCreatedWebhook(payload);
    // other cases...
  }
}
```
- Use a top-level type guard to check for required fields.
- Use discriminated unions to branch validation logic.
- Each branch can use a Zod schema for deep validation.

---

## 9c. Loose Object Handling

For schemas that must allow extra properties (e.g., third-party API payloads), use `z.looseObject` (or `z.object({ ... }).passthrough()`):

**Example:**
```ts
const ThirdPartyPayloadSchema = z.object({
  importantField: z.string(),
}).passthrough(); // Allows unknown extra fields
```
This ensures you validate what you care about, but don't fail on unexpected properties.

---

## 9d. Email Validation Example

Zod provides built-in validators for common patterns, such as email addresses:

**Example:**
```ts
const UserSchema = z.object({
  email: z.string().email(),
});
```
This ensures only valid email addresses are accepted.

---

## 9e. Standardized Return Types for Validation

Adopt a consistent "Result" pattern for validation results:

**Example:**
```ts
const result = schema.safeParse(data);
if (result.success) {
  return { success: true, data: result.data };
} else {
  return { success: false, error: z.treeifyError(result.error) };
}
```
This makes error handling and type narrowing predictable across your codebase.

---

## 9f. Boundary Validation and Error Reporting

Always validate data at system boundaries (e.g., API endpoints, webhooks, external integrations) and report errors in a structured way. Use the patterns above to ensure:
- All incoming data is validated before use.
- All validation failures are reported with clear, actionable error messages.
- No untrusted data is allowed to propagate unchecked.

---

## 9g. Type Safety Comparison: Fojo vs. Vision Approaches

After analyzing both approaches, here are some key differences and recommendations:

### Fojo Features (Notable for External Data)

- **Error Handling with `treeifyError`:**  
  Fojo uses `z.treeifyError()` to generate human-readable error structures for API responses and debugging.
- **Webhook Validation Pattern:**  
  Sophisticated pattern for validating webhooks using type guards, discriminated unions, and centralized validation.
- **Loose Object Handling:**  
  Uses `z.looseObject`/`.passthrough()` for schemas that must permit extra properties.
- **Email Validation Example:**  
  Explicit use of `z.string().email()` for strong email validation.
- **Standardized Return Types:**  
  Consistent `{ success, data }` or `{ success, error }` result pattern for all validation.
- **Boundary Validation:**  
  Emphasizes validating all external data at system boundaries.

### Vision Features (Notable for Internal Consistency)

- **Convex Integration:**  
  Comprehensive type safety for Convex integration, with strongly-typed IDs and custom query/mutation wrappers.
- **Single Source of Truth:**  
  All schemas and types are defined in one place and reused everywhere.
- **TypeScript Inference:**  
  Types are always inferred from schemas, ensuring consistency.

### Recommendation

For best results, combine Vision's comprehensive internal validation and Convex integration with Fojo's robust external data validation and error handling patterns. This ensures:
- All internal and external data is validated.
- Errors are reported in a user-friendly, structured way.
- Type safety is enforced at every boundary.

---

## 9. Example: Bills Domain

**Schema (zod/bills-schema.ts):**
```ts
export const BillSchema = z.object({
  _id: zid('bills'),
  amount: z.number(),
  billDate: z.number(),
  ...
});
export type Bill = z.infer<typeof BillSchema>;
```

**Convex Function (convex/bills.ts):**
```ts
export const get = zQuery({
  args: { id: zid('bills') },
  output: BillSchema,
  handler: async (ctx, args) => {
    const bill = await ctx.db.get(args.id);
    if (!bill) throw new Error('Bill not found');
    return BillSchema.parse(bill);
  }
});
```

---

## 10. Advanced Patterns

- **Recursive schemas:**  
  For hierarchies (e.g., tags), use `z.lazy`:
  ```ts
  export const TagWithChildrenSchema = TagSchema.extend({
    children: z.array(z.lazy(() => TagWithChildrenSchema)).default([])
  });
  ```

- **Discriminated unions:**  
  For polymorphic relationships:
  ```ts
  export const RelationshipLinkSchemaDiscriminated = z.discriminatedUnion("subject_type", [
    z.object({ subject_type: z.literal("task"), subject_id: zid("tasks") }),
    ...
  ]);
  ```

---

## 11. References

- [Zod Documentation](https://zod.dev/)
- [Convex Docs: Type Safety](https://docs.convex.dev/typescript/zod)
- See also: `zguide/guide-zod-schema.md` for general Zod patterns.

---

## 12. Summary

- All data models, API inputs/outputs, and types are defined in `zod/` using Zod.
- Convex functions use these schemas for runtime validation and type inference.
- This approach ensures safety, maintainability, and a single source of truth for all data structures in the project.
