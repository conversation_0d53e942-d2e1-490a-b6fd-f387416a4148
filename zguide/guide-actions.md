---
description: guide on how to write convex actions and zaction
globs: *.*
alwaysApply: false
---
<convex_guidelines>
  <zod_action_guidelines>
    <overview>
      <!--
        Guide to Writing Convex Actions with Zod Validation (Using zAction)
        ---------------------------------------------------------
        This section describes how to:

        1. Use zAction (from zCustomAction) to define Convex Actions with Zod-based validation.
        2. Follow the new function syntax (from <function_guidelines>).
        3. Integrate the Vercel AI SDK with Google's "flash" model in a Node.js environment.
        4. Incorporate error handling (single operations, bulk operations) and documentation best practices.
      -->
    </overview>

    <basic_structure>
      <!--
        Example "functions.ts" showing how to define zAction using zCustomAction(action, NoOp).
        This file wraps Convex's core function decorators with Zod-based helpers.
      -->
      <file name="convex/functions.ts">
        <![CDATA[
        import { z } from "zod";
        import { zCustomQuery, zCustomMutation, zCustomAction } from "convex-helpers/server/zod";
        import { NoOp } from "convex-helpers/server/customFunctions";
        import { query, mutation, action } from "./_generated/server";

        /**
         * A wrapper around Convex's query function that uses Zod for validation.
         * Provides better type safety and more flexible validation.
         */
        export const zQuery = zCustomQuery(query, NoOp);

        /**
         * A wrapper around Convex's mutation function that uses Zod for validation.
         * Provides better type safety and more flexible validation.
         */
        export const zMutation = zCustomMutation(mutation, NoOp);

        /**
         * A wrapper around Convex's action function that uses Zod for validation.
         * Provides better type safety and more flexible validation.
         */
        export const zAction = zCustomAction(action, NoOp);
        ]]>
      </file>
    </basic_structure>

    <single_operation_example>
      <!--
        Demonstrates a basic Convex action with Zod validation using the new function syntax.
        Also shows minimal placeholders for args/returns from convex/values, with real checks in Zod.
      -->
      <file name="convex/categorizeText.ts">
        <![CDATA[
        "use node"; // <action_guidelines>: Required if using Node modules

        import { z } from "zod";
        import { zAction } from "./functions";
        import { v } from "convex/values";

        /**
         * Example Zod schema for the action's arguments.
         * Use z.infer<typeof CategorizeTextArgs> for type derivation if needed.
         */
        const CategorizeTextArgs = z.object({
          text: z.string().min(1, "Text is required"),
        });

        /**
         * Example Zod schema for the action's return type.
         * Here, it enforces that the result is a string.
         */
        const CategorizeTextReturns = z.string();

        /**
         * categorizeText action
         *
         * @description
         * Simple example of categorizing text (placeholder). Demonstrates:
         *  - Zod input validation
         *  - Zod output validation
         *  - New function syntax for actions
         *
         * @example
         * const result = await convex.runAction(api.categorizeText, { text: "Hello World" });
         * console.log(result); // "Placeholder Category"
         *
         * @throws {ZodError} If args.text is invalid (empty or missing).
         */
        export const categorizeText = zAction({
          args: {
            text: z.string(), // minimal usage to match new function syntax
          },
          returns: z.string(),
          handler: async (ctx, args) => {
            // Validate input with Zod
            const { text } = CategorizeTextArgs.parse(args);

            // Perform categorization (placeholder)
            const category = "Placeholder Category";

            // Validate output before returning
            return CategorizeTextReturns.parse(category);
          },
        });
        ]]>
      </file>
    </single_operation_example>

    <vercel_ai_integration>
      <!--
        Demonstrates usage of the Vercel AI SDK with Google's "flash" model in a Node environment.
        Also shows doc comments and robust Zod validation.
      -->
      <file name="convex/categorizeTextWithAI.ts">
        <![CDATA[
        "use node"; // <action_guidelines>

        import { z } from "zod";
        import { zAction } from "./functions";
        import { v } from "convex/values";
        import { generateText } from "ai";
        import { google } from "@ai-sdk/google";

        // Define input schema
        const CategorizeTextArgs = z.object({
          text: z.string().min(1, "Text is required"),
        });

        // Define output schema
        const CategorizeTextReturns = z.string();

        /**
         * categorizeTextWithAI action
         *
         * @description
         * Integrates with the Vercel AI SDK and Google's "gemini-1.5-flash" model
         * to categorize text. Demonstrates Node environment usage, plus input/output
         * validation with Zod. 
         *
         * @example
         * const result = await convex.runAction(api.categorizeTextWithAI, { text: "Explain quantum computing" });
         * console.log(result); // e.g., "Technology"
         *
         * @throws {ZodError} Thrown if input is invalid or if the AI response cannot be parsed as a string.
         */
        export const categorizeTextWithAI = zAction({
          args: {
            text: z.string(),
          },
          returns: z.string(),
          handler: async (ctx, args) => {
            // 1) Validate input
            const { text } = CategorizeTextArgs.parse(args);

            // 2) Initialize the Google "flash" model
            const model = google("gemini-2.5-flash-preview-04-17");

            // 3) Craft prompt and call the AI
            const prompt = `Categorize the following text into a broad topic:\n"${text}"\nCategory:`;
            const { text: aiResponse } = await generateText({ model, prompt });

            // 4) Validate output
            return CategorizeTextReturns.parse(aiResponse.trim());
          },
        });
        ]]>
      </file>
    </vercel_ai_integration>

    <error_handling>
      <!--
        Section 7: Error Handling
        - Throw errors for single operations
        - Track failures in bulk operations
        - Return both successes and failures
      -->
      <file name="convex/bulkCategorize.ts">
        <![CDATA[
        "use node";

        import { z } from "zod";
        import { zAction } from "./functions";
        import { v } from "convex/values";
        import { generateText } from "ai";
        import { google } from "@ai-sdk/google";

        /**
         * Zod schema for array of strings. We'll categorize each string in a single action.
         */
        const BulkCategorizeArgs = z.object({
          texts: z.array(z.string().min(1)),
        });

        /**
         * We'll return an object containing successes and failures.
         */
        const BulkCategorizeReturns = z.object({
          successes: z.array(z.object({ index: z.number(), category: z.string() })),
          failures: z.array(z.object({ index: z.number(), error: z.string() })),
        });

        /**
         * bulkCategorize action
         *
         * @description
         * Demonstrates error handling for multiple text items. Each text is processed individually,
         * with partial successes and failures tracked. 
         *
         * @throws {ZodError} If overall input is invalid (e.g., texts is not an array of strings).
         */
        export const bulkCategorize = zAction({
          args: {
            texts: z.array(z.string()),
          },
          returns: z.any(), // We'll rely on Zod for the precise shape
          handler: async (ctx, args) => {
            const { texts } = BulkCategorizeArgs.parse(args);

            const model = google("gemini-2.5-flash-preview-04-17");
            const successes: { index: number; category: string }[] = [];
            const failures: { index: number; error: string }[] = [];

            // Process each text individually
            for (let i = 0; i < texts.length; i++) {
              const text = texts[i];
              try {
                // Craft prompt
                const prompt = `Categorize this text:\n"${text}"\nCategory:`;
                const { text: response } = await generateText({ model, prompt });

                successes.push({ index: i, category: response.trim() });
              } catch (err) {
                failures.push({ index: i, error: (err as Error).message });
              }
            }

            // Validate final output shape
            return BulkCategorizeReturns.parse({ successes, failures });
          },
        });
        ]]>
      </file>
    </error_handling>

    <documentation>
      <!--
        Section 8: Documentation
        - JSDoc comments with examples
        - Document error states and edge cases
        Demonstration is already shown in each example: doc comments above each exported action.
      -->
      <notes>
        <![CDATA[
        - Use @description to briefly explain the function purpose.
        - Use @example to show how to call the function from either the client or another Convex function.
        - Use @throws to list possible error classes (e.g., ZodError, custom errors).
        - For edge cases, highlight behaviors such as empty arrays, invalid text, or external API failures.
        ]]>
      </notes>
    </documentation>

    <typing_args>
      <!--
        Properly Typing Args in Handlers
        ------------------------------------------------
        Let type inference do most of the work. If you need derived types:
           type CategorizeTextArgsType = z.infer<typeof CategorizeTextArgs>;
           Then in your code: (args: CategorizeTextArgsType) => { ... }
      -->
      <file name="convex/typedArgsExample.ts">
        <![CDATA[
        import { z } from "zod";

// Example of deriving a type from Zod
const ExampleArgsSchema = z.object({
  userId: z.string(),
});

type ExampleArgsType = z.infer<typeof ExampleArgsSchema>;

// "ExampleArgsType" is now { userId: string }

        ]]>
      </file>
    </typing_args>

    <conclusion>
      <!--
        By combining Convex's new function syntax, zAction wrappers, and Zod for input/output validation,
        you can build robust actions that integrate external services (like Vercel AI + Google).
        This structure aligns with <function_guidelines>, <action_guidelines>, and general best practices
        for error handling, documentation, and type safety.
      -->
    </conclusion>
  </zod_action_guidelines>
</convex_guidelines>