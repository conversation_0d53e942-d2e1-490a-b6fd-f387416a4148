#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios, { AxiosInstance, AxiosError } from 'axios';

// Environment variables for authentication
const BILL_USER = process.env.BILL_USER;
const BILL_PASS = process.env.BILL_PASS;
const BILL_ORG_ID = process.env.BILL_ORG_ID;
const BILL_DEV_KEY = process.env.BILL_DEV_KEY;
const BILL_API_URL = process.env.BILL_API_URL || 'https://gateway.stage.bill.com/connect';

// Check if required environment variables are set
if (!BILL_USER || !BILL_PASS || !BILL_ORG_ID || !BILL_DEV_KEY) {
  console.error('[Error] Missing required environment variables. Please set BILL_USER, BILL_PASS, BILL_ORG_ID, and BILL_DEV_KEY.');
  process.exit(1);
}

// Session management
interface SessionInfo {
  sessionId: string;
  expiresAt: number; // Timestamp when the session expires
}

// Type definitions for Bill.com API responses
interface LoginResponse {
  sessionId: string;
  userId: string;
  orgId: string;
  [key: string]: any;
}

interface BillComError {
  code: string;
  message: string;
  severity: string;
  [key: string]: any;
}

class BillDotComServer {
  private server: Server;
  private axiosInstance: AxiosInstance;
  private sessionInfo: SessionInfo | null = null;
  private readonly SESSION_TIMEOUT_MS = 30 * 60 * 1000; // 30 minutes in milliseconds

  constructor() {
    console.error('[Setup] Initializing Bill.com MCP server...');
    
    this.server = new Server(
      {
        name: 'bill-dotcom-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.axiosInstance = axios.create({
      baseURL: BILL_API_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add request interceptor to add authentication headers
    this.axiosInstance.interceptors.request.use(async (config) => {
      // Always add devKey to headers
      config.headers['devKey'] = BILL_DEV_KEY;
      
      // If we need a session and don't have a valid one, get one
      if (config.url !== '/v3/login' && (!this.sessionInfo || this.isSessionExpired())) {
        console.error('[Auth] Session expired or not available. Getting a new session...');
        await this.login();
      }
      
      // Add sessionId to headers if available
      if (this.sessionInfo && config.url !== '/v3/login') {
        config.headers['sessionId'] = this.sessionInfo.sessionId;
      }
      
      return config;
    });

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        if (error.response) {
          const status = error.response.status;
          const data = error.response.data as BillComError;
          
          console.error(`[API Error] Status: ${status}, Code: ${data?.code}, Message: ${data?.message}`);
          
          // Handle session expiration
          if (status === 401 || (data?.code && ['BDC_0100', 'BDC_0101', 'BDC_0102'].includes(data.code))) {
            this.sessionInfo = null; // Clear the invalid session
          }
        } else if (error.request) {
          console.error('[Network Error] No response received:', error.message);
        } else {
          console.error('[Request Error]', error.message);
        }
        
        return Promise.reject(error);
      }
    );

    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    
    // Handle process termination
    process.on('SIGINT', async () => {
      console.error('[Shutdown] Closing server...');
      if (this.sessionInfo) {
        try {
          await this.logout();
        } catch (error) {
          console.error('[Shutdown] Error during logout:', error);
        }
      }
      await this.server.close();
      process.exit(0);
    });
  }

  private isSessionExpired(): boolean {
    if (!this.sessionInfo) return true;
    return Date.now() >= this.sessionInfo.expiresAt;
  }

  private async login(): Promise<void> {
    console.error('[Auth] Logging in to Bill.com...');
    try {
      const payload = {
        username: BILL_USER,
        password: BILL_PASS,
        organizationId: BILL_ORG_ID,
        devKey: BILL_DEV_KEY
      };

      const response = await this.axiosInstance.post<LoginResponse>('/v3/login', payload);
      
      this.sessionInfo = {
        sessionId: response.data.sessionId,
        expiresAt: Date.now() + this.SESSION_TIMEOUT_MS
      };
      
      console.error('[Auth] Login successful. Session established.');
    } catch (error) {
      console.error('[Auth] Login failed:', error);
      throw new McpError(ErrorCode.InternalError, 'Failed to authenticate with Bill.com');
    }
  }

  private async logout(): Promise<void> {
    if (!this.sessionInfo) return;
    
    console.error('[Auth] Logging out from Bill.com...');
    try {
      await this.axiosInstance.post('/v3/logout');
      console.error('[Auth] Logout successful.');
    } catch (error) {
      console.error('[Auth] Logout failed:', error);
    } finally {
      this.sessionInfo = null;
    }
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_vendors',
          description: 'Retrieves a list of vendors from Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              limit: {
                type: 'number',
                description: 'Maximum number of vendors to return (default: 10)',
              },
              nextPage: {
                type: 'string',
                description: 'Token for pagination',
              }
            },
            additionalProperties: false
          },
        },
        {
          name: 'create_vendor',
          description: 'Creates a new vendor in Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                description: 'Vendor name',
              },
              email: {
                type: 'string',
                description: 'Vendor email',
              },
              address1: {
                type: 'string',
                description: 'Address line 1',
              },
              address2: {
                type: 'string',
                description: 'Address line 2',
              },
              city: {
                type: 'string',
                description: 'City',
              },
              state: {
                type: 'string',
                description: 'State/Province',
              },
              zip: {
                type: 'string',
                description: 'ZIP/Postal code',
              },
              country: {
                type: 'string',
                description: 'Country',
              },
              phone: {
                type: 'string',
                description: 'Phone number',
              }
            },
            required: ['name'],
            additionalProperties: false
          },
        },
        {
          name: 'get_bills',
          description: 'Retrieves a list of bills from Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              limit: {
                type: 'number',
                description: 'Maximum number of bills to return (default: 10)',
              },
              nextPage: {
                type: 'string',
                description: 'Token for pagination',
              }
            },
            additionalProperties: false
          },
        },
        {
          name: 'create_bill',
          description: 'Creates a new bill in Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              vendorId: {
                type: 'string',
                description: 'ID of the vendor',
              },
              invoiceNumber: {
                type: 'string',
                description: 'Invoice number',
              },
              invoiceDate: {
                type: 'string',
                description: 'Invoice date (YYYY-MM-DD)',
              },
              dueDate: {
                type: 'string',
                description: 'Due date (YYYY-MM-DD)',
              },
              billLineItems: {
                type: 'array',
                description: 'Line items for the bill',
                items: {
                  type: 'object',
                  properties: {
                    description: {
                      type: 'string',
                      description: 'Description of the line item',
                    },
                    amount: {
                      type: 'number',
                      description: 'Amount of the line item',
                    }
                  },
                  required: ['description', 'amount']
                }
              }
            },
            required: ['vendorId', 'invoiceNumber', 'invoiceDate', 'dueDate', 'billLineItems'],
            additionalProperties: false
          },
        },
        {
          name: 'get_payments',
          description: 'Retrieves a list of payments from Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              limit: {
                type: 'number',
                description: 'Maximum number of payments to return (default: 10)',
              },
              nextPage: {
                type: 'string',
                description: 'Token for pagination',
              }
            },
            additionalProperties: false
          },
        },
        {
          name: 'create_payment',
          description: 'Schedules a payment for a bill',
          inputSchema: {
            type: 'object',
            properties: {
              billId: {
                type: 'string',
                description: 'ID of the bill to pay',
              },
              bankAccountId: {
                type: 'string',
                description: 'ID of the bank account to use for payment',
              },
              processDate: {
                type: 'string',
                description: 'Date to process the payment (YYYY-MM-DD)',
              },
              amount: {
                type: 'number',
                description: 'Amount to pay (must match the bill amount)',
              },
              description: {
                type: 'string',
                description: 'Payment description',
              },
              vendorId: {
                type: 'string',
                description: 'ID of the vendor to pay',
              },
              transactionNumber: {
                type: 'string',
                description: 'Payment transaction reference used as an external identifier',
              },
              cardFundingPurpose: {
                type: 'string',
                description: 'Card funding purpose (required for CARD_ACCOUNT funding type)',
              },
              requestCheckDeliveryType: {
                type: 'string',
                description: 'Check delivery type (e.g., STANDARD, USPS_PRIORITY, UPS_1DAY)',
                enum: ['STANDARD', 'USPS_PRIORITY', 'UPS_1DAY', 'UPS_2DAY', 'UPS_3DAY', 'RTP_DELIVERY']
              },
              requestPayFaster: {
                type: 'boolean',
                description: 'Set as true if the payment is eligible for faster payments',
              }
            },
            required: ['billId', 'bankAccountId', 'processDate', 'amount'],
            additionalProperties: false
          },
        },
        {
          name: 'get_funding_accounts',
          description: 'Retrieves a list of funding accounts (banks) from Bill.com',
          inputSchema: {
            type: 'object',
            properties: {
              limit: {
                type: 'number',
                description: 'Maximum number of accounts to return (default: 10)',
              },
              nextPage: {
                type: 'string',
                description: 'Token for pagination',
              }
            },
            additionalProperties: false
          },
        },
        {
          name: 'get_session_info',
          description: 'Retrieves the current session information',
          inputSchema: {
            type: 'object',
            properties: {},
            additionalProperties: false
          },
        }
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      console.error(`[Tool] Executing tool: ${request.params.name}`);
      
      try {
        switch (request.params.name) {
          case 'get_vendors':
            return await this.getVendors(request.params.arguments);
          case 'create_vendor':
            return await this.createVendor(request.params.arguments);
          case 'get_bills':
            return await this.getBills(request.params.arguments);
          case 'create_bill':
            return await this.createBill(request.params.arguments);
          case 'get_payments':
            return await this.getPayments(request.params.arguments);
          case 'get_funding_accounts':
            return await this.getFundingAccounts(request.params.arguments);
          case 'create_payment':
            return await this.createPayment(request.params.arguments);
          case 'get_session_info':
            return await this.getSessionInfo();
          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${request.params.name}`
            );
        }
      } catch (error) {
        console.error(`[Tool Error] ${request.params.name}:`, error);
        
        if (error instanceof McpError) {
          throw error;
        }
        
        // Handle axios errors
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;
          const status = axiosError.response?.status;
          const data = axiosError.response?.data as BillComError;
          
          return {
            content: [
              {
                type: 'text',
                text: `Bill.com API Error: ${status} - ${data?.message || axiosError.message}`,
              },
            ],
            isError: true,
          };
        }
        
        // Handle other errors
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${(error as Error).message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async getVendors(args: any) {
    console.error('[API] Getting vendors from Bill.com...');
    
    const limit = args?.limit || 10;
    const params: any = { limit };
    
    if (args?.nextPage) {
      params.nextPage = args.nextPage;
    }
    
    try {
      const response = await this.axiosInstance.get('/v3/vendors', { params });
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Get vendors failed:', error);
      throw error;
    }
  }

  private async createVendor(args: any) {
    console.error('[API] Creating vendor in Bill.com...');
    
    // Validate required name field
    if (!args?.name) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Vendor name is required',
          },
        ],
        isError: true,
      };
    }
    
    const vendorData: any = {
      name: args.name,
    };
    
    if (args.email) vendorData.email = args.email;
    if (args.phone) vendorData.phone = args.phone;
    
    // Add address if provided - using correct field names according to API docs
    if (args.address1 || args.city || args.state || args.zip || args.country) {
      vendorData.address = {
        // Required fields according to API docs
        line1: args.address1 || '',
        city: args.city || '',
        zipOrPostalCode: args.zip || '',
        country: args.country || 'US', // Default to US if not provided
      };
      
      // Optional address fields
      if (args.address2) vendorData.address.line2 = args.address2;
      if (args.state) vendorData.address.stateOrProvince = args.state;
    }
    
    console.error('[API] Vendor data:', JSON.stringify(vendorData, null, 2));
    
    try {
      const response = await this.axiosInstance.post('/v3/vendors', vendorData);
      
      console.error('[API] Vendor created successfully:', response.data.id);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Create vendor failed:', error);
      throw error;
    }
  }

  private async getBills(args: any) {
    console.error('[API] Getting bills from Bill.com...');
    
    const limit = args?.limit || 10;
    const params: any = { limit };
    
    if (args?.nextPage) {
      params.nextPage = args.nextPage;
    }
    
    try {
      const response = await this.axiosInstance.get('/v3/bills', { params });
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Get bills failed:', error);
      throw error;
    }
  }

  private async createBill(args: any) {
    console.error('[API] Creating bill in Bill.com...');
    
    // Validate required fields
    if (!args?.vendorId) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Vendor ID is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.invoiceNumber) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Invoice number is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.invoiceDate) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Invoice date is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.dueDate) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Due date is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.billLineItems || !Array.isArray(args.billLineItems) || args.billLineItems.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: At least one bill line item is required',
          },
        ],
        isError: true,
      };
    }
    
    try {
      // Format the bill data according to the API documentation
      const billData = {
        vendorId: args.vendorId,
        dueDate: args.dueDate,
        description: args.description || '',
        billLineItems: args.billLineItems.map((item: any) => ({
          amount: item.amount,
          description: item.description || ''
        })),
        invoice: {
          invoiceNumber: args.invoiceNumber,
          invoiceDate: args.invoiceDate
        }
      };
      
      console.error('[API] Bill data:', JSON.stringify(billData, null, 2));
      
      const response = await this.axiosInstance.post('/v3/bills', billData);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Create bill failed:', error);
      throw error;
    }
  }

  private async getPayments(args: any) {
    console.error('[API] Getting payments from Bill.com...');
    
    const limit = args?.limit || 10;
    const params: any = { limit };
    
    if (args?.nextPage) {
      params.nextPage = args.nextPage;
    }
    
    try {
      const response = await this.axiosInstance.get('/v3/payments', { params });
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Get payments failed:', error);
      throw error;
    }
  }

  private async getFundingAccounts(args: any) {
    console.error('[API] Getting funding accounts from Bill.com...');
    
    const limit = args?.limit || 10;
    const params: any = { limit };
    
    if (args?.nextPage) {
      params.nextPage = args.nextPage;
    }
    
    try {
      const response = await this.axiosInstance.get('/v3/funding-accounts/banks', { params });
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Get funding accounts failed:', error);
      throw error;
    }
  }

  private async getSessionInfo() {
    console.error('[API] Getting session information...');
    
    // If no session exists, try to create one
    if (!this.sessionInfo || this.isSessionExpired()) {
      try {
        await this.login();
      } catch (error) {
        console.error('[API] Failed to establish session:', error);
        return {
          content: [
            {
              type: 'text',
              text: 'No active session. Failed to establish a new session.',
            },
          ],
          isError: true,
        };
      }
    }
    
    // Return the session information
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            sessionId: this.sessionInfo?.sessionId,
            expiresAt: this.sessionInfo?.expiresAt,
            expiresIn: this.sessionInfo ? Math.floor((this.sessionInfo.expiresAt - Date.now()) / 1000) + ' seconds' : 'N/A',
            isExpired: this.isSessionExpired(),
          }, null, 2),
        },
      ],
    };
  }

  private async createPayment(args: any) {
    console.error('[API] Creating payment in Bill.com...');
    
    // Validate required fields
    if (!args?.billId) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Bill ID is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.bankAccountId) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Bank account ID is required',
          },
        ],
        isError: true,
      };
    }
    
    if (!args?.processDate) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Process date is required',
          },
        ],
        isError: true,
      };
    }
    
    if (args?.amount === undefined || args?.amount === null) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: Payment amount is required',
          },
        ],
        isError: true,
      };
    }
    
    try {
      // Construct the payment request according to the API documentation
      const paymentRequest: any = {
        billId: args.billId,
        processDate: args.processDate,
        fundingAccount: {
          type: "BANK_ACCOUNT",
          id: args.bankAccountId
        },
        amount: args.amount,
        processingOptions: {
          createBill: false
        }
      };
      
      // Add optional fields if provided
      if (args.description) {
        paymentRequest.description = args.description;
      }
      
      if (args.vendorId) {
        paymentRequest.vendorId = args.vendorId;
      }
      
      if (args.transactionNumber) {
        paymentRequest.transactionNumber = args.transactionNumber;
      }
      
      if (args.cardFundingPurpose) {
        paymentRequest.cardFundingPurpose = args.cardFundingPurpose;
      }
      
      // Add optional processing options if provided
      if (args.requestCheckDeliveryType) {
        paymentRequest.processingOptions.requestCheckDeliveryType = args.requestCheckDeliveryType;
      }
      
      if (args.requestPayFaster) {
        paymentRequest.processingOptions.requestPayFaster = args.requestPayFaster;
      }
      
      console.error('[API] Payment request:', JSON.stringify(paymentRequest, null, 2));
      
      const response = await this.axiosInstance.post('/v3/payments', paymentRequest);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response.data, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('[API] Create payment failed:', error);
      throw error;
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Bill.com MCP server running on stdio');
  }
}

const server = new BillDotComServer();
server.run().catch(console.error);
