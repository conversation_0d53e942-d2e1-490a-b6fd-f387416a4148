Trieve Primitives
Basic terms and concepts used commonly within the Trieve ecosystem.

Chunk: A chunk is a piece of data that is uploaded to Trieve. It is the smallest unit of data that can be searched, recommended, or used in RAG. Chunks are typically created by chunking a larger piece of data into smaller pieces. For example, a document can be chunked into paragraphs, sentences, or even words depending on the use case.

Tag Set: A tag set is a collection of tags that can be associated with a chunk. Tags can be used to categorize or filter chunks based on specific criteria. For example, a chunk representing a job posting could be tagged with the job title, location, company name, etc.

Metadata: Metadata is a json object that can be associated with a chunk. Metadata can be used to store additional information about a chunk, such as the source of the data, creation date, author, etc. Although filtering by metadata is supported, it is not recommended if latency is a concern.

Groups: Groups are a way to associate related chunks together. Chunks that belong to the same group are considered to be related in some way. For example, chunks representing different sections of a document can be grouped together to indicate that they are part of the same document. You can perform queries within a group or across groups.

Dataset: A dataset is a collection of chunks. Datasets are created in the Trieve dashboard and are used to organize and manage chunks. Datasets can be used to search, recommend, or generate responses using RAG.

Search: Search is the process of finding relevant chunks in a dataset based on a query. Trieve provides a search API that allows you to search for chunks based on text similarity.

Recommendations: Recommendations are a list of chunks that are similar to a given chunk. Trieve provides a recommendation API that allows you to get recommendations for a chunk based on text similarity.

RAG (Retrieval Augmented Generation): RAG is a technique that combines search and generation to generate responses to user queries. Trieve provides a RAG API that allows you to generate responses to user queries based on the content of your dataset.


Uploading Chunks to Trieve
Learn how to upload your chunks to Trieve

​
Overview
We provide an easy interface for users to upload their chunks individually so that users should have control over how they chunk their data

​
Uploading Individual Chunks to Trieve
The main route that we expose to handle this functionality is our create chunk route.

​
Important Parameters
chunk_html: This is the content that will be embedded and made searchable. You can pass HTML here, and our service will automatically clean it for the embeddings.
group_ids and group_tracking_ids: These fields let you specify a group to associate the chunks with, useful for linking multiple chunks from one document.
link, location, tag_set, num_value, and time_stamp: These fields are indexed to enable fast filtering of chunks based on these attributes.
metadata: This field allows you to include any arbitrary metadata in the form of a JSON object with the chunk.
For the best filtering performance, we recommend using the link, location, tag_set, num_value and time_stamp rather than the metadata field as there are dedicated indexes for these. The metadata has an index built on it, but it is only optimized for match queries

tracking_id: This field allows you to assign an arbitrary ID to the chunk, aiding in coordination with your database system. You can search for chunks using this ID.
weight: This field allows you to assign a weight to the chunks, which can influence the chunk’s ranking within search results. This is similar to merchandising features on other platforms.
semantic_boost and fulltext_boost: These fields allow you to boost the relevance of the chunk in the search results, by aligning the chunk closer to a specified phrase. This is useful for ensuring that for longer chunks, you can manually specify the most important part of the chunk, and improve relevance for the query patterns of your users.
​
Formatting chunk_html
Creating a good chunk_html is crucial for the quality of your search results. Here are some tips to help you create a good chunk_html:

Include all relevant information: Make sure to include all the information that you want to be searchable in the chunk_html.
Use newlines: Put semantically distinct information on separate lines to help the model understand the structure of the text and search quality for each field better
Label each field: If you have multiple fields in your chunk_html, label each field with a header to help the model better embed the text and improve search quality.
Example of a good chunk_html:


Copy
    Price: $50\n
    Brand: AmazonBasics\n
    Product Name: AmazonBasics Gaming Office Chair, Racing Design, PU leather, White + Philips Hue Play White double pack Bundle\n
    Description: High-back gaming chair provides ultimate comfort and control, whether at work or play; black with red accents;Made of premium PU leather upholstery on top, PVC material along the sides and bottom, and a nylon base to provide sporty racer look;Custom fit with height-adjustable armrest and tilt control for easily reclining; headset pillow and lumbar cushion offer added support;Compact and versatile, perfect TV backlight;\n
    Color: White / Black\n
    Product Type: CHAIR\n
    Style: Office Chair + Hue Play\n
    Furniture;chair;desk accessories;desk chair;game chair;gaming;gaming chair;gaming office chair;home furniture;office and computer chair;office chair;chair;chairs ergonomic;computer chair;desk chair; ergonomic office chair;essential gaming chair;gaming chair office;gaming desk;modern desk chair;office chair;
    Country: GB\n
    Marketplace: Amazon\n
    Domain: amazon.co.uk
​
Example Create Chunk Request
Whenever you make a request to the Trieve API, you need to include the TR-Dataset header with your dataset ID and the Authorization header with your API key.

For more performant uploads, we recommend you batch your chunk uploads. You can upload multiple chunks in a single request by passing an array of chunks in the body of the request up to 120 at a time.


Copy
POST /api/chunk
Headers:
{
    "TR-Dataset": "<Your Dataset ID>",
    "Authorization": "<Your API Key>"
}
Body:
{
    "chunk_html": "EcoFusion Technologies provides innovative, eco-friendly technology solutions. We specialize in renewable ...",
    "link": "https://example.com",
    "tracking_id": "134",
    "image_urls": ["https://example.com"],
    "tag_set": ["324", "product", "sale"],
    "metadata": {
      "phone_number": "************",
      "price_range": "$21-$25",
      "reviews_count": "15",
      "address": {
        "city": "Austin",
        "country": "United States",
        "state": "Texas",
        "zip_code": "78781"
      }
    }
}


Uploading Files to Trieve
Learn how to upload your files to Trieve

​
Overview
You must specifically use the base64url encoding for the base64_file field.

We provide the ability for users to upload their files to Trieve and use our automatic large language vision model or Apache Tika chunking. When uploading a file to Trieve, we automatically group the chunks together to link them. This is done through our upload file route.

​
Uploading a File to Trieve
Trieve supports various file types (e.g., HTML, DOCX, PDF). The file is uploaded to a S3 bucket associated with your dataset. As a user, you can use Apache Tika to convert these files to HTML or use a vision LLM to convert the files to markdown. To understand the difference between the two, refer to the section on Apache Tika vs vision model chunking.

​
Important Parameters
base64_file: To allow users to pass metadata with their file uploads, we require you to specifically use the base64url encoding. Convert + to -, / to _, and remove the ending = if present.
file_name: The name of the file being uploaded, including the extension. This will become the name of the resulting group.
group_tracking_id: This field allows you to assign an arbitrary ID to the group, aiding in coordination with your database system. You can search for this group using this ID.
link, tag_set, and time_stamp: These fields are indexed to enable fast filtering of groups based on these attributes.
target_splits_per_chunk: This is an optional field to specify number of splits you want per chunk. If not specified, the default 20 is used.
metadata: This field allows you to include any arbitrary metadata in the form of a JSON object with the group.
pdf2md_options: This allows you to use vision LLM to convert the files to markdown.
use_pdf2md_ocr: If true, the file will be converted to markdown using vision LLM. You can test pdf2md performance at pdf2md.trieve.ai.
For the best filtering performance, we recommend using the link, tag_set, and time_stamp fields, as there are dedicated indexes for these. The metadata field has an index built for match queries but is not optimized for range queries.

​
Example Upload File Request
Whenever you make a request to the Trieve API, you need to include the TR-Dataset header with your dataset ID and the Authorization header with your API key.


cURL

Python SDK

Python Requests

Copy
curl --request POST \
  --url https://api.trieve.ai/api/file \
  --header 'Authorization: <api-key>' \ // Replace with your API key
  --header 'Content-Type: application/json' \
  --header 'TR-Dataset: <tr-dataset>' \ // Replace with your dataset ID
  --data '{
  "base64_file": "<base64_encoded_file>",
  "file_name": "example.pdf",
  "link": "https://example.com",
  "tag_set": [
    "tag1",
    "tag2"
  ],
  "time_stamp": "2025-02-09T22:15:51",
  "target_splits_per_chunk": 20,
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  },
  "pdf2md_options": { 
    "use_pdf2md_ocr": false // Set to true if you want to use a vision LLM to convert file to markdown
  } 
}'
​
Example Response

200

400

Copy
{
  "file_metadata": {
    "created_at": "2025-02-09 22:30:00.000",
    "dataset_id": "********-****-****-****-************",
    "file_name": "example.pdf",
    "id": "********-****-****-****-************",
    "link": "https://example.com",
    "metadata": {
      "key1": "value1",
      "key2": "value2"
    },
    "size": 1000,
    "tag_set": "tag1,tag2",
    "time_stamp": "2025-02-09 22:30:00.000",
    "updated_at": "2025-02-09 22:30:00.000"
  }
}
​
File chunking with vision LLM
When uploading a file to Trieve, you can use vision LLMs through our pdf2md service for intelligent document parsing to easily convert PDF content to LLM-ready, structured Markdown. You can test pdf2md performance at pdf2md.trieve.ai.

This allows for better preserving document context, readability, and structure and is especially useful when working with documents containing complex layouts (tables, lists, code blocks, etc).

After the uploaded file is converted into structured Markdown, chunks are created based on the semantic structure of the document allowing for better semantic coherence.

​
Apache Tika vs Vision LLM chunking
Apache Tika is a more traditional approach that extracts raw text from a document and converts it into HTML. The chunks are subsequently created by splitting at fixed lengths or pre-defined delimeters.

Recommended for:

Simple, unstructured documents (e.g. plain text files)
Vision LLM chunking creates chunks based on the semantic structure of the document, allowing for sections (e.g. headers, lists, tables, etc) to stay grouped.

Recommended for:

Complex document structures (e.g. technical documentation and reports)
Documents where context must be preserved
​
Heading Based Chunking
Heading based chunking allows you to use split contents based on the headings and body content of the document.

Each chunk will contain a heading and its corresponding content.
All related content (paragraphs, lists, tables) under the same heading are grouped together.
For example, if you upload the following HTML:


Copy
<h1>Introduction</h1>
<p>Hey, this is Trieve!</p>
<h2>Background</h2>
<p>Trieve brings AI search to the modern world.</p>
The response will be:


Copy
{
  "chunks": [
    {
      "headings": ["Introduction"],
      "body": "<h1>Introduction</h1><p>Hey, this is Trieve!</p>"
    },
    {
      "headings": ["Background"],
      "body": "<h2>Background</h2><p>Trieve brings AI search to the modern world.</p>"
    }
  ]
}
​
Grouping with File Upload
When uploading a file to Trieve, all chunks created from the file will be grouped together. The file_name field is used to specify the name of the resulting groups. Once uploaded, documents can be queried using the file_name field, allowing you to retrieve and perform operations on all chunks created from the file.


Guides
Uploading CSV and JSONL Files to Trieve
Learn how to upload bulk structured data to Trieve

​
Overview
Trieve allows you to upload structured data in CSV and JSONL formats. We automatically create chunks for each row in the file, allowing you to search and filter your data easily.

​
Uploading a CSV or JSONL File to Trieve
Since CSV and JSONL files can be large, our API allows you to provision a signed PUT URL to upload the file directly to our storage. Once the file is uploaded, Trieve will automatically process the file and create chunks for each row.

​
Step 1: Request a Signed PUT URL
Use the /api/file/csv_or_jsonl to acquire a signed PUT URL for your CSV or JSONL file from the Trieve API. This URL is valid for 24 hours and allows you to upload the file directly to our storage.

You can leverage the mappings field to control how the columns in the CSV or fields in the JSONL file are mapped to the chunks created by Trieve. This is optional and can be used to ensure that the data is structured correctly.


Copy
curl -X POST "https://api.trieve.ai/api/file/csv_or_jsonl" \
  -H "Content-Type: application/json" \
  -H "TR-Dataset: <Your Dataset ID>" \
  -H "Authorization: <Your API Key>" \
  -d '{
    "description": "This is an example file containing information about titanic passengers.",
    "file_name": "titantic.csv",
    "mappings": [
      {
        "csv_jsonl_field": "PassengerId",
        "chunk_req_payload_field": "tracking_id"
      },
      {
        "csv_jsonl_field": "Survived",
        "chunk_req_payload_field": "tag_set"
      },
      {
        "csv_jsonl_field": "Fare",
        "chunk_req_payload_field": "num_value"
      }
    ],
    "link": "https://raw.githubusercontent.com/datasciencedojo/datasets/refs/heads/master/titanic.csv"
  }'
Trieve’s API will respond with an object containing the signed PUT URL and the file’s properties as shown in the below example.


Copy
{
  "file_metadata": {
    "id": "9ab52e58-0b38-4e4c-b114-139337f0548e",
    "file_name": "titantic.csv",
    "created_at": "2024-12-07T06:00:13.984143747",
    "updated_at": "2024-12-07T06:00:13.984144067",
    "size": 0,
    "metadata": null,
    "link": "https://raw.githubusercontent.com/datasciencedojo/datasets/refs/heads/master/titanic.csv",
    "time_stamp": null,
    "dataset_id": "<Your Dataset ID>",
    "tag_set": null
  },
  "presigned_put_url": "https://trieve-s3bucket.s3.amazonaws.com/trieve-s3bucket/<id>"
}
​
Step 2: Upload the File to the Signed PUT URL
Use the signed PUT URL provided by Trieve to upload the CSV or JSONL file to our storage. You can use tools like curl, wget, or any other HTTP client to upload the file.


Copy
curl -o ./titanic.csv https://raw.githubusercontent.com/datasciencedojo/datasets/refs/heads/master/titanic.csv

curl -X PUT -T ./titanic.csv "<Presigned PUT URL>"
You are now done with the file upload process. Trieve will automatically process the file and create chunks for each row. You can check the progress by migrating your dataset’s chunk count in the dashboard and test via the search playground or chat playground.

​
Advanced Options
Additional options are available to customize the csv or jsonl file upload process. Reference the documentation for our /api/file/csv_or_jsonl route for more information.


Searching with Trieve
Learn how to search over your data with Trieve

​
Overview
We provide the ability for you to search your data in a fast and performant manner. We have multiple search paradigms, which are exposed through the search over chunks route, the search within groups route, and the search over groups route.

query: The user query that is embedded and searched against the dataset.
search_type: Can be semantic, fulltext, or hybrid. Semantic: Uses cosine distance to determine the most relevant results. Fulltext: Uses a SPLADE model to find the most relevant results. Hybrid: Uses a reranker model that pulls one page of results from both fulltext and semantic searches to find the most relevant results.
page: The page of chunks to fetch. Pages are 1-indexed.
page_size: This lets you tune the number of results that are returned.
highlight_results: Enables subsentence highlighting of relevant portions of the text.
slim_chunks: Excludes chunk_html from the returned results to reduce network bandwidth. Useful for large chunks.
recency_bias: A value from 0-1 that tunes how much the recency of chunks (based on the timestamp field) affects the ranking.
sort_options: Options on how to sort.
filters: Apply filters to get exactly the results you want.
​
Search Modes
Trieve offers 4 different types of search.

​
Semantic Search
Semantic search uses an embeddnig model to generate a query vector. Defaults to using cosine similarity and jina-base-en

Trieve uses only the embedding model to select and rerank the results.

This search_type is semantic.

​
Full Text search
FullText search uses a SPLADE model to find the most relevant results to your given query.

This search_type is fulltext.

​
BM25
BM25 is the classical type of search index, it uses the BM25 ranking function to determine the results that are most similar to your given query.

This search_type is bm25.

​
Hybrid
Hybrid search, does both a full text search, and semantic search. From those results it then uses a reranker model ( defaults to bge-reranker-large).

This search_type is hybrid.

​
Search Paradigms
We offer three different search strategies for you to choose from:

Search over chunks: This strategy allows you to search all of your chunks independently. This is useful when your chunks are independent and do not need to be grouped together.
Search within groups: This strategy lets you constrain your results to within a selected group. This is useful for searching distinct groups within your dataset independently.
Search over groups: This strategy allows you to search over the groups of chunks within your dataset. This returns the groups and the top chunks within each group that matched your query, providing better search quality for datasets with highly related chunks within groups.
​
Search over chunks

Copy
POST /api/chunk/search
Headers:
{
  "TR-Dataset": "<your-dataset-id>",
  "Authorization": "tr-*******************"
}
Body:
{
  "query": "How to search with Trieve",
  "search_type": "fulltext",
  "page": 1,
  "page_size": 10,
  "score_threshold": 0.5
}
​
Search within group

Copy
POST /api/chunk_group/search
Headers:
{
  "TR-Dataset": "<your-dataset-id>",
  "Authorization": "tr-*******************"
}
Body:
{
  "group_tracking_id": "my-group-tracking-id",
  "query": "How to search with Trieve",
  "search_type": "fulltext",
  "page": 1,
  "page_size": 10,
  "score_threshold": 0.5
}
​
Search over groups

Copy
POST api/chunk_group/group_oriented_search
Headers:
{
  "TR-Dataset": "<your-dataset-id>",
  "Authorization": "tr-*******************"
}
Body:
{
  "group_tracking_id": "my-group-tracking-id",
  "group_size": 5,
  "query": "How to search with Trieve",
  "search_type": "fulltext",
  "page": 1,
  "page_size": 10,
  "score_threshold": 0.5
}
You can use the search UI at search.trieve.ai to A/B test which search method works best for you.

​
Filters
Trieve filters are structured around three clauses:

must: All filters within this clause must be matched to return the chunks.
must_not: All filters in this clause must not be matched to return the chunks.
should: Any of these conditions can be matched to return a chunk.
Each clause contains a field_condition.

range: Match a number between a range of lt, gt, lte or gte
match_all: A list, every field must have have a match.
match_any: A list, at least 1 field must be present.
date_range: Match a date between a range of lt, gt, lte or gte
geo_radius: Match a radius based on a center and a radius
boolean: Matches if the field is true or false
Get chunks with both “CO” and “321” in their tag_set


Copy
"filters": {
	"must": [
		{
			"field": "tag_set",
			"match_all": ["CO", "321"]
		}
	]
}
Get chunks with either “CO” OR “321” in their tag_set:


Copy
"filters": {
  "must": [
    {
      "field": "tag_set",
      "match_any": ["CO", "321"]
    }
  ]
}
Get chunks that are tagged within a GEO radius


Copy
"filters": {
  "must": [
    {
      "field": "geo_radius",
      "geo_radius": {
	    "center": {
	      "lat": 20,
	      "long": -30
	    },
	    "radius": 20,
      }
    }
  ]
}
Get chunks with neither “CO” nor “321” in their tag_set:


Copy
"filters": {
  "must_not": [
    {
      "field": "tag_set",
      "match_all": ["CO", "321"]
    }
  ]
}
Get chunks that either don’t have “CO” in their tag_set or don’t have “321” in their tag_set:


Copy
"filters": {
  "must_not": [
    {
      "field": "tag_set",
      "match_any": ["CO", "321"]
    },
  ]
}
Get chunks that either have “CO” in their tag_set or “http://example.com” in their link:


Copy
"filters": {
  "should": [
    {
      "field": "tag_set",
      "match": ["CO"]
    },
    {
      "field": "link",
      "match": ["http://example.com"]
    }
  ]
}
Get Chunks that have num_value between 20 and 30


Copy
"filters": {
  "must": [
    {
      "field": "num_value",
      "range": {
	"gte": 20.0,
	"lte": 30.0,
	"gt": 30.0,
	"lt": 20.0
      }
    }
  ]
}
​
Rerank By
rerank_type can be either

fulltext: This will use the fulltext index to rerank the results, if search_type is fulltext then nothing different will happen.
cross_encoder: This will use the Reranker model that you predefined. By default hybrid search will use the cross_encoder.
bm25: This will use the bm25 matching algorithm rerank the results, if search_type is bm25 then nothing different will happen.
semantic: This will use the semantic vectors to rerank the results, if search_type is semantic then nothing different will happen.

Copy
{
  "sort_options": {
    "sort_by" {
      "rerank_type": "fulltext"
    }
  }
}
​
Multi Query
MultiQuery provides a way to give multiple query objects with a given weight bias.

To use the multiquery, instead of a single string, the query parameter receives a list of tuples, value 1 being the query and value 2 being a value on how important it is.

As an example, search

Searching, but the search term of “iphone” and a color.


Copy
"query": [
  [ "Flagship", 2 ],
  [ "Red", 2 ],
  [ "Iphone mini", 10 ]
]
​
Customizing your search models
Trieve offers many ways to customize your embedding models and reranker models. Different embedding models and different reranker models are better suited for different tasks.

​
Embedding Models
Trieve supports multiple embedding models that can be used to search over your data. You can specify the embedding model to use in the server_configuration field when creating a dataset.

After creating a dataset, you cannot change the embedding model. If you need to change the embedding model, you must create a new dataset.

​
Reranker models
Trieve supports multiple reranker models that can be used to rerank the search results.

Currently, Trieve supports bge-reranker-large and cohere’s rerank-v3.5 model.



​
BAAI/bge-reranker-large
bge-reranker-large is a model by the Beijing Academy of Artificial Intelligence (BAAI) and is hosted by Trieve. This model does not require any additional configuration. and will be used by default on all hybrid searches.


Guides
Recommending with Trieve
Learn how to recommend content with Trieve

​
Overview
We provide the ability to get recommended chunks similar to your data through the get recommended chunks route or through the get recommended groups route.

​
Different Recommendation Types
We have two different recommendation types:

Recommend Chunks: Recommends chunks that are similar to the provided examples. This is useful when your chunks are independent and do not need to be grouped together.
Recommend Groups: Recommends groups that are similar to the provided examples. It returns the groups and the top chunks within each group that are most similar to your input, providing better search quality for datasets with highly related chunks within groups.
​
Different Recommendation Strategies
We offer different recommendation strategies for users to choose from:

average_vector: Averages the vectors of the positive examples and finds the most similar chunks.

This strategy averages all the positive and negative examples separately, then combines them into a single vector. It then searches for chunks that are most similar to this combined vector.
best_score: Finds chunks that are most similar to any of the positive examples provided.

This strategy measures each example against every other example, then selects the best positive and best negative scores.
​
Important Parameters
positive_examples and positive_tracking_ids: Provide the IDs of chunks of which similar ones will be recommended.
negative_examples and negative_tracking_ids: Provide the IDs of chunks of which dissimilar ones will be avoided.
recommendation_type: Can be either semantic or fulltext.
Semantic: Uses cosine distance.
Fulltext: Uses a SPLADE model.
strategy: Can be either average_vector or best_score.
slim_chunks: Reduces the size of the returned chunks by excluding unnecessary fields.
filters: Apply filters to get exactly the results you want.

Guides
Using Groups with Trieve
Learn how to create and use groups with Trieve

​
Overview
We provide the ability to cluster and structure related chunks of data. This allows for specific search and recommendation paradigms — constraining results to specific groups.

​
Groups vs Tags
Tags provide users the ability, through the tag_set attribute in chunks and groups, to categorize and label specific chunks, allowing for quick filtering. This lacks many of the higher level functionality around groups, which allow you to organize and search related content at a broader level.

​
Creating Groups
To create a group, use the create or upsert group API route.

​
Important Parameters
name: The name of the group. This does not need to be unique.
description: A description of the group and its corresponding chunks.
metadata: A JSON object containing any additional information to associate with chunks in this chunk group.
tracking_id: An optional, unique identifier to assign to this chunk group.
tag_set: A list of strings to categorize chunks within this chunk group.
curl

Copy
curl --request POST \
  --url https://api.trieve.ai/api/chunk_group \
  --header 'Authorization: <api-key>' \
  --header 'Content-Type: application/json' \
  --header 'TR-Dataset: <tr-dataset>' \
  --data '{
  "name": "Example Group",
  "description": "This is an example group.",
  "metadata": {},
  "tag_set": ["example", "guide", "group"],
  "tracking_id": "EXAMPLEGROUPGUIDE",
  }'
​
Accessing Chunks in a Group
Trieve allows you to assign unique, arbitrary IDs to groups, through the group_tracking_id field, to sync with external systems. This also means that you can access and search groups using this field.

For example, to fetch all chunks in a group by the group_tracking_id, use the get chunks in group by tracking ID route.

curl

Copy
curl --request GET \
  // Replace {group_tracking_id} with the actual tracking ID of the chunk group
  --url https://api.trieve.ai/api/chunk_group/tracking_id/{group_tracking_id}/{page} \
  --header 'Authorization: <api-key>' \
  --header 'TR-Dataset: <tr-dataset>'
​
Adding Chunks to Groups
To add an existing chunk to a group, use the add chunk to group route.

curl

Copy
curl --request POST \
    // Replace {group_id} with the Trieve generated ids for the group
  --url https://api.trieve.ai/api/chunk_group/chunk/{group_id} \
  --header 'Authorization: <api-key>' \
  --header 'Content-Type: application/json' \
  --header 'TR-Dataset: <tr-dataset>' \
  --data '{
	// ID of the chunk to add to the group
  "chunk_id": "********-****-****-****-********",
	// Tracking ID of the chunk to add to the group
  "chunk_tracking_id": "<string>"
}'
group_id is the Trieve generated ids for groups whereas the group_tracking_id is a user-assigned tracking_ids for groups. Groups with group_ids must be created first and cannot be arbitrarily created.

To assign a chunk to a group on creation, use the create or upsert chunk or chunks route and specify the group_ids or group_tracking_ids. Specify one or more existing group IDs that the chunks should be placed into, or provide group tracking IDs. If any IDs within the list of group_tracking_ids do not exist, new groups corresponding to those tracking IDs will be created.

curl

Copy
curl --request POST \
  --url https://api.trieve.ai/api/chunk \
  --header 'Authorization: <api-key>' \
  --header 'Content-Type: application/json' \
  --header 'TR-Dataset: <tr-dataset>' \
  --data '{
  "chunk_html": "<p>Some HTML content</p>",
  // Enter a list of group IDs to associate with the chunk
  "group_ids": [
    "********-****-****-****-************"
  ],
  // Enter a list of group tracking IDs to associate with the chunk
  // If a group_tracking_id does not exist, a corresponding group will be created
  "group_tracking_ids": [
    "example_group_tracking_id"
  ],
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  },
  "link": "https://example.com",
  "tracking_id": "example_chunk_tracking_id"
}'
​
View Groups in Search Playground
Once you create a group and assign chunks, you can easily view and manage these groups through the search playground.

To view groups:

Navigate and login to the search playground at https://search.trieve.ai/.
Click on Groups in the Navbar.
View group details (such as name, description, and date created).
Click on any group name to access its corresponding chunks.
​
Searching Groups
In addition to searching over independent chunks, we allow users to search within groups and search over groups.

Search over groups returns the groups of chunks similar to the search query while search within groups restricts the search to a single group.

Search over groups can be performed similar to search over chunks (checkout guide here), with the addition of the group_size field to specify the number of chunks to fetch for each group.

Similarly, to search within a group, use the group_id or group_tracking_id fields to specify which group you want to search in.

​
Recommendations Groups
Trieve also provides the ability to get recommended groups and recommended chunks within a group through the get recommended groups and the get recommended chunks routes.

Fetching recommended groups through the get recommended groups route is similiar to using the get recommended chunks route (checkout guide here), with the addition of:

positive_group_tracking_ids: the tracking IDs of groups that serve as positive examples when curating recommendations.
negative_group_tracking_ids: the tracking IDs of groups that serve as negative examples when getting recommendations.
group_size: the number of chunks to be fetched for each group.
limit: the max number of groups (rather than chunks) that should be returned.
To get recommended chunks within a group, you can use the get_recommended_chunks route with a filter on group_ids or group_tracking_ids.


List of API Calls
Chunk
POST
Create or Upsert Chunk or Chunks
POST
Search
POST
Autocomplete
POST
Get Recommended Chunks
POST
Scroll Chunks
POST
Count chunks above threshold
POST
Generate suggested queries
POST
RAG on Specified Chunks
PUT
Update Chunk
PUT
Update Chunk By Tracking Id
GET
Get Chunk By Id
GET
Get Chunk By Tracking Id
POST
Get Chunks By Tracking Ids
POST
Get Chunks By Ids
DEL
Delete Chunk
DEL
Delete Chunk By Tracking Id
DEL
Bulk Delete Chunks
POST
Split HTML Content into Chunks
Chunk Group
POST
Create or Upsert Group or Groups
POST
Search Over Groups
POST
Search Within Group
POST
Get Recommended Groups
POST
Add Chunk to Group
POST
Add Chunk to Group by Tracking ID
POST
Get Groups for Chunks
GET
Get Chunks in Group by Tracking ID
GET
Get Group by Tracking ID
PUT
Update Group
DEL
Remove Chunk from Group
DEL
Delete Group by Tracking ID
DEL
Delete Group
GET
Get Group
GET
Get Chunks in Group
GET
Get Groups for Dataset
Topic
POST
Create Topic
POST
Clone Topic
PUT
Update Topic
GET
Get All Topics for Owner ID
DEL
Delete Topic
Message
POST
Create message
PUT
Edit message
PATCH
Regenerate message
POST
Get tool function parameters
GET
Get a message by its ID
GET
Get all messages for a given topic
Crawl
GET
Get all crawl requests for a dataset
PUT
Update a crawl request
POST
Create a new crawl request
DEL
Delete a crawl request
File
POST
Upload File
POST
Create Presigned CSV/JSONL S3 PUT URL
POST
Upload HTML Page
GET
Get Files and Group IDs for Dataset
GET
Scroll Files with Groups
GET
Get File Signed URL
GET
Get File Signed URL
DEL
Delete File
Analytics
POST
Get All User Events
POST
Get CTR Analytics
POST
Get RAG Analytics
POST
Get Recommendation Analytics
POST
Get Search Analytics
POST
Get Cluster Analytics
POST
Get Top Datasets
PUT
Send User Event Data
PUT
Send CTR Data
PUT
Rate RAG
PUT
Rate Search
GET
Get User Event By ID
Dataset
POST
Create Dataset
POST
Batch Create Datasets
POST
Get All Tags
POST
Get events for the dataset
PUT
Update Dataset by ID or Tracking ID
PUT
Clear Dataset
GET
Get Dataset By ID
GET
Get Dataset by Tracking ID
GET
Get Datasets from Organization
POST
Create ETL Job
PUT
Create Pagefind Index for Dataset
GET
Get Pagefind Index Url for Dataset
GET
Get Usage By Dataset ID
GET
Get dataset crawl options
GET
Get apipublic page
DEL
Delete Dataset
DEL
Delete Dataset by Tracking ID
Organization
POST
Create Organization
POST
Create Organization Api Key
POST
Update All Dataset Configurations
PUT
Update Organization
GET
Get Organization Api Keys
GET
Get organization usage
GET
Get Organization Users
GET
Get Organization
DEL
Delete Organization
DEL
Delete Organization Api Key
User
PUT
Update User Org Role
GET
Get User Api Keys
DEL
Delete User Api Key
Auth
GET
Login
GET
OpenID Connect callback
GET
Get Me
DEL
Logout
Health
GET
Health Check
Invitation
POST
Send Invitation
GET
Get Invitations
DEL
Delete Invitation



Below is one approach that leverages the core primitives to organize your data and support both granular search and efficient filtering.

⸻

1. Define a Consistent Schema per Entity Type

For each major entity (documents, people, organizations, decisions, tasks) create a standard chunk format. For example, for documents you might structure your chunk_html like this:

Title: Quarterly Contract Review
Type: Contract
Category: Contracts
Date: 2025-03-31
Content: [The body of the contract, key clauses, etc.]

And for a person:

Name: Jane Doe
Role: Software Engineer
Entity: Person
Description: [Long-form biography, skills, notes, etc.]

By labeling fields (with “Title”, “Type”, “Name”, “Content”, etc.) you help the embedding model capture the structure and improve search quality.

⸻

2. Use Tag Sets for Filtering

Assign each entity type a unique tag in the tag_set. For instance:
	•	Documents:
	•	For Knowledge Base documents: ["document", "knowledge_base"]
	•	For Meetings: ["document", "meeting"]
	•	For Minutes: ["document", "minutes"]
	•	For Contracts: ["document", "contract"]
	•	For General documents: ["document", "general"]
	•	Other Entities:
	•	People: ["entity", "person"]
	•	Organizations: ["entity", "organization"]
	•	Decisions: ["entity", "decision"]
	•	Tasks: ["entity", "task"]

This makes it straightforward to filter using TRIB (which can target your tag_set) and Qwik filtering (which you can leverage on the indexed fields like tag_set, link, or even time_stamp).

⸻

3. Chunking vs. Grouping Strategy
	•	Documents:
If a document is long, split it into multiple chunks (e.g., one per section or paragraph).
	•	Use the group_tracking_ids or group_ids to associate all chunks from a single document together. This helps if you ever need to search within a document or display it as one unit.
	•	People, Organizations, etc.:
Even if the description is long, you can often ingest it as one chunk (or split it if needed for semantic clarity).
	•	Each should have its own unique tracking_id so that you can update or re-query specific entities.
	•	Embed the title or name as well as the description in the chunk_html using the labeling approach described above.

⸻

4. Index Key Attributes for Fast Filtering

Since the best filtering performance is achieved using indexed fields rather than metadata:
	•	Populate fields such as tag_set, link, time_stamp, or even a dedicated custom field (if supported) for the entity type.
	•	For example, use the tag_set to designate “document” vs. “person” and further specify the subtype (like “meeting”, “contract”, etc.), which enables both TRIB and Qwik filtering to quickly narrow your search.

⸻

5. Search Strategy

When running searches:
	•	Use the search APIs (search over chunks, within groups, or over groups) with appropriate filters targeting your indexed fields.
	•	For instance, a query for “contracts” could use a filter like:

"filters": {
  "must": [
    {
      "field": "tag_set",
      "match_any": ["contract"]
    }
  ]
}

	•	Similarly, for people or organizations, a filter on tag_set with "person" or "organization" will allow independent searching.

⸻

Summary
	1.	Schema & Labeling: Design a uniform chunk_html that clearly labels each field.
	2.	Tag Sets: Use descriptive tags to classify each type (documents, people, etc.).
	3.	Grouping: For documents, group related chunks together using group_tracking_ids; for entities with a single record (like a person or organization), a single chunk with its own tracking_id is sufficient.
	4.	Indexing: Prioritize using indexed fields (especially tag_set) to achieve efficient TRIB and Qwik filtering.
	5.	Search API: Configure your search queries to filter based on these tags and indexed fields, ensuring that each document type or entity is independently searchable.

This strategy keeps your ingestion process modular, makes it easier to manage different types of content, and leverages Trieve’s primitives to optimize both search relevance and filtering performance.