Tools
- You have a convex-introspect MCP tool to view the database and run queries and mutations if you need. 

Args
{"projectDir": "/Users/<USER>/W2B1-Projects-local/product-vision"}

Development Env
{"deploymentSelector": "ownDev:eyJwcm9qZWN0RGlyIjoiL1VzZXJzL2J5YW5nL1cyQjEtUHJvamVjdHMtbG9jYWwvcHJvZHVjdC12aXNpb24iLCJkZXBsb3ltZW50Ijp7ImtpbmQiOiJvd25EZXYifX0="}

Prod Environment deploymentSelector: ownProd:eyJwcm9qZWN0RGlyIjoiL1VzZXJzL2J5YW5nL1cyQjEtUHJvamVjdHMtbG9jYWwvcHJvZHVjdC12aXNpb24iLCJkZXBsb3ltZW50Ijp7ImtpbmQiOiJvd25Qcm9kIn19

The convex-introspect MCP server provides the following tools:
- status: Get all available deployments for a given Convex project directory.
- data: Read a page of data from a table in the project's Convex deployment.
- tables: List all tables in a particular Convex deployment and their inferred and declared schema.
- functionSpec: Get the function metadata from a Convex deployment.
- run: Run a Convex function (query, mutation, or action) on your deployment.
- runOneoffQuery: Run a one-off readonly query on your Convex deployment.

our functions are in ./convex/_generated/api.js