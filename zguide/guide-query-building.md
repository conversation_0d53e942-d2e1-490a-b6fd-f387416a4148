# Convex Query & Mutation Guidelines

## Folder Structure

```plaintext
my-project/
├── app/
├── convex/
│   ├── schema.ts                  # Convex DB schema using v.*
│   ├── domains.ts                 # Operations for domains table
│   ├── projects.ts                # Operations for projects table
│   ├── functions.ts               # Defines zQuery/zMutation helpers
│   └── ...
├── zod/                           # Separate directory for Zod schemas
│   ├── domains-schema.ts          # Schema definitions for domains
│   ├── projects-schema.ts         # Schema definitions for projects
│   └── ...
└── ...
```

## Setting Up Zod Integration

We have set up helpers for integrating Zod via zQuery, zMutation, and zAction with Convex in ./convex/functions.ts

## CRUD Operations for `domains`
CRUD Operations for domains

Implementations should handle both single and bulk cases with consistent naming:
 - createDomain (Single + Bulk)
 - listDomains (Query with Filters & Pagination)
 - getDomain (Single Item)
 - updateDomain (Single + Bulk)
 - deleteDomain (Single + Bulk)

### 1. Create Domain (Single + Bulk)

First, define your schemas:

```typescript
// zod/domains-schema.ts
import { z } from "zod";
import { zid } from "convex-helpers/server/zod";

/** Fields required to create a single domain. */
export const SingleDomainInput = z.object({
  name: z.string(),
  description: z.string().optional(),
  status: z.enum(["active", "pending", "archived"]).optional().default("active"),
  owner_id: zid("users").optional(),
});

/** Array of domain inputs for bulk creation. */
export const MultipleDomainsInput = z.array(SingleDomainInput);
```

Now implement the mutation:

```typescript
// convex/domains.ts
import { zMutation } from "./functions";
import { SingleDomainInput, MultipleDomainsInput } from "../zod/domains-schema";
import { z } from "zod";

/**
 * Creates one or multiple domains
 * @example
 * // Create a single domain
 * const { ids } = await client.mutation.createDomain({
 *   input: { name: "example.com", status: "active" }
 * });
 * 
 * // Create multiple domains
 * const { ids } = await client.mutation.createDomain({
 *   input: [
 *     { name: "example1.com", status: "active" },
 *     { name: "example2.com", status: "pending" }
 *   ]
 * });
 */
export const createDomain = zMutation({
  // We allow either a single item or an array of items
  args: {
    input: z.union([SingleDomainInput, MultipleDomainsInput])
  },
  // Return the created IDs
  output: z.object({
    ids: z.array(z.string())
  }),
  handler: async (ctx, args) => {
    const { db } = ctx;
    const input = args.input;
    const now = Date.now();

    // Helper function for inserting one domain
    const insertOne = async (domainData) => {
      const doc = {
        ...domainData,
        updated_at: now
      };
      return await db.insert("domains", doc);
    };

    // Bulk creation if input is an array
    if (Array.isArray(input)) {
      const insertedIds = [];
      for (const domain of input) {
        const newId = await insertOne(domain);
        insertedIds.push(newId);
      }
      return { ids: insertedIds };
    } else {
      // Single creation
      const domainId = await insertOne(input);
      return { ids: [domainId] };
    }
  }
});
```

### 2. List Domains (Query with Pagination & Filters)

Schemas for filtering and pagination:

```typescript
// zod/domains-schema.ts
export const DomainFilterSchema = z.object({
  searchText: z.string().optional(),
  status: z.enum(["active", "pending", "archived"]).optional(),
  owner_id: zid("users").optional(),
});

export const PaginationSchema = z.object({
  limit: z.number().optional().default(50),
  sortBy: z.enum(["_creationTime", "name", "updated_at"]).optional(),
  sortDirection: z.enum(["asc", "desc"]).optional().default("desc"),
  cursor: z.any().optional()
});
```

Implementation of list query:

```typescript
 
/**
 * Lists domains with optional filtering and pagination
 * @example
 */
// convex/domains.ts
export const listDomains = zQuery({
  args: {
    filter: DomainFilterSchema.optional(),
    pagination: PaginationSchema.optional()
  },
  output: z.object({
    page: z.array(Domain),
    continuationToken: z.any().optional()
  }),
  handler: async (ctx, args) => {
    const { db } = ctx;
    const { filter, pagination = {} } = args;
    const {
      limit = 50,
      sortBy = "_creationTime",
      sortDirection = "desc",
      cursor
    } = pagination;

    // Start building the query
    let query = db.query("domains");
    
    // Apply filters using appropriate indexes
    if (filter) {
      if (filter.status) {
        // Using an index for status filters
        query = query.withIndex("by_status", q => 
          q.eq("status", filter.status)
        );
      }
      
      // For text search, use a search index if available
      if (filter.searchText) {
        // ✅ Option 1: Use a search index if available
        query = query.withSearchIndex("search_by_name", q => 
          q.search("name", filter.searchText)
        );
        
        // If no search index is available, consider:
        // ✅ Option 2: Use a regular index with .startsWith or exact match
        // query = query.withIndex("by_name", q => 
        //   q.startsWith("name", filter.searchText)
        // );
      }
      
      // Owner filter using an index
      if (filter.owner_id) {
        // ✅ Using an index for owner filtering
        query = query.withIndex("by_owner", q => 
          q.eq("owner_id", filter.owner_id)
        );
      }
    }

    // Apply sorting
    query = query.order(sortDirection);

    // Execute query with pagination
    const result = await query.paginate({
      cursor: cursor ?? null,
      numItems: limit
    });

    return {
      page: result.page,
      continuationToken: result.continuation
    };
  }
});
```

### 3. Get Domain (Single Item Query)

```typescript
// convex/domains.ts
import { zQuery } from "./functions";
import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { Domain } from "../zod/domains-schema";

/**
 * Retrieves a single domain by ID
 * @example
 * const domain = await client.query.getDomain({ domainId: "123abc" });
 */
export const getDomain = zQuery({
  args: {
    domainId: zid("domains")
  },
  output: Domain,
  handler: async (ctx, args) => {
    const { db } = ctx;
    const { domainId } = args;

    const domainDoc = await db.get(domainId);
    if (!domainDoc) {
      throw new Error(`No domain found with ID ${domainId}`);
    }
    return domainDoc;
  }
});
```

### 4. Update Domain (Single + Bulk)

First, update the schemas:

```typescript
// zod/domains-schema.ts
// For updating, make all fields optional
export const DomainUpdateFields = SingleDomainInput.partial();

/** For single + bulk updates:
    id or ids is required, plus the update fields. */
export const DomainUpdateSchema = z.object({
  id: zid("domains").optional(),
  ids: z.array(zid("domains")).optional(),
  updates: DomainUpdateFields
}).refine(
  data => !!(data.id || (data.ids && data.ids.length > 0)),
  { message: "Either id or ids must be provided" }
);
```

Then implement the update mutation:

```typescript
// convex/domains.ts
import { zMutation } from "./functions";
import { DomainUpdateSchema } from "../zod/domains-schema";
import { z } from "zod";
import { Id } from "./_generated/dataModel";

/**
 * Update a single domain or multiple domains with the same changes
 * @example
 * // Update a single domain
 * const result = await client.mutation.updateDomain({
 *   input: {
 *     id: "123abc",
 *     updates: { name: "new-domain.com", status: "archived" }
 *   }
 * });
 * 
 * // Update multiple domains with the same changes
 * const result = await client.mutation.updateDomain({
 *   input: {
 *     ids: ["123abc", "456def"],
 *     updates: { status: "archived" }
 *   }
 * });
 */
export const updateDomain = zMutation({
  args: { input: DomainUpdateSchema },
  output: z.object({
    ids: z.array(z.string()),
    failed: z.array(z.object({
      id: z.string(),
      error: z.string()
    })).optional()
  }),
  handler: async (ctx, args) => {
    const { db } = ctx;
    const { input } = args;
    const { id, ids, updates } = input;
    const now = Date.now();
    const updatedIds = [];
    const failedUpdates = [];

    /**
     * Helper to update a single domain
     */
    async function updateSingleDomain(domainId: Id<"domains">) {
      try {
        const doc = await db.get(domainId);
        if (!doc) {
          throw new Error(`Domain not found: ${domainId}`);
        }
        await db.patch(domainId, {
          ...updates,
          updated_at: now
        });
        return domainId;
      } catch (error) {
        // Tracking failures for bulk operations
        failedUpdates.push({
          id: domainId,
          error: error.message || "Unknown error"
        });
        return null;
      }
    }

    // Handle bulk update case
    if (ids && ids.length > 0) {
      for (const domainId of ids) {
        const result = await updateSingleDomain(domainId);
        if (result) updatedIds.push(result);
      }
    }
    // Handle single update case
    else if (id) {
      const result = await updateSingleDomain(id);
      if (result) updatedIds.push(result);
    }

    // Return both successes and failures
    const response: { ids: string[], failed?: any[] } = { ids: updatedIds };
    if (failedUpdates.length > 0) {
      response.failed = failedUpdates;
    }
    return response;
  }
});
```

### 5. Delete Domain (Single + Bulk)

```typescript
// zod/domains-schema.ts
export const DeleteDomainSchema = z.object({
  domainId: zid("domains").optional(),
  domainIds: z.array(zid("domains")).optional()
}).refine(
  data => !!(data.domainId || (data.domainIds && data.domainIds.length > 0)),
  { message: "Either domainId or domainIds must be provided" }
);
```

```typescript
// convex/domains.ts
import { zMutation } from "./functions";
import { DeleteDomainSchema } from "../zod/domains-schema";
import { z } from "zod";

/**
 * Delete a single domain or multiple domains
 * @example
 * // Delete a single domain
 * const result = await client.mutation.deleteDomain({
 *   input: { domainId: "123abc" }
 * });
 * 
 * // Delete multiple domains
 * const result = await client.mutation.deleteDomain({
 *   input: { domainIds: ["123abc", "456def"] }
 * });
 */
export const deleteDomain = zMutation({
  args: { input: DeleteDomainSchema },
  output: z.object({
    ids: z.array(z.string()),
    failed: z.array(z.object({
      id: z.string(),
      error: z.string()
    })).optional()
  }),
  handler: async (ctx, args) => {
    const { db } = ctx;
    const { input } = args;
    const { domainId, domainIds } = input;
    const deletedIds = [];
    const failedDeletes = [];

    // Helper to delete a single document
    async function deleteOne(id) {
      try {
        const doc = await db.get(id);
        if (!doc) {
          throw new Error(`Domain with ID ${id} not found`);
        }
        await db.delete(id);
        return id;
      } catch (error) {
        failedDeletes.push({
          id,
          error: error.message || "Unknown error"
        });
        return null;
      }
    }

    // Handle bulk delete
    if (domainIds && domainIds.length > 0) {
      for (const id of domainIds) {
        const result = await deleteOne(id);
        if (result) deletedIds.push(result);
      }
    }
    // Handle single delete
    else if (domainId) {
      const result = await deleteOne(domainId);
      if (result) deletedIds.push(result);
    }

    // Return both successes and failures
    const response: { ids: string[], failed?: any[] } = { ids: deletedIds };
    if (failedDeletes.length > 0) {
      response.failed = failedDeletes;
    }
    return response;
  }
});
```

## Query Building Best Practices

### Chaining Query Methods

Always chain query builder methods in a single expression:

```typescript
// Good: Chained methods in a single expression
const tasks = await ctx.db.query("tasks")
  .withIndex("by_taskList", (q) => q.eq("taskListId", args.taskListId))
  .order("desc")
  .take(100);

// Avoid: Reassigning variables
let query = ctx.db.query("tasks");
query = query.withIndex("by_taskList", (q) => q.eq("taskListId", args.taskListId));
query = query.order("desc");
const tasks = await query.take(100);
```

### Using Indexes for Performance

Define indexes in your schema and use `.withIndex()` instead of `.filter()`:

```typescript
// ❌ Avoid: Using .filter (slower for large tables)
const tasks = await ctx.db.query("tasks")
  .filter((q) => q.eq(q.field("taskListId"), args.taskListId))
  .order("desc")
  .take(100);

// ✅ Recommended: Use an index (faster for large tables)
const tasks = await ctx.db.query("tasks")
  .withIndex("by_taskList", (q) => q.eq("taskListId", args.taskListId))
  .order("desc")
  .take(100);

// ✅ Alternative for small datasets or when index doesn't exist:
// Filter in code (more readable than .filter())
const allTasks = await ctx.db.query("tasks").collect();
const filteredTasks = allTasks.filter(task => task.taskListId === args.taskListId);
```

### Sorting and Pagination

Use `.order()` to specify sort direction and `.paginate()` for cursor-based pagination:

```typescript
/// Sorting
query = query.order("desc"); // or "asc"

// Pagination with filtering (recommended approach)
// When using pagination with filters, prefer .withIndex over filtering in code
// This ensures each page returns the expected number of items
const result = await query
  .withIndex("by_status", q => q.eq("status", "active"))
  .paginate({
    cursor: args.cursor ?? null,
    numItems: args.limit || 10
  });

// result has:
// - page: the items for the current page
// - continuation: token for the next page
```

### TypeScript Integration

#### Type Inference with Zod

When using Zod with Convex, the type system works differently than with native Convex validators. With our `zQuery` and `zMutation` helpers, types are automatically inferred from the Zod schemas:

```typescript
// The helper functions handle type inference
export function zQuery<Args extends z.ZodTypeAny, Output extends z.ZodTypeAny>({
  args,
  output,
  handler
}: {
  args: Args;
  output?: Output;
  handler: (ctx: QueryCtx, args: z.infer<Args>) => Promise<z.infer<Output>>;
}) {
  return query({
    args: zodToValidator(args),
    handler: async (ctx, args) => {
      const result = await handler(ctx, args);
      if (output) {
        return output.parse(result);
      }
      return result;
    }
  });
}
```

#### Properly Typing Args in Handlers
Args is an object with named parameters
There are three approaches for handling types in your handler functions:

1. **Let type inference work automatically** (recommended):
```typescript
export const getDomain = zQuery({
  args: {
    domainId: zid("domains")
  },
  handler: async (ctx, args) => {
    // args.domainId is already correctly typed as Id<"domains">
    const domain = await ctx.db.get(args.domainId);
    return domain;
  }
});
```

2. **Use explicit type annotation** (when needed for clarity):
```typescript
import { QueryCtx } from "./_generated/server";
import { Id } from "./_generated/dataModel";

export const getDomain = zQuery({
  args: {
    domainId: zid("domains")
  },
  handler: async (ctx: QueryCtx, args: { domainId: Id<"domains"> }) => {
    const domain = await ctx.db.get(args.domainId);
    return domain;
  }
});
```

3. **Use type assertion** (only when necessary):
```typescript
export const complexQuery = zQuery({
  args: {
    complexFilter: z.object({/* ... */})
  },
  handler: async (ctx, args) => {
    // If TypeScript has trouble with complex nested types
    const typedFilter = (args.complexFilter as ComplexFilterType);
    // ... use typedFilter
  }
});
```

For most cases, option 1 is sufficient as the zQuery/zMutation helpers handle type inference. Options 2 and 3 are for special cases where you need more explicit typing.

### Error Handling

For single operations, throw errors with descriptive messages:

```typescript
const domain = await ctx.db.get(domainId);
if (!domain) {
  throw new Error(`Domain with ID ${domainId} not found`);
}
```

For bulk operations, track failures and return partial results:

```typescript
const updatedIds = [];
const failedUpdates = [];

for (const id of ids) {
  try {
    // Update operation
    updatedIds.push(id);
  } catch (error) {
    failedUpdates.push({
      id,
      error: error.message
    });
  }
}

return {
  ids: updatedIds,
  failed: failedUpdates.length > 0 ? failedUpdates : undefined
};
```

## Key Takeaways

1. **Operations & Naming**: Use consistent naming for CRUD operations
   - `createDomain` (single + bulk)
   - `listDomains` (query with filters & pagination)
   - `getDomain` (single item by ID)
   - `updateDomain` (single + bulk)
   - `deleteDomain` (single + bulk)

2. **Single + Bulk**: Combine them in the same operation for a cleaner API

3. **Zod for Validation**:
   - Use Zod (`z.*`) for runtime validation
   - Use `v.*` in `schema.ts` for DB schema only

4. **Query Performance**:
   - Define indexes for frequently filtered fields
   - Use `.withIndex()` or `.withSearchIndex()` instead of `.filter()`  
   - Chain query methods in a single expression
   - With pagination, always use indexes for filtering to ensure proper page sizes


5. **TypeScript Integration**:
   - Use `z.infer<typeof Schema>` for type derivation
   - Use `Id<"tableName">` from generated types

6. **Consistent Return Formats**:
   - For create/update/delete: `{ ids: string[], failed?: array }`
   - For queries: `{ page: array, continuationToken?: any }`

7. **Error Handling**:
   - Throw errors for single operations
   - Track failures in bulk operations
   - Return both successes and failures

8. **Documentation**:
   - Add JSDoc comments with examples
   - Document error states and edge cases