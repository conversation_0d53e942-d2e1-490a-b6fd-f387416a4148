import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  // ...other imports
} from '@tanstack/react-table';

function MyTable({ data, columns }) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    // ... optional state or onStateChange overrides
  });

  return (
    <table>
      <thead>
        {table.getHeaderGroups().map(headerGroup => (
          <tr key={headerGroup.id}>
            {headerGroup.headers.map(header => (
              <th key={header.id} colSpan={header.colSpan}>
                {header.isPlaceholder
                  ? null
                  : flexRender(header.column.columnDef.header, header.getContext())}
                {/* Sorting indicator (example) */}
                {header.column.getCanSort() && (
                  <span onClick={header.column.getToggleSortingHandler()}>
                    {header.column.getIsSorted() === 'asc'
                      ? ' 🔼'
                      : header.column.getIsSorted() === 'desc'
                      ? ' 🔽'
                      : ' ↕'}
                  </span>
                )}
              </th>
            ))}
          </tr>
        ))}
      </thead>

      <tbody>
        {/* Pinned Top Rows */}
        {table.getTopRows?.().map(row => (
          <tr key={row.id} className="pinned-top">
            {row.getVisibleCells().map(cell => (
              <td key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        ))}

        {/* Main Rows */}
        {table.getRowModel().rows.map(row => (
          <React.Fragment key={row.id}>
            <tr>
              {row.getVisibleCells().map(cell => (
                <td key={cell.id} style={{ paddingLeft: `${row.depth * 1.5}rem` }}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
            {/* Expanded Detail (if no subRows) */}
            {row.getIsExpanded() && !row.subRows?.length && (
              <tr key={row.id + '-expanded'}>
                <td colSpan={table.getVisibleLeafColumns().length}>
                  {/* Some detail component */}
                  Additional detail for row {row.id}
                </td>
              </tr>
            )}
          </React.Fragment>
        ))}

        {/* Pinned Bottom Rows */}
        {table.getBottomRows?.().map(row => (
          <tr key={row.id} className="pinned-bottom">
            {row.getVisibleCells().map(cell => (
              <td key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        ))}
      </tbody>

      {/* Optional footers */}
      {table.getFooterGroups && (
        <tfoot>
          {table.getFooterGroups().map(footerGroup => (
            <tr key={footerGroup.id}>
              {footerGroup.headers.map(header => (
                <td key={header.id} colSpan={header.colSpan}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.footer, header.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tfoot>
      )}
    </table>
  );
}
