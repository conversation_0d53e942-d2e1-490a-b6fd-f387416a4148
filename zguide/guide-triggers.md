  <trigger_guidelines>
    <overview>
      - Triggers automatically run functions in the same mutation that changes data in a table.
      - They operate atomically with data changes: queries in parallel never see a partially-updated state without triggers having run.
      - Use the "convex-helpers/server/triggers" library to wrap the built-in Convex DB methods (`ctx.db.insert`, `ctx.db.delete`, etc.).
      - Only mutations wrapped with the custom DB will invoke triggers.
    </overview>

    <trigger_definition>
      - Below is a minimal example of defining triggers and wrapping mutation functions:
      ```typescript
      /* convex/functions.ts */
      import { mutation as rawMutation } from "./_generated/server";
      import { Triggers } from "convex-helpers/server/triggers";
      import { DataModel } from "./_generated/dataModel";
      import { customCtx, customMutation } from "convex-helpers/server/customFunctions";

      // Create a Triggers instance for the schema
      const triggers = new Triggers<DataModel>();

      // Register a trigger for the "users" table
      triggers.register("users", async (ctx, change) => {
        console.log("User changed:", change);
      });

      // Wrap the built-in mutation with a custom DB
      export const mutation = customMutation(rawMutation, customCtx(triggers.wrapDB));

      /* usage-example.ts */
      import { mutation } from "./functions";

      // When this mutation runs, "users" triggers automatically fire on write
      export const myMutation = mutation({
        handler: async (ctx, args) => {
          await ctx.db.insert("users", { name: "Alice" });
        },
      });
      ```
    </trigger_definition>

    <logging>
      - Log changes to help debug issues or track a history of modifications.
      ```typescript
      triggers.register("users", async (ctx, change) => {
        console.log("User changed:", change);
      });
      ```
      - Store logs in a separate table:
      ```typescript
      triggers.register("teams", async (ctx, change) => {
        const tokenIdentifier = (await ctx.auth.getUserIdentity())?.tokenIdentifier;
        await ctx.db.insert("teamAuditLog", {
          teamId: change.id,
          change,
          tokenIdentifier,
        });
      });
      ```
    </logging>

    <denormalizing_fields>
      - Maintain a derived field in the same document to optimize lookups or indexing.
      ```typescript
      triggers.register("trips", async (ctx, change) => {
        if (change.newDoc) {
          const layovers = change.newDoc.flights.length;
          if (change.newDoc.layovers !== layovers) {
            await ctx.db.patch(change.id, { layovers });
          }
        }
      });
      ```
      - Concatenate multiple fields for a full-text search index:
      ```typescript
      triggers.register("books", async (ctx, change) => {
        if (change.newDoc) {
          const allFields =
            change.newDoc.title + " " + change.newDoc.author + " " + change.newDoc.summary;
          if (change.newDoc.allFields !== allFields) {
            await ctx.db.patch(change.id, { allFields });
          }
        }
      });
      ```
      - Always guard against infinite recursion by checking current values before patching.
    </denormalizing_fields>

    <validating_data>
      - Throw errors in triggers to prevent invalid records from being committed:
      ```typescript
      triggers.register("users", async (ctx, change) => {
        if (change.newDoc) {
          const emailRegex = /* your email regex */;
          if (!emailRegex.test(change.newDoc.email)) {
            throw new Error(`Invalid email: ${change.newDoc.email}`);
          }
        }
      });
      ```
      - If the trigger throws, the entire mutation is rolled back unless the mutation itself catches the error (which allows the write to commit).
    </validating_data>

    <authorizing_writes>
      - Restrict modifications to only the owner or other custom logic:
      ```typescript
      triggers.register("messages", async (ctx, change) => {
        const user = await getAuthedUser(ctx);
        const owner = change.oldDoc?.owner ?? change.newDoc?.owner;
        if (user !== owner) {
          throw new Error(`User ${user} not authorized to modify ${owner}'s message`);
        }
      });
      ```
      - Similar to validation, if an error is thrown and not caught, the data change is rolled back.
    </authorizing_writes>

    <cascade_deletes>
      - When a document is deleted, remove or update dependent records:
      ```typescript
      triggers.register("users", async (ctx, change) => {
        if (change.operation === "delete") {
          for await (const msg of ctx.db
            .query("messages")
            .withIndex("owner", (q) => q.eq("owner", change.id))) {
            await ctx.db.delete(msg._id);
          }
        }
      });
      ```
      - Be aware of size limits if many documents need deleting (in which case, schedule async deletions).
    </cascade_deletes>

    <async_processing>
      - If you need to run side-effectful or slower tasks, schedule them instead of running inline:
      ```typescript
      const scheduled: Record<string, string> = {};

      triggers.register("users", async (ctx, change) => {
        if (scheduled[change.id]) {
          await ctx.scheduler.cancel(scheduled[change.id]);
        }
        scheduled[change.id] = await ctx.scheduler.runAfter(0, internal.users.updateClerkUser, {
          id: change.id,
          user: change.newDoc,
        });
      });
      ```
      - This approach is useful for debouncing frequent writes or performing tasks outside the main transaction.
    </async_processing>

    <count_denormalization>
      - Track aggregate counts in a single document:
      ```typescript
      triggers.register("users", async (ctx, change) => {
        const countDoc = (await ctx.db.query("userCount").unique())!;
        if (change.operation === "insert") {
          await ctx.db.patch(countDoc._id, { count: countDoc.count + 1 });
        } else if (change.operation === "delete") {
          await ctx.db.patch(countDoc._id, { count: countDoc.count - 1 });
        }
      });
      ```
      - Storing a global count in one document can cause write contention. Consider sharding or different designs for high-traffic writes.
    </count_denormalization>

    <isolation>
      - Trigger logic is transactional and serializable.
      - Parallel writes on the same table will not commit partially; triggers ensure consistent updates even if multiple inserts happen simultaneously.
    </isolation>

    <sync_with_components>
      - You can integrate triggers with Convex "components" to enrich data or perform advanced tasks:
      ```typescript
      import { ShardedCounter } from "convex-helpers/components/shardedCounter";
      const counter = new ShardedCounter(components.shardedCounter);

      triggers.register("mytable", counter.trigger("mycounter"));
      ```
      - This automatically updates the necessary data for the component.
    </sync_with_components>

    <best_practices>
      - Consider explicit function calls (wrapping DB writes) instead of triggers for clarity—excess triggers can cause "spooky action at a distance."
      - Always wrap mutations (using `customMutation` or similar) so triggers fire reliably.
      - Be cautious with error handling; if a mutation catches a trigger’s error, the data changes persist anyway.
      - Avoid triggers for large batch operations when possible; schedule asynchronous tasks or chunk operations.
      - Keep your triggers code minimal and maintainable to avoid confusion and unintended side effects.
    </best_practices>
  </trigger_guidelines>
