---
description: building drag and drop interfaces with hello pangea library

---
<drag_drop_guide>

<drag_drop_context_responders>
This section covers how to wrap your drag-and-drop area in <DragDropContext> and handle responder callbacks that fire during the drag lifecycle.

Key Responder Props:
	•	onDragStart – Fired when a drag begins (after initial DOM capture).
	•	onDragUpdate – Fired when something changes during drag (like position or droppable).
	•	onDragEnd (required) – Fired when a drag finishes (dropped or canceled). You must finalize reordering here.
	•	onBeforeDragStart – Fired just before drag starts. Rarely needed for basic usage.

Example:

<DragDropContext
  onBeforeDragStart={() => {
    // Optionally prepare or lock something before drag starts
  }}
  onDragStart={(start, provided) => {
    console.log(`Dragging item ${start.draggableId} from ${start.source.droppableId}`);
    provided.announce(`Picked up item ${start.draggableId}`);
  }}
  onDragUpdate={(update, provided) => {
    if (update.destination) {
      console.log(
        `Moving over ${update.destination.droppableId} at index ${update.destination.index}`
      );
    } else {
      console.log("Not over a droppable");
    }
  }}
  onDragEnd={(result, provided) => {
    if (!result.destination) {
      // Dropped outside a list
      return;
    }
    const { source, destination } = result;
    if (source.droppableId === destination.droppableId) {
      // Reorder in same list
      reorderItems(source.index, destination.index);
    } else {
      // Move item between lists
      moveItem(source, destination);
    }
    if (result.reason === 'DROP') {
      provided.announce(`Dropped in ${destination.droppableId} at position ${destination.index + 1}`);
    } else if (result.reason === 'CANCEL') {
      provided.announce(`Drag cancelled`);
    }
  }}
>
  {/* ... droppables here ... */}
</DragDropContext>

Tips:
	•	onDragEnd is required; the library throws an error if missing.
	•	Keep onDragUpdate lean for performance.
	•	You can set a flag (e.g. isDragging=true) in onDragStart to block expensive re-renders until onDragEnd.
</drag_drop_context_responders>

<combining_draggables>
This section explains how to enable “combining” of one draggable with another rather than just reordering or moving items.

Usage:
	•	Set isCombineEnabled on the <Droppable>:

<Droppable droppableId="my-list" isCombineEnabled={true}>
  {/* children */}
</Droppable>

	•	When combine mode is on, dropping one draggable onto another can merge them. In onDragEnd, check result.combine:

onDragEnd={(result) => {
  if (result.combine) {
    const { draggableId, droppableId } = result.combine;
    console.log(`Item ${result.draggableId} combined with ${draggableId} in ${droppableId}`);
    // e.g. remove the dragged item from the list
    setItems(items.filter(item => item.id !== result.draggableId));
    return;
  }
  if (!result.destination) return;
  // Handle normal reorder if not combined
}}

Points:
	•	isCombineEnabled applies to all items in that droppable.
	•	Common approach after combine: remove the dragged item or mark it as merged.
	•	You still handle normal reordering (combine is an additional behavior).
</combining_draggables>

<common_setup_issues>
This section details common pitfalls when first setting up @hello-pangea/dnd:
	•	Console warnings: Check for library warnings if drag doesn’t work (e.g., “Droppable needs a unique ID”).
	•	React version: Ensure React 18+ (matching the library’s peer dependency).
	•	Unique IDs: Each draggableId and droppableId must be globally unique in a <DragDropContext>.
	•	Index rules: Draggable’s index values must be unique/consecutive within a droppable.
	•	Missing key: Always use a stable React key on mapped <Draggable> items.
	•	Empty droppable: Give the container min-height or a visible outline if your list is empty, so it can still accept drops.
	•	Margin collapse: Margins can collapse in ways that cause unexpected spacing during drag. Consider using padding or only top/bottom margins carefully.
	•	Forgetting innerRef: If you see “cannot find draggable with id…”, you likely missed attaching provided.innerRef.
	•	Wrap with : All <Droppable> / <Draggable> must be children of it.
	•	HTML elements: Ensure the final rendered element for Draggable or Droppable is a native DOM node (not an abstract component that swallows the ref).
	•	StrictMode: In React Strict Mode, dev logs might appear twice – not a bug, just double rendering checks.

By addressing these issues, most initial setup problems get resolved.
</common_setup_issues>

<using_innerRef>
innerRef is crucial for DOM measurements and event binding.

Example with a <Droppable>:

<Droppable droppableId="taskList">
  {(provided) => (
    <div
      ref={provided.innerRef}
      {...provided.droppableProps}
      className="task-list"
    >
      {tasks.map((task, index) => (
        <Draggable key={task.id} draggableId={task.id} index={index}>
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              className="task-item"
            >
              {task.content}
            </div>
          )}
        </Draggable>
      ))}
      {provided.placeholder}
    </div>
  )}
</Droppable>

	•	Attach ref={provided.innerRef} to the DOM element you spread provided.droppableProps or provided.draggableProps onto.
	•	If you wrap the item in a custom component, forward the ref to a DOM node. For instance:

const MyItem = React.forwardRef((props, ref) => (
  <div ref={ref} {...props}>
    {props.children}
  </div>
));

Attaching the ref incorrectly or omitting it will break drag-and-drop.
</using_innerRef>

<setup_problem_detection_and_error_recovery>
Dev warnings: The library logs configuration mistakes (e.g., duplicate IDs) via console.error in development. Always check the console.

Error recovery:
	•	If an error occurs in the DnD library, it cancels the drag gracefully.
	•	If your app code throws an error during drag, the library also cancels the drag to avoid leaving the UI stuck. It re-throws the error so your error boundary or global handler can handle it.
	•	Typically you do not need special error handling for DnD; it’s designed to fail safely (cancel the drag).
	•	Keep an eye on dev warnings and fix them rather than suppressing with window['__@hello-pangea/dnd-disable-dev-warnings'] = true;.

This ensures a stable experience even if something unexpected happens mid-drag.
</setup_problem_detection_and_error_recovery>

<rules_for_draggableId_and_droppableId>
IDs:
	•	Must be strings.
	•	Must be globally unique in a <DragDropContext>.
	•	Use stable identifiers (like DB IDs) rather than array indices if possible.
	•	Each <Draggable> has a draggableId; each <Droppable> has a droppableId.
	•	The Draggable index is not the same as its ID – index can change as items reorder, ID stays fixed.

Examples:
	•	Good: droppableId="todo-list" with multiple items draggableId="todo-1", draggableId="todo-2".
	•	Bad: two different droppables both using droppableId="list" or reusing the same draggableId="item-1" in two places.

Indices:
	•	Must be consecutive and unique within each droppable. Typically 0..(N-1).
	•	If you skip or duplicate indexes, it leads to library errors.

Following these rules avoids most runtime errors related to item identification.
</rules_for_draggableId_and_droppableId>

<browser_focus_retention>
For accessibility and a polished UX, @hello-pangea/dnd manages focus:
	•	Mouse/Touch: Clicking to drag usually doesn’t focus the item. After dropping, focus remains wherever it was.
	•	Keyboard dragging: If you lift an item via keyboard (e.g., pressing Space on a focused draggable), the library ensures it stays focused through the drag and after drop, so keyboard users don’t lose their place.

Implementation:
	•	Make draggables focusable by adding tabIndex={0} (or use a naturally focusable element like <button>).
	•	The library uses ARIA attributes and retains focus automatically. You can do additional announcements if needed (Section 10).

This ensures screen reader and keyboard users have a smooth navigation experience.
</browser_focus_retention>

<customizing_or_skipping_drop_animation>
By default, the dropped item animates back into place. You can customize or disable it:

Custom:

<Draggable draggableId={id} index={index}>
  {(provided, snapshot) => {
    let style = provided.draggableProps.style;
    if (snapshot.isDropAnimating) {
      const { curve, duration, moveTo } = snapshot.dropAnimation;
      style = {
        ...style,
        transition: `all ${duration * 1.5}s ${curve}`,
        transform: `${style.transform} rotate(10deg)`,
      };
    }
    return (
      <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps} style={style}>
        {content}
      </div>
    );
  }}
</Draggable>

Here, we slow down the animation and rotate during drop.

Skipping:
	•	Set transitionDuration: '0.001s' when snapshot.isDropAnimating.
	•	Avoid using 0s exactly; the library relies on transitionend events.

This either customizes or removes the final “snap-back” movement to fit your UX needs.
</customizing_or_skipping_drop_animation>

<auto_scrolling>
Auto-scrolling automatically scrolls containers or the window when you drag near the edge.
	•	If a <Droppable> is scrollable (overflow: auto;), it will scroll as you drag items close to its boundary.
	•	The window also scrolls if you drag near the viewport edges.
	•	This is enabled by default, with adjustable thresholds using autoScrollerOptions on <DragDropContext>:

<DragDropContext
  onDragEnd={...}
  autoScrollerOptions={{
    disabled: false,
    startFromPercentage: 0.25,
    maxScrollAtPercentage: 0.05,
    maxPixelScroll: 20,
    ease: (p) => p * p,
    durationDampening: {
      accelerateAt: 1000,
      stopDampeningAt: 2000,
    },
  }}
>
  {/* ... */}
</DragDropContext>

	•	Set disabled: true to turn it off. Otherwise, it “just works” for typical lists and page scrolling.
</auto_scrolling>

<controlling_screen_reader>
@hello-pangea/dnd includes built-in announcements for drag events. You can override them for localization or clarity using provided.announce(message) in your responders:

<DragDropContext
  onDragStart={(start, provided) => {
    const item = findItemById(start.draggableId);
    provided.announce(`Start dragging "${item.label}"`);
  }}
  onDragUpdate={(update, provided) => {
    if (!update.destination) {
      provided.announce(`"${update.draggableId}" is not over a droppable`);
    } else {
      provided.announce(
        `"${update.draggableId}" over column ${update.destination.droppableId}, position ${update.destination.index + 1}`
      );
    }
  }}
  onDragEnd={(result, provided) => {
    if (result.combine) {
      provided.announce(`Merged "${result.draggableId}" with "${result.combine.draggableId}"`);
    } else if (!result.destination) {
      provided.announce(`Cancelled dragging "${result.draggableId}"`);
    } else {
      provided.announce(
        `Dropped "${result.draggableId}" in ${result.destination.droppableId} at position ${result.destination.index + 1}`
      );
    }
    // ... state updates ...
  }}
>
  {/* ... */}
</DragDropContext>

	•	If you don’t call announce(), the library uses default English messages.
	•	Keep messages concise and relevant for users relying on screen readers.
</controlling_screen_reader>

<using_html5_doctype>
Include the standard HTML5 doctype:

<!DOCTYPE html>
<html>
  <head></head>
  <body></body>
</html>

Without it, the browser may enter quirks mode, causing incorrect layout/measurement. The library depends on standard box model calculations to position dragged items accurately. If you see weird positioning or console warnings about doctype, verify <!DOCTYPE html> is present at the top of your page.
</using_html5_doctype>

<typescript_type_information>
@hello-pangea/dnd is written in TypeScript, providing rich definitions. Common types:
	•	DropResult – object in onDragEnd. Contains:
	•	draggableId, source, destination, combine, reason.
	•	DraggableLocation – has droppableId and index.
	•	ResponderProvided – second arg to responders (gives announce()).
	•	DraggableProvided / DraggableStateSnapshot – render props for <Draggable>.
	•	DroppableProvided / DroppableStateSnapshot – render props for <Droppable>.

Example usage:

import { DropResult, DraggableProvided, DraggableStateSnapshot } from '@hello-pangea/dnd';

function handleDragEnd(result: DropResult) {
  if (!result.destination) return;
  // ...
}

<Droppable droppableId="list">
  {(provided, snapshot) => (
    <div ref={provided.innerRef} {...provided.droppableProps}>
      {items.map((it, idx) => (
        <Draggable key={it.id} draggableId={it.id} index={idx}>
          {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
            <div 
              ref={provided.innerRef} 
              {...provided.draggableProps} 
              {...provided.dragHandleProps}
            >
              {it.content}
            </div>
          )}
        </Draggable>
      ))}
      {provided.placeholder}
    </div>
  )}
</Droppable>

TypeScript ensures you handle optional fields correctly (e.g., result.destination can be null) and provides autocompletion for DnD props.
</typescript_type_information>

<dragging_svg_elements>
Directly making an <svg> draggable can be problematic. Instead, wrap it in an HTML element:

<Draggable draggableId="icon1" index={0}>
  {(provided) => (
    <span 
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      style={{ display: 'inline-block' }}
    >
      <svg width={50} height={50}>
        {/* ...paths... */}
      </svg>
    </span>
  )}
</Draggable>

	•	<svg> itself doesn’t handle the inline drag styles well and may fail to register events correctly.
	•	Wrapping ensures a standard DOM node for the library to track.
	•	For accessibility, ensure the wrapper or SVG is labeled/focusable if needed for keyboard dragging.
</dragging_svg_elements>

<avoiding_image_flickering>
Images can flicker when dragged because the library creates a clone. The browser might re-fetch or re-render the <img>.

Prevention:
	•	Caching: In dev, disable “Disable Cache” in Chrome DevTools to avoid re-requesting the image on each clone. In production, use proper cache headers.
	•	Inline images: Use data URIs (if small) so there’s no network request on new element creation.
	•	draggable="false" on <img> tags to prevent native HTML drag ghost.
	•	Set explicit width/height on <img> or container so layout doesn’t jump.

These steps eliminate or greatly reduce image flicker.
</avoiding_image_flickering>

<non_visible_preset_styles>
@hello-pangea/dnd injects some default CSS to handle cursor, text selection, pointer events, etc. Highlights:
	•	Drag handle:
	•	cursor: grab; or grabbing;
	•	touch-action: manipulation;
	•	Disables iOS context menu on long press (-webkit-touch-callout: none;)
	•	Droppable:
	•	overflow-anchor: none; to prevent scroll anchoring.
	•	Pointer events:
	•	The dragged item and all other droppables except the current target have pointer-events: none; to avoid accidental hovers/clicks.
	•	User select:
	•	user-select: none; during drag to prevent text highlighting.
	•	Position:
	•	The dragged element is position: fixed; and top-level z-index for floating.

You can override them with higher-specificity CSS if needed, but these defaults usually give a smooth, glitch-free drag experience.
</non_visible_preset_styles>

<how_scroll_containers_are_detected>
The library auto-detects scrollable ancestors:
	•	Checks the dragged item’s ancestors for overflow: auto/scroll.
	•	Allows only one scroll container per axis. If a <Droppable> is scrollable, it typically becomes the auto-scrolled container, not its parent.
	•	The window is also considered for auto-scrolling if near viewport edges.
	•	For nested scrollable containers, the library tries to pick the relevant one. Avoid multiple nested scrollable parents around the same droppable, if possible.

Usually, if your CSS is typical (a single scrollable wrapper), scrolling “just works”.
</how_scroll_containers_are_detected>

<handling_dom_events>
@hello-pangea/dnd manages low-level DOM events (mouse, touch, keyboard) so you don’t have to:
	•	Default prevention: It prevents text selection and native HTML5 drag. You can check event.defaultPrevented in your own handlers to differentiate real clicks from drag-clicks.
	•	Key events: Space or arrow keys might be prevented if used by keyboard dragging.
	•	Propagation: The library stops propagation on certain events to avoid interfering with parent handlers.
	•	Native HTML5 drop events are not used; rely on the library’s onDragEnd for final drop logic.

In most cases, you only implement your logic in DnD’s responders instead of raw DOM event handlers.
</handling_dom_events>

<adding_draggables_during_a_drag_11x_behavior>
It’s possible to modify the list during a drag (e.g., infinite scroll or auto-expanding folders):
	•	Allowed: Adding/removing <Draggable> items in the same list if it’s scrollable. The library updates dimension calculations on the fly.
	•	Conditions:
	•	Same list / same droppable type.
	•	The droppable is scrollable, so added items don’t resize the container drastically.
	•	Insert/remove items instantly without a size transition that would disrupt layout.
	•	In onDragUpdate, you can detect if the user is near the end and load more items. Or expand subitems in a folder.

Example:

onDragUpdate={(update) => {
  if (!update.destination) return;
  if (update.destination.index > items.length - 2 && !loadingMore) {
    loadMoreItems();
  }
}}

This pattern is advanced; thoroughly test to ensure smooth performance and correct item positioning.
</adding_draggables_during_a_drag_11x_behavior>

<setting_up_csp>
When using a strict Content Security Policy (CSP), inline <style> injection by the library can be blocked. You’ll see an error like “Refused to apply inline style…”

Solutions:
	1.	Allow 'unsafe-inline':

Content-Security-Policy: style-src 'self' 'unsafe-inline';

Quick, but less secure.

	2.	Use CSP nonces (recommended):
	•	Generate a nonce on the server for each request, e.g. “ABC123”.
	•	Add 'nonce-ABC123' to style-src in your CSP header.
	•	Pass the same nonce to <DragDropContext nonce={nonceValue}>.
	•	The library’s injected style tag will have nonce="ABC123", satisfying CSP.

This ensures drag styles (e.g., user-select: none) are allowed under strict CSP without weakening security globally.
</setting_up_csp>

<virtual_lists>
For very large lists, you might render only visible items using react-window or react-virtualized. DnD can still work with “virtual mode”:
	•	Use mode="virtual" on <Droppable>.
	•	Provide a renderClone prop that renders the draggable item when it’s out of the DOM.
	•	Don’t render provided.placeholder; instead, manually reserve space by increasing the list’s itemCount (e.g., items.length + 1) when snapshot.isUsingPlaceholder is true.

Example with react-window:

<Droppable droppableId="big-list" mode="virtual" 
           renderClone={(provided, snapshot, rubric) => {
             const item = items[rubric.source.index];
             return (
               <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}
                    style={{ ...provided.draggableProps.style }}>
                 {item.content}
               </div>
             );
           }}>
  {(provided, snapshot) => (
    <List
      height={600}
      itemCount={snapshot.isUsingPlaceholder ? items.length + 1 : items.length}
      itemSize={50}
      outerRef={provided.innerRef}
      itemData={items}
    >
      {({ index, style, data }) => {
        if (index === data.length) return null; // placeholder slot
        const item = data[index];
        return (
          <Draggable draggableId={item.id} index={index}>
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                style={{ ...style, ...provided.draggableProps.style }}
              >
                {item.content}
              </div>
            )}
          </Draggable>
        );
      }}
    </List>
  )}
</Droppable>

This approach yields infinite scrolling or large-data DnD without performance slowdowns from rendering every item in the DOM.
</virtual_lists>

<multi_drag>
To drag multiple items simultaneously, you must implement selection logic on top of the library. The library inherently treats a single draggable at a time, so you handle the “multi” part:
	1.	Selection: Let users select multiple items (e.g. Ctrl+Click). Track them in selectedIds.
	2.	onDragStart: If the dragged item is in selectedIds, treat all of them as “picked up”.
	3.	renderClone: Show a custom clone indicating multiple items are being dragged.
	4.	onDragEnd: Move all selected items together to the new location. The library’s result only references the single draggable ID, but you know which items are selected.

Pseudocode:

onDragEnd={(result) => {
  if (!result.destination) return;
  if (isMultiDrag(result.draggableId)) {
    const movedItems = items.filter(i => selectedIds.has(i.id));
    // remove them from source list, then insert into destination
  } else {
    // normal single drag
  }
  clearSelection();
}}

You can optionally hide or ghost out the other selected items in the source list. This pattern is more involved than basic DnD, but feasible with some careful state management.
</multi_drag>

<tables>
Reordering table rows:


Basic:

<Droppable droppableId="table-body" as="tbody">
  {(provided) => (
    <tbody ref={provided.innerRef} {...provided.droppableProps}>
      {rows.map((row, index) => (
        <Draggable key={row.id} draggableId={row.id} index={index} as="tr">
          {(provided, snapshot) => (
            <tr
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              style={snapshot.isDragging ? { display: 'table', ...provided.draggableProps.style } : undefined}
            >
              <td>{row.name}</td>
              <td>{row.value}</td>
            </tr>
          )}
        </Draggable>
      ))}
      {provided.placeholder}
    </tbody>
  )}
</Droppable>

Layout concerns:
	•	If possible, use table-layout: fixed; and assign fixed widths so columns don’t shift.
	•	For auto layout, lock widths on drag start (e.g. measure each <td> and set inline width).
	•	The dragged row might be cloned or portaled to avoid overflow clipping or column misalignment.

Dragging entire columns is more complex, since a column spans multiple rows. Typically, row DnD is simpler to implement with the approach above.


<reparenting_a_draggable>
Reparenting (portaling) moves the dragged item out of its original DOM container, often to <body>, to avoid z-index or overflow issues:

Preferred: Use renderClone on <Droppable>:

<Droppable
  droppableId="list"
  renderClone={(provided, snapshot, rubric) => (
    <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
      {/* clone content */}
    </div>
  )}
>

By default, the clone is appended to document.body. You can change it via <DragDropContext getContainerForClone={() => someElement} />.

Alternatively, you can manually portal inside the Draggable’s render function, but that’s more complicated. The built-in clone approach is usually enough.

Reparenting helps if:
	•	A parent has transform or overflow: hidden that would clip the dragged item.
	•	You need the dragged item to appear above all other layers.

The library automatically removes the cloned element after dropping, simplifying cleanup.
</reparenting_a_draggable>

<conclusion>
By following these step-by-step sections and code examples, you can build robust drag-and-drop interfaces with @hello-pangea/dnd. You’ve seen how to configure responders, handle combining items, debug setup issues, leverage custom drop animations, integrate screen reader support, manage advanced scenarios like virtual lists, multi-drag, and table rows, and even account for strict CSP or reparenting. With this knowledge, your application’s drag-and-drop features can be accessible, performant, and well-structured.
</conclusion>
Below are some recommended steps and patterns to ensure your dragged card always appears on top, without breaking DnD functionality or throwing runtime errors. In short, you either need to remove any parent stacking‐context triggers (such as filters, transforms, isolation, z‐indices) or explicitly “portal” the drag preview to the document body so it cannot be visually clipped by intermediate ancestors.

⸻

1. Let the DnD Library Handle Z‐Index

By default, @hello-pangea/dnd sets the dragged element’s style to:

position: fixed;
z-index: 9999; /* or a similar large number */
pointer-events: none;

This should naturally float the “drag ghost” above everything—unless there is a parent element creating a new stacking context. Common culprits include:
	1.	position: relative combined with a z-index on a parent.
	2.	isolation: isolate or contain: content;.
	3.	backdrop-filter, filter, or transform on a parent container.
	4.	opacity other than 1 or mix-blend-mode on an ancestor.

If the parent has any of these, the child’s high z‐index can be “trapped” behind siblings inside that stacking context. Removing or minimizing these properties often solves the stacking problem.

Try this first:
	•	Make sure your .Card or .CardContent components do not have any position: relative; z-index: something; that is creating a new stacking context.
	•	Remove isolation: isolate; or backdrop-blur-none; if possible (especially if they cause a stacking context in Safari).
	•	In general, let the library’s default “drag ghost” styles do their job.

If removing these transforms/filters is not feasible (e.g., you need them for design), you can force the dragged item to be portaled to the end of the document, as described below.

⸻

2. Portal the Drag Preview (Reparenting)

A robust fix is to have the library reparent the dragged item into a top-level DOM node (usually document.body). Once portaled outside your local stacking context, the ghost can’t be hidden behind sibling elements. There are two main approaches:

A. Using getContainerForClone on <DragDropContext>

import React from "react";
import { DragDropContext } from "@hello-pangea/dnd";

function KanbanView() {
  return (
    <DragDropContext
      onDragEnd={handleDragEnd}
      getContainerForClone={() => document.body} // <--- Add this
    >
      {/* ... your Droppable columns ... */}
    </DragDropContext>
  );
}

When getContainerForClone returns document.body, @hello-pangea/dnd inserts its “drag preview” element directly into the <body>, bypassing any parent stacking context.

B. Using renderClone on <Droppable>

Another pattern is to define a custom “clone” for each item in your <Droppable> (especially helpful if you need highly customized drag previews). Example:

<Droppable
  droppableId="myColumn"
  mode="standard"
  renderClone={(provided, snapshot, rubric) => {
    // The "clone" is portaled automatically to a top-level container
    const clonedDecision = decisions[rubric.source.index];
    return (
      <div
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        style={provided.draggableProps.style}
      >
        <DecisionsCard decision={clonedDecision} />
      </div>
    );
  }}
>
  {(provided) => (
    <div ref={provided.innerRef} {...provided.droppableProps}>
      {/* your mapped Draggables here */}
      {provided.placeholder}
    </div>
  )}
</Droppable>

When renderClone is used, the library portals that clone out of the normal document flow, ensuring a high stacking level.

⸻

3. Keep the Drag‐Handle & Provided Props in the Right Places

The original crash—“Unhandled Runtime Error: Can’t find variable: isDragging”—often arises if you inadvertently overwrite or omit the library’s provided props on the real <Draggable> or <Droppable> element. Make sure:
	1.	You spread all relevant props:

<div
  ref={provided.innerRef}
  {...provided.draggableProps}
  {...provided.dragHandleProps}
>
  {/* child content */}
</div>


	2.	You do not wrap the final DOM in a component that discards or incorrectly forwards ref. If you do wrap, use React.forwardRef and forward the ref.
	3.	You do not manually set your own z-index or transform on the dragged item—this can conflict with the library’s dynamic styles. If you must do so, merge them carefully with provided.draggableProps.style.

⸻

4. Double-Check No Parent Overflows or Scrollable Wrappers

If your card is inside another element with overflow: hidden; or overflow: clip;, it might clip the drag preview. Typically, you want the top-level Kanban container to be scrollable, not each card. The library’s auto‐scroll logic also expects droppables to be in a “normal” layout.

Make sure you’re not inadvertently clipping the ghost due to an overflow: hidden on a parent <Card> or a nest of div elements. If the entire page is scrollable, that’s often simpler.

⸻

5. Summary of Fix Steps
	1.	Remove stacking contexts from parent .Card or .CardContent if you can (e.g., remove isolation: isolate; and position: relative; z-index: 1; or filter/backdrop-filter).
	2.	Stop applying your own manual z-index or position to the <Draggable> element or child if possible. Let @hello-pangea/dnd do it.
	3.	Spread all library props (draggableProps, dragHandleProps, innerRef) onto a native DOM element.
	4.	Portal the dragged ghost if any transformation or filter is truly necessary in the parent:

<DragDropContext
  onDragEnd={handleDragEnd}
  getContainerForClone={() => document.body}
>
  ...
</DragDropContext>


	5.	Verify in your browser’s DevTools that the dragged item is position: fixed; z-index: 9999;. If so, it should appear above siblings unless an ancestor forcibly clips or masks it.

Following these guidelines will keep the DnD library’s default stacking behavior intact and let you drag cards over all other content without reintroducing the behind-other-cards bug.

</drag_drop_guide>