Playwright Test Cheat Sheet (TypeScript)

Setup and Configuration
	•	Install and Init: Install Playwright Test via NPM (npm i -D @playwright/test) or use the init script (npm init playwright@latest). This sets up the test runner and example config.
	•	Configuration File: Create a playwright.config.ts using defineConfig. Key options include:
	•	testDir – root directory for test files (default "tests" ￼).
	•	testMatch – glob or regex for test file names (default runs *.spec.ts, *.test.ts, etc. ￼).
	•	retries – retry failed tests (e.g. retries: 2 on CI, 0 locally ￼).
	•	workers – number of parallel worker processes (e.g. set to 1 to run tests sequentially ￼).
	•	use – global fixtures/options applied to all tests (see Test use options). For example: base URL, browser launch config, context options, etc. ￼.
	•	webServer – start a dev server before tests (with command and url to wait for ￼).
	•	reporter – test reporter format (e.g. 'html', 'list', etc. ￼).
	•	Running Tests: Use the Playwright Test CLI to run tests. For example:
	•	npx playwright test – run all tests (picks up playwright.config.ts by default).
	•	npx playwright test tests/todo.spec.ts – run tests in a specific file.
	•	CLI flags include --project, --debug, --grep (filter by title), --headed (run with UI), etc. ￼.
	•	Example Config (TS): Basic playwright.config.ts structure:

import { defineConfig, devices } from '@playwright/test';
export default defineConfig({
  testDir: 'tests',
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  use: {
    baseURL: 'http://localhost:3000',
    headless: true,
    trace: 'on-first-retry',  // capture trace on failures
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } }
  ],
  reporter: [['html', { open: 'never' }]],
});



Core Concepts
	•	Test Structure: Import { test, expect } from @playwright/test to write tests ￼. A test is declared with test('name', async ({ page }) => { ... }). The callback receives fixtures (e.g. page) in an object.
	•	Fixtures: Playwright tests run with fixtures that provide context (browser, context, page, etc.) for each test. By default, each test gets a new isolated Browser Context and Page via the built-in context and page fixtures ￼. For example:

test('basic test', async ({ page }) => {
  await page.goto('/login');
  // ... test steps
});

Here page is a fixture giving a new Page object that is set up before the test and torn down after ￼.

	•	test API: The test object provides methods to define test suites and hooks:
	•	test.describe(title, callback) – group tests under a title (acts like a suite) ￼.
	•	test.beforeAll / test.afterAll – hook that runs once before/after all tests in a file or describe-group ￼.
	•	test.beforeEach / test.afterEach – runs before/after each test in a file or describe-group ￼.
	•	expect API – for assertions inside tests (automatically retried for certain conditions – see Assertions below).
	•	Auto-waiting: Playwright Test auto-waits for elements to be ready before actions. Actions like click/fill wait for the element to appear and be actionable ￼, so explicit waits are often not needed. Assertions (expect) on locators also wait for conditions to be met.
	•	TypeScript Support: Playwright Test has first-class TypeScript support ￼. Tests and config can be written in TS – ensure the config’s use: {} has testDir and test files configured, and that your environment is set up for TS (Playwright can auto compile TS via the test runner).

Page and Locator Methods
	•	Page Navigation & Info:
	•	await page.goto('https://example.com') – navigate to a URL ￼. Resolved when the page is loaded (until network idle by default).
	•	await page.waitForLoadState('networkidle') – wait for no network traffic (common after navigation).
	•	page.url() and page.title() – get current page URL and title.
	•	page.waitForSelector(selector[, options]) – explicitly wait for an element to appear/disappear. (Often not needed due to auto-wait, but available.)
	•	Locating Elements (Locators): Use the page.locator() and related methods to find elements. Locator objects are lazy finders that auto-retry until actions/assertions succeed ￼.
	•	By Role: page.getByRole('button', { name: 'Submit' }) finds a button with accessible name “Submit” ￼. This leverages ARIA roles and is robust to DOM changes ￼.
	•	By Text: page.getByText('Welcome, John!') finds element with text content containing “Welcome, John!” ￼.
	•	By Label: page.getByLabel('Email') finds an input associated with the label “Email” (useful for form fields).
	•	By Test ID: page.getByTestId('settings-dialog') targets elements by data-testid attribute ￼ (configurable attribute, default is data-testid ￼).
	•	CSS / XPath: page.locator('css=div.card') or simply page.locator('div.card') finds by CSS selector ￼. You can also use page.locator('//xpath') for XPath. Playwright auto-detects if a string is CSS or XPath ￼.
	•	Chaining Locators: You can chain locators to narrow down. e.g. page.getByRole('list').getByText('Item 1') – first finds a list, then an item in it.
	•	Nth Elements: Use locator.nth(index) to pick a specific element from a list (e.g. page.locator('div.item').nth(2) for the 3rd match).
	•	Filtering: locator.filter({ hasText: 'Foo' }) filters a locator list to those containing text “Foo” ￼. locator.first() and locator.last() are also available.
	•	Page-Scoped Methods:
	•	page.locator(selector) – primary way to get a Locator for elements matching a selector.
	•	page.$(selector) / page.$$(selector) – get ElementHandle (single or all). Note: In tests, prefer locator over ElementHandle for auto-waiting and better assertions.
	•	page.getBy* methods – recommended for reliability (see above).
	•	page.locator().locator(childSelector) – find a child element from an existing locator.
	•	page.frameLocator('iframe_selector') – create a locator for an element inside an <iframe> (see Frames).
	•	Assertions on Locators: Locators can be directly passed to expect() for assertions like visibility or text (see Assertions section).

Handling UI Interactions (Click, Fill, Select, etc.)
	•	Clicking: Use await locator.click() to click an element. Playwright waits for the element to be visible and enabled before clicking ￼. You can also specify click options:
	•	e.g. locator.click({ button: 'right' }) for right-click ￼, or locator.dblclick() for double-click.
	•	Holding key modifiers: locator.click({ modifiers: ['Shift'] }) for Shift+click ￼.
	•	Filling Text Fields: Use await locator.fill('text') to focus and fill input fields ￼. This sets the value and triggers input events. (It clears any existing text by default.)
	•	Example: await page.getByLabel('Username').fill('alice'); – fills the “Username” input.
	•	Alternatively, locator.type('abc') types text character by character (useful if you need key timing or to not clear existing text).
	•	Checkboxes and Radios: Use await locator.check() to check a checkbox or select a radio button ￼. Use locator.uncheck() to uncheck a checkbox.
	•	Example: await page.getByLabel('I agree to terms').check(); – checks the checkbox labeled “I agree to terms” ￼.
	•	You can verify state with assertions like expect(locator).toBeChecked() ￼ (see Assertions).
	•	Select Dropdown Options: Use await locator.selectOption(valueOrOptions) to select an option in a <select> dropdown ￼.
	•	You can pass a single value or an object with { label: 'Option Text' }, or an array for multiple select.
	•	Example: await page.getByLabel('Choose a color').selectOption('blue'); – selects the option with value or label “blue” ￼.
	•	Multiple: await page.getByLabel('Languages').selectOption(['en', 'fr']); – selects multiple options by values.
	•	Keyboard Input: The page.keyboard API allows low-level key presses:
	•	await page.keyboard.press('Enter') – press a key.
	•	await page.keyboard.type('hello') – type a sequence of characters.
	•	Often you can use higher-level methods (fill, press on locators), but page.keyboard is available for custom scenarios (like shortcuts).
	•	Hovering: await locator.hover() to move the mouse over an element (fires hover/tooltip events) ￼.
	•	Other Actions:
	•	locator.press('Enter') – press a key while an element is focused.
	•	locator.clear() – clear input field content (or use fill('') to clear).
	•	locator.focus() and locator.blur() – focus or blur an element.
	•	locator.setInputFiles('path/to/file') – for file upload inputs, sets the file to upload ￼.
	•	Auto-wait & Timing: All these actions automatically wait for necessary conditions (element visible, stable, etc.). If an action times out, you might need to ensure the element appears or is enabled. You can adjust the default action timeout via config (e.g. use: { actionTimeout: 10000 } for 10s) or per action by options.

Assertions (Expect API)

Playwright’s expect API provides a rich set of assertions for UI tests. Import it from @playwright/test (already done when using test).
	•	Basic Assertions: Similar to Jest:
	•	expect(actual).toBe(expected) – strict equality.
	•	expect(value).toEqual(obj) – deep equality.
	•	expect(array).toContain(item) – containment check.
	•	expect(fn).toThrow() – function throws (for sync code).
	•	Assertions on Elements (Locators): Playwright extends expect to handle promises and locators:
	•	Visibility & State: await expect(locator).toBeVisible() – element is visible in DOM ￼. Use .toBeHidden() for hidden, .toBeEnabled() / .toBeDisabled() for enabled/disabled state ￼, .toBeChecked() for checkbox/radio checked state.
	•	Text/Value: await expect(locator).toHaveText('Exact text') – element’s text matches exactly ￼. Supports regex or partial with toContainText for substring ￼.
await expect(locator).toHaveValue('input value') for input fields ￼.
	•	Attributes: await expect(locator).toHaveAttribute('href', '/home') – check element’s attribute value.
	•	Count: await expect(locator).toHaveCount(3) – check number of matching elements.
	•	Misc: await expect(locator).toHaveClass(/active/) to match CSS class, toHaveCSS for computed CSS styles (less common).
	•	Assertions on Page: You can assert on Page context:
	•	await expect(page).toHaveURL(/.*dashboard/) – current URL matches pattern ￼.
	•	await expect(page).toHaveTitle('My App') – page title is “My App” ￼.
	•	await expect(page).toHaveScreenshot() – compare current screen with a baseline screenshot (for visual regression).
	•	Timing and Retries: All locator and page assertions automatically retry until the condition is met or timeout (default timeout ~5s). No need for explicit waiting for most assertions. You can override timeout per assertion: e.g. expect(locator).toHaveText('foo', { timeout: 10000 }).
	•	Soft Assertions: Playwright allows soft assertions that don’t immediately fail the test. For example:

const softExpect = expect.soft;
await softExpect(locator).toHaveText('Foo');

Soft assertions collect failures and report them at the end.

	•	Negation: Use .not for negative assertions:
	•	await expect(locator).not.toBeVisible(); – element should not be visible.
	•	await expect(page).not.toHaveURL('error').
	•	Expect Polling: For complex conditions, expect.poll(() => /* return value */).toBe(...) can be used to wait on arbitrary conditions.

Hooks and Fixtures
	•	Before/After Hooks:
	•	test.beforeAll(async (fixtures) => { ... }) – Runs once before all tests in the file (or within a describe group if used inside one) ￼. Use for one-time setup (e.g. starting server, seeding data). Receives worker-scoped fixtures (like browser) if needed.
	•	test.afterAll(async (fixtures) => { ... }) – Runs once after all tests in the scope have finished ￼. Use for teardown (closing resources, etc.).
	•	test.beforeEach(async ({ page }) => { ... }) – Runs before each test ￼. Common for navigation to a base URL, test setup, etc. Has access to test fixtures (e.g. page, context) ￼ and testInfo.
	•	test.afterEach(async ({ page }, testInfo) => { ... }) – Runs after each test. Useful for cleanup (e.g. deleting created data, logging out) or post-test actions (like screenshot on failure: if (testInfo.status !== 'passed') ...).
	•	Usage: You can call these in the global scope of a test file or inside test.describe(). If inside a describe, they apply only to that group. Multiple hooks can be declared; they run in order of declaration ￼ ￼.

test.beforeEach(async ({ page }) => {
  // Example: go to base URL before each test
  await page.goto('https://example.com'); 
});
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    // e.g., on failure, take screenshot
    await page.screenshot({ path: `screenshots/${testInfo.title}.png` });
  }
});


	•	Fixtures: Playwright’s test runner is fixture-based ￼. Fixtures are the building blocks that supply tests with the objects they need (like page). Key built-in fixtures:
	•	browser – Reuses a single Browser instance for the test worker ￼.
	•	context – A fresh BrowserContext created for each test, providing isolated session (cookies, storage) ￼.
	•	page – A new Page (in a new context) for each test ￼. This is the main handle for UI actions.
	•	browserName – Name of the browser (e.g. 'chromium', 'firefox'), useful for conditional test logic ￼.
	•	testInfo – (via test.info() or as arg in hooks) metadata about the current test (title, file, status, etc.), often used in hooks for logging or attachments.
	•	These fixtures are available by destructuring the argument of test() and hook callbacks (e.g. async ({ page, context, browserName })).
	•	Custom Fixtures: You can extend Playwright Test with your own fixtures. Two approaches:
	1.	test.use (per file or suite): Supply fixture overrides or options for a scope ￼. For example, to use a logged-in state for all tests in a describe:

test.describe('Dashboard', () => {
  test.use({ storageState: 'storage/loggedInState.json' });
  test('shows user data', async ({ page }) => { ... });
});

Or globally in a file: test.use({ locale: 'fr-FR' }); to run tests in French locale ￼.

	2.	test.extend (define new fixtures): Create a new test object with additional fixtures. For example:

const test = base.extend<{ adminPage: Page }>({
  adminPage: async ({ browser }, use) => {
    // Launch a page as admin user
    const adminContext = await browser.newContext({ storageState: 'adminState.json' });
    const adminPage = await adminContext.newPage();
    await use(adminPage);
    await adminContext.close();
  },
});

Then use test('admin test', async ({ adminPage }) => { ... });. The fixture setup/teardown is handled automatically (before/after the test).

	•	Sharing Data Between Tests: Avoid sharing state via globals – instead use fixtures or test.beforeAll to produce data. If you need to pass data from one test to another, consider making them separate tests that share a fixture (e.g., a fixture that sets up initial data accessible to both tests).
	•	Order of Execution: beforeAll runs before any tests, beforeEach runs before each test, then the test, then afterEach, and finally afterAll. Within the same level, hooks respect definition order ￼. (Note: afterAll always runs even if tests fail, in order to teardown.)

Handling Downloads, Dialogs, Frames, and Network
	•	Downloads: To handle file downloads in tests, use the page.waitForEvent('download'):

// Start waiting for the download before triggering it
const downloadPromise = page.waitForEvent('download');
await page.getByText('Download file').click();
const download = await downloadPromise;
// Wait for download to complete, then save to disk
await download.saveAs('./downloads/myfile.zip');
const path = await download.path(); // get path to temp file if needed
const fileName = download.suggestedFilename();

The Download object provides methods like saveAs() to save the file and path() to get the temp file path ￼. If you don’t know what action triggers the download, you can use page.on('download', handler) to catch any download ￼. By default, downloads go into a temporary folder and are deleted when context closes.

	•	Dialogs (Alerts/Prompts/Confirms): Playwright can intercept page dialogs:
	•	Set up an event listener on the page for 'dialog':

page.on('dialog', async dialog => {
  console.log(`Dialog message: ${dialog.message()}`);
  await dialog.accept(); // or dialog.dismiss() for confirm/prompt
});
await page.getByRole('button', { name: 'Open Modal' }).click();

Here, when the button click triggers an alert/confirm/prompt, the handler will auto-accept it ￼. Important: If there is no page.on('dialog') handler, Playwright automatically dismisses dialogs by default ￼. However, it’s good practice to handle them to assert dialog text or to provide inputs to prompts (dialog.accept(promptText) for prompt dialogs).

	•	The test will pause at the action that caused the dialog until the dialog is handled (since dialogs are modal). Ensure your handler calls accept/dismiss to resume the test ￼.

	•	Frames (iFrames): Interacting with elements inside iframes can be done in two ways:
	1.	Frame Locators (Preferred): Use page.frameLocator(...) to create a locator scoped to an iframe. For example:

const frameLocator = page.frameLocator('#my-iframe');
await frameLocator.getByLabel('User Name').fill('John'); 

This finds an iframe with id="my-iframe" and then the “User Name” field inside it, filling it ￼. Frame locators simplify working across frames by chaining locators.

	2.	Frame Objects: Access the Frame object and use it like a page:

const frame = page.frame({ name: 'iframe-name' }); // or { url: /regex/ }
await frame?.fill('#username', 'John');

You can get a frame by name, URL, or other properties using page.frame() ￼. Once you have a frame, you can call frame.click(), frame.locator(), etc., similar to page. Ensure to null-check (page.frame(...) returns Frame | null).
	•	Each page has a main frame (page.mainFrame()), and potentially multiple child frames (page.frames() array). Most page methods act on the main frame by default.

	•	Network Handling:
	•	Waiting for network responses: If your test needs to await an API response or network call (for example, after clicking a save button), you can use page.waitForResponse:

const [response] = await Promise.all([
  page.waitForResponse(resp => resp.url().includes('/api/save') && resp.status() === 200),
  page.click('text=Save')  // action that triggers the request
]);
const data = await response.json();

This waits for a response that matches the predicate (URL contains /api/save and returns 200 OK) ￼. You can also pass a URL string or RegExp to waitForResponse ￼.

	•	Network Request Interception (Mocking): Use page.route(urlPattern, route => {}) to intercept network requests and stub or modify them ￼. Examples:
	•	Mock an API:

await page.route('**/api/data', route =>
  route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ items: [] }) })
);

This intercepts requests to */api/data and returns a custom response ￼. Great for isolating tests from backend or simulating responses.

	•	Modify a Request:

await page.route('**/*', route => {
  const headers = { ...route.request().headers(), 'X-Test': 'true' };
  route.continue({ headers });
});

This adds an X-Test: true header to all requests by continuing the request with modified headers ￼.

	•	Block content:

await page.route('**/*.{png,jpg,jpeg}', route => route.abort());

This blocks all image requests to speed up or test offline scenarios ￼.

	•	When using page.route, set it before the page action that triggers the request (routes must be enabled prior to the request). You can also use browserContext.route to apply to all pages in the context ￼.
	•	Remove a route with await page.unroute(urlPattern) if needed (not often necessary in tests).

	•	Network Event Monitoring: Listen to page.on('request') and page.on('response') events to log or assert network activity:

page.on('requestfailed', req => {
  console.error(`Request failed: ${req.url()} - ${req.failure()?.errorText}`);
});

or use browserContext.on('request', ...) for all pages. Playwright also provides response.status(), response.headers(), etc., if you want to make assertions on network calls.

	•	API Testing: Note that Playwright Test also has an APIRequestContext fixture for direct API calls (outside browser). But for UI test context, intercepting via page.route or waiting for page.waitForResponse covers most needs.

Parallelization and Projects
	•	Test Parallelism: By default, Playwright runs tests in parallel across files. Each test file is run in a separate worker process (up to the workers limit) ￼. Within a single file, tests run in order by default (serially). Key points:
	•	To run tests in one file concurrently, use test.describe.configure({ mode: 'parallel' }) inside that file ￼. This lets tests in that describe block run in parallel (each in isolated contexts). Use with care (tests must not interfere with each other).
	•	To ensure tests (or an entire describe) run in sequence, you can set mode: 'serial' in test.describe.configure or use test.describe.serial(title, callback) helper. This is useful for dependent tests or when reusing state within a file.
	•	Workers: The number of parallel workers defaults to the number of CPU cores. You can override via config (workers: 4) or CLI (--workers=4) ￼. Setting workers: 1 forces all tests to run sequentially (no parallelism) ￼.
	•	Shard tests: For splitting tests across CI machines, use the CLI --shard=X/Y option or shard in config.
	•	Playwright ensures each worker has a fresh browser context environment. Tests in different workers cannot share state (by design) ￼.
	•	Projects: Projects in Playwright Test allow you to define multiple execution configurations (often different browsers or device settings) and run tests in all of them:
	•	Define an array of projects in playwright.config.ts ￼. Each project can specify a name and its own use options (like which browser, viewport, context options, etc.).
	•	Example:

projects: [
  { name: 'Chromium', use: { browserName: 'chromium', ...devices['Desktop Chrome'] } },
  { name: 'Firefox', use: { browserName: 'firefox', ...devices['Desktop Firefox'] } },
  { name: 'WebKit Mobile', use: { browserName: 'webkit', ...devices['iPhone 12'] } },
]

This config will run all tests on three projects: Desktop Chrome, Desktop Firefox, and Mobile Safari (WebKit) ￼ ￼.

	•	Run all projects with one command (npx playwright test) – tests will be grouped by project, and can run in parallel across projects too. The default CLI runs all projects; use --project=name to run a specific one.
	•	Different baseURL or settings: You can also use projects to test against multiple environments or configurations. For instance, two projects pointing to different baseURL (staging vs prod), or one with use: { colorScheme: 'dark' } and another 'light' to test theming.
	•	In tests, you can access the project name via testInfo.project.name if needed (for conditional logic or logging).
	•	Parallel across projects: By default, tests in different projects are also run in parallel (one project per worker or sequentially depending on workers). If you have limited resources or want to run projects sequentially, you can launch separate playwright test --project=X commands or adjust workers to 1 when running multiple projects.

	•	Test Distribution: With multiple projects and many test files, Playwright will distribute tests across workers such that all projects run to completion. Each project essentially runs its full suite – so total test count = tests * number of projects.
	•	Isolation: Each project run is isolated: it uses its own browser binaries (Chromium vs Firefox etc.), its own context options, and can even have separate outputDir for artifacts. This ensures one project’s configuration doesn’t bleed into another.

Test Annotations (skip, fixme, slow, etc.)

Playwright Test supports annotations to modify test behavior or expectations. These are called via the test object.
	•	Focus a test: test.only(title, fn) marks that test (or suite) as the only one to run ￼. If any tests are marked .only, Playwright will skip all others ￼. You can also use test.describe.only to focus an entire group.
	•	Skip a test:
	•	Static Skip: test.skip(title, fn) defines a test that is expected to be skipped (it won’t run at all) ￼. Similarly, test.describe.skip(title, callback) will skip all tests in a describe group.
	•	Conditional Skip: You can call test.skip(condition, 'reason') inside a test or beforeEach to skip at runtime if condition is true ￼. For example:

test('feature X - Chrome only', async ({ browserName }) => {
  test.skip(browserName !== 'chromium', 'Only applicable to Chromium browsers');
  // test code...
});

If not Chromium, the test is skipped with the given reason ￼.

	•	Expected Fail (fail & fixme):
	•	test.fail() marks a test as expected to fail. Playwright will run the test, but won’t mark it as a failure if it does fail – instead it will mark the test as “expected fail”. If the test passes unexpectedly, it will be reported as a failure (because it was supposed to fail) ￼.
	•	test.fixme() is similar to skip but indicates the test is a known issue (“to fix”). Playwright will not run the test (like skip), but still report it in results as skipped with intention to fix ￼. Use this for tests that are broken and you plan to fix later.
	•	Slow tests: test.slow() triples the default timeout for that test ￼. Use this if a particular test consistently needs more time. Can be called inside the test or in a beforeEach for that test.
	•	Tagging tests: You can tag tests with metadata. For example:

test('my test', { tag: '@smoke' }, async () => { ... });

This tag shows in reports ￼ and can be used to filter tests via CLI (--grep @smoke). You can also include @tag in the title which has a similar effect ￼.

	•	Skipping on condition (platform/browser): Instead of in-test test.skip, you can use annotation at top:

test.describe.configure({ skip: Boolean(condition) });

to skip a whole suite based on a condition. Or use test.skip(condition, reason) in the first line of the test as shown above.

	•	Combining annotations: You can apply multiple annotations. E.g. test.skip() and test.slow() together if a test is temporarily skipped and slow.
	•	CI safeguards: forbidOnly: true in config (often set on CI) will make the run fail if any .only is left in the code ￼, preventing accidentally skipping other tests in CI.
	•	Example:

test('known bug test', async () => {
  test.fixme(true, 'Bug #1234 - failing, skip until fixed');
  // ... this won't run
});

test('conditional skip', async ({ browserName }) => {
  test.skip(browserName === 'webkit', 'Flaky on WebKit');
  // ... runs only if not WebKit
});

These annotations can be placed at the top of the test body.

Reporting

Playwright Test includes various reporters to output results in different formats.
	•	Built-in Reporters:
	•	List (default): Prints a detailed report to the console, with one line per test and failures with traces. This is the default reporter ￼.
	•	Line: A concise reporter that updates a single line in the console (good for CI to minimize log size) ￼.
	•	Dot: Minimal output – prints one dot per test, and a full report for failures ￼.
	•	HTML: Generates a comprehensive HTML report with results, traces, screenshots, etc. After running with the HTML reporter, open it via npx playwright show-report ￼. By default, the report is saved in playwright-report/ folder ￼.
	•	JSON: Outputs results to a JSON file (machine-readable) ￼.
	•	JUnit: Outputs an XML report (JUnit format) for CI integration ￼.
	•	Microsoft (MS Teams/GitHub): A special github reporter annotates failures in GitHub Actions.
	•	Multiple reporters: You can specify more than one reporter at once. For example, in config:

reporter: [
  ['list'], 
  ['json', { outputFile: 'results.json' }],
  ['html', { open: 'never' }]
],

This will print the list report to console, write results.json, and generate an HTML report (without auto-opening it) ￼ ￼.

	•	Using Reporters: Configure the reporter in playwright.config.ts (as shown above) or via CLI --reporter option for a one-time run ￼. For example:
	•	npx playwright test --reporter=dot will use the dot reporter.
	•	npx playwright test --reporter=html will generate the HTML report.
	•	HTML Reporter: After a run with reporter: 'html', use npx playwright show-report to open the report in a browser ￼. The HTML report includes a summary, statistics, and detailed steps for each test (with screenshots, traces if available).
	•	Trace Viewer: Not exactly a reporter, but if you have tracing enabled (e.g. trace: 'on-first-retry' in config), you can open trace files with npx playwright show-trace trace.zip for a timeline of actions. Traces are linked in the HTML report for failed tests when collected.
	•	Custom Reporters: You can write your own reporter by extending the Reporter API (exporting a class with methods like onBegin, onTestEnd, etc.). Specify the path to your reporter file in config (reporter: './MyReporter.ts') ￼. This is advanced usage when built-ins are not sufficient.
	•	CI Integration: The built-in reporters (JSON, JUnit, etc.) can be used to integrate with CI systems. For example, use JUnit reporter to generate results.xml then have CI parse it for test results. On GitHub Actions, using reporter: 'github' will post annotations to the PR for failures.
	•	Summary: Pick a reporter that fits your use case: use HTML for local debugging of test results, line or dot for CI console output, JUnit/JSON for CI parsing, and multiple reporters if you need both human-readable output and machine-readable logs in one run. Adjust in the config as needed. ￼