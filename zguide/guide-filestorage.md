<convex_file_storage_guide>
  <!-- 
    This guide is structured similarly to the existing <convex_guidelines> artifact.
    It covers best practices and advanced usage of Convex’s file storage in a Next.js (TypeScript) application.
    All code snippets adhere to Convex’s new function syntax and the rules outlined in the original guidelines.
  -->

  <uploading_files>
    <overview>
      Convex’s file storage API uses one-time **upload URLs**:
      1. A client requests an upload URL by calling a Convex mutation (which invokes `ctx.storage.generateUploadUrl()`).
      2. The client `POST`s the file to that URL within 1 hour.
      3. Convex returns a `storageId` for the newly uploaded file.
      4. The client calls another mutation to save the `storageId` in your application’s tables.
    </overview>

   <backend_functions>
      <!-- Following the new function syntax from the guidelines -->
      <file path="convex/files.ts">
        <![CDATA[
        import { mutation } from "./_generated/server";
        import { v } from "convex/values";

        /**
         * A mutation that generates a short-lived URL for uploading a file to Convex.
         */
        export const generateUploadUrl = mutation({
          args: {},
          returns: v.string(),
          handler: async (ctx, args) => {
            // Optionally check user auth or role-based permissions here.
            const uploadUrl = await ctx.storage.generateUploadUrl();
            // Returns a URL that the client can POST a file to.
            return uploadUrl;
          },
        });

        /**
         * A mutation to record the uploaded file in a Convex table, for reference.
         */
        export const saveFile = mutation({
          args: {
            storageId: v.id("_storage"),  // Validate that it matches a storage ID
            userId: v.string(),
            filename: v.optional(v.string()),
          },
          returns: v.null(),
          handler: async (ctx, args) => {
            // Insert a document in the "user_files" table (defined in schema)
            await ctx.db.insert("user_files", {
              owner: args.userId,
              fileId: args.storageId,
              filename: args.filename,
              uploadedAt: Date.now(),
            });
            return null;
          },
        });
        ]]>
   </file>

   <notes>
        - **New function syntax** is used (`mutation({ args: ..., returns: ..., handler: ... })`).
        - **Argument validation** uses `v.id("_storage")` to ensure the passed ID is a valid Convex file reference.
        - **Return values**: The `generateUploadUrl` mutation returns a string (the upload URL). The `saveFile` mutation returns `null`.
      </notes>
    </backend_functions>

   <frontend_usage>
      <file path="app/FileUploadForm.tsx">
        <![CDATA[
        "use client";
        import { FormEvent, useRef, useState } from "react";
        import { useMutation } from "convex/react";
        import { api } from "../convex/_generated/api";

        export default function FileUploadForm() {
          const fileInput = useRef<HTMLInputElement>(null);
          const [selectedFile, setSelectedFile] = useState<File | null>(null);

          const generateUploadUrl = useMutation(api.files.generateUploadUrl);
          const saveFile = useMutation(api.files.saveFile);

          async function handleUpload(event: FormEvent) {
            event.preventDefault();
            if (!selectedFile) return;

            // 1. Ask Convex for an upload URL
            const uploadUrl = await generateUploadUrl({});
            // 2. POST the file to that URL
            const response = await fetch(uploadUrl, {
              method: "POST",
              headers: { "Content-Type": selectedFile.type },
              body: selectedFile,
            });
            const { storageId } = await response.json();

            // 3. Save the storageId in our Convex table
            await saveFile({
              storageId,
              userId: "demoUserId",
              filename: selectedFile.name,
            });

            // Cleanup
            setSelectedFile(null);
            if (fileInput.current) {
              fileInput.current.value = "";
            }
          }

          return (
            <form onSubmit={handleUpload}>
              <input
                type="file"
                ref={fileInput}
                onChange={(e) => setSelectedFile(e.target.files?.[0] ?? null)}
              />
              <button type="submit" disabled={!selectedFile}>Upload</button>
            </form>
          );
        }
        ]]>
   </file>

  <explanation>
    - The form requests a fresh upload URL from `generateUploadUrl`.
    - The file is then posted **directly** to that URL, returning a `storageId`.
    - Finally, we call `saveFile` to store the file’s reference (and any metadata) in our `user_files` table.
  </explanation>
    </frontend_usage>
  </uploading_files>

  <schema>
    <definition>
      <!-- Example schema that includes a user_files table. -->
      <file path="convex/schema.ts">
        <![CDATA[
        import { defineSchema, defineTable } from "convex/server";
        import { v } from "convex/values";

        export default defineSchema({
          user_files: defineTable({
            owner: v.string(),
            fileId: v.id("_storage"),
            filename: v.optional(v.string()),
            uploadedAt: v.number(),
          }).index("by_owner", ["owner"]),
          // You can define other tables like 'users' or 'posts' here as needed.
        });
        ]]>
      </file>
    </definition>
    <notes>
      - We create a table named <b>user_files</b> to store references to uploaded files.
      - The <b>fileId</b> field uses <code>v.id("_storage")</code> to ensure it points to a valid document in the _storage system table.
      - We define an index, <code>"by_owner"</code>, to quickly retrieve files by their owner.
    </notes>
  </schema>

  <serving_files>
    <dynamic_urls>
      <explanation>
        The simplest way to serve files is to generate temporary URLs from Convex. 
        Use <code>ctx.storage.getUrl()</code> in a query or mutation to get a public URL for a file, 
        which you can return to the client. This URL remains valid for a period (e.g. ~1 hour).
      </explanation>
      <file path="convex/listUserFiles.ts">
        <![CDATA[
        import { query } from "./_generated/server";
        import { v } from "convex/values";

        export const listUserFiles = query({
          args: { userId: v.string() },
          returns: v.array(
            v.object({
              _id: v.id("user_files"),
              owner: v.string(),
              fileId: v.id("_storage"),
              filename: v.optional(v.string()),
              uploadedAt: v.number(),
              url: v.optional(v.string()),
            })
          ),
          handler: async (ctx, args) => {
            // Fetch user's files from user_files.
            const files = await ctx.db
              .query("user_files")
              .withIndex("by_owner", (q) => q.eq("owner", args.userId))
              .collect();

            // For each file, generate a URL if the file still exists.
            const augmented = [];
            for (const fileRecord of files) {
              const url = await ctx.storage.getUrl(fileRecord.fileId);
              augmented.push({ ...fileRecord, url: url ?? undefined });
            }
            return augmented;
          },
        });
        ]]>
      </file>
  <client_side_example>
    ```tsx
    import { useQuery } from "convex/react";
    import { api } from "../convex/_generated/api";

    export default function UserFileList({ userId }: { userId: string }) {
      const files = useQuery(api.listUserFiles, { userId }) || [];
      return (
        <div>
          {files.map((f) => (
            <div key={f._id.toString()}>
              <p>Filename: {f.filename}</p>
              {f.url && <img src={f.url} alt={f.filename} style={{ maxWidth: 300 }} />}
            </div>
          ))}
        </div>
      );
    }
    ```
    - Each file object includes a <code>url</code> property that can be used in an <code>&lt;img&gt;</code> tag or <code>&lt;a&gt;</code> link.
  </client_side_example>
      <notes>
        - <b>Access control</b>: If only certain users may view a file, perform checks before calling <code>ctx.storage.getUrl</code>.
        - If a file doesn’t exist or was deleted, <code>getUrl()</code> returns <code>null</code>.
      </notes>
    </dynamic_urls>

    <alternative_http>
      <explanation>
        For streaming large files or adding per-request authorization, you can define an HTTP action. 
        This can wrap <code>ctx.storage.get</code> and <code>return new Response(...)</code> with the file content. 
        However, actions are limited to ~20MB response payloads and do not have direct DB access (only via <code>ctx.runQuery</code> or <code>ctx.runMutation</code>).
      </explanation>
    </alternative_http>
  </serving_files>

  <managing_metadata>
    <metadata_overview>
      Every uploaded file is tracked in the <code>_storage</code> system table with fields like <code>size</code>, <code>contentType</code>, and <code>sha256</code>. 
      You can retrieve this using <code>ctx.db.system.get(fileId)</code>:
    </metadata_overview>
    <example>
      <file path="convex/getFileMetadata.ts">
        <![CDATA[
        import { query } from "./_generated/server";
        import { v } from "convex/values";
        import { Id } from "./_generated/dataModel";

        type FileMetadata = {
          _id: Id<"_storage">;
          _creationTime: number;
          contentType?: string;
          sha256: string;
          size: number;
        };

        export const getFileMetadata = query({
          args: { fileId: v.id("_storage") },
          returns: v.union(
            v.object({
              _id: v.id("_storage"),
              _creationTime: v.number(),
              contentType: v.optional(v.string()),
              sha256: v.string(),
              size: v.number(),
            }),
            v.null()
          ),
          handler: async (ctx, { fileId }) => {
            const metadata: FileMetadata | null = await ctx.db.system.get(fileId);
            return metadata;
          },
        });
        ]]>
  </file>
  </example>
  <notes>
    - Use these fields to display file size, check content type, or detect duplicates via <code>sha256</code>.
    - You can also query <code>_storage</code> globally with <code>ctx.db.system.query("_storage")</code> to list all files.
  </notes>
  </managing_metadata>

  <deleting_files>
    <overview>
      <code>ctx.storage.delete(fileId)</code> permanently removes a file from Convex storage and its metadata entry in <code>_storage</code>. 
      Any previously generated URL for that file becomes invalid.
    </overview>
    <example>
      <file path="convex/deleteFile.ts">
        <![CDATA[
        import { mutation } from "./_generated/server";
        import { v } from "convex/values";
        // If you need typed Id references:
        // import { Id } from "./_generated/dataModel";

        export const deleteFile = mutation({
          args: {
            fileId: v.id("_storage"),
          },
          returns: v.null(),
          handler: async (ctx, args) => {
            // Remove from storage
            await ctx.storage.delete(args.fileId);

            // Optionally remove references from your own tables.
            // For example, if you keep a 'user_files' document referencing this file:
            const existing = await ctx.db
              .query("user_files")
              .filter(q => q.eq(q.field("fileId"), args.fileId))
              .collect();
            for (const doc of existing) {
              await ctx.db.delete(doc._id); 
            }
            return null;
          },
        });
        ]]>
      </file>
    </example>
  <notes>
    - Make sure only authorized users (e.g., file owner) can trigger this. 
    - Deletion is irreversible. 
    - Keep your app’s tables in sync (remove any stale references to the deleted file).
  </notes>
  </deleting_files>

  <security_best_practices>
    <upload_restrictions>
      - **Restrict who can upload**: In <code>generateUploadUrl</code>, verify the user is logged in (e.g., <code>ctx.auth</code> checks) and meets criteria to upload.
      - **Validate file size and type**: On the client, limit acceptable file types (e.g., images) and sizes. On the server, consider checking <code>contentType</code> after upload or verifying <code>size</code> in metadata.
      - **Clean up orphaned files**: If an upload was successful but not stored in your DB, that file becomes “orphaned.” Periodically compare <code>_storage</code> with your tables to remove unused files.
    </upload_restrictions>
    <serving_restrictions>
      - **Secure file URLs**: <code>ctx.storage.getUrl(fileId)</code> produces a URL anyone with the link can access until it expires. If your app is multi-tenant or has private files, only generate URLs for authorized users or implement an HTTP action with an auth check.
      - **Virus scanning / advanced checks**: If your app allows arbitrary file uploads, consider scanning them in a Node-based action or an external service before distributing.
    </serving_restrictions>
  </security_best_practices>

  <conclusion>
    <summary>
      By following these patterns—generating secure upload URLs, storing file references in your Convex tables, 
      returning file URLs in queries, and applying robust security checks—you can manage user-uploaded files in 
      a Next.js TypeScript application with ease. Refer to 
      <link>https://docs.convex.dev</link> 
      for the latest details on Convex file storage features.
    </summary>
  </conclusion>
</convex_file_storage_guide>
