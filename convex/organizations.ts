import { query } from './_generated/server';
import { v } from 'convex/values';
import { QueryCtx } from './_generated/server';
import { Doc } from './_generated/dataModel';

/**
 * Search organizations by name using the search index.
 * Returns a limited list of matching organizations.
 */
export const searchOrganizationsByName = query({
  args: { 
    searchQuery: v.string(), 
    limit: v.optional(v.number()) 
  },
  handler: async (ctx: QueryCtx, args): Promise<Doc<'organizations'>[]> => { 
    const { searchQuery, limit = 10 } = args;

    if (!searchQuery) {
      return []; 
    }

    const organizations = await ctx.db
      .query('organizations')
      .withSearchIndex('search_name', (q) => 
        q.search('name', searchQuery) 
      )
      .take(limit);
      
    // Return raw documents
    return organizations; 
  },
});

// TODO: Add other organization-related queries and mutations here (e.g., get, list, create, update, delete)
