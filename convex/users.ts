import { v } from 'convex/values';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery, zMutation } from './functions';
import { getAuthUserId } from '@convex-dev/auth/server';
import { Id } from './_generated/dataModel';
import { ConvexError } from "convex/values";
import {
  UserSchema,
  CreateUserSchema,
  UpdateUserSchema,
  UserFilterSchema,
  UserPaginationSchema,
  User,
  UserWithPersonSchema // Import the new schema
} from '../zod/users-schema';
import { PersonSchema } from '../zod/directory-schema'; // Import PersonSchema
import { mutation, query } from "./_generated/server";

/**
 * Get the current authenticated user's information
 *
 * This query uses getAuthUserId to directly retrieve the user's ID from the authentication system,
 * then fetches the complete user document from the database.
 *
 * If the user is authenticated but doesn't have a record in the database,
 * it will return null.
 *
 * @returns The user document or null if not authenticated or not found
 */
export const currentUser = zQuery({
  args: {},
  output: z.union([UserSchema, z.null()]),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    const user = await ctx.db.get(userId);
    return user as User | null;
  }
});

/**
 * Create missing people records for existing users
 * 
 * This mutation iterates through all users and creates a corresponding
 * person record in the "people" table if one doesn't already exist.
 */
export const createMissingPeopleRecords = zMutation({
  args: {},
  output: z.object({
    createdCount: z.number(),
    failed: z.array(z.object({
      email: z.string(),
      error: z.string(),
      code: z.string()
    })).optional()
  }),
  handler: async (ctx) => {
    const createdCount = 0;
    const failedCreations: { email: string; error: string; code: string }[] = [];

    // Retrieve all users
    const users = await ctx.db.query("users").collect();

    // Run all person creation attempts in parallel for efficiency
    await Promise.all(users.map(async (user) => {
      try {
        // Check if a person record with this email already exists
        const existingPerson = await ctx.db
          .query("people")
          .withIndex("by_email", q => q.eq("email", user.email))
          .unique();

        if (!existingPerson) {
          // Create the person record with all required fields, using empty arrays for embeddings
          await ctx.db.insert("people", {
            name: user.name || 'Unnamed User',
            email: user.email,
            image: user.image,
            user_id: user._id,
            updated_at: Date.now(),
            description_embedding: [],
            short_description_embedding: [],
            research_embedding: []
          });
        }
      } catch (error: unknown) {
        // Track failures
        let errorMessage: string;
        let errorCode: string;

        if (error instanceof ConvexError) {
          errorMessage = error.data.message;
          errorCode = error.data.code;
        } else {
          errorMessage = error instanceof Error ? error.message : "Unknown error";
          errorCode = "CREATION_FAILED";
        }

        failedCreations.push({
          email: user.email,
          error: errorMessage,
          code: errorCode
        });
      }
    }));
    // Fix: increment createdCount in the loop
    let finalCreatedCount = 0;
    for (const user of users) {
      const existingPerson = await ctx.db
          .query("people")
          .withIndex("by_email", q => q.eq("email", user.email))
          .unique();
      if (!existingPerson) {
        finalCreatedCount++;
      }
    }

    // Return both successes and failures
    const response: { 
      createdCount: number, 
      failed?: { email: string; error: string; code: string }[] 
    } = { 
      createdCount: finalCreatedCount
    };
    
    if (failedCreations.length > 0) {
      response.failed = failedCreations;
    }
    
    return response;
  }
});

/**
 * List all users in the system with pagination
 * 
 * This query returns users ordered by creation time (newest first)
 * with pagination support.
 * 
 * @example
 * const { users, continueCursor } = await client.query.listUsers({ limit: 20, cursor });
 */
export const listUsers = zQuery({
  args: {
    cursor: z.any().optional(),
    limit: z.number().min(1).max(100).default(50),
    sortDirection: z.enum(["asc", "desc"]).default("desc")
  },
  output: z.object({
    users: z.array(UserSchema),
    continueCursor: z.any().nullable()
  }),
  handler: async (ctx, args: { cursor?: any; limit: number; sortDirection: "asc" | "desc" }) => {
    const { limit, cursor, sortDirection } = args;
    const results = await ctx.db
      .query("users")
      .order(sortDirection)
      .paginate({ cursor, numItems: limit });

    return {
      users: results.page as User[],
      continueCursor: results.continueCursor
    };
  }
});

/**
 * List all users who have a corresponding person record, including team assignments.
 * This performs a join between users and people tables.
 *
 * @returns An array of combined user and person data.
 */
export const listUsersWithPeopleAndTeams = zQuery({
  args: {}, // No arguments for now, fetches all linked users/people
  output: z.array(UserWithPersonSchema),
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    const results: z.infer<typeof UserWithPersonSchema>[] = [];

    for (const user of users) {
      // Find the corresponding person record using the index
      const person = await ctx.db
        .query("people")
        .withIndex("by_userId", (q) => q.eq("user_id", user._id))
        .unique();

      // Only include if a matching person record exists
      if (person) {
        try {
          // Validate person data (optional but good practice)
          const validatedPerson = PersonSchema.parse(person);

          // Construct the combined object
          const combinedData = {
            userId: user._id,
            personId: validatedPerson._id,
            name: validatedPerson.name,
            // Prefer person's email/image, fallback to user's if needed
            email: validatedPerson.email || user.email,
            title: validatedPerson.title,
            image: validatedPerson.image || user.image,
            teams: user.teams || [], // Ensure teams is an array
          };

          // Validate the final combined object against the schema
          results.push(UserWithPersonSchema.parse(combinedData));

        } catch (error) {
           // Log parsing errors but continue processing other users
           console.error(`Error processing user ${user._id} or person ${person._id}:`, error);
        }
      }
    }

    return results;
  },
});


/**
 * Check if a user exists and create directory people  data if they're new
 */
export const checkNewUser = zMutation({
  args: {},
  output: z.object({
    isNewUser: z.boolean(),
    user: z.union([UserSchema, z.null()])
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return { isNewUser: false, user: null };

    // Get the user document
    const user = await ctx.db.get(userId);
    
    if (user) {
      // If last_active is not set, this is a new user
      if (!user.last_active) {
        console.log("[New User Detected]", {
          userId,
          email: user.email,
          name: user.name,
          timestamp: new Date().toISOString()
        });

        // Create a corresponding person record
        const createPeopleResult = await ctx.db.query('people')
          .withIndex('by_email', q => q.eq('email', user.email))
          .unique();

        if (!createPeopleResult) {
          // Only create if person doesn't exist
          await ctx.db.insert('people', {
            name: user.name || 'Unnamed User',
            email: user.email,
            image: user.image,
            user_id: userId,
            updated_at: Date.now(),
            description_embedding: [],
            short_description_embedding: [],
            research_embedding: []
          });
        }

        // Update the user with last_active timestamp and re-read the updated user
        await ctx.db.patch(userId, {
          last_active: Date.now()
        });
        const updatedUser = await ctx.db.get(userId);
        return {
          isNewUser: true,
          user: updatedUser as User
        };
      }

      // For existing users, return the user as is without updating last_active to prevent reactivity loops
      return {
        isNewUser: false,
        user: user as User
      };
    }

    return {
      isNewUser: false,
      user: user as User | null
    };
  }
});

/**
 * Create one or multiple users
 * 
 * @example
 * // Create a single user
 * const result = await client.mutation.createUsers({
 *   user: { email: "<EMAIL>", name: "New User" }
 * });
 * 
 * // Create multiple users
 * const result = await client.mutation.createUsers({
 *   users: [
 *     { email: "<EMAIL>", name: "User 1" },
 *     { email: "<EMAIL>", name: "User 2" }
 *   ]
 * });
 * 
 * @throws {ConvexError} 
 * - With code "MISSING_USER" if neither user nor users is provided
 * - With code "DUPLICATE_EMAIL" if a user with the email already exists
 * - With code "CREATION_FAILED" if the creation operation fails
 */
export const createUsers = zMutation({
  args: {
    user: CreateUserSchema.optional(),
    users: z.array(CreateUserSchema).optional(),
  },
  output: z.object({
    ids: z.array(zid("users")),
    failed: z.array(z.object({
      email: z.string(),
      error: z.string(),
      code: z.string()
    })).optional()
  }),
  handler: async (ctx, args) => {
    const { user, users } = args;
    const now = Date.now();
    const createdIds: Id<"users">[] = [];
    const failedCreations: { email: string; error: string; code: string }[] = [];

    // Validate that either user or users is provided
    if (!user && (!users || users.length === 0)) {
      throw new ConvexError({ 
        message: "Either user or users must be provided",
        code: "MISSING_USER"
      });
    }

    /**
     * Helper to create a single user
     */
    async function createSingleUser(userData: z.infer<typeof CreateUserSchema>) {
      try {
        // Check if user with this email already exists
        const existingUser = await ctx.db
          .query("users")
          .withIndex("email", q => q.eq("email", userData.email))
          .unique();

        if (existingUser) {
          throw new ConvexError({ 
            message: `User with email ${userData.email} already exists`,
            code: "DUPLICATE_EMAIL"
          });
        }

        // Create the user with the provided fields and timestamps
        const userId = await ctx.db.insert("users", {
          ...userData,
          last_active: now
        });

        return userId;
      } catch (error: unknown) {
        // Track failures for bulk operations
        let errorMessage: string;
        let errorCode: string;

        if (error instanceof ConvexError) {
          errorMessage = error.data.message;
          errorCode = error.data.code;
        } else {
          errorMessage = error instanceof Error ? error.message : "Unknown error";
          errorCode = "CREATION_FAILED";
        }

        failedCreations.push({
          email: userData.email,
          error: errorMessage,
          code: errorCode
        });
        return null;
      }
    }

    // Handle bulk creation case
    if (users && users.length > 0) {
      for (const userData of users) {
        const result = await createSingleUser(userData);
        if (result) createdIds.push(result);
      }
    }
    // Handle single creation case
    else if (user) {
      const result = await createSingleUser(user);
      if (result) createdIds.push(result);
    }

    // Return both successes and failures
    const response: { 
      ids: Id<"users">[], 
      failed?: { email: string; error: string; code: string }[] 
    } = { 
      ids: createdIds 
    };
    
    if (failedCreations.length > 0) {
      response.failed = failedCreations;
    }
    
    return response;
  }
});

/**
 * Update one or multiple users
 * 
 * @example
 * // Update a single user
 * const result = await client.mutation.updateUsers({
 *   id: "123abc",
 *   updates: { name: "New Name" }
 * });
 * 
 * // Update multiple users
 * const result = await client.mutation.updateUsers({
 *   ids: ["123abc", "456def"],
 *   updates: { roles: ["role1", "role2"] }
 * });
 * 
 * @throws {ConvexError} 
 * - With code "MISSING_ID" if neither id nor ids is provided
 * - With code "USER_NOT_FOUND" if a user doesn't exist
 * - With code "UPDATE_FAILED" if the update operation fails
 */
export const updateUsers = zMutation({
  args: {
    id: zid("users").optional(),
    ids: z.array(zid("users")).optional(),
    updates: UpdateUserSchema
  },
  output: z.object({
    ids: z.array(z.string()),
    failed: z.array(z.object({
      id: z.string(),
      error: z.string(),
      code: z.string()
    })).optional()
  }),
  handler: async (ctx, args) => {
    const { id, ids, updates } = args;
    const now = Date.now();
    const updatedIds: string[] = [];
    const failedUpdates: { id: string; error: string; code: string }[] = [];

    // Validate that either id or ids is provided
    if (!id && (!ids || ids.length === 0)) {
      throw new ConvexError({ 
        message: "Either id or ids must be provided",
        code: "MISSING_ID"
      });
    }

    /**
     * Helper to update a single user
     */
    async function updateSingleUser(userId: Id<"users">) {
      try {
        const user = await ctx.db.get(userId);
        if (!user) {
          throw new ConvexError({ 
            message: `User not found: ${userId}`,
            code: "USER_NOT_FOUND"
          });
        }

        // Update the user with the provided fields and last_active timestamp
        await ctx.db.patch(userId, {
          ...updates,
          last_active: now
        });

        return userId;
      } catch (error: unknown) {
        // Track failures for bulk operations
        let errorMessage: string;
        let errorCode: string;

        if (error instanceof ConvexError) {
          errorMessage = error.data.message;
          errorCode = error.data.code;
        } else {
          errorMessage = error instanceof Error ? error.message : "Unknown error";
          errorCode = "UPDATE_FAILED";
        }

        failedUpdates.push({
          id: userId.toString(),
          error: errorMessage,
          code: errorCode
        });
        return null;
      }
    }

    // Handle bulk update case
    if (ids && ids.length > 0) {
      for (const userId of ids) {
        const result = await updateSingleUser(userId);
        if (result) updatedIds.push(result.toString());
      }
    }
    // Handle single update case
    else if (id) {
      const result = await updateSingleUser(id);
      if (result) updatedIds.push(result.toString());
    }

    // Return both successes and failures
    const response: { ids: string[], failed?: { id: string; error: string; code: string }[] } = { 
      ids: updatedIds 
    };
    if (failedUpdates.length > 0) {
      response.failed = failedUpdates;
    }
    return response;
  }
});

/**
 * Get user data by ID
 */
export const get = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("tokenIdentifier"), args.userId))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    return user;
  },
});

/**
 * Get AI-generated summary of user data
 */
export const getSummary = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("tokenIdentifier"), args.userId))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    // This would typically be generated from analyzing user documents
    // For now, we'll return a placeholder structure
    return {
      professionalSummary: "Professional summary will be generated from your documents.",
      skills: ["Skill 1", "Skill 2", "Skill 3"],
      experienceHighlights: [
        "Experience highlight 1",
        "Experience highlight 2",
        "Experience highlight 3"
      ],
      education: [
        "Education detail 1",
        "Education detail 2"
      ],
      certifications: [
        "Certification 1",
        "Certification 2"
      ]
    };
  },
});

/**
 * Update user data
 */
export const update = zMutation({
  args: {
    userId: zid("users"),
    ...Object.fromEntries(
      Object.entries(UpdateUserSchema.shape).map(([key, value]) => [
        key,
        z.any().optional()
      ])
    ),
  },
  handler: async (ctx, args) => {
    const { userId, ...updateData } = args;

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Remove undefined values from updateData
    const cleanedData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Update user data
    await ctx.db.patch(userId, cleanedData);

    return userId;
  },
});

/**
 * Check if a user exists by ID without throwing an error
 */
export const currentUserExists = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db.get(args.userId);
      return !!user; // Return true if user exists, false otherwise
    } catch (error) {
      console.log(`User ID check failed for ${args.userId}:`, error);
      return false;
    }
  },
});
