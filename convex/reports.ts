// // reports.ts
// // A collection of Convex queries for financial reporting needs, including:
// //  - Vendor trends
// //  - Expense category analysis
// //  - Large transaction detection
// //  - Overall spending trends
// //  - Bill status analysis
// //  - Reconciliation checks
// //  - Temporal frequency analysis
// //  - Cross-dimensional insights
// //  - Data consistency checks

// import { zQuery } from './functions'; // Must be a custom wrapper that calls zodToValidator internally
// import { zodToValidator } from 'convex-helpers/server/zod'; // Or wherever your helper is
// import { z } from 'zod';
// import { zid } from 'convex-helpers/server/zod';

// // Example: If you have "bills" and "lineItems" in your schema, ensure
// // those tables define `amount: v.number()`, etc., so the DB calls match.

// // ----------------------------------------------------------------------------
// // Zod schemas from your "zod/" directory
// // ----------------------------------------------------------------------------

// import { LineItemSchema, LineItemFilterSchema } from '../zod/lineItems-schema';
// import { BillSchema, BillQueryFiltersSchema } from '../zod/bills-schema';

// /**
//  * --------------------------------------------------------
//  * Common schemas for aggregation return types
//  * --------------------------------------------------------
//  */

// // 1. Vendor Stats
// const VendorStatsSchema = z.object({
//   vendor_id: z.string(),
//   totalSpending: z.number(),
//   txnCount: z.number()
// });

// // 2. Category Stats
// const CategoryStatsSchema = z.object({
//   category: z.string(),
//   totalSpending: z.number(),
//   txnCount: z.number()
// });

// // 3. Daily Spending Stats
// const DailySpendingStatsSchema = z.object({
//   date: z.string(), // e.g. "YYYY-MM-DD"
//   totalSpending: z.number(),
//   txnCount: z.number()
// });

// // 4. Bill Status Stats
// const BillStatusStatsSchema = z.object({
//   billStatus: z.string(),
//   totalBillAmount: z.number(),
//   billCount: z.number()
// });

// // 5. New Vendor
// const NewVendorSchema = z.object({
//   vendor_id: z.string(),
//   firstDate: z.number()
// });

// // 6. Bill Reconciliation
// const BillReconciliationSchema = z.object({
//   billId: z.string(),
//   billAmount: z.number(),
//   lineItemsTotal: z.number(),
//   discrepancy: z.number()
// });

// // 7. Data Consistency Issue
// const DataConsistencyIssueSchema = z.object({
//   billId: z.string(),
//   issue: z.string()
// });

// /**
//  * --------------------------------------------------------
//  * 1a. Vendor Spending Trends Query
//  * --------------------------------------------------------
//  */
// export const vendorSpendingTrends = zQuery({
//   args: zodToValidator(
//     LineItemFilterSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(VendorStatsSchema),
//   /**
//    * Aggregates line items by vendor over an optional date range.
//    * Returns total spending and transaction count per vendor.
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     // We build our filter expression in one go:
//     const lineItems = await ctx.db
//       .query('lineItems')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('post_date'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('post_date'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     // Aggregate spending and count per vendor
//     const vendorMap = new Map<
//       string,
//       { totalSpending: number; txnCount: number }
//     >();
//     for (const item of lineItems) {
//       const vendor = item.vendor_id;
//       if (!vendorMap.has(vendor)) {
//         vendorMap.set(vendor, { totalSpending: 0, txnCount: 0 });
//       }
//       const stats = vendorMap.get(vendor)!;
//       stats.totalSpending += item.amount;
//       stats.txnCount += 1;
//     }

//     return Array.from(vendorMap.entries()).map(([vendor_id, stats]) => ({
//       vendor_id,
//       ...stats
//     }));
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 1b. New Vendor Detection Query
//  * --------------------------------------------------------
//  */
// export const newVendors = zQuery({
//   args: zodToValidator(z.object({ thresholdDate: z.number() })),
//   output: z.array(NewVendorSchema),
//   /**
//    * Identifies vendors whose first recorded transaction is on or after
//    * a specified threshold date.
//    */
//   handler: async (ctx, { thresholdDate }) => {
//     const lineItems = await ctx.db.query('lineItems').collect();

//     // Track earliest post_date per vendor
//     const vendorFirstDate = new Map<string, number>();
//     for (const item of lineItems) {
//       const { vendor_id, post_date } = item;
//       const existing = vendorFirstDate.get(vendor_id);
//       if (existing == null || post_date < existing) {
//         vendorFirstDate.set(vendor_id, post_date);
//       }
//     }

//     // Filter to those >= thresholdDate
//     const result = Array.from(vendorFirstDate.entries())
//       .filter(([, firstDate]) => firstDate >= thresholdDate)
//       .map(([vendor_id, firstDate]) => ({ vendor_id, firstDate }));

//     return result;
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 2. Expense Category Trends Query
//  * --------------------------------------------------------
//  */
// export const expenseCategoryTrends = zQuery({
//   args: zodToValidator(
//     LineItemFilterSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(CategoryStatsSchema),
//   /**
//    * Aggregates spending by spending_category over an optional date range.
//    * Items without spending_category => "Uncategorized".
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     const lineItems = await ctx.db
//       .query('lineItems')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('post_date'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('post_date'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     const categoryMap = new Map<
//       string,
//       { totalSpending: number; txnCount: number }
//     >();
//     for (const item of lineItems) {
//       const cat = item.spending_category ?? 'Uncategorized';
//       if (!categoryMap.has(cat)) {
//         categoryMap.set(cat, { totalSpending: 0, txnCount: 0 });
//       }
//       const stats = categoryMap.get(cat)!;
//       stats.totalSpending += item.amount;
//       stats.txnCount += 1;
//     }

//     return Array.from(categoryMap.entries()).map(([category, stats]) => ({
//       category,
//       ...stats
//     }));
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 3. Large Transaction Analysis Query
//  * --------------------------------------------------------
//  */
// export const largeTransactionAnalysis = zQuery({
//   args: zodToValidator(
//     z.object({
//       threshold: z.number(),
//       startDate: z.number().optional(),
//       endDate: z.number().optional()
//     })
//   ),
//   output: z.array(LineItemSchema),
//   /**
//    * Retrieves line items with amount >= threshold within an optional date range.
//    */
//   handler: async (ctx, { threshold, startDate, endDate }) => {
//     return ctx.db
//       .query('lineItems')
//       .filter((q) => {
//         let expr = q.gte(q.field('amount'), threshold);
//         if (startDate != null) {
//           expr = expr.gte(q.field('post_date'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('post_date'), endDate);
//         }
//         return expr;
//       })
//       .collect();
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 4. Overall Spending Trends Query
//  * --------------------------------------------------------
//  */
// export const overallSpendingTrends = zQuery({
//   args: zodToValidator(
//     LineItemFilterSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(DailySpendingStatsSchema),
//   /**
//    * Aggregates overall spending by day (YYYY-MM-DD).
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     const lineItems = await ctx.db
//       .query('lineItems')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('post_date'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('post_date'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     // Group by day
//     const dayMap = new Map<
//       string,
//       { totalSpending: number; txnCount: number }
//     >();
//     for (const item of lineItems) {
//       const dateStr = new Date(item.post_date).toISOString().split('T')[0];
//       if (!dayMap.has(dateStr)) {
//         dayMap.set(dateStr, { totalSpending: 0, txnCount: 0 });
//       }
//       const stats = dayMap.get(dateStr)!;
//       stats.totalSpending += item.amount;
//       stats.txnCount += 1;
//     }

//     return Array.from(dayMap.entries()).map(([date, stats]) => ({
//       date,
//       ...stats
//     }));
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 5a. Bill Status Analysis Query
//  * --------------------------------------------------------
//  */
// export const billStatusAnalysis = zQuery({
//   args: zodToValidator(
//     BillQueryFiltersSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(BillStatusStatsSchema),
//   /**
//    * Aggregates bills by status over an optional date range,
//    * returning total amount and count of bills in each status.
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     const bills = await ctx.db
//       .query('bills')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('billDate'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('billDate'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     const statusMap = new Map<
//       string,
//       { totalBillAmount: number; billCount: number }
//     >();
//     for (const bill of bills) {
//       // Some bills might not have a status. Use "Unknown" as fallback
//       const status = bill.billStatus ?? 'Unknown';
//       if (!statusMap.has(status)) {
//         statusMap.set(status, { totalBillAmount: 0, billCount: 0 });
//       }
//       const stats = statusMap.get(status)!;
//       stats.totalBillAmount += bill.amount; // 'amount' must exist in your DB schema for "bills"
//       stats.billCount += 1;
//     }

//     return Array.from(statusMap.entries()).map(([billStatus, stats]) => ({
//       billStatus,
//       ...stats
//     }));
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 5b. Bill Reconciliation Check Query
//  * --------------------------------------------------------
//  */
// export const billReconciliationCheck = zQuery({
//   args: zodToValidator(z.object({ billId: zid('bills') })),
//   output: BillReconciliationSchema.nullable(),
//   /**
//    * Compares a bill's total amount with the sum of its associated line items,
//    * returning the discrepancy if any.
//    */
//   handler: async (ctx, { billId }) => {
//     // Tells TS it's an ID of "bills"
//     const bill = await ctx.db.get(billId);
//     if (!bill) {
//       return null;
//     }

//     // lineItems that reference this bill's _id
//     const lineItems = await ctx.db
//       .query('lineItems')
//       .filter((q) => q.eq(q.field('bill_id'), bill._id))
//       .collect();

//     const lineTotal = lineItems.reduce((sum, item) => sum + item.amount, 0);
//     return {
//       billId: bill._id,
//       billAmount: bill.amount,
//       lineItemsTotal: lineTotal,
//       discrepancy: bill.amount - lineTotal
//     };
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 6. Temporal Frequency Analysis Query
//  * --------------------------------------------------------
//  */
// export const temporalFrequencyAnalysis = zQuery({
//   args: zodToValidator(
//     BillQueryFiltersSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(DailySpendingStatsSchema),
//   /**
//    * Aggregates bills by day => frequency of bills & total amounts,
//    * mapped to a 'DailySpendingStats' style object.
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     const bills = await ctx.db
//       .query('bills')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('billDate'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('billDate'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     // Group by day
//     const dayMap = new Map<
//       string,
//       { billCount: number; totalBillAmount: number }
//     >();
//     for (const bill of bills) {
//       const dateStr = new Date(bill.billDate).toISOString().split('T')[0];
//       if (!dayMap.has(dateStr)) {
//         dayMap.set(dateStr, { billCount: 0, totalBillAmount: 0 });
//       }
//       const stats = dayMap.get(dateStr)!;
//       stats.billCount += 1;
//       stats.totalBillAmount += bill.amount;
//     }

//     // Map to the same shape as DailySpendingStats
//     return Array.from(dayMap.entries()).map(([date, stats]) => ({
//       date,
//       totalSpending: stats.totalBillAmount,
//       txnCount: stats.billCount
//     }));
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 7. Cross-dimensional Analysis Query
//  * --------------------------------------------------------
//  */
// export const crossDimensionalAnalysis = zQuery({
//   args: zodToValidator(
//     LineItemFilterSchema.pick({ startDate: true, endDate: true })
//   ),
//   output: z.array(
//     z.object({
//       vendor_id: z.string(),
//       category: z.string(),
//       totalSpending: z.number(),
//       txnCount: z.number()
//     })
//   ),
//   /**
//    * Cross-analysis between vendors & spending categories
//    * => groups by (vendor_id, spending_category).
//    */
//   handler: async (ctx, { startDate, endDate }) => {
//     const lineItems = await ctx.db
//       .query('lineItems')
//       .filter((q) => {
//         let expr = q;
//         if (startDate != null) {
//           expr = expr.gte(q.field('post_date'), startDate);
//         }
//         if (endDate != null) {
//           expr = expr.lte(q.field('post_date'), endDate);
//         }
//         return expr;
//       })
//       .collect();

//     const crossMap = new Map<
//       string,
//       { totalSpending: number; txnCount: number }
//     >();
//     for (const item of lineItems) {
//       const categoryKey = item.spending_category ?? 'Uncategorized';
//       const key = `${item.vendor_id}||${categoryKey}`;
//       if (!crossMap.has(key)) {
//         crossMap.set(key, { totalSpending: 0, txnCount: 0 });
//       }
//       const stats = crossMap.get(key)!;
//       stats.totalSpending += item.amount;
//       stats.txnCount += 1;
//     }

//     return Array.from(crossMap.entries()).map(([key, stats]) => {
//       const [vendor_id, category] = key.split('||');
//       return {
//         vendor_id,
//         category,
//         totalSpending: stats.totalSpending,
//         txnCount: stats.txnCount
//       };
//     });
//   }
// });

// /**
//  * --------------------------------------------------------
//  * 8. Data Consistency Check Query
//  * --------------------------------------------------------
//  */
// export const dataConsistencyCheck = zQuery({
//   args: zodToValidator(z.object({})), // No inputs
//   output: z.array(DataConsistencyIssueSchema),
//   /**
//    * Checks for bills with no associated line items.
//    */
//   handler: async (ctx) => {
//     const bills = await ctx.db.query('bills').collect();
//     const issues: { billId: string; issue: string }[] = [];

//     for (const bill of bills) {
//       const lineItems = await ctx.db
//         .query('lineItems')
//         .filter((q) => q.eq(q.field('bill_id'), bill._id))
//         .collect();

//       if (lineItems.length === 0) {
//         issues.push({
//           billId: bill._id,
//           issue: 'No line items found for this bill'
//         });
//       }
//     }
//     return issues;
//   }
// });
