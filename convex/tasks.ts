import { z<PERSON><PERSON>y, zMutation } from './functions';
import { z } from 'zod';
import {
  Task,
  CreateTaskInput,
  CreateMultipleTasksInput,
  UpdateTaskInput,
  BulkTaskUpdateSchema,
  TaskFilterSchema,
  PaginationSchema
} from '../zod/tasks-schema';
import { zid } from 'convex-helpers/server/zod';
import { MutationCtx, QueryCtx } from './_generated/server';
import { Id, DataModel, Doc } from './_generated/dataModel';
import { QueryInitializer } from 'convex/server';
import { ConvexError } from "convex/values";
import { api } from './_generated/api';
import { extractFullMentionsFromText } from './utils/mentions';
import { paginationOptsValidator } from 'convex/server'; // Import for pagination
import { v } from 'convex/values'; // Import v

// Define a simple type for our filter functions
// Using 'any' here because Convex query builders are complex types
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type FilterFunction = (q: any) => boolean;

/**
 * Creates one or multiple tasks
 * Accepts either a single task object or an array of task objects
 * Returns an array of created task IDs
 */
export const createTasks = zMutation({
  args: {
    input: z.union([CreateTaskInput, CreateMultipleTasksInput])
  },
  output: z.object({
    ids: z.array(z.string())
  }),
  handler: async (ctx, args) => {
    const now = Date.now();
    const input = args.input;

    if (Array.isArray(input)) {
      // Bulk insert scenario
      const insertedIds = [];
      for (const task of input) {
        // Ensure task has required name field
        if (!task.name) {
          throw new Error('Task name is required');
        }

        const taskData = {
          ...task,
          updated_at: now
        };

        const newId = await ctx.db.insert('tasks', taskData);
        insertedIds.push(newId);
      }
      return { ids: insertedIds };
    } else {
      // Single task insert
      // Ensure task has required name field
      const singleTaskInput = input as z.infer<typeof CreateTaskInput>; // Explicit cast

      if (!singleTaskInput.name) {
        throw new Error('Task name is required');
      }

      // Extract full mentions (IDs and display names) from the name
      const fullMentions = extractFullMentionsFromText(singleTaskInput.name);

      // Create a "Mentions" section for the description
      let mentionsSection = '';
      if (fullMentions.length > 0) {
        mentionsSection = '\n\n**Mentions**\n' +
          fullMentions.map(mention => `- @${mention.displayName}`).join('\n');
      }

      // Add the mentions section to the description
      const description = singleTaskInput.description
        ? singleTaskInput.description + mentionsSection
        : mentionsSection.length > 0
          ? mentionsSection
          : undefined;

      // Create the task data, including new DCI fields
      const taskData = {
        ...singleTaskInput, // Use the explicitly typed variable
        description, // Potentially updated description with mentions
        updated_at: now
      };

      const taskId = await ctx.db.insert('tasks', taskData);

      return { ids: [taskId] };
    }
  }
});

import { query } from './_generated/server'; // Use standard query

/**
 * Search tasks by name using the search index.
 * Returns a limited list of matching tasks.
 */
export const searchTasksByName = zQuery({ // Use zQuery
  args: {
    searchQuery: z.string(), // Use z.string() for args definition
    limit: z.number().optional() // Use z.number().optional() for args definition
  },
  // Define output type using the imported Zod Task schema
  output: z.array(Task),
  handler: async (ctx: QueryCtx, args) => { // Keep types for ctx and args
    const { searchQuery, limit = 10 } = args;

    if (!searchQuery) {
      return []; // Return empty if search query is empty
    }

    const tasks = await ctx.db
      .query('tasks')
      .withSearchIndex('search_name', (q) =>
        q.search('name', searchQuery)
      )
      .take(limit);

    // Validate results against the Task schema before returning
    return tasks.map((task: Doc<'tasks'>) => Task.parse(task));
  },
});


/**
 * Retrieves a single task by its ID
 * Returns the task or null if not found
 */
export const getTask = zQuery({
  args: {
    taskId: zid('tasks')
  },
  // Allow null in the output type
  output: z.union([Task, z.null()]),
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    if (!task) {
      // Return null instead of throwing an error
      return null;
    }

    // Validate and parse the task using the Zod schema
    // Use safeParse to handle potential validation errors gracefully, although less likely here
    const parsed = Task.safeParse(task);
    if (!parsed.success) {
        // Log error or handle appropriately if parsing fails
        console.error("Failed to parse task:", parsed.error);
        return null; // Or throw a different error if parsing failure is critical
    }
    return parsed.data;
  }
});

/**
 * Lists tasks with optional filtering and pagination
 * Returns an array of tasks and a continuation token for pagination
 */
export const listTasks = zQuery({
  args: {
    filter: TaskFilterSchema,
    pagination: PaginationSchema
  },
  output: z.object({
    tasks: z.array(Task),
    continuation: z.string().nullable().optional()
  }),
  handler: async (ctx, args) => {
    const {
      filter,
      pagination = {
        sortBy: '_creationTime' as const,
        sortDirection: 'desc' as const,
        // limit: 10 // Removed default limit
      }
    } = args;

    // Step 1: Initialize the query with the appropriate index
    let query;
    if (filter?.project_id) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_project', (q) => q.eq('project_id', filter.project_id));
    } else if (filter?.decision_id) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_decision', (q) =>
          q.eq('decision_id', filter.decision_id)
        );
    } else if (filter?.status) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_status', (q) => q.eq('status', filter.status));
    // Add index usage for 'driver' if an index 'by_driver' exists
    // else if (filter?.driver) {
    //   query = ctx.db
    //     .query('tasks')
    //     .withIndex('by_driver', (q) => q.eq('driver', filter.driver));
    // }
    } else if (filter?.importance) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_importance', (q) => q.eq('importance', filter.importance));
    } else {
      // No index needed, use the base query
      query = ctx.db.query('tasks');
    }

    // Step 2: Apply additional filters as a single operation
    const additionalFilters: FilterFunction[] = [];

    if (filter?.name) {
      additionalFilters.push((q) => q.eq(q.field('name'), filter.name));
    }

    // Add filter for driver if provided (Note: No index used currently)
    if (filter?.driver) {
       additionalFilters.push((q) => q.eq(q.field('driver'), filter.driver));
    }
    // Filtering by array fields (approver, contributors, informed) is omitted
    // as it requires more complex logic (e.g., checking if array contains value)
    // which is not straightforward with basic Convex query filters without specific indexes/functions.

    if (filter?.due_date_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('due_date'), filter.due_date_before)
      );
    }

    if (filter?.due_date_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('due_date'), filter.due_date_after)
      );
    }

    // Add updated_at filters
    if (filter?.updated_at_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('updated_at'), filter.updated_at_before)
      );
    }

    if (filter?.updated_at_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('updated_at'), filter.updated_at_after)
      );
    }

    // Apply the filters if there are any
    if (additionalFilters.length > 0) {
      query = query.filter((q) => {
        if (additionalFilters.length === 1) {
          return additionalFilters[0](q);
        }
        return q.and(...additionalFilters.map((filterFn) => filterFn(q)));
      });
    }

    // Step 3: Apply sorting and pagination in a single chain
    const sortField = pagination?.sortBy || 'updated_at'; // Default sort by updated_at now
    const sortDirection = pagination?.sortDirection || 'desc';

    // Execute the paginated query
    const paginationResult = await query.order(sortDirection).paginate({
      cursor: pagination?.cursor || null,
      // Use provided limit or let Convex handle default (which might be larger or unlimited depending on context)
      numItems: pagination?.limit ?? 1000 // Using a large number as a proxy for 'all' for now, adjust if Convex has better way
    });

    // Validate the structure of each task to ensure it matches our schema
    const validatedTasks = paginationResult.page.map((task) =>
      Task.parse(task)
    );

    // Return the paginated result with the correct structure
    return {
      tasks: validatedTasks,
      continuation: paginationResult.continueCursor // Use the correct property 'continueCursor'
    };
  }
});

/**
 * Counts tasks with optional filters
 * Returns the total count of matching tasks
 */
export const countTasks = zQuery({
  args: {
    filter: TaskFilterSchema
  },
  output: z.number(),
  handler: async (ctx, args) => {
    const { filter } = args;

    // Step 1: Initialize the query with the appropriate index
    let query;
    if (filter?.project_id) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_project', (q) => q.eq('project_id', filter.project_id));
    } else if (filter?.status) {
      query = ctx.db
        .query('tasks')
        .withIndex('by_status', (q) => q.eq('status', filter.status));
    // Add index usage for 'driver' if an index 'by_driver' exists
    // else if (filter?.driver) {
    //   query = ctx.db
    //     .query('tasks')
    //     .withIndex('by_driver', (q) => q.eq('driver', filter.driver));
    // }
    } else if (filter?.importance) { // Add missing else if for importance
      query = ctx.db
        .query('tasks')
        .withIndex('by_importance', (q) => q.eq('importance', filter.importance));
    } else {
      // No index needed, use the base query
      query = ctx.db.query('tasks');
    }

    // Step 2: Apply additional filters as a single operation
    const additionalFilters: FilterFunction[] = [];

    if (filter?.name) {
      additionalFilters.push((q) => q.eq(q.field('name'), filter.name));
    }

    // Add filter for driver if provided (Note: No index used currently)
    if (filter?.driver) {
       additionalFilters.push((q) => q.eq(q.field('driver'), filter.driver));
    }
    // Filtering by array fields (approver, contributors, informed) is omitted here as well.

    if (filter?.due_date_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('due_date'), filter.due_date_before)
      );
    }

    if (filter?.due_date_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('due_date'), filter.due_date_after)
      );
    }

    // Add updated_at filters
    if (filter?.updated_at_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('updated_at'), filter.updated_at_before)
      );
    }

    if (filter?.updated_at_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('updated_at'), filter.updated_at_after)
      );
    }

    // Apply the filters if there are any
    if (additionalFilters.length > 0) {
      query = query.filter((q) => {
        if (additionalFilters.length === 1) {
          return additionalFilters[0](q);
        }
        return q.and(...additionalFilters.map((filterFn) => filterFn(q)));
      });
    }

    // Execute the query and return the count
    const tasks = await query.collect();
    return tasks.length;
  }
});

// Helper function for single task update
async function updateSingleTask(
  ctx: MutationCtx,
  taskId: Id<'tasks'>,
  updateFields: Partial<Doc<'tasks'>>
) {
  const task = await ctx.db.get(taskId);
  if (!task) {
    throw new Error(`Task with ID ${taskId} not found`);
  }

  // Create updates object with the correct type
  const updates: Partial<Doc<'tasks'>> = {
    ...updateFields,
    updated_at: Date.now()
  };

  // If status is being changed to completed and completed_at isn't explicitly set,
  // automatically set completed_at to now
  if (updateFields.status === 'completed' && !updateFields.completed_at) {
    updates.completed_at = Date.now();
  }

  await ctx.db.patch(taskId, updates);
  return taskId;
}

// Helper function for bulk task updates
async function updateMultipleTasks(
  ctx: MutationCtx,
  taskIds: Id<'tasks'>[],
  updateFields: Partial<Doc<'tasks'>>
) {
  const updatedIds = [];
  const failedIds = [];

  for (const id of taskIds) {
    try {
      await updateSingleTask(ctx, id, updateFields);
      updatedIds.push(id);
    } catch (error) {
      failedIds.push({
        id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  return { updatedIds, failedIds };
}

/**
 * Updates one or multiple tasks
 * Returns the IDs of updated tasks
 */
export const updateTasks = zMutation({
  args: {
    updates: BulkTaskUpdateSchema
  },
  output: z.object({
    ids: z.array(z.string()),
    failedIds: z
      .array(
        z.object({
          id: z.string(),
          error: z.string()
        })
      )
      .optional()
  }),
  handler: async (ctx, args) => {
    const { id, ids, updates } = args.updates;

    // Handle bulk update
    if (ids && ids.length > 0) {
      const { updatedIds, failedIds } = await updateMultipleTasks(
        ctx,
        ids,
        updates
      );
      return {
        ids: updatedIds,
        failedIds: failedIds.length > 0 ? failedIds : undefined
      };
    }
    // Handle single task update
    else if (id) {
      try {
        const updatedId = await updateSingleTask(ctx, id, updates);
        return { ids: [updatedId] };
      } catch (error) {
        return {
          ids: [],
          failedIds: [
            {
              id,
              error: error instanceof Error ? error.message : String(error)
            }
          ]
        };
      }
    } else {
      throw new Error(
        "Invalid arguments: must provide either 'id' or 'ids' with 'updates'"
      );
    }
  }
});

/**
 * Marks a task as complete
 * Convenience function that sets status to completed and completed_at to now
 */
export const completeTask = zMutation({
  args: {
    taskId: zid('tasks'),
    completedNotes: z.string().optional()
  },
  output: z.object({
    success: z.boolean(),
    id: z.string()
  }),
  handler: async (ctx, args) => {
    const now = Date.now();

    try {
      const task = await ctx.db.get(args.taskId);
      if (!task) {
        throw new Error(`Task with ID ${args.taskId} not found`);
      }

      await ctx.db.patch(args.taskId, {
        status: 'completed',
        completed_at: now,
        updated_at: now,
        completed_notes: args.completedNotes
      });

      return {
        success: true,
        id: args.taskId
      };
    } catch (error) {
      return {
        success: false,
        id: args.taskId
      };
    }
  }
});

/**
 * Deletes one or multiple tasks
 * Accepts an array of task IDs to delete
 * Returns an array of deleted task IDs
 */
export const deleteTasks = zMutation({
  args: {
    taskIds: z.array(zid('tasks'))
  },
  output: z.object({
    ids: z.array(z.string()),
    failedIds: z
      .array(
        z.object({
          id: z.string(),
          error: z.string()
        })
      )
      .optional()
  }),
  handler: async (ctx, args) => {
    const deletedIds: string[] = [];
    const failedIds: { id: string; error: string }[] = [];

    for (const taskId of args.taskIds) {
      try {
        // Check if task exists
        const task = await ctx.db.get(taskId);
        if (!task) {
          failedIds.push({
            id: taskId,
            error: 'Task not found'
          });
          continue;
        }

        // Delete the task
        await ctx.db.delete(taskId);
        deletedIds.push(taskId);
      } catch (error) {
        failedIds.push({
          id: taskId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      ids: deletedIds,
      failedIds: failedIds.length > 0 ? failedIds : undefined
    };
  }
});

/**
 * Gets tasks for dashboard with basic filtering
 * Optimized query that returns only essential fields
 */
export const getDashboardTasks = zQuery({
  args: {
    userId: zid('users').optional(),
    status: z.array(z.string()).optional(),
    limit: z.number().optional()
  },
  output: z.array(Task),
  handler: async (ctx, args) => {
    const { userId, status, limit = 10 } = args;

    // Step 1: Initialize the query (Relying on Convex type inference)
    let query = ctx.db.query('tasks');

    // Step 2: Apply status filters conditionally
    if (status && status.length > 0) {
      // Apply filter directly to the query variable
      query = query.filter((q) => {
        // Using 'any' temporarily to bypass complex filter type issues
        const filterQuery: any = q;
        if (status.length === 1) {
          return filterQuery.eq(filterQuery.field('status'), status[0]);
        } else {
          const statusConditions = status.map((s) =>
            filterQuery.eq(filterQuery.field('status'), s)
          );
          // Using 'any' temporarily for the spread argument
          return (filterQuery.or as any)(...statusConditions);
        }
      });
    }
    // If no status filter, 'query' remains the base query

    // Step 3: Apply sorting (Relying on Convex type inference)
    const orderedQuery = query.order('asc');

    // Step 4: Execute the query
    // Keep the explicit type for the 'tasks' array for clarity
    let tasks: Doc<'tasks'>[] = await orderedQuery.collect();

    // If filtering by a specific DCI role (e.g., driver, contributor) is needed based on userId,
    // it should be added here. Currently, it fetches based on status.

    // Apply limit
    tasks = tasks.slice(0, limit);

    // Validate and transform the tasks to ensure they match our schema
    return tasks.map((task) => Task.parse(task));
  }
});




