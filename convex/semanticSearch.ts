import { query, QueryCtx } from "./_generated/server"; // Import QueryCtx
import { v } from "convex/values";
import { trieve, isTrieveConfigured } from "./trieve/trieve"; // Import Trieve SDK instance and helper
import { ConvexError } from "convex/values";
import { Id } from "./_generated/dataModel";
// Import SearchChunksReqPayload for payload, SearchResponseBody for return type
import { SearchChunksReqPayload, SearchResponseBody } from "trieve-ts-sdk"; 

// Define the expected input structure for the search query
const searchParamsValidator = v.object({
  orgId: v.string(),          // Organization context (to filter results to this tenant)
  projectId: v.optional(v.union(v.id('projects'), v.null())), // (Optional) Project context to bias results
  query: v.string(),          // User search query string
  filters: v.optional(v.object({ 
    type: v.optional(v.string()) // (Optional) filter by document type, e.g., "KNOWLEDGE_BASE"
  })),
  searchType: v.optional(v.union( // Optional search type, default to hybrid
    v.literal("semantic"), 
    v.literal("fulltext"), 
    v.literal("hybrid")
  )),
  page: v.optional(v.number()), // Optional pagination
  pageSize: v.optional(v.number()), // Optional page size
});

// Define an empty search response for the case where Trieve is not configured
// Include the 'id' field, typically an empty string or null in this case
const emptySearchResponse: SearchResponseBody = { id: "", chunks: [], total_pages: 0 };

export default query({
  args: { params: searchParamsValidator },
  handler: async (ctx: QueryCtx, args): Promise<SearchResponseBody> => { // Add explicit return type
    if (!isTrieveConfigured() || !trieve) {
      console.warn("Trieve is not configured. Skipping search.");
      // Return an empty result matching the expected structure
      return emptySearchResponse; 
    }

    const { orgId, projectId, query: userQuery, filters, searchType = "hybrid", page = 1, pageSize = 10 } = args.params;

    // Build filter clauses - Use any[] as specific SDK filter type isn't easily imported/found
    const mustFilters: any[] = [
      { field: "tag_set", match: [`Org:${orgId}`] } // Restrict to this Org's data
    ];
    if (filters?.type) {
      mustFilters.push({ field: "tag_set", match: [`Type:${filters.type}`] });
      // e.g., filters.type = "MEETING_NOTES"
    }

    const shouldFilters: any[] = [];
    if (projectId) {
      // Boost chunks tagged with the given project
      shouldFilters.push({ field: "tag_set", match: [`Project:${projectId}`] });
    }

    // Construct search payload for the SDK using SearchChunksReqPayload type
    const searchPayload: SearchChunksReqPayload = {
      query: userQuery,
      search_type: searchType,
      filters: {
        must: mustFilters.length > 0 ? mustFilters : undefined,
        should: shouldFilters.length > 0 ? shouldFilters : undefined,
      },
      sort_options: {
        // Use a gentle recency bias (1.0) to slightly favor newer content
        recency_bias: 1.0,
        // Enable using weights in the result set for sorting
        use_weights: true,
        // Add tag weights to boost project-specific results if a project is selected
        tag_weights: projectId ? { [`Project:${projectId}`]: 2.0 } : undefined
      },
      highlight_options: {
        // Enable result highlighting
        highlight_results: true,
        // Increase max highlights per chunk for better context
        highlight_max_num: 5,
        // Allow longer highlights to capture more context
        highlight_max_length: 15,
        // Add some window context around highlights
        highlight_window: 3,
        // Lower threshold slightly to catch more relevant matches
        highlight_threshold: 0.7,
        // Use custom tags for highlighting that work well with React
        pre_tag: '<mark class="bg-yellow-200">',
        post_tag: '</mark>',
        // Include more delimiters for better sentence splitting
        highlight_delimiters: [".", "!", "?", ",", ";", ":", "\n"]
      },
      page,
      page_size: pageSize
    };

    try {
      console.log("Performing Trieve search with payload:", JSON.stringify(searchPayload, null, 2));
      // Type the result as SearchResponseBody
      // Use the 'search' method
      const results: SearchResponseBody = await trieve.search(searchPayload); 
      // Access results via 'chunks' property based on SearchResponseBody type
      console.log(`Trieve search returned ${results.chunks.length} chunks.`); 
      return results; 
    } catch (error: unknown) {
      console.error("Error performing Trieve search:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new ConvexError(`Trieve search failed: ${errorMessage}`);
      // Or return an empty/error structure matching SearchResponseBody:
      // return { ...emptySearchResponse, error: errorMessage }; 
    }
  },
});
