import { TrieveSDK } from "trieve-ts-sdk";

// Configure Trieve client using environment variables
const TRIEVE_API_KEY = process.env.TRIEVE_API_KEY;
const TRIEVE_DATASET_ID = process.env.TRIEVE_DATASET_ID;

if (!TRIEVE_API_KEY) {
  console.warn('Missing TRIEVE_API_KEY environment variable - Trieve SDK initialization skipped.');
}
if (!TRIEVE_DATASET_ID) {
  console.warn('Missing TRIEVE_DATASET_ID environment variable - Trieve SDK initialization skipped.');
}

// Initialize the SDK only if credentials are provided
// Functions using 'trieve' should handle the case where it might be null.
export const trieve = (TRIEVE_API_KEY && TRIEVE_DATASET_ID) ? new TrieveSDK({
  apiKey: TRIEVE_API_KEY,
  datasetId: TRIEVE_DATASET_ID,
  // Optionally configure the base URL if not using the default Trieve cloud
  // baseUrl: process.env.TRIEVE_API_URL || 'https://api.trieve.ai', 
}) : null;

// Helper function to check if <PERSON><PERSON> is configured
export function isTrieveConfigured(): boolean {
  return !!trieve;
}
