import { mutation } from "../_generated/server";
import { v } from "convex/values";
import { trieve, isTrieveConfigured } from "./trieve"; // Import Trieve SDK instance and helper
import { ConvexError } from "convex/values";
import { Id } from "../_generated/dataModel";

// Mutation to delete a document's data (chunk group and chunks) from Trieve
export default mutation({
  args: { 
    docId: v.id('files') // Expecting the FOJO file ID, which is used as the group tracking_id
  },
  handler: async (ctx, args) => {
    if (!isTrieveConfigured() || !trieve) {
      console.warn("Trieve is not configured. Skipping deletion.");
      return { status: "skipped", reason: "not_configured" };
    }

    const { docId } = args;

    try {
      console.log(`Attempting to delete Trieve group and chunks for document ID: ${docId}`);
      
      // Get dataset ID from environment variable
      const trDataset = process.env.TRIEVE_DATASET_ID;
      if (!trDataset) {
        // This should have been caught by isTrieveConfigured, but double-check
        throw new ConvexError("Trieve Dataset ID is not configured.");
      }

      // Delete the chunk group identified by this document's tracking_id (and all its chunks)
      // Providing both snake_case and camelCase due to conflicting TS errors
      await trieve.deleteGroupByTrackingId({
        tracking_id: docId, 
        trackingId: docId, // Add camelCase version as well
        trDataset: trDataset, 
        deleteChunks: true 
      });
      console.log(`Successfully deleted Trieve group and chunks for document ID: ${docId}`);
      return { status: "deleted", documentId: docId };
    } catch (error: unknown) { // Explicitly type error as unknown
      // Log the error, but potentially don't throw if the group not existing is acceptable
      console.error(`Error deleting Trieve group for document ID ${docId}:`, error);
      
      // Type guard to check if error is an instance of Error
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Check if the error indicates the group wasn't found (which might be okay)
      if (errorMessage.includes('404') || errorMessage.toLowerCase().includes('not found')) {
         console.warn(`Trieve group for ${docId} not found, likely already deleted or never existed.`);
         return { status: "not_found", documentId: docId };
      }
      // For other errors, re-throw or return an error status
      throw new ConvexError(`Trieve group deletion failed for ${docId}: ${errorMessage}`);
      // return { status: "error", message: errorMessage };
    }
  },
});
