import { v } from 'convex/values';
import { zMutation } from '../functions';
import { ConvexError } from 'convex/values';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import {
  ClientInputSchema,
  ClientUpdateSchema,
  ClientAssignmentInputSchema,
  ClientAssignmentUpdateSchema,
  BulkClientOperationSchema,
  BulkAssignmentCreateSchema,
  BulkAssignmentUpdateSchema,
} from '../../zod/clients-schema';

// Create a new client
export const createClient = zMutation({
  args: ClientInputSchema,
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const clientData = {
      ...args,
      updated_at: now,
    } as any; // Type assertion to work around complex type validation

    const clientId = await ctx.db.insert('clients', clientData);
    return await ctx.db.get(clientId);
  },
});

// Update an existing client
export const updateClient = zMutation({
  args: z.object({
    id: zid('clients'),
    updates: ClientUpdateSchema,
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    const updateData = {
      ...args.updates,
      updated_at: now,
    };

    await ctx.db.patch(args.id, updateData);
    return await ctx.db.get(args.id);
  },
});

// Update client status specifically
export const updateClientStatus = zMutation({
  args: z.object({
    id: zid('clients'),
    status: z.enum(['active', 'on_hold', 'prospective', 'former']),
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    await ctx.db.patch(args.id, {
      client_status: args.status,
      updated_at: now,
    });

    return await ctx.db.get(args.id);
  },
});

// Update client tier
export const updateClientTier = zMutation({
  args: z.object({
    id: zid('clients'),
    tier: z.enum(['platinum', 'gold', 'silver', 'bronze', 'none']),
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    await ctx.db.patch(args.id, {
      client_tier: args.tier,
      updated_at: now,
    });

    return await ctx.db.get(args.id);
  },
});

// Update client last contact date
export const updateClientLastContact = zMutation({
  args: z.object({
    id: zid('clients'),
    lastContactDate: z.number().optional(),
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    await ctx.db.patch(args.id, {
      client_last_contact: args.lastContactDate || now,
      updated_at: now,
    });

    return await ctx.db.get(args.id);
  },
});

// Soft delete a client (set status to former)
export const deleteClient = zMutation({
  args: z.object({
    id: zid('clients'),
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    await ctx.db.patch(args.id, {
      client_status: 'former',
      updated_at: now,
    });

    return await ctx.db.get(args.id);
  },
});

// Hard delete a client (permanently remove)
export const hardDeleteClient = zMutation({
  args: z.object({
    id: zid('clients'),
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new ConvexError('Client not found');
    }

    // First delete all assignments for this client
    const assignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_id', (q) => q.eq('client_id', args.id))
      .collect();

    for (const assignment of assignments) {
      await ctx.db.delete(assignment._id);
    }

    // Then delete the client
    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Client Assignment Mutations

// Assign a user to a client
export const assignUserToClient = zMutation({
  args: ClientAssignmentInputSchema,
  handler: async (ctx, args) => {
    // Check if client exists
    const client = await ctx.db.get(args.client_id);
    if (!client) {
      throw new ConvexError('Client not found');
    }

    // Check if user exists
    const user = await ctx.db.get(args.user_id);
    if (!user) {
      throw new ConvexError('User not found');
    }

    // Check if assignment already exists
    const existingAssignment = await ctx.db
      .query('client_assignments')
      .filter((q) => 
        q.and(
          q.eq(q.field('client_id'), args.client_id),
          q.eq(q.field('user_id'), args.user_id)
        )
      )
      .unique();

    if (existingAssignment) {
      throw new ConvexError('User is already assigned to this client');
    }

    const now = Date.now();
    const assignmentData = {
      ...args,
      updated_at: now,
    } as any; // Type assertion to work around complex type validation

    const assignmentId = await ctx.db.insert('client_assignments', assignmentData);
    return await ctx.db.get(assignmentId);
  },
});

// Remove a user assignment from a client
export const removeClientAssignment = zMutation({
  args: z.object({
    assignmentId: zid('client_assignments'),
  }),
  handler: async (ctx, args) => {
    const assignment = await ctx.db.get(args.assignmentId);
    if (!assignment) {
      throw new ConvexError('Assignment not found');
    }

    await ctx.db.delete(args.assignmentId);
    return { success: true };
  },
});

// Remove user from client by client_id and user_id
export const removeUserFromClientByIds = zMutation({
  args: z.object({
    clientId: zid('clients'),
    userId: zid('users'),
  }),
  handler: async (ctx, args) => {
    const assignment = await ctx.db
      .query('client_assignments')
      .filter((q) => 
        q.and(
          q.eq(q.field('client_id'), args.clientId),
          q.eq(q.field('user_id'), args.userId)
        )
      )
      .unique();

    if (!assignment) {
      throw new ConvexError('Assignment not found');
    }

    await ctx.db.delete(assignment._id);
    return { success: true };
  },
});

// Update a client assignment
export const updateClientAssignment = zMutation({
  args: z.object({
    assignmentId: zid('client_assignments'),
    updates: ClientAssignmentUpdateSchema,
  }),
  handler: async (ctx, args) => {
    const existing = await ctx.db.get(args.assignmentId);
    if (!existing) {
      throw new ConvexError('Assignment not found');
    }

    const now = Date.now();
    const updateData = {
      ...args.updates,
      updated_at: now,
    };

    await ctx.db.patch(args.assignmentId, updateData);
    return await ctx.db.get(args.assignmentId);
  },
});

// Assign a role to a user for a specific client
export const assignClientRole = zMutation({
  args: z.object({
    clientId: zid('clients'),
    userId: zid('users'),
    is_primary: z.boolean(),
  }),
  handler: async (ctx, args) => {
    const { clientId, userId, is_primary } = args;

    // 1. Check if client and user exist
    const client = await ctx.db.get(clientId);
    if (!client) throw new ConvexError('Client not found');
    const user = await ctx.db.get(userId);
    if (!user) throw new ConvexError('User not found');

    const now = Date.now();

    // 2. If assigning as primary, ensure no other user is primary
    if (is_primary) {
      const currentPrimary = await ctx.db
        .query('client_assignments')
        .withIndex('by_client_id', q => q.eq('client_id', clientId))
        .filter(q => q.eq(q.field('is_primary_assignment'), true))
        .filter(q => q.neq(q.field('user_id'), userId)) // Exclude the user being assigned
        .unique();

      if (currentPrimary) {
        // Demote the old primary user to a team member
        await ctx.db.patch(currentPrimary._id, {
          is_primary_assignment: false,
          updated_at: now,
        });
      }
    }

    // 3. Check if an assignment for this user already exists
    const existingAssignment = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_user', q => q.eq('client_id', clientId))
      .filter(q => q.eq(q.field('user_id'), userId))
      .unique();

    if (existingAssignment) {
      // 4a. If it exists, update the role
      await ctx.db.patch(existingAssignment._id, {
        is_primary_assignment: is_primary,
        updated_at: now,
      });
      return await ctx.db.get(existingAssignment._id);
    } else {
      // 4b. If it doesn't exist, create a new assignment
      const assignmentId = await ctx.db.insert('client_assignments', {
        client_id: clientId,
        user_id: userId,
        is_primary_assignment: is_primary,
        updated_at: now,
      });
      return await ctx.db.get(assignmentId);
    }
  },
});


// Bulk Operations

// Bulk update multiple clients
export const bulkUpdateClients = zMutation({
  args: BulkClientOperationSchema,
  handler: async (ctx, args) => {
    // Validate that either id or ids is provided
    if (!args.id && (!args.ids || args.ids.length === 0)) {
      throw new ConvexError('Either id or ids must be provided');
    }

    const now = Date.now();
    const updateData = {
      ...args.updates,
      updated_at: now,
    };

    const results = [];

    if (args.id) {
      // Single client update
      const existing = await ctx.db.get(args.id);
      if (!existing) {
        throw new ConvexError(`Client with id ${args.id} not found`);
      }
      await ctx.db.patch(args.id, updateData);
      const updated = await ctx.db.get(args.id);
      results.push(updated);
    } else if (args.ids) {
      // Multiple client updates
      for (const id of args.ids) {
        const existing = await ctx.db.get(id);
        if (!existing) {
          throw new ConvexError(`Client with id ${id} not found`);
        }
        await ctx.db.patch(id, updateData);
        const updated = await ctx.db.get(id);
        results.push(updated);
      }
    }

    return results;
  },
});

// Bulk assign multiple users to a client
export const bulkAssignUsersToClient = zMutation({
  args: BulkAssignmentCreateSchema,
  handler: async (ctx, args) => {
    // Check if client exists
    const client = await ctx.db.get(args.client_id);
    if (!client) {
      throw new ConvexError('Client not found');
    }

    const now = Date.now();
    const results = [];

    for (const assignment of args.assignments) {
      // Check if user exists
      const user = await ctx.db.get(assignment.user_id);
      if (!user) {
        throw new ConvexError(`User with id ${assignment.user_id} not found`);
      }

      // Check if assignment already exists
      const existingAssignment = await ctx.db
        .query('client_assignments')
        .filter((q) => 
          q.and(
            q.eq(q.field('client_id'), args.client_id),
            q.eq(q.field('user_id'), assignment.user_id)
          )
        )
        .unique();

      if (existingAssignment) {
        // Skip if already assigned
        results.push(existingAssignment);
        continue;
      }

      const assignmentData = {
        client_id: args.client_id,
        user_id: assignment.user_id,
        is_primary_assignment: false, // Default to not primary on bulk assign
        updated_at: now,
      };

      const assignmentId = await ctx.db.insert('client_assignments', assignmentData);
      const newAssignment = await ctx.db.get(assignmentId);
      results.push(newAssignment);
    }

    return results;
  },
});

// Bulk remove multiple assignments
export const bulkRemoveAssignments = zMutation({
  args: z.object({
    assignmentIds: z.array(zid('client_assignments')),
  }),
  handler: async (ctx, args) => {
    const results = [];

    for (const assignmentId of args.assignmentIds) {
      const assignment = await ctx.db.get(assignmentId);
      if (assignment) {
        await ctx.db.delete(assignmentId);
        results.push({ assignmentId, success: true });
      } else {
        results.push({ assignmentId, success: false, error: 'Assignment not found' });
      }
    }

    return results;
  },
});

// Replace all assignments for a client
export const replaceClientAssignments = zMutation({
  args: z.object({
    clientId: zid('clients'),
    userIds: z.array(zid('users')),
  }),
  handler: async (ctx, args) => {
    // Check if client exists
    const client = await ctx.db.get(args.clientId);
    if (!client) {
      throw new ConvexError('Client not found');
    }

    // Remove all existing assignments
    const existingAssignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_id', (q) => q.eq('client_id', args.clientId))
      .collect();

    for (const assignment of existingAssignments) {
      await ctx.db.delete(assignment._id);
    }

    // Add new assignments
    const now = Date.now();
    const results = [];

    for (const userId of args.userIds) {
      // Check if user exists
      const user = await ctx.db.get(userId);
      if (!user) {
        throw new ConvexError(`User with id ${userId} not found`);
      }

      const assignmentData = {
        client_id: args.clientId,
        user_id: userId,
        is_primary_assignment: false, // Default to not primary on replace
        updated_at: now,
      };

      const assignmentId = await ctx.db.insert('client_assignments', assignmentData);
      const newAssignment = await ctx.db.get(assignmentId);
      results.push(newAssignment);
    }

    return results;
  },
});
