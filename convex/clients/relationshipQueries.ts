import { query } from '../_generated/server';
import { v } from 'convex/values';
import { Id } from '../_generated/dataModel';

/**
 * List relationship types that are valid for a client as the source
 * This query will populate the "Select Relationship Type" dropdown. It correctly filters the types valid for a `client` source.
 */
export const listRelationshipTypesForClient = query({
  args: {},
  // Senior Engineer Note: Defining a precise return type ensures our frontend
  // contract is clear and type-safe.
  returns: v.array(
    v.object({
      _id: v.id("relationship_types"),
      relationship_name: v.string(),
      valid_combinations: v.array(v.object({
        source_type: v.union(
          v.literal('person'),
          v.literal('organization'),
          v.literal('client')
        ),
        target_type: v.union(
          v.literal('person'),
          v.literal('organization'),
          v.literal('client')
        )
      })),
      // Include any other fields the frontend might need, like description.
      relationship_description: v.optional(v.string()),
      custom_fields_schema: v.optional(v.array(v.object({
        field_key: v.string(),
        field_name: v.string(),
        field_type: v.union(
          v.literal('text'),
          v.literal('number'),
          v.literal('date'),
          v.literal('boolean'),
          v.literal('select'),
          v.literal('currency')
        ),
        is_required: v.optional(v.boolean()),
        options: v.optional(v.array(v.string())),
        description: v.optional(v.string()),
        default_value: v.optional(v.string()),
        field_order: v.number()
      }))),
    })
  ),
  handler: async (ctx) => {
    // Senior Engineer Note: The current schema requires us to fetch all relationship
    // types and filter in memory. This is acceptable for a small number of types,
    // but if this table grows large, we should consider adding a dedicated, denormalized
    // indexable field like `supported_source_types: v.array(v.string())`.
    // 
    // ✅ PERFORMANCE: Added .take() limiter to prevent unbounded table scan
    // Assuming relationship_types is a small reference table (< 100 records)
    const allTypes = await ctx.db.query("relationship_types").take(100);

    // Filter to only include types where a 'client' can be the source.
    const clientValidTypes = allTypes.filter(type =>
      type.valid_combinations.some((combo: any) => combo.source_type === 'client')
    );

    // Return only the fields specified in the validator to avoid validation errors
    return clientValidTypes.map(type => ({
      _id: type._id as Id<"relationship_types">,
      relationship_name: type.relationship_name,
      valid_combinations: type.valid_combinations,
      relationship_description: type.relationship_description,
      custom_fields_schema: type.custom_fields_schema,
    }));
  },
});

// =============================================
// NEW QUERY FOR REFACTORED RELATIONSHIPS PAGE
// =============================================
// Architectural Note: This query is designed to fetch and categorize relationships
// for the redesigned client relationships page. It separates relationships into two
// distinct groups: "Family" (person-to-person) and "Client" (client-to-entity).
//
// FAMILY SECTION LOGIC:
// 1. Find all client->person relationships where relationship_type.is_principal_type = true
//    These are the "principals" (primary family members like spouse, children)
// 2. Find all person->person relationships where:
//    - Source is one of the principals from step 1
//    - relationship_type.is_client_family = true
//    These are the family relationships between principals and other family members
// 3. Display both the principals and their family relationships in the Family section
export const getRelationshipsForClientPage = query({
  args: {
    clientId: v.id('clients'),
  },
  returns: v.object({
    familyRelationships: v.array(
      v.object({
        source: v.object({
          _id: v.id('people'),
          name: v.string(),
          imageUrl: v.optional(v.union(v.string(), v.null())),
        }),
        target: v.object({
          _id: v.id('people'),
          name: v.string(),
          imageUrl: v.optional(v.union(v.string(), v.null())),
        }),
        relationshipId: v.id('relationships'),
        relationshipName: v.string(),
      })
    ),
    clientRelationships: v.array(
      v.object({
        target: v.object({
          _id: v.union(v.id('people'), v.id('organizations')),
          name: v.string(),
          type: v.union(v.literal('person'), v.literal('organization')),
          imageUrl: v.optional(v.union(v.string(), v.null())),
        }),
        relationshipId: v.id('relationships'),
        relationshipName: v.string(),
      })
    ),
  }),
  handler: async (ctx, args) => {
    // 1. Identify Principals
    // ✅ PERFORMANCE: Add limiter to prevent unbounded scan
    // Assuming a client won't have more than 100 relationships
    const principalRelationships = await ctx.db
      .query('relationships')
      .filter((q) => 
        q.and(
          q.eq(q.field('source_type'), 'client'),
          q.eq(q.field('source_id'), args.clientId),
          q.eq(q.field('is_active'), true)
        )
      )
      .take(100);

    const principalRelTypes = await ctx.db.query('relationship_types')
      .filter(q => q.eq(q.field('is_principal_type'), true))
      .take(50); // ✅ PERFORMANCE: Added limiter for small reference table
    const principalRelTypeIds = new Set(principalRelTypes.map(t => t._id));

    const principalIds = principalRelationships
      .filter(r => principalRelTypeIds.has(r.relationship_type_id))
      .map(r => r.target_id as Id<'people'>); // Assuming principals are always people

    // 2. Fetch Family Relationships (Person-to-Person involving Principals)
    const familyRelationships: any[] = [];
    
    // Add principals as individual cards (not relationships)
    await Promise.all(principalRelationships.map(async (rel) => {
      if (principalRelTypeIds.has(rel.relationship_type_id) && rel.target_type === 'person') {
        const relType = await ctx.db.get(rel.relationship_type_id);
        const person = await ctx.db.get(rel.target_id as Id<'people'>);
        
        if (relType && person) {
          // Show principal as a simple person card with their relationship type
          familyRelationships.push({
            source: {
              _id: person._id,
              name: person.name,
              imageUrl: person.image ?? null,
            },
            target: {
              _id: person._id,
              name: person.name,
              imageUrl: person.image ?? null,
            },
            relationshipId: rel._id,
            relationshipName: relType.relationship_name, // Just the relationship type (e.g., "Spouse", "Child")
          });
        }
      }
    }));

    // Then add person-to-person family relationships involving principals
    if (principalIds.length > 0) {
      const principalPersonDetails = new Map();
      await Promise.all(principalIds.map(async (id) => {
        const person = await ctx.db.get(id);
        if (person) principalPersonDetails.set(id, person);
      }));

      // Get relationship types that are marked as client family relationships
      const familyRelTypes = await ctx.db.query('relationship_types')
        .filter(q => q.eq(q.field('is_client_family'), true))
        .take(50); // ✅ PERFORMANCE: Added limiter for small reference table
      const familyRelTypeIds = new Set(familyRelTypes.map(t => t._id));

      const allFamilyRels = await Promise.all(
        principalIds.map(id =>
          ctx.db
            .query('relationships')
            .filter((q) => 
              q.and(
                q.eq(q.field('source_type'), 'person'),
                q.eq(q.field('source_id'), id),
                q.eq(q.field('is_active'), true)
              )
            )
            .take(50) // ✅ PERFORMANCE: Add limiter - assuming max 50 family relationships per person
        )
      );
      const flattenedFamilyRels = allFamilyRels.flat();

      await Promise.all(flattenedFamilyRels.map(async (rel) => {
        // Only include relationships that are marked as client family relationships
        if (rel.target_type !== 'person' || !familyRelTypeIds.has(rel.relationship_type_id)) return;

        const sourceType = await ctx.db.get(rel.relationship_type_id);
        const targetPerson = await ctx.db.get(rel.target_id as Id<'people'>);
        const sourcePerson = principalPersonDetails.get(rel.source_id as Id<'people'>);

        if (sourceType && targetPerson && sourcePerson) {
          familyRelationships.push({
            source: {
              _id: sourcePerson._id,
              name: sourcePerson.name,
              imageUrl: sourcePerson.image ?? null,
            },
            target: {
              _id: targetPerson._id,
              name: targetPerson.name,
              imageUrl: targetPerson.image ?? null,
            },
            relationshipId: rel._id,
            relationshipName: sourceType.relationship_name,
          });
        }
      }));
    }

    // 3. Fetch Client Relationships (Client-to-Person/Organization)
    // IMPORTANT: Exclude principal relationships as they should appear in Family section
    const clientRelationships: any[] = [];
    const clientSourceRels = await ctx.db
      .query('relationships')
      .filter((q) => 
        q.and(
          q.eq(q.field('source_type'), 'client'),
          q.eq(q.field('source_id'), args.clientId),
          q.eq(q.field('is_active'), true)
        )
      )
      .take(100); // ✅ PERFORMANCE: Add limiter - assuming max 100 client relationships

    await Promise.all(clientSourceRels.map(async (rel) => {
      const relType = await ctx.db.get(rel.relationship_type_id);
      if (!relType) return;

      // BUGFIX: Skip principal relationships - they should appear in Family section
      if (relType.is_principal_type) {
        return;
      }

      let targetEntity;
      if (rel.target_type === 'person') {
        targetEntity = await ctx.db.get(rel.target_id as Id<'people'>);
      } else if (rel.target_type === 'organization') {
        targetEntity = await ctx.db.get(rel.target_id as Id<'organizations'>);
      }

      if (targetEntity) {
        clientRelationships.push({
          target: {
            _id: targetEntity._id,
            name: (targetEntity as any).name || (targetEntity as any).client_name,
            type: rel.target_type,
            imageUrl: (targetEntity as any).image ?? null,
          },
          relationshipId: rel._id,
          relationshipName: relType.relationship_name,
        });
      }
    }));

    return { familyRelationships, clientRelationships };
  },
});

// Architectural Note: This query is designed to be robust and type-safe.
// The 'returns' validator ensures that the data contract with the frontend is strictly enforced.
// We use a precise union of Id types for `relatedEntity._id` to maintain type safety across polymorphic relationships.
// PERFORMANCE: Uses proper indexes ('by_source_active', 'by_target_active') to avoid full table scans.
export const getRelationshipsForClient = query({
  args: {
    clientId: v.id('clients'),
  },
  returns: v.object({
    principals: v.array(v.object({
      relationshipId: v.id('relationships'),
      relationshipName: v.string(),
      relatedEntity: v.object({
        _id: v.union(v.id('people'), v.id('organizations'), v.id('clients')),
        name: v.string(),
        type: v.union(v.literal('person'), v.literal('organization'), v.literal('client')),
        imageUrl: v.optional(v.union(v.string(), v.null())),
      }),
    })),
    family: v.array(v.object({
      relationshipId: v.id('relationships'),
      relationshipName: v.string(),
      relatedEntity: v.object({
        _id: v.union(v.id('people'), v.id('organizations'), v.id('clients')),
        name: v.string(),
        type: v.union(v.literal('person'), v.literal('organization'), v.literal('client')),
        imageUrl: v.optional(v.union(v.string(), v.null())),
      }),
    })),
    serviceProviders: v.array(v.object({
      relationshipId: v.id('relationships'),
      relationshipName: v.string(),
      relatedEntity: v.object({
        _id: v.union(v.id('people'), v.id('organizations'), v.id('clients')),
        name: v.string(),
        type: v.union(v.literal('person'), v.literal('organization'), v.literal('client')),
        imageUrl: v.optional(v.union(v.string(), v.null())),
      }),
    })),
  }),
  handler: async (ctx, args) => {
    // ✅ PERFORMANCE OPTIMIZATION: Use proper indexes to efficiently query relationships
    // Instead of scanning the entire table, we use specific indexes for active relationships
    
    // Query relationships where this client is the source
    const sourceRelationshipsPromise = ctx.db
      .query('relationships')
      .filter((q) =>
        q.and(
          q.eq(q.field('source_type'), 'client'),
          q.eq(q.field('source_id'), args.clientId),
          q.eq(q.field('is_active'), true)
        )
      )
      .take(200); // ✅ PERFORMANCE: Add limiter - assuming max 200 relationships per client

    // Query relationships where this client is the target
    const targetRelationshipsPromise = ctx.db
      .query('relationships')
      .filter((q) =>
        q.and(
          q.eq(q.field('target_type'), 'client'),
          q.eq(q.field('target_id'), args.clientId),
          q.eq(q.field('is_active'), true)
        )
      )
      .take(200); // ✅ PERFORMANCE: Add limiter - assuming max 200 relationships per client

    // Execute both queries in parallel for better performance
    const [sourceRelationships, targetRelationships] = await Promise.all([
      sourceRelationshipsPromise,
      targetRelationshipsPromise
    ]);

    const allRelationships = [...sourceRelationships, ...targetRelationships];

    const principals: any[] = [];
    const family: any[] = [];
    const serviceProviders: any[] = [];

    // Process relationships in parallel for efficiency
    await Promise.all(allRelationships.map(async (rel) => {
      const relType = await ctx.db.get(rel.relationship_type_id);
      if (!relType) return;

      // Determine the other entity in the relationship
      const isSourceClient = rel.source_id === args.clientId;
      const otherEntityType = isSourceClient ? rel.target_type : rel.source_type;
      const otherEntityId = isSourceClient ? rel.target_id : rel.source_id;

      let otherEntity;
      let entityName;
      let entityImage;

      if (otherEntityType === 'person') {
        otherEntity = await ctx.db.get(otherEntityId as Id<'people'>);
        if (otherEntity) {
          entityName = otherEntity.name;
          entityImage = otherEntity.image;
        }
      } else if (otherEntityType === 'organization') {
        otherEntity = await ctx.db.get(otherEntityId as Id<'organizations'>);
        if (otherEntity) {
          entityName = otherEntity.name;
          entityImage = undefined; // Organizations do not have an image
        }
      } else if (otherEntityType === 'client') {
        otherEntity = await ctx.db.get(otherEntityId as Id<'clients'>);
        if (otherEntity) {
          entityName = otherEntity.client_name; // Handle client-to-client relationships
          entityImage = undefined; // Clients do not have a direct image
        }
      }

      if (!otherEntity || !entityName) return;

      const relationshipData = {
        relationshipId: rel._id,
        relationshipName: relType.relationship_name,
        relatedEntity: {
          _id: otherEntity._id,
          name: entityName,
          type: otherEntityType,
          imageUrl: entityImage ?? null, // Ensure consistency by providing null
        },
      };

      // Categorize based on relationship type properties
      let categorized = false;
      
      if (relType.is_principal_type) {
        principals.push(relationshipData);
        categorized = true;
      }
      if (relType.is_client_family) {
        family.push(relationshipData);
        categorized = true;
      }
      if (relType.relationship_category === 'professional') {
        serviceProviders.push(relationshipData);
        categorized = true;
      }
      
      // BUGFIX: Add fallback categorization for relationships that don't match specific criteria
      // This ensures all valid relationships are displayed somewhere
      if (!categorized) {
        // Default categorization based on entity type
        if (otherEntityType === 'person') {
          // Person relationships go to principals by default
          principals.push(relationshipData);
        } else if (otherEntityType === 'organization') {
          // Organization relationships go to service providers by default
          serviceProviders.push(relationshipData);
        } else if (otherEntityType === 'client') {
          // Client-to-client relationships go to principals by default
          principals.push(relationshipData);
        }
      }
    }));

    return { principals, family, serviceProviders };
  },
});

/**
 * Search for entities that can be targets in relationships
 * This query searches people, organizations, and clients for the relationship modal
 */
export const searchEntitiesForRelationships = query({
  args: {
    searchTerm: v.string(),
    entityType: v.union(v.literal("person"), v.literal("organization"), v.literal("client")),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.union(v.id("people"), v.id("organizations"), v.id("clients")),
      name: v.string(),
      type: v.union(v.literal("person"), v.literal("organization"), v.literal("client")),
      description: v.optional(v.string()),
      imageUrl: v.optional(v.string()),
    })
  ),
  handler: async (ctx, args) => {
    const { searchTerm, entityType, limit = 10 } = args;
    
    if (!searchTerm.trim()) {
      return [];
    }

    const results: Array<{
      _id: any;
      name: string;
      type: "person" | "organization" | "client";
      description?: string;
      imageUrl?: string;
    }> = [];

    switch (entityType) {
      case "person":
        const people = await ctx.db
          .query("people")
          .withSearchIndex("search_name", (q) => q.search("name", searchTerm))
          .take(limit);
        
        for (const person of people) {
          results.push({
            _id: person._id,
            name: person.name,
            type: "person",
            description: person.short_description || person.description,
            imageUrl: person.image,
          });
        }
        break;

      case "organization":
        const organizations = await ctx.db
          .query("organizations")
          .withSearchIndex("search_name", (q) => q.search("name", searchTerm))
          .take(limit);
        
        for (const org of organizations) {
          results.push({
            _id: org._id,
            name: org.name,
            type: "organization",
            description: org.short_description || org.description,
            imageUrl: undefined, // Organizations don't have images
          });
        }
        break;

      case "client":
        const clients = await ctx.db
          .query("clients")
          .withSearchIndex("search_name", (q) => q.search("client_name", searchTerm))
          .take(limit);
        
        for (const client of clients) {
          results.push({
            _id: client._id,
            name: client.client_name,
            type: "client",
            description: client.client_short_description || client.client_description,
            imageUrl: undefined, // Clients don't have images
          });
        }
        break;
    }

    return results;
  },
});

/**
 * Get all relationship types that can be used for creating principal relationships
 * (client -> person relationships where is_principal_type = true)
 */
export const getPrincipalRelationshipTypes = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id('relationship_types'),
    relationship_name: v.string(),
    relationship_description: v.optional(v.string()),
    relationship_category: v.optional(v.string()),
  })),
  handler: async (ctx) => {
    // Get all relationship types that are principal types
    // ✅ PERFORMANCE: Add limiter to prevent unbounded scan for small reference table
    const allPrincipalTypes = await ctx.db
      .query('relationship_types')
      .filter(q => q.eq(q.field('is_principal_type'), true))
      .take(50);

    // Filter in memory for valid combinations (client -> person)
    const validPrincipalTypes = allPrincipalTypes.filter(type =>
      type.valid_combinations.some((combo: any) => 
        combo.source_type === 'client' && combo.target_type === 'person'
      )
    );

    return validPrincipalTypes.map(type => ({
      _id: type._id as Id<"relationship_types">,
      relationship_name: type.relationship_name,
      relationship_description: type.relationship_description,
      relationship_category: type.relationship_category,
    }));
  },
});

/**
 * Search for people to add as principals
 */
export const searchPeopleForPrincipal = query({
  args: {
    searchText: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id('people'),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    title: v.optional(v.string()),
    image: v.optional(v.string()),
  })),
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    
    if (!args.searchText || args.searchText.trim() === '') {
      // Return recent people if no search text
      const people = await ctx.db
        .query('people')
        .order('desc')
        .take(limit);
      
      return people.map(person => ({
        _id: person._id as Id<"people">,
        name: person.name,
        email: person.email,
        phone: person.phone,
        title: person.title,
        image: person.image,
      }));
    }

    // Search by name using the search index
    const searchResults = await ctx.db
      .query('people')
      .withSearchIndex('search_name', q => q.search('name', args.searchText!))
      .take(limit);

    return searchResults.map(person => ({
      _id: person._id as Id<"people">,
      name: person.name,
      email: person.email,
      phone: person.phone,
      title: person.title,
      image: person.image,
    }));
  },
});

/**
 * Get all relationship types that can be used for creating non-principal client relationships
 * (client -> person/organization relationships where is_principal_type = false)
 */
export const getNonPrincipalRelationshipTypes = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id('relationship_types'),
    relationship_name: v.string(),
    relationship_description: v.optional(v.string()),
    relationship_category: v.optional(v.string()),
  })),
  handler: async (ctx) => {
    // Get all non-principal relationship types and filter in memory
    // This is acceptable for a small reference table
    const allTypes = await ctx.db
      .query('relationship_types')
      .filter(q => q.eq(q.field('is_principal_type'), false))
      .take(50); // Performance: limit for small reference table

    // Filter to only include types where a 'client' can be the source to person or organization
    const clientValidTypes = allTypes.filter(type =>
      type.valid_combinations.some((combo: any) => 
        combo.source_type === 'client' && 
        (combo.target_type === 'person' || combo.target_type === 'organization')
      )
    );

    return clientValidTypes.map(type => ({
      _id: type._id as Id<"relationship_types">,
      relationship_name: type.relationship_name,
      relationship_description: type.relationship_description,
      relationship_category: type.relationship_category,
    }));
  },
});

/**
 * Get all relationship types that can be used for creating family relationships
 * (person -> person relationships where is_client_family = true)
 */
export const getFamilyRelationshipTypes = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id('relationship_types'),
    relationship_name: v.string(),
    relationship_description: v.optional(v.string()),
    relationship_category: v.optional(v.string()),
  })),
  handler: async (ctx) => {
    // Get all family relationship types and filter in memory
    // This is acceptable for a small reference table
    const allTypes = await ctx.db
      .query('relationship_types')
      .filter(q => q.eq(q.field('is_client_family'), true))
      .take(50); // Performance: limit for small reference table

    // Filter to only include types where a 'person' can be the source to another person
    const familyValidTypes = allTypes.filter(type =>
      type.valid_combinations.some((combo: any) => 
        combo.source_type === 'person' && combo.target_type === 'person'
      )
    );

    return familyValidTypes.map(type => ({
      _id: type._id as Id<"relationship_types">,
      relationship_name: type.relationship_name,
      relationship_description: type.relationship_description,
      relationship_category: type.relationship_category,
    }));
  },
});
