import { MutationCtx, mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * 🌙 Mutation to update the "ASEFAD" column for all documents in the "categories" table and set its value to null.
 *
 * This function follows the new Convex function syntax.
 * It chains query methods to retrieve all documents from the "categories" table using `.collect()`,
 * and then iterates over the results. For each document, it patches the document to set the "ASEFAD" field to null.
 *
 * IMPORTANT: This mutation overwrites the "ASEFAD" column in every record.
 * Make sure to perform a backup and run this during a maintenance window.
 */
export default mutation({
  args: {
    columnName: v.string()
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string()
  }),
  handler: async (ctx, args) => {
    const { columnName } = args;

    const tableNames = await ctx.db
      .query('tags') // Using the specific table name
      .collect();

    for (const table of tableNames) {
      await ctx.db.patch(table._id, { [columnName]: undefined });
    }

    return {
      success: true,
      message: `Overwrote the ${columnName} column to null in ${tableNames.length} documents.`
    };
  }
});
