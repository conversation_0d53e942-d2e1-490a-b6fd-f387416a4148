import { z } from "zod";
import { zQuery } from "./functions";
import { QueryCtx } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { zid } from "convex-helpers/server/zod";

// Import the schemas we created
import {
  BadgeFilterSchema,
  PaginationOptionsSchema,
  PaginatedResultSchema,
  BadgeSchema
} from "../zod/badges-schema";

/**
 * List badges with optional filtering and pagination
 * 
 * Features:
 * - Full-text search on badge names
 * - Filter by domain (task, project, etc.)
 * - Sort by name or creation time
 * - Pagination support
 */
export const listBadges = zQuery({
  args: {
    filter: BadgeFilterSchema.optional(),
    paginationOpts: PaginationOptionsSchema.optional()
  },
  output: PaginatedResultSchema,
  handler: async (ctx: QueryCtx, { filter, paginationOpts }) => {
    const { db } = ctx;

    // Default sorting options if not provided
    const sortBy = filter?.sortBy || "name";
    const sortDirection = filter?.sortDirection || "asc";

    let result: { page: Doc<"badges">[]; isDone: boolean; continueCursor: string | null };

    // Build query based on the filter conditions
    if (filter?.searchText && filter.searchText.length > 0) {
      // If searching by text, use the search index
      result = await db
        .query("badges")
        .withSearchIndex("search_badge_name", q => q.search("name", filter.searchText as string))
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });
    } else if (filter?.badgeDomain) {
      // If filtering by domain, use the domain index
      result = await db
        .query("badges")
        .withIndex("by_badgeDomain", q => q.eq("badgeDomain", filter.badgeDomain))
        .order(sortDirection)
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });
    } else if (sortBy === "name") {
      // If sorting by name, use the name index
      result = await db
        .query("badges")
        .withIndex("by_name", q => q)
        .order(sortDirection)
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });
    } else {
      // Default case: sort by creation time
      result = await db
        .query("badges")
        .order(sortDirection)
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });
    }

    // Validate each badge against the schema before returning
    const validatedPage = result.page.map(badge => BadgeSchema.parse(badge));

    return {
      page: validatedPage,
      isDone: result.isDone,
      continueCursor: result.continueCursor ?? undefined
    };
  }
});

/**
 * Get badges by their IDs
 * 
 * This function retrieves multiple badges based on an array of badge IDs.
 * Useful for displaying badges associated with other entities like ts.
 */
export const getBadgesByIds = zQuery({
  args: {
    badgeIds: z.array(zid("badges"))
  },
  output: z.array(BadgeSchema),
  handler: async (ctx: QueryCtx, { badgeIds }) => {
    const { db } = ctx;
    
    // Fetch all badges in parallel
    const badges = await Promise.all(
      badgeIds.map(async (badgeId) => {
        try {
          const badge = await db.get(badgeId);
          return badge ? BadgeSchema.parse(badge) : null;
        } catch (error) {
          console.error(`Failed to fetch badge with ID ${badgeId}:`, error);
          return null;
        }
      })
    );
    
    // Filter out any null values and ensure all badges match the schema
    return badges.filter((badge): badge is z.infer<typeof BadgeSchema> => badge !== null);
  }
});
