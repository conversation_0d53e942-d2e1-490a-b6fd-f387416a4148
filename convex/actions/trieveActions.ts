"use node";

import { action } from "../_generated/server";
import { v } from "convex/values";
import { trieve, isTrieveConfigured } from "../trieve/trieve"; // Import Trieve SDK instance and helper
import { ConvexError } from "convex/values";
import { Id, Doc } from "../_generated/dataModel"; // Added Doc import
import { api } from "../_generated/api";
// getSubTableData is not directly used here anymore, but called via public query
// import { getSubTableData } from "../utils/fileUtils";
import { ActionCtx } from "../_generated/server"; // Import ActionCtx for typing
import { chunkContentByParagraphs, checkTrieveConfiguration, handleTrieveError, TRIEVE_OPERATION_DELAY, prepareTreivedDocData } from "../utils/trieveUtils";

// Define the expected input structure for document operations passed to trieveUpsertDocumentChunks
const documentDataForUpsertValidator = v.object({
  id: v.id('files'),         // FOJO document ID (files._id)
  title: v.string(),
  short_description: v.optional(v.union(v.string(), v.null())), // Added
  content: v.string(),       // HTML content from TipTap (from sub-table)
  category: v.optional(v.union(v.string(), v.null())),        // Added (from sub-table)
  docType: v.string(),       // e.g., 'KNOWLEDGE_BASE', 'MEETING_NOTES'
  projectId: v.optional(v.union(v.id('projects'), v.null())),
  orgId: v.string(),         // Organization ID
  createdAt: v.number(),     // Convex _creationTime (timestamp number)
});

/**
 * Diagnostic tool to check the state of a Trieve group and its chunks
 *
 * This action:
 * 1. Verifies if the group exists
 * 2. Gets all chunks associated with the group
 * 3. Returns comprehensive diagnostic information
 */
export const diagnosticCheckTrieveGroup = action({
  args: {
    groupId: v.string(),     // The Trieve group tracking_id (usually the file ID)
  },
  handler: async (ctx, args) => {
    const { groupId } = args;

    // Check if Trieve is configured
    if (!isTrieveConfigured() || !trieve) {
      return {
        status: "error",
        error: "Trieve not configured",
        details: "The Trieve client is not properly configured with API keys."
      };
    }

    try {
      // Get the group information
      console.log(`Fetching group information for tracking ID: ${groupId}`);
      const group = await trieve.getGroupByTrackingId({
        trackingId: groupId,
      });

      // Get all chunks in the group
      console.log(`Fetching chunks in group: ${groupId}`);
      let chunksResponse;
      try {
        // According to docs, only need groupTrackingId and page
        chunksResponse = await trieve.getChunksGroupByTrackingId({
          groupTrackingId: groupId,
          page: 1
        });
      } catch (error) {
        console.warn(`Error fetching chunks for ${groupId}:`, error);
        chunksResponse = { chunks: [] };
      }

      // Return comprehensive diagnostic information
      return {
        status: "success",
        group: {
          exists: !!group,
          details: group || null,
        },
        chunks: {
          count: chunksResponse?.chunks?.length || 0,
          details: chunksResponse?.chunks || [],
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error(`Error in diagnosticCheckTrieveGroup: ${error}`);
      return {
        status: "error",
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      };
    }
  },
});

/**
 * Core function to upsert document chunks in Trieve
 *
 * This action:
 * 1. Gets existing chunks for the document's group
 * 2. Chunks the new content
 * 3. Compares new vs existing chunks
 * 4. Updates existing chunks that changed
 * 5. Creates new chunks
 * 6. Deletes obsolete chunks
 * 7. Verifies results
 */
export const trieveUpsertDocumentChunks = action({
  args: {
    doc: v.optional(documentDataForUpsertValidator), // Make doc optional
    fileId: v.optional(v.id('files')), // Add optional fileId parameter for direct file reference
  },
  handler: async (ctx, args): Promise<{
    status: string;
    documentId?: Id<"files">;
    reason?: string;
    operations?: {
      created: number;
      updated: number;
      deleted: number;
      skipped: number;
      total: number;
    };
    timestamp?: string;
  }> => {
    // Determine which approach to use - either direct fileId or full doc object
    const useDirectFileId = args.fileId !== undefined;
    let documentId = useDirectFileId ? args.fileId : args.doc?.id;

    // Ensure we have a valid fileId
    if (!documentId) {
      console.error("No valid fileId provided");
      return { status: "error", reason: "missing_file_id" };
    }

    // Use documentId as our fileId for the rest of the function
    const fileId: Id<"files"> = documentId;

    console.log(`[trieveUpsertDocumentChunks] Action handler started for doc ID: ${fileId}`);

    // Check if Trieve is configured using the utility function
    const trieveConfig = checkTrieveConfiguration();
    if (!trieveConfig.isConfigured) {
      console.warn("Trieve is not configured. Skipping ingestion.");
      return { status: "skipped", reason: "not_configured" };
    }

    // If using direct fileId, fetch the latest data from the database
    let title: string;
    let short_description: string | null | undefined;
    let content: string;
    let category: string | null | undefined;
    let docType: string;
    let projectId: Id<'projects'> | null;
    let orgId: string;
    let createdAt: number;

    if (useDirectFileId) {
      console.log(`Using direct fileId approach - fetching latest data for ${fileId}`);
      // Fetch the file record to get the latest data
      const fileRecord = await ctx.runQuery(api.files.files.getFileRecordBasic, { fileId });
      if (!fileRecord) {
        console.error(`File record not found for ID: ${fileId}`);
        return { status: "error", reason: "file_not_found" };
      }

      // Get the docType from the file record
      docType = fileRecord.docType;

      // Fetch the content and category from the appropriate sub-table
      const subTableData = await ctx.runQuery(api.files.files.getSubTableDataPublic, {
        fileId,
        docType
      });

      // Set all the variables with the latest data
      title = fileRecord.title ?? "Untitled Document";
      short_description = fileRecord.short_description; // This is the key field we need to be up-to-date
      content = subTableData.content ?? "";
      category = subTableData.category;
      // Safely handle parentEntityId which might be undefined
      const parentId = fileRecord.parentEntityId;
      projectId = parentId ? parentId as Id<'projects'> : null;
      orgId = "PLACEHOLDER_ORG_ID"; // TODO: Implement proper orgId logic
      createdAt = fileRecord._creationTime;

      console.log(`Fetched latest data for ${fileId}:`, {
        title,
        hasShortDescription: !!short_description,
        contentLength: content?.length || 0,
        category,
        docType
      });
    } else if (args.doc) {
      // Use the data provided in the doc argument
      const doc = args.doc;
      documentId = doc.id;
      title = doc.title;
      short_description = doc.short_description;
      content = doc.content;
      category = doc.category;
      docType = doc.docType;
      projectId = doc.projectId || null;
      orgId = doc.orgId;
      createdAt = doc.createdAt;
    } else {
      // This should never happen due to our validation above
      return { status: "error", reason: "invalid_arguments" };
    }

    // Ensure content is not null or empty before proceeding
    if (!content || content.trim().length === 0) {
      console.warn(`Document ${fileId} has empty content. Skipping ingestion.`);
      return { status: "skipped", reason: "empty_content" };
    }

    try {
      console.log(`Starting Trieve upsert process for document ID: ${fileId}`);

      // Step 1: Ensure the chunk group exists for this document
      const groupTags = [`Type:${docType}`, `Org:${orgId}`];
      if (projectId) {
        groupTags.push(`Project:${projectId}`);
      }

      // Step 1A: Check if group exists
      let groupExists = false;
      try {
        const existingGroup = await trieve?.getGroupByTrackingId({
          trackingId: fileId,
        });
        groupExists = !!existingGroup;
        console.log(`Group check for ${fileId}: ${groupExists ? 'exists' : 'does not exist'}`);
      } catch (error) {
        console.log(`Group does not exist for ${fileId}, will create it`);
        groupExists = false;
      }

      // Step 1B: Create or update the group
      if (!groupExists) {
        console.log(`Creating new group for document ${fileId}`);
        await trieve?.createChunkGroup({
          tracking_id: fileId,
          name: title,
          description: `FOJO document ${fileId}`,
          tag_set: groupTags,
          metadata: { title, docType, projectId, orgId } // Include relevant metadata
        });
      } else {
        console.log(`Updating existing group for document ${fileId}`);
        await trieve?.updateGroup({
          tracking_id: fileId,
          name: title,
          description: `FOJO document ${fileId}`,
          tag_set: groupTags,
          metadata: { title, docType, projectId, orgId } // Include relevant metadata
        });
      }

      // Step 2: Chunk the content
      const paragraphs = chunkContentByParagraphs(content);
      if (paragraphs.length === 0) {
        console.warn(`Document ${fileId} produced no chunks after splitting. Skipping ingestion.`);
        return { status: "skipped", reason: "no_chunks" };
      }
      console.log(`Document ${fileId} chunked into ${paragraphs.length} paragraphs`);

      // Step 3: Get existing chunks for this group
      let existingChunks: any[] = [];
      try {
        // Fetch all chunks for the group - Trieve SDK might require pagination handling for large numbers
        // For simplicity, assuming page 1 gets enough or all chunks for now.
        const response = await trieve?.getChunksGroupByTrackingId({
          groupTrackingId: fileId,
          page: 1
        });
        existingChunks = response?.chunks || [];
        console.log(`Found ${existingChunks.length} existing chunks for document ${fileId}`);
      } catch (error) {
        console.warn(`Error fetching existing chunks for ${fileId}, assuming none exist:`, error);
        existingChunks = [];
      }

      // Create a map of existing chunks by tracking_id for easier lookup
      const existingChunksMap = new Map();
      existingChunks.forEach(chunk => {
        // Ensure tracking_id exists before adding to map
        if (chunk?.chunk?.tracking_id) {
          existingChunksMap.set(chunk.chunk.tracking_id, chunk.chunk);
        }
      });

      // Step 4: Prepare chunk objects with consistent tracking IDs
      const chunkOperations = paragraphs.map((paragraph, index) => {
        const chunkTrackingId = `${fileId}_chunk_${index}`;
        const chunkTags = [...groupTags]; // Clone the base tags

        // Construct the labeled chunk_html
        const chunkHtml = `Title: ${title}\nShort Description: ${short_description || 'N/A'}\nCategory: ${category || 'N/A'}\nContent: ${paragraph}`;

        const newChunkData = {
          chunk_html: chunkHtml, // Use the new labeled format
          tag_set: chunkTags,
          metadata: {
            documentId: fileId,
            title, // Include title in metadata too for potential direct access
            docType,
            chunkIndex: index
          },
          tracking_id: chunkTrackingId,
          group_tracking_ids: [fileId], // Associate chunk with the document's group
          time_stamp: new Date(createdAt).toISOString(),
          upsert_by_tracking_id: true // Ensure updates happen if chunk exists
        };

        // Check if this chunk already exists
        const existingChunk = existingChunksMap.get(chunkTrackingId);

        if (existingChunk) {
          // Mark existing chunk as processed
          existingChunksMap.delete(chunkTrackingId);

          // Compare content to see if update is needed
          if (existingChunk.chunk_html !== newChunkData.chunk_html) {
            return { operation: 'update', data: newChunkData };
          } else {
            return { operation: 'skip', data: newChunkData };
          }
        } else {
          return { operation: 'create', data: newChunkData };
        }
      });

      // Step 5: Handle operations - create, update, or delete chunks
      // 5A: Handle creates (batch if possible)
      const chunksToCreate = chunkOperations
        .filter(op => op.operation === 'create')
        .map(op => op.data);

      if (chunksToCreate.length > 0) {
        console.log(`Creating ${chunksToCreate.length} new chunks for document ${fileId}`);
        // Trieve SDK's createChunk can handle an array for batching
        await trieve?.createChunk(chunksToCreate);
      }

      // 5B: Handle updates (convert loop to Promise.all pattern)
      const chunksToUpdate = chunkOperations
        .filter(op => op.operation === 'update')
        .map(op => op.data);

      if (chunksToUpdate.length > 0) {
        console.log(`Updating ${chunksToUpdate.length} existing chunks for document ${fileId}`);
        const updatePromises = chunksToUpdate.map(chunk => {
          return trieve?.updateChunkByTrackingId({
            tracking_id: chunk.tracking_id,
            chunk_html: chunk.chunk_html,
            metadata: chunk.metadata,
            time_stamp: chunk.time_stamp
          })
          .catch(updateError => {
            console.error(`Error updating chunk ${chunk.tracking_id}:`, updateError);
            // Attempt to create the chunk if update fails
            console.log(`Attempting to create chunk ${chunk.tracking_id} as fallback`);
            return trieve?.createChunk({ ...chunk, upsert_by_tracking_id: true })
              .catch(createError => {
                console.error(`Fallback create also failed for chunk ${chunk.tracking_id}:`, createError);
                return { error: createError, chunkId: chunk.tracking_id };
              });
          });
        });

        // Wait for all update operations to complete
        await Promise.all(updatePromises.filter(Boolean));
      }

      // 5C: Handle deletes (use Promise.all for batch processing)
      const chunksToDelete = Array.from(existingChunksMap.keys());
      if (chunksToDelete.length > 0 && trieve) {
        console.log(`Deleting ${chunksToDelete.length} obsolete chunks for document ${fileId}`);
        const deletePromises = chunksToDelete.map(chunkTrackingId => {
          // Make a non-null copy of trieve for the promise
          const trieverSDK = trieve;
          if (!trieverSDK) {
            console.error(`Trieve SDK is null when trying to delete chunk ${chunkTrackingId}`);
            return Promise.resolve({ error: "Trieve SDK is null", chunkId: chunkTrackingId });
          }

          return trieverSDK.deleteChunkByTrackingId({
            trackingId: chunkTrackingId // Ensure correct field name (camelCase)
          })
          .catch(deleteError => {
            console.error(`Error deleting chunk ${chunkTrackingId}:`, deleteError);
            return { error: deleteError, chunkId: chunkTrackingId };
          });
        });

        // Wait for all delete operations to complete
        await Promise.all(deletePromises);
      }

      // Step 6: Verification and final status
      const finalStatus = {
        status: "success",
        documentId: fileId,
        operations: {
          created: chunksToCreate.length,
          updated: chunksToUpdate.length,
          deleted: chunksToDelete.length,
          skipped: chunkOperations.filter(op => op.operation === 'skip').length,
          total: paragraphs.length
        },
        timestamp: new Date().toISOString()
      };

      console.log(`Trieve upsert completed for document ${fileId}:`, finalStatus);

      // Clear the scheduled job ID from the files table upon successful completion
      try {
        // Use updateAnyField instead of internalClearScheduledJobId to avoid ID type issues
        await ctx.runMutation(api.utils.fieldUtils.updateAnyField, {
          tableName: "files",
          documentId: fileId.toString(), // Convert to string for updateAnyField
          field: "trieveIndexingJobId",
          value: null, // Use null instead of undefined to ensure the value is properly serialized
          updateTimestamp: false // Don't update the timestamp for this operation
        });
        console.log(`[Debounce Internal Call] Cleared Trieve indexing job ID for file ${fileId}`);
      } catch (clearError) {
        console.error(`[Debounce Internal Call] Failed to clear Trieve indexing job ID for file ${fileId}:`, clearError);
        // Just log the error, don't fail the whole operation
      }

      return finalStatus;

    } catch (error) {
      // Use the standardized error handler
      return handleTrieveError("upsert", fileId, error);
    }
  },
});

/**
 * Action to perform a complete reindex of a document in Trieve
 *
 * This action:
 * 1. Fetches the document content if not provided
 * 2. Calls trieveUpsertDocumentChunks to update Trieve
 * 3. Verifies the reindexing was successful
 * 4. Provides detailed status information
 */
export const performTrieveReindex = action({
  args: {
    fileId: v.id('files'),
    title: v.optional(v.string()), // Allow overriding fetched title
    content: v.optional(v.string()), // Allow overriding fetched content
    docType: v.optional(v.string()), // Allow overriding fetched docType
    forceDelete: v.optional(v.boolean()), // Option to delete group first
  },
  // Define return type in handler signature
  handler: async (ctx: ActionCtx, args): Promise<{
    status: "scheduled" | "skipped" | "error";
    documentId: Id<"files">;
    message?: string;
    reason?: string;
    error?: string;
    timestamp?: string;
  }> => {
    const { fileId, title: argTitle, content: argContent, docType: argDocType, forceDelete } = args;

    try {
      console.log(`Starting Trieve reindex process for document ID: ${fileId}`);

      // Step 1: If force delete is set, delete the existing group and chunks first
      if (forceDelete && isTrieveConfigured() && trieve) {
        try {
          console.log(`Force delete requested for ${fileId}, deleting group and chunks first`);
          const trDataset = process.env.TRIEVE_DATASET_ID;
          if (!trDataset) {
            throw new Error("Trieve Dataset ID is not configured");
          }

          await trieve.deleteGroupByTrackingId({
            tracking_id: fileId, // Use snake_case if SDK expects it
            trackingId: fileId,  // Use camelCase if SDK expects it
            trDataset: trDataset,
            deleteChunks: true
          });
          console.log(`Successfully deleted existing group and chunks for ${fileId}`);
        } catch (deleteError) {
          console.warn(`Error during force delete for ${fileId} (might not exist):`, deleteError);
          // Continue even if delete fails
        }
      }

      // Step 2: Fetch document details from Convex using public queries
      // Fetch the main file record using the public query from files.ts
      const fileRecord = await ctx.runQuery(api.files.files.getFileRecordBasic, { fileId });
      if (!fileRecord) {
        throw new ConvexError(`File record not found: ${fileId}`);
      }

      const finalDocType = argDocType ?? fileRecord.docType;

      // Call the public query wrapper using runQuery
      const { category: fetchedCategory, content: fetchedContent } = await ctx.runQuery(api.files.files.getSubTableDataPublic, { fileId, docType: finalDocType });


      const finalTitle = argTitle ?? fileRecord.title ?? "Untitled Document";
      const finalContent = argContent ?? fetchedContent ?? "";
      const finalCategory = fetchedCategory ?? null; // Use fetched category if available
      const finalShortDescription = fileRecord.short_description ?? null;

      // Ensure we have content to ingest
      if (!finalContent || finalContent.trim().length === 0) {
        console.warn(`Document ${fileId} has empty content. Skipping re-index.`);
        return { status: "skipped", documentId: fileId, reason: "empty_content", message: "Skipped: empty content" };
      }

      // Step 3: Schedule the upsert action using the direct fileId approach
      console.log(`Scheduling trieveUpsertDocumentChunks action for Trieve re-ingestion: ${fileId}`);
      await ctx.scheduler.runAfter(TRIEVE_OPERATION_DELAY, api.actions.trieveActions.trieveUpsertDocumentChunks, {
        fileId: fileId
      });

      console.log(`Successfully scheduled re-index for document ID: ${fileId} in Trieve`);
      return {
        status: "scheduled",
        documentId: fileId,
        message: `Document ${fileId} successfully scheduled for re-index in Trieve`,
      };

    } catch (error: unknown) {
      console.error(`Error in performTrieveReindex for document ${fileId}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        status: "error",
        documentId: fileId,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      };
    }
  },
});

/**
 * Manual tool to trigger a reindex of all documents of a specific type
 * Useful for debugging or recovering from indexing failures
 */
// Define the return type for the action handler
type ReindexAllResult = {
  status: "success" | "error";
  message: string;
  docType: string;
  limit: number;
  processedCount: number;
  scheduledCount: number;
  errorCount: number;
};

// Define a basic type for the file records fetched - ensure title is optional or handled
type FileToReindex = {
  _id: Id<"files">;
  title: string | null;
  docType: string;
};

export const reindexAllDocumentsOfType = action({
  args: {
    // Use the specific union type from the schema for docType validation
    docType: v.union(
      v.literal('KNOWLEDGE_BASE'),
      v.literal('MEETING_NOTES'),
      v.literal('CONTRACT'),
      v.literal('DOCUMENT'),
      v.literal('BILL')
    ),
    limit: v.optional(v.number()), // Optional limit for how many to reindex
    skipExisting: v.optional(v.boolean()), // Option to skip already indexed (not implemented yet)
  },
  handler: async (ctx, args): Promise<ReindexAllResult> => { // Add explicit return type
    const { docType, limit = 100 } = args; // Default limit

    console.log(`Starting reindex of all documents with type: ${docType} (limit: ${limit})`);

    try {
      // Fetch documents of the specified type up to the limit using public queries
      let filesToReindex: FileToReindex[] = [];

      // Use the correct public list queries based on docType
      if (docType === 'KNOWLEDGE_BASE') {
         const result = await ctx.runQuery(api.files.files.listKnowledgeBaseArticles, {
           paginationOpts: {
             numItems: limit,
             cursor: null
           }
         });
         // Ensure proper typing when mapping the results
         filesToReindex = result.page.map((f) => ({
           _id: f._id as Id<"files">,
           title: f.title ?? null,
           docType: f.docType
         }));
      } else if (docType === 'MEETING_NOTES') {
         // Use searchMeetingNotes with empty query to list all
         const result = await ctx.runQuery(api.files.files.searchMeetingNotes, {
           searchQuery: "",
           pagination: {
             numItems: limit, // Use numItems for compatibility with both schemas
             cursor: null
           }
         });
         // Access the meetingNotes array from the response
         filesToReindex = result.meetingNotes.map((f: any) => ({
           _id: f._id as Id<"files">,
           title: f.title ?? null,
           docType: f.docType
         }));
      }
      // Add else if blocks for other types (CONTRACT, DOCUMENT, BILL) using their respective list queries if they exist

      if (!filesToReindex || filesToReindex.length === 0) {
        return {
          status: "success",
          message: `No documents found with type ${docType} to reindex.`,
          docType,
          limit,
          processedCount: 0,
          scheduledCount: 0,
          errorCount: 0,
        };
      }

      console.log(`Found ${filesToReindex.length} documents of type ${docType} to reindex.`);

      let scheduledCount = 0;
      let errorCount = 0;

      // Schedule reindex for each document
      const reindexPromises = filesToReindex.map(file => {
        return ctx.scheduler.runAfter(TRIEVE_OPERATION_DELAY, api.actions.trieveActions.performTrieveReindex, {
          fileId: file._id,
          // Pass basic info, let performTrieveReindex fetch the rest
          title: file.title ?? undefined, // Pass title if available
          docType: file.docType,
          forceDelete: false // Default to not force delete during bulk reindex
        })
        .then(() => scheduledCount++)
        .catch(scheduleError => {
          console.error(`Failed to schedule reindex for file ${file._id}:`, scheduleError);
          errorCount++;
        });
      });

      // Wait for all promises to complete
      await Promise.all(reindexPromises);

      return {
        status: "success",
        message: `Scheduled reindex for ${scheduledCount} out of ${filesToReindex.length} ${docType} documents. ${errorCount} errors occurred during scheduling.`,
        docType,
        limit,
        processedCount: filesToReindex.length,
        scheduledCount,
        errorCount,
      };

    } catch (error) {
      console.error(`Error during bulk reindex for type ${docType}:`, error);
      return {
        status: "error",
        message: `Failed to reindex documents of type ${docType}: ${error instanceof Error ? error.message : String(error)}`,
        docType,
        limit,
        processedCount: 0,
        scheduledCount: 0,
        errorCount: 1, // Indicate the main query/loop failed
      };
    }
  },
});

/**
 * Action to update a document in Trieve by deleting and re-ingesting
 *
 * This is a consolidated version of the functionality from updateDocument.ts
 * that uses our shared utility functions for consistency.
 */
export const updateDocument = action({
  args: {
    fileId: v.id('files'),
    forceDelete: v.optional(v.boolean())
  },
  handler: async (ctx, args): Promise<{
    status: "scheduled" | "error";
    documentId: Id<"files">;
    message?: string;
    reason?: string;
    error?: string;
    timestamp?: string;
  }> => {
    const { fileId, forceDelete = true } = args;

    console.log(`Starting Trieve document update process for document ID: ${fileId}`);

    try {
      // Check if Trieve is configured
      if (!isTrieveConfigured() || !trieve) {
        console.warn("Trieve is not configured. Skipping update.");
        return {
          status: "scheduled",
          documentId: fileId,
          message: "Skipped: Trieve not configured"
        };
      }

      // Use the utility to prepare document data
      // We'll use runQuery to fetch data since we're in an action
      const fileRecord = await ctx.runQuery(api.files.files.getFileRecordBasic, { fileId });
      if (!fileRecord) {
        throw new ConvexError(`File record not found: ${fileId}`);
      }

      // Get content data from the subTable using the existing query
      const { category, content } = await ctx.runQuery(api.files.files.getSubTableDataPublic, {
        fileId,
        docType: fileRecord.docType
      });

      // Construct the document data
      const docData = {
        id: fileId,
        title: fileRecord.title ?? "Untitled Document",
        short_description: fileRecord.short_description ?? null,
        content: content ?? "",
        category: category ?? null,
        docType: fileRecord.docType,
        projectId: fileRecord.parentEntityId ? fileRecord.parentEntityId as Id<'projects'> : null,
        orgId: "PLACEHOLDER_ORG_ID",
        createdAt: fileRecord._creationTime,
      };

      // Ensure we have content to ingest
      if (!docData.content || docData.content.trim().length === 0) {
        console.warn(`Document ${fileId} has empty content. Skipping update.`);
        return {
          status: "scheduled",
          documentId: fileId,
          reason: "empty_content",
          message: "Skipped: empty content"
        };
      }

      // Step 1: If forceDelete is true, remove existing chunks/group
      if (forceDelete) {
        console.log(`Force delete requested for ${fileId}, deleting group and chunks first`);
        try {
          const trDataset = process.env.TRIEVE_DATASET_ID;
          if (!trDataset) {
            throw new Error("Trieve Dataset ID is not configured");
          }

          await trieve.deleteGroupByTrackingId({
            tracking_id: fileId,
            trackingId: fileId,
            trDataset: trDataset,
            deleteChunks: true
          });
          console.log(`Successfully deleted existing group and chunks for ${fileId}`);
        } catch (deleteError) {
          console.warn(`Error during force delete for ${fileId} (might not exist):`, deleteError);
          // Continue even if delete fails
        }
      }

      // Step 2: Schedule the re-ingestion action
      console.log(`Scheduling Trieve upsert for document ${fileId}`);
      await ctx.scheduler.runAfter(TRIEVE_OPERATION_DELAY, api.actions.trieveActions.trieveUpsertDocumentChunks, {
        fileId: fileId
      });

      console.log(`Successfully scheduled update for document ID: ${fileId} in Trieve`);
      return {
        status: "scheduled",
        documentId: fileId,
        message: `Document ${fileId} successfully scheduled for update in Trieve`
      };
    } catch (error) {
      console.error(`Error in updateDocument for document ${fileId}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        status: "error",
        documentId: fileId,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      };
    }
  },
});
