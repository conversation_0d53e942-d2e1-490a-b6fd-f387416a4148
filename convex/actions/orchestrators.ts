"use node";

import { z } from 'zod';
import { zAction } from '../functions';
import { api, internal } from '../_generated/api'; // Import internal
import { ConvexError } from 'convex/values';
import { Id } from '../_generated/dataModel';
import { ActionCtx } from '../_generated/server';

type GenerateAndStoreFieldResult = {
  success: boolean;
  message: string;
  generatedText?: string;
};

/**
 * Orchestrates the generation of a field value using AI and stores it in the database.
 *
 * This action:
 * 1. Takes a document ID, source text, target field, and prompt slug
 * 2. Calls the AI generation action to generate the field value
 * 3. Updates the document with the generated value
 * 4. Clears the associated job ID from meeting_notes if applicable
 * 5. Returns the result of the operation
 *
 * @example
 * const result = await convex.runAction(api.actions.orchestrators.generateAndStoreField, {
 *   tableName: "organizations",
 *   documentId: "abc123",
 *   sourceText: "Example text to process",
 *   targetFieldName: "description",
 *   promptSlug: "org-description"
 * });
 *
 * @throws {ConvexError} If the field update fails
 */
export const generateAndStoreField = zAction({
  args: {
    tableName: z.string().min(1, "Table name is required"),
    documentId: z.string().min(1, "Document ID is required"),
    sourceText: z.string(),
    targetFieldName: z.string().min(1).refine(name => !name.startsWith('_'), {
      message: "Target field cannot be an internal field starting with '_'",
    }),
    promptSlug: z.string().min(1, "Prompt slug is required"),
    jobIdFieldName: z.string().optional(), // Added field for job ID field name
  },
  output: z.object({
    success: z.boolean(),
    message: z.string(),
    generatedText: z.string().optional(),
  }),
  handler: async (
    ctx: ActionCtx,
    args: {
      tableName: string;
      documentId: string;
      sourceText: string;
      targetFieldName: string;
      promptSlug: string;
      jobIdFieldName?: string; // Added to match the Zod schema
    }
  ): Promise<GenerateAndStoreFieldResult> => {
    console.log(`Orchestrating field update for ${args.tableName} ${args.documentId}, target: ${args.targetFieldName}`);
    console.log(`This action was scheduled and is now executing. Prompt: ${args.promptSlug}, Source text length: ${args.sourceText.length} chars`);
    console.log(`If you're seeing this message, it means the debouncing mechanism allowed this job to execute.`);

    let generatedText: string | undefined = undefined; // Define generatedText outside try block

    try {
      // 1. Generate the text using AI
      generatedText = await ctx.runAction(api.actions.aiUtils.generateDescriptionWithPrompt, {
        sourceText: args.sourceText,
        promptSlug: args.promptSlug,
      });

      // 2. Update the document with the generated text
      const updateResult = await ctx.runMutation(api.utils.fieldUtils.updateAnyField, {
        tableName: args.tableName,
        documentId: args.documentId,
        field: args.targetFieldName,
        value: generatedText,
        updateTimestamp: true,
      });

      if (!updateResult.success) {
        throw new ConvexError({
          message: `Failed to update ${args.targetFieldName} in ${args.tableName}: ${updateResult.message}`,
          code: "UPDATE_FAILED",
        });
      }

      // Clear the job ID field if one was provided
      if (args.jobIdFieldName) {
        try {
          // We need to handle the ID differently since it's coming in as a string
          // but the validator expects an Id<any>
          // For now, we'll use a workaround by directly updating the field
          // This avoids the validator issue in internalClearScheduledJobId

          // Use the updateAnyField mutation which already handles string IDs
          await ctx.runMutation(api.utils.fieldUtils.updateAnyField, {
            tableName: args.tableName,
            documentId: args.documentId,
            field: args.jobIdFieldName,
            value: null, // Use null - undefined gets stripped from the object
            updateTimestamp: false // Don't update the timestamp for this operation
          });

          console.log(`[Debounce Internal Call] Cleared ${args.jobIdFieldName} for ${args.tableName} ${args.documentId}`);
        } catch (clearError: any) {
          console.error(`[Debounce Internal Call] Failed to clear ${args.jobIdFieldName} for ${args.tableName} ${args.documentId}:`, clearError);
          // Just log the error, don't fail the whole operation
        }
      }

      return {
        success: true,
        message: `Successfully generated and stored ${args.targetFieldName} in ${args.tableName} ${args.documentId}`,
        generatedText, // Return the generated text
      };
    } catch (error: any) { // Added type annotation for error
      console.error(`Error in generateAndStoreField for ${args.tableName} ${args.documentId}:`, error);

      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error occurred",
        generatedText, // Still return generatedText if generation succeeded but update failed
      };
    }
  },
});
