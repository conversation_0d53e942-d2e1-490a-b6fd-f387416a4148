"use node";
import { v } from "convex/values";
import { internal, api } from "../_generated/api";
import { action } from "../_generated/server";
import { Id } from "../_generated/dataModel";

import { BoxClient, BoxDeveloperTokenAuth } from 'box-typescript-sdk-gen';

// Helper function to extract file extension
function getFileExtension(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
}

// Helper function to get content type from file extension
function getContentTypeFromExtension(extension: string): string {
  const contentTypeMap: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt': 'text/plain',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'gdoc': 'application/vnd.google-apps.document',
    'gsheet': 'application/vnd.google-apps.spreadsheet',
    'gslides': 'application/vnd.google-apps.presentation',
  };
  
  return contentTypeMap[extension] || 'application/octet-stream';
}

export const syncFromBox = action({
  args: { clientId: v.id("clients") },
  handler: async (ctx, args) => {
    const integration = await ctx.runQuery(api.integrations.integrations.get, {});

    if (!integration || !integration.user_config?.developerToken) {
      throw new Error("Box integration not configured or developer token is missing.");
    }

    const auth = new BoxDeveloperTokenAuth({ token: integration.user_config.developerToken });
    const client = new BoxClient({ auth });

    async function listAllFiles(folderId = '0') {
      const response = await client.folders.getFolderItems(folderId);
      if (response.entries) {
        for (const file of response.entries) {
          await processFile(file);
        }
      }
    }

    async function processFile(file: any) {
      if (file.type === 'file') {
        try {
          // Get detailed file information from Box API
          const fileDetails = await client.files.getFileById(file.id);
          
          // Extract file metadata
          const fileName = fileDetails.name || file.name;
          const fileExtension = getFileExtension(fileName);
          const fileSize = fileDetails.size || 0;
          const contentType = getContentTypeFromExtension(fileExtension);
          
          console.log(`Processing file: ${fileName} (${fileSize} bytes, .${fileExtension})`);
          
          // Check if this Box file already exists in our system
          const existingFile = await ctx.runQuery(internal.files.files.getFileByBoxId, {
            boxFileId: file.id.toString(),
          });
          
          // If file exists, delete it and all related records
          if (existingFile) {
            console.log(`Deleting existing file: ${fileName} (Box ID: ${file.id})`);
            await ctx.runMutation(internal.files.files.deleteFileAndRelationships, {
              fileId: existingFile._id as Id<"files">,
            });
          }
          
          const fileStream = await client.downloads.downloadFile(file.id);
          
          if (!fileStream) {
            console.warn(`Failed to download file: ${fileName}`);
            return;
          }
          
          // Convert Node.js Readable stream to Buffer, then to Blob
          const chunks: Buffer[] = [];
          
          return new Promise<void>((resolve, reject) => {
            fileStream.on('data', (chunk: Buffer) => {
              chunks.push(chunk);
            });
            
            fileStream.on('end', async () => {
              try {
                // Combine all chunks into a single Buffer
                const buffer = Buffer.concat(chunks);
                
                // Verify file size matches
                const actualSize = buffer.length;
                if (actualSize !== fileSize && fileSize > 0) {
                  console.warn(`File size mismatch for ${fileName}: expected ${fileSize}, got ${actualSize}`);
                }
                
                // Create a Blob from the buffer with correct content-type
                const blob = new Blob([buffer], { type: contentType });

                // Store the file in Convex
                const storageId = await ctx.storage.store(blob);

                // Save the file with complete metadata to the database
                await ctx.runMutation(internal.files.files.internalCreateDocument, {
                  clientId: args.clientId,
                  storageId: storageId,
                  fileName: fileName,
                  fileExtension: fileExtension,
                  fileSize: actualSize, // Use actual size from downloaded file
                  boxFileId: file.id.toString(), // Save the Box file ID
                });
                
                console.log(`Successfully synced: ${fileName}`);
                resolve();
              } catch (error) {
                reject(new Error(`Error uploading file ${fileName}: ${JSON.stringify(error)}`));
              }
            });
            
            fileStream.on('error', (error: Error) => {
              reject(new Error(`Error downloading file ${fileName}: ${error.message}`));
            });
          });
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          throw error;
        }
      }
    }

    await listAllFiles();
    
    console.log("Syncing from Box for client:", args.clientId);
    return { success: true };
  },
});
