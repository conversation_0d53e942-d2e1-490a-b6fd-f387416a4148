"use node";

import { z } from 'zod';
import { zAction } from '../functions';
import { api } from '../_generated/api';
import { ConvexError } from 'convex/values';
import { google } from "@ai-sdk/google";
import { generateText } from "ai";
import { v } from "convex/values";
import { ActionCtx } from '../_generated/server';
import { Id, Doc } from '../_generated/dataModel'; // Import Doc type

/**
 * generateDescriptionWithPrompt action
 * 
 * @description
 * Generates a description using AI based on source text and a prompt.
 * This action:
 * 1. Takes source text and a prompt slug
 * 2. Fetches the prompt from the prompts table
 * 3. Uses the AI to generate a description
 * 4. Returns the generated description
 *
 * @example
 * const result = await convex.runAction(api.aiUtils.generateDescriptionWithPrompt, {
 *   sourceText: "Example text to describe",
 *   promptSlug: "description-prompt"
 * });
 * console.log(result); // Generated description
 * 
 * @throws {ConvexError} If the prompt is not found or AI generation fails
 */
export const generateDescriptionWithPrompt = zAction({
  args: {
    sourceText: z.string().min(1, "Source text is required"),
    promptSlug: z.string().min(1, "Prompt slug is required"),
  },
  output: z.string(),
  handler: async (
    ctx: ActionCtx,
    args: { sourceText: string; promptSlug: string }
  ): Promise<string> => {
    // 1. Fetch the specified prompt
    // Corrected type annotation
    const promptRecord: Doc<"prompts"> | null = await ctx.runQuery(api.prompts.getPromptBySlug, {
      slug: args.promptSlug,
    });

    if (!promptRecord?.prompt_text) {
      console.error(`Prompt not found for slug: ${args.promptSlug}`);
      throw new ConvexError({
        message: `Required prompt not found: ${args.promptSlug}`,
        code: "PROMPT_NOT_FOUND",
        data: { slug: args.promptSlug } as const,
      });
    }

    // 2. Initialize AI Model
    const model = google("gemini-2.5-flash-preview-04-17");

    // 3. Craft final prompt and call AI
    const finalPrompt: string = `${promptRecord.prompt_text}\n\n${args.sourceText}`;
    console.log(`Generating description with prompt: ${args.promptSlug}`);

    try {
      const { text: aiResponse } = await generateText({
        model,
        prompt: finalPrompt,
      });

      return aiResponse.trim();
    } catch (error) {
      console.error("AI generation error:", error);
      throw new ConvexError({
        message: "Failed to generate description with AI",
        code: "AI_GENERATION_FAILED",
        data: { error: String(error) } as const,
      });
    }
  },
});
