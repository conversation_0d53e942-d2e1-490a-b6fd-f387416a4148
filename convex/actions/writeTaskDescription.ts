"use node";

import { ConvexError } from "convex/values";
import { z } from "zod";
import { google } from "@ai-sdk/google";
import { generateText } from "ai";
import { zAction } from "../functions"; // Assuming zAction handles Zod validation
import { api } from "../_generated/api";
import { Id, Doc, TableNames } from "../_generated/dataModel"; 
import { zid } from "convex-helpers/server/zod"; 
// NOTE: extractFullMentionsFromText is no longer used here
import { TypedMention, extractFullMentionsFromText } from "../utils/mentions"; 
import { v } from "convex/values"; // Import Convex validators
import { action, ActionCtx } from "../_generated/server";

// Define UserDetail interface locally if not imported elsewhere
interface UserDetail {
  id: Id<'users'>;
  name: string;
  type: 'user';
}

/* -------------------------------------------------------------------
   Zod <PERSON> (Keep existing ones for writeTaskDescription)
   ------------------------------------------------------------------- */
const WriteTaskDescriptionArgs = z.object({
  taskName: z.string().min(1, "Task name is required"),
});
const WriteTaskDescriptionReturns = z.string();

// Define the type for the prompt record
interface PromptRecord {
  _id: Id<"prompts">;
  _creationTime?: number;
  prompt_text?: string;
  name?: string;
  updated_at?: number;
  immutable_slug?: string;
}

/* -------------------------------------------------------------------
   Action: writeTaskDescription (Keep existing)
   ------------------------------------------------------------------- */
export const writeTaskDescription = zAction({
  args: {
    taskName: z.string().min(1, "Task name is required"),
  },
  output: z.string(),
  handler: async (ctx, args): Promise<string> => {
    try {
      const { taskName } = args;

      const promptRecord = await ctx.runQuery(api.prompts.getPromptBySlug, { slug: "quick-task" }) as PromptRecord | null;
      if (!promptRecord || !promptRecord.prompt_text) throw new ConvexError({ message: "Required prompt not found", code: "PROMPT_NOT_FOUND" });
      
      const promptText: string = promptRecord.prompt_text;
      const model = google("gemini-2.5-flash-preview-04-17");
      const finalPrompt: string = `${promptText}\n\n${taskName}`;
      
      console.log("Generating task description with prompt:", { promptSlug: "quick-task", taskName });
      const { text: aiResponse } = await generateText({ model, prompt: finalPrompt });
      const modifiedDescription: string = "This Description Written by AI: " + aiResponse.trim();
      
      // No need for Zod parse on output if using v.string()
      // const taskDescription = WriteTaskDescriptionReturns.parse(modifiedDescription); 
      const taskDescription: string = modifiedDescription;

      console.log("Generated task description:", { length: taskDescription.length, preview: taskDescription.substring(0, 100) + (taskDescription.length > 100 ? "..." : "") });
      return taskDescription;
    } catch (error) {
      if (error instanceof ConvexError) throw error;
      throw new ConvexError({ 
        message: error instanceof Error ? error.message : "Failed to generate task description", 
        code: "AI_GENERATION_ERROR", 
        data: { details: error instanceof Error ? error.message : String(error) } 
      });
    }
  },
});

/* -------------------------------------------------------------------
   Action: generateTaskNameAndDescription (REVISED WITH v)
   ------------------------------------------------------------------- */

// Schema for the action's return value (Still useful for defining structure)
const GenerateTaskNameAndDescriptionReturns = z.object({
  success: z.boolean(),
  message: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  driver: z.union([zid('users'), z.null()]).optional(),
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
  informed: z.array(z.union([zid('users'), zid('teams')])).optional(),
});

// Define interfaces for our types
interface TaskGenerationResult {
  success: boolean;
  message: string;
  name?: string;
  description?: string;
  driver: Id<'users'> | null;
  contributors: Array<Id<'users'> | Id<'teams'>>;
  informed: Array<Id<'users'> | Id<'teams'>>;
}

interface ParsedAIResult {
  name?: string;
  description?: string;
  driver?: Id<'users'> | null;
  contributors?: Array<Id<'users'> | Id<'teams'>>;
  informed?: Array<Id<'users'> | Id<'teams'>>;
}

// Type helper for the structure of driver/contributor args
type DriverArg = { id: Id<'users'>; type: 'user' } | null;
type ContributorArg = { id: Id<'users'> | Id<'teams'>; type: 'user' | 'team' };

// Add this function after the findPromptBySlug function
// Helper function to validate user IDs against the database
async function validateUserId(ctx: ActionCtx, userId: Id<'users'> | string): Promise<Id<'users'> | null> {
  try {
    // Try to fetch the user by ID
    const user = await ctx.runQuery(api.users.get, { userId: userId as Id<'users'> });
    if (user) {
      return userId as Id<'users'>;
    }
  } catch (e) {
    console.error("Failed to validate user ID:", userId, e);
  }
  return null;
}

// Helper function to validate team IDs
async function validateTeamId(ctx: ActionCtx, teamId: Id<'teams'> | string): Promise<Id<'teams'> | null> {
  try {
    const team = await ctx.runQuery(api.teams.getTeam, { id: teamId as Id<'teams'> });
    if (team) {
      return teamId as Id<'teams'>;
    }
  } catch (e) {
    console.error("Failed to validate team ID:", teamId, e);
  }
  return null;
}

// For backward compatibility with existing usages
export const generateTaskNameAndDescription = action({
  args: {
    userInput: v.string(),
    driver: v.optional(v.union(v.id("users"), v.null())),
    contributors: v.optional(v.array(v.union(v.id("users"), v.id("teams"))))
  },
  handler: async (ctx, args) => {
    // Debug the input arguments with more detail
    console.log("=============================================");
    console.log("TASK ASSIGNMENT DEBUGGING");
    console.log("=============================================");
    console.log("RAW INPUT ARGS:", {
      userInput: args.userInput,
      driver: args.driver,
      contributors: args.contributors
    });
    
    const userInput: string = args.userInput;
    
    // Process driver ID - now directly an Id<"users"> or null
    let driverId: Id<"users"> | null = null;
    if (args.driver) {
      console.log("Driver ID (from people.user_id):", args.driver);
      
      // Validate driver ID exists - using a safer approach that doesn't throw
      try {
        // First try a direct get to check if it's valid
        const userExists = await ctx.runQuery(api.users.currentUserExists, { userId: args.driver });
        if (userExists) {
          driverId = args.driver;
          console.log("Driver ID validated successfully:", driverId);
        } else {
          console.log("Driver ID not found in users table:", args.driver);
        }
      } catch (e) {
        console.error("Error validating driver:", e);
        // Continue with null driverId rather than throwing
      }
    } else {
      console.log("No driver provided in args");
    }
    
    // Process contributor IDs - now directly an array of Id<"users"> | Id<"teams">
    const validUserIds: Id<"users">[] = [];
    const validTeamIds: Id<"teams">[] = [];
    
    if (args.contributors && Array.isArray(args.contributors)) {
      for (const contribId of args.contributors) {
        // Try to validate as user first using the safer approach
        try {
          const userExists = await ctx.runQuery(api.users.currentUserExists, { userId: contribId as Id<'users'> });
          if (userExists) {
            validUserIds.push(contribId as Id<'users'>);
            console.log(`Validated user contributor: ${contribId}`);
            continue;
          }
        } catch (e) {
          // Not a valid user, try teams next
        }
        
        // Try to validate as team using a safer approach
        try {
          const teamExists = await ctx.runQuery(api.teams.teamExists, { id: contribId as Id<'teams'> });
          if (teamExists) {
            validTeamIds.push(contribId as Id<'teams'>);
            console.log(`Validated team contributor: ${contribId}`);
            continue;
          }
        } catch (e) {
          // Not a valid team either
        }
        
        console.log(`ID ${contribId} is not a valid user or team ID`);
      }
    }
    
    // Combined validated contributor IDs
    const contributorIds = [...validUserIds, ...validTeamIds];
    
    // Summary of validation
    console.log("VALIDATION SUMMARY:", { validUserIds, validTeamIds });
    
    // Debug the processed IDs
    console.log("PROCESSED IDs:", {
      userInput,
      driverId,
      contributorIds
    });
    
    try {
      // 1. Fetch details for the provided driver and contributors
      const userIdsToFetch: Id<'users'>[] = [];
      if (driverId) {
        userIdsToFetch.push(driverId);
      }
      
      // Add valid user IDs from contributors
      userIdsToFetch.push(...validUserIds);
      
      // Fetch user and team details
      let userDocs: Doc<"users">[] = [];
      let teamDocs: Doc<"teams">[] = [];
      
      if (userIdsToFetch.length > 0) {
        try {
          userDocs = await Promise.all(
            userIdsToFetch.map(id => ctx.runQuery(api.users.get, { userId: id }).catch(() => null))
          ).then(results => results.filter(Boolean) as Doc<"users">[]);
        } catch (e) {
          console.error("Error fetching user documents:", e);
        }
      }
      
      if (validTeamIds.length > 0) {
        try {
          teamDocs = await Promise.all(
            validTeamIds.map(id => ctx.runQuery(api.teams.getTeam, { id }).catch(() => null))
          ).then(results => results.filter(Boolean) as Doc<"teams">[]);
        } catch (e) {
          console.error("Error fetching team documents:", e);
        }
      }
      
      console.log("FETCHED USER DOCS:", userDocs);
      console.log("FETCHED TEAM DOCS:", teamDocs);

      // Proceed with AI generation
      let promptText: string;
      try {
        const promptRecord = await ctx.runQuery(api.prompts.getPromptBySlug, { slug: "quick-task" }) as PromptRecord | null;
        if (promptRecord?.prompt_text) {
          promptText = promptRecord.prompt_text;
          console.log("Using prompt from database: quick-task");
        } else {
          // Throw error if prompt is not found or empty
          console.error("Prompt 'quick-task' not found in database or is empty.");
          throw new ConvexError({ 
            message: "Required prompt 'quick-task' not found or is empty in the database.", 
            code: "PROMPT_NOT_FOUND" 
          });
        }
      } catch (e) {
        // Re-throw any error during prompt fetching
        console.error("Error fetching prompt 'quick-task':", e);
        const errorMessage = e instanceof Error ? e.message : String(e);
        throw new ConvexError({ 
          message: `Failed to fetch required prompt 'quick-task': ${errorMessage}`, 
          code: "PROMPT_FETCH_ERROR",
          data: { details: errorMessage }
        });
      }
      
      const model = google("gemini-2.5-flash-preview-04-17");
      
      console.log("Generating task details with existing prompt:", {
        promptSlug: "quick-task",
        userInput
      });
      
      // Append driver and contributor information to the prompt
      const finalPrompt = `${promptText}

Input Task: "${userInput}"


Response:`;
      
      const { text: aiResponse } = await generateText({ model, prompt: finalPrompt });
      
      // Try to parse the AI response as JSON
      let aiResult: ParsedAIResult = { name: undefined, description: undefined };
      try {
        // First attempt: Look for JSON object in the response
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            aiResult = JSON.parse(jsonMatch[0]);
            console.log("Successfully parsed JSON from AI response");
          } catch (parseError) {
            console.log("Found JSON-like content but couldn't parse it:", parseError);
            // Continue to fallback parsing
          }
        } else {
          console.log("No JSON object found in AI response, using fallback parsing");
        }
        
        // Fallback parsing: Extract title and body if JSON parsing failed
        if (!aiResult.name || !aiResult.description) {
          // Look for a title (first line or something with ## or # prefix)
          const titleMatch = aiResponse.match(/^([^\n]+)/) || 
                            aiResponse.match(/^#+\s*([^\n]+)/m);
          
          if (titleMatch) {
            aiResult.name = titleMatch[1].replace(/^#+\s*/, '').trim();
          }
          
          // Everything else is the description
          let description = aiResponse;
          if (titleMatch && titleMatch[0]) {
            // Remove the title from the description
            description = description.replace(titleMatch[0], '').trim();
          }
          
          aiResult.description = description;
          console.log("Used fallback parsing for AI response");
        }
      } catch (e) {
        // If all parsing fails, use simple extraction
        console.log("Error parsing AI response:", e);
        aiResult = {
          name: userInput.substring(0, 40) + (userInput.length > 40 ? "..." : ""),
          description: aiResponse
        };
      }
      
      console.log("AI Result:", aiResult);
      
      // Format the final result
      const description = aiResult.description || aiResponse;
      // Check if we need to add HTML formatting
      const formattedDescription = description.includes('<') && description.includes('>')
        ? description  // Already has HTML tags
        : `<p>${description.split('\n\n').join('</p><p>')}</p>`;  // Add paragraph tags

      const finalResult: TaskGenerationResult = {
        success: true,
        message: "Task details generated",
        name: aiResult.name || userInput.substring(0, 40),
        description: `<em>This Description Written by AI:</em>\n\n${formattedDescription}`,
        driver: driverId,
        contributors: contributorIds,
        informed: []
      };
      
      console.log("Final task generation result:", finalResult);
      
      // Final validation
      console.log("FINAL VALIDATION PHASE");
      
      // Validate contributors for final output
      const validatedContributors = contributorIds;
      console.log("Validating contributors:", validatedContributors);
      
      // Validate informed list (AI generated)
      const validatedInformed: Array<Id<'users'> | Id<'teams'>> = [];
      if (aiResult.informed && Array.isArray(aiResult.informed)) {
        console.log("Validating informed:", aiResult.informed);
        
        for (const informedId of aiResult.informed) {
          if (typeof informedId === 'string') {
            // Validate as user or team - similar to contributors
            const userValid = await ctx.runQuery(api.users.currentUserExists, { 
              userId: informedId as Id<'users'> 
            }).catch(() => false);
            
            if (userValid) {
              validatedInformed.push(informedId as Id<'users'>);
              continue;
            }
            
            const teamValid = await ctx.runQuery(api.teams.teamExists, { 
              id: informedId as Id<'teams'> 
            }).catch(() => false);
            
            if (teamValid) {
              validatedInformed.push(informedId as Id<'teams'>);
            }
          }
        }
      }
      
      // Set the final values
      finalResult.driver = driverId;
      finalResult.contributors = validatedContributors;
      finalResult.informed = validatedInformed;
      
      console.log("FINAL RESULT:", { 
        driver: finalResult.driver, 
        contributors: finalResult.contributors,
        informed: finalResult.informed
      });
      
      return finalResult;
    } catch (error) {
      // Handle general errors gracefully
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error generating task details:", errorMessage);
      
      return {
        success: false,
        message: `Failed to generate task details: ${errorMessage}`,
        name: userInput.substring(0, 40),
        description: `<p>Failed to generate description.</p><p>Original input: ${userInput}</p>`,
        driver: null,
        contributors: [],
        informed: []
      };
    }
  }
});
