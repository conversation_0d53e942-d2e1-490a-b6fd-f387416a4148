"use node";

import { ConvexError } from "convex/values";
import { z } from "zod";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { zAction } from "../functions";
import { zid } from "convex-helpers/server/zod";
import { api } from "../_generated/api";
import { Id, TableNames } from "../_generated/dataModel";

/* -------------------------------------------------------------------
   Custom Error Type
   ------------------------------------------------------------------- */
/**
 * Custom error class used to wrap unknown/unexpected errors
 * encountered during AI categorization operations.
 */
class AICategorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AICategorizationError";
  }
}

/* -------------------------------------------------------------------
   Zod Schemas
   ------------------------------------------------------------------- */

/** 
 * Schema for a line item in preview mode (no _id needed)
 */
const PreviewLineItemSchema = z.object({
  merchant_name: z.string(),
  description: z.string(),
  amount: z.number(),
});

/** 
 * Schema for a line item with ID (for database updates)
 */
const DatabaseLineItemSchema = z.object({
  _id: zid("lineItems"),
  merchant_name: z.string(),
  description: z.string(),
  amount: z.number(),
});

/**
 * Combined schema that validates based on skipDatabaseUpdate flag
 */
const AICategorizeArgsSchema = z.object({
  lineItems: z.array(z.union([PreviewLineItemSchema, DatabaseLineItemSchema])),
  skipDatabaseUpdate: z.boolean().optional(),
});

/**
 * Schema for the structure returned by the AI model:
 * Array of categories matching the input line items order
 */
const CategoryMappingSchema = z.array(
  z.object({
    category: z.string()
  })
);

type CategoryMapping = z.infer<typeof CategoryMappingSchema>;

/**
 * Schema for the action's return payload:
 * - success: did the categorization process complete?
 * - categorized: number of items categorized
 * - mappings: the final category mapping
 */
const AICategorizeReturnsSchema = z.object({
  success: z.boolean(),
  categorized: z.number(),
  mappings: z.record(
    z.string(),
    z.object({
      category: z.string(),
    })
  ),
});

/* -------------------------------------------------------------------
   Category & Table Type
   ------------------------------------------------------------------- */
interface Category {
  _id: Id<"tags">;
  name: string;
  parent_id?: Id<"tags">;
}

type TagsTable = Extract<TableNames, "tags">;

/* -------------------------------------------------------------------
   Action: aiCategorize
   ------------------------------------------------------------------- */
/**
 * Categorize line items using AI, optionally updating the database.
 *
 * Modes:
 * 1. Preview Mode (skipDatabaseUpdate=true):
 *    - Returns AI-suggested categories only
 *    - Does NOT write to the database
 *
 * 2. Update Mode (skipDatabaseUpdate=false or undefined):
 *    - Returns AI-suggested categories
 *    - Updates line items in the DB with the selected categories
 *
 * @param lineItems           - Array of line items to categorize
 * @param skipDatabaseUpdate  - If true, DB updates are skipped (preview only)
 *
 * @returns {object} {
 *   success: boolean,
 *   categorized: number of items successfully categorized,
 *   mappings: key-value pairs of indices -> { category: "Parent: Child" }
 * }
 *
 * Possible errors (thrown as ConvexErrors):
 * - CATEGORY_CONFIG_NOT_FOUND: Missing "expense-categories" in DB
 * - EMPTY_CATEGORY_HIERARCHY: No valid categories found
 * - AI_GENERATION_ERROR: AI call failed
 * - MISSING_FALLBACK_CATEGORY: "Needs Review: AI Uncertain" not found in DB
 * - DB_UPDATE_ERROR: Problem occurred while persisting categories
 *
 * All other unknown errors are wrapped and thrown as AICategorizationError.
 */
export const aiCategorize = zAction({
  args: {
    lineItems: z.array(z.object({
      _id: zid("lineItems").optional(),
      merchant_name: z.string(),
      description: z.string(),
      amount: z.number(),
    })),
    skipDatabaseUpdate: z.boolean().optional()
  },
  output: AICategorizeReturnsSchema,
  handler: async (ctx, args) => {
    const { lineItems, skipDatabaseUpdate = false } = args;

    // For preview mode, strip out _id fields to avoid confusion
    const processedLineItems = skipDatabaseUpdate 
      ? lineItems.map(({ merchant_name, description, amount }) => ({
          merchant_name,
          description,
          amount
        }))
      : lineItems;

    // If we're going to update the DB, validate and fetch full line items
    let typedLineItems = processedLineItems;
    if (!skipDatabaseUpdate) {
      // If we're in update mode, we should require _id for all items
      // This simpler approach avoids the deep type instantiation error entirely
      const missingIds = processedLineItems.filter(item => !('_id' in item) || !item._id);
      
      if (missingIds.length > 0) {
        throw new ConvexError({
          message: "Missing _id for some line items",
          code: "MISSING_LINE_ITEM_IDS",
          data: {
            details: "In update mode, all line items must have an _id field",
            count: missingIds.length,
            examples: missingIds.slice(0, 3).map(item => ({
              merchant_name: item.merchant_name,
              description: item.description,
              amount: item.amount,
            })),
          },
        });
      }
      
      // Safe to cast as all items have been verified to have _id
      typedLineItems = processedLineItems.map(item => ({
        ...item,
        _id: ('_id' in item) ? item._id as Id<"lineItems"> : undefined as never,
      }));
    }

    try {
      /* -----------------------------------------------------------------
         1. Fetch & Validate Category Hierarchy
         ----------------------------------------------------------------- */
      // @ts-ignore - Bypass complex type instantiation
      const expenseCategoryType = await ctx.runQuery(api.tags.getTagTypeBySlug, {
        slug: "expense-categories",
      });
      if (!expenseCategoryType) {
        throw new ConvexError({
          message: "Failed to find expense categories configuration",
          code: "CATEGORY_CONFIG_NOT_FOUND",
          data: {
            details:
              "The 'expense-categories' tag type is missing. Please set up expense categories first.",
          },
        });
      }

      // Retrieve the hierarchical structure of expense categories.
      const categoriesHierarchy = await ctx.runQuery(api.tags.getHierarchy, {
        params: { tag_type: expenseCategoryType._id },
      });

      // Build a list of valid "ParentCategory: Subcategory" strings.
      const categories: string[] = [];
      if (Array.isArray(categoriesHierarchy) && categoriesHierarchy.length > 0) {
        categoriesHierarchy.forEach((parent: any) => {
          if (parent?.children?.length > 0) {
            parent.children.forEach((sub: any) => {
              // Ensure clean category strings with proper spacing
              const parentName = parent.name.trim();
              const subName = sub.name.trim();
              categories.push(`${parentName}: ${subName}`);
            });
          }
        });
      }

      console.log("Available categories:", {
        count: categories.length,
        categories,
        sample: categories.slice(0, 3)
      });

      if (categories.length === 0) {
        throw new ConvexError({
          message: "No expense categories available for AI categorization",
          code: "EMPTY_CATEGORY_HIERARCHY",
          data: {
            details:
              "The expense category hierarchy is empty or missing subcategories. " +
              "Each parent category must have at least one subcategory.",
            tagTypeId: expenseCategoryType._id,
          },
        });
      }

      /* -----------------------------------------------------------------
         2. Generate AI Suggestions (simplified for mapping)
         ----------------------------------------------------------------- */
      const formattedLineItems = typedLineItems.map((item, index) => ({
        index,
        merchant_name: item.merchant_name,
        description: item.description,
        amount: item.amount,
      }));

      let aiResult;
      try {
        aiResult = await generateObject({
          model: google("gemini-2.5-flash-preview-04-17", { structuredOutputs: true }),
          schema: z.array(z.object({
            category: z.string().describe('The category in "ParentCategory: Subcategory" format')
          })),
          prompt: `You are an expert expense categorizer. Your task is to categorize each expense into exactly one of these valid categories:

Valid Categories:
${categories.map(c => `- ${c}`).join("\n")}

Rules:
1. Each category MUST be in the exact format "ParentCategory: Subcategory" from the list above
2. The response must be an array of objects with a "category" field
3. Return exactly one category per input expense
4. Use the exact category strings from the list - no variations allowed

Here are the expenses to categorize:
${JSON.stringify(formattedLineItems, null, 2)}`,
        });

        console.log("AI Response:", aiResult.object);
      } catch (error) {
        console.error("AI Generation Error:", error);
        throw new ConvexError({
          message: "Failed to generate AI response",
          code: "AI_GENERATION_ERROR",
          data: {
            errorMessage: error instanceof Error ? error.message : String(error),
            lineItemCount: formattedLineItems.length,
          },
        });
      }

      /* -----------------------------------------------------------------
         3. Process Results Based on Mode
         ----------------------------------------------------------------- */
      if (skipDatabaseUpdate) {
        // Preview mode: just return the mappings
        const result = {
          success: true,
          categorized: aiResult.object.length,
          mappings: aiResult.object.reduce((acc, mapping, index) => {
            acc[index.toString()] = { category: mapping.category };
            return acc;
          }, {} as Record<string, { category: string }>),
        };
        return AICategorizeReturnsSchema.parse(result);
      }

      /* -----------------------------------------------------------------
         4. Validate & Normalize AI Response
         ----------------------------------------------------------------- */
      let validatedMappings: CategoryMapping;
      try {
        if (!aiResult || !aiResult.object) {
          throw new ConvexError({
            message: "AI response is null or invalid",
            code: "AI_EMPTY_RESPONSE",
          });
        }

        validatedMappings = CategoryMappingSchema.parse(aiResult.object);

        // Ensure each mapping is valid or fallback to "Needs Review: AI Uncertain"
        validatedMappings = validatedMappings.map(mapping => {
          const isValid = mapping?.category?.trim() && 
            mapping.category.includes(":") && 
            categories.includes(mapping.category);
          
          console.log("Validating category:", {
            original: mapping.category,
            isValid,
            fallback: !isValid ? "Needs Review: AI Uncertain" : undefined
          });
          
          return {
            category: isValid ? mapping.category : "Needs Review: AI Uncertain"
          };
        });

        // Fetch categories to match names -> IDs
        const allCategories = await ctx.runQuery(api.tags.fetchTags, {
          filter: { tag_type: expenseCategoryType._id },
        });
        if (!Array.isArray(allCategories)) {
          throw new ConvexError({
            message: "Failed to fetch categories from database",
            code: "DB_CATEGORIES_FETCH_ERROR",
          });
        }

        // Find fallback: "Needs Review" -> "AI Uncertain"
        const needsReviewCategory = allCategories.find((cat: Category) => {
          const parent = allCategories.find((p: Category) => p._id === cat.parent_id);
          return parent?.name === "Needs Review" && cat.name === "AI Uncertain";
        });
        if (!needsReviewCategory) {
          throw new ConvexError({
            message: "Required fallback category not found",
            code: "MISSING_FALLBACK_CATEGORY",
            data: {
              details: "The 'Needs Review: AI Uncertain' category is required but not found in the database.",
            },
          });
        }

        // Database update mode
        const updates = validatedMappings
          .map((mapping, index) => {
            const item = typedLineItems[index];
            // Skip items without IDs
            if (!('_id' in item) || !item._id) return null;

            // If invalid or AI uncertain, use fallback
            if (!mapping.category || mapping.category === "Needs Review: AI Uncertain") {
              return {
                _id: item._id,
                updates: {
                  spending_category: needsReviewCategory._id as Id<"tags">
                }
              };
            }

            const [parentName, subName] = mapping.category.split(":").map((s) => s.trim());
            const subCategory = allCategories.find((cat) => {
              if (!cat.parent_id) return false;
              const parent = allCategories.find((p) => p._id === cat.parent_id);
              return parent?.name === parentName && cat.name === subName;
            });

            return {
              _id: item._id,
              updates: {
                spending_category: (subCategory
                  ? subCategory._id
                  : needsReviewCategory._id) as Id<"tags">
              }
            };
          })
          .filter((update): update is {
            _id: Id<"lineItems">;
            updates: { spending_category: Id<"tags"> };
          } => update !== null);

        // Send bulk update
        try {
          await ctx.runMutation(api.lineItems.updateLineItemsBulk, {
            items: updates.map(update => ({
              _id: update._id,
              updates: {
                spending_category: update.updates.spending_category
              }
            }))
          });
        } catch (err) {
          throw new ConvexError({
            message: err instanceof Error ? err.message : "Failed to update line items",
            code: "DB_UPDATE_ERROR",
            data: {
              errorMessage: err instanceof Error ? err.message : String(err),
              updateCount: updates.length,
            },
          });
        }
      } catch (error) {
        if (error instanceof ConvexError) throw error;
        throw new ConvexError({
          message: error instanceof Error ? error.message : "Failed to validate AI categorization",
          code: "AI_VALIDATION_ERROR",
        });
      }

      /* -----------------------------------------------------------------
         5. Return Final Mappings
         ----------------------------------------------------------------- */
      return AICategorizeReturnsSchema.parse({
        success: true,
        categorized: validatedMappings.length,
        mappings: validatedMappings.reduce((acc, mapping, index) => {
          acc[index.toString()] = { category: mapping.category };
          return acc;
        }, {} as Record<string, { category: string }>),
      });
    } catch (error) {
      // Preserve known ConvexError codes; wrap everything else
      if (error instanceof ConvexError) throw error;
      throw new AICategorizationError(
        error instanceof Error ? error.message : "Unknown error in AI categorization"
      );
    }
  },
});
