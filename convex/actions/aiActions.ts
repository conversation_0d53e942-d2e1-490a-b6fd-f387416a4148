"use node";

import { action } from '../_generated/server';
import { v } from 'convex/values';
import { z } from 'zod';
import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { api } from '../_generated/api';
import { Id } from '../_generated/dataModel';

import {
  DECISION_FIELDS,
  GENERAL_DECISION_FIELDS,
  INVESTMENT_DECISION_FIELDS,
  pickFields,
} from "../entityFieldHelpers";

/**
 * Helper to render a prompt template with variables.
 * Simple mustache-style replacement: {{var}}
 */
function renderTemplate(template: string, variables: Record<string, any>): string {
  return template.replace(/{{\s*([\w]+)\s*}}/g, (_, key) => {
    const value = variables[key];
    if (Array.isArray(value)) {
      // Render arrays as bullet lists
      return value.map((item: any) => `- ${item}`).join('\n');
    }
    return value !== undefined && value !== null ? String(value) : '';
  });
}

/**
 * Example: How to inject full entity data for AI prompt using the centralized helpers.
 * 
 * // Suppose you have a decision, generalDecision, and investmentDecision object:
 * const entityData = {
 *   ...pickFields(decision, DECISION_FIELDS),
 *   ...Object.fromEntries(GENERAL_DECISION_FIELDS.map(f => [`general_${f}`, generalDecision?.[f] ?? null])),
 *   ...Object.fromEntries(INVESTMENT_DECISION_FIELDS.map(f => [`investment_${f}`, investmentDecision?.[f] ?? null])),
 * };
 * 
 * // Then pass entityData as a variable to your prompt template:
 * const prompt = renderTemplate(promptTemplate, { ...otherVars, ...entityData });
 */

// Zod schema for column summary output
const ColumnSummarySchema = z.object({
  report: z.string()
});

// Zod schema for column comparison output
const ColumnComparisonSchema = z.object({
  comparison: z.string()
});

/**
 * Converts an array of objects to a markdown table string.
 * Each object in the array should have the same keys.
 */
function arrayToMarkdownTable(arr: Record<string, any>[]): string {
  if (!arr.length) return '';
  const headers = Object.keys(arr[0]);
  const headerRow = `| ${headers.join(' | ')} |`;
  const separatorRow = `| ${headers.map(() => '---').join(' | ')} |`;
  const dataRows = arr.map(obj =>
    `| ${headers.map(h => (obj[h] !== undefined ? String(obj[h]) : '')).join(' | ')} |`
  );
  return [headerRow, separatorRow, ...dataRows].join('\n');
}

/**
 * Action: Summarize a single Kanban column using Google Flash LLM.
 * Args: { columnName: string, columnItems: string[] }
 * Returns: { summary: string }
 */
export const tagReporterColumnSummaryAction = action({
  args: v.object({
    columnName: v.string(),
    columnItems: v.array(v.string()) // Assume these are decision IDs
  }),
  handler: async (ctx, args) => {
    // Fetch the prompt template from the prompts table
    const promptObj = await ctx.runQuery(api.prompts.getPromptBySlug, {
      slug: 'tagreporter-column-summary'
    });
    if (!promptObj || !promptObj.prompt_text) {
      throw new Error('Prompt template not found for tagreporter-column-summary');
    }

    // Log input IDs
    console.log('[tagReporterColumnSummaryAction] args.columnItems:', args.columnItems);

    // 1. Only accept valid Convex decision IDs
    const invalidIds = args.columnItems.filter(id => !/^[a-zA-Z0-9]{10,}$/.test(id));
    if (invalidIds.length > 0) {
      throw new Error(
        `[tagReporterColumnSummaryAction] Invalid input: All columnItems must be Convex decision IDs. Received invalid values: ${JSON.stringify(invalidIds)}`
      );
    }
    const validDecisionIds = args.columnItems as string[];

    // 2. Fetch full decision objects for each valid ID
    const decisions = await Promise.all(
      validDecisionIds.map(id => ctx.runQuery(api.decisions.getDecision, { id: id as Id<"decisions"> }))
    );

    // Log fetched decisions
    console.log('[tagReporterColumnSummaryAction] decisions:', decisions);

    // 2. For each decision, fetch related generalDecision and investmentDecision
    const generalDecisions = await Promise.all(
      decisions.map((decision: any) =>
        decision
          ? ctx.runQuery(api.decisions.getGeneralDecisionByDecisionId, { decisionId: decision._id })
          : undefined
      )
    );
    const investmentDecisions = await Promise.all(
      decisions.map((decision: any) =>
        decision
          ? ctx.runQuery(api.decisions.getInvestmentDecisionByDecisionId, { decisionId: decision._id })
          : undefined
      )
    );

    // 3. Build detailed entity info for each decision
    const detailedEntities = decisions.map((decision: any, idx: number) => ({
      ...pickFields(decision, DECISION_FIELDS),
      ...Object.fromEntries(
        GENERAL_DECISION_FIELDS.map(f => [
          `general_${f}`,
          generalDecisions[idx] ? (generalDecisions[idx] as Record<string, any>)[f] ?? undefined : undefined
        ])
      ),
      ...Object.fromEntries(
        INVESTMENT_DECISION_FIELDS.map(f => [
          `investment_${f}`,
          investmentDecisions[idx] ? (investmentDecisions[idx] as Record<string, any>)[f] ?? undefined : undefined
        ])
      )
    }));

    // Log detailedEntities
    console.log('[tagReporterColumnSummaryAction] detailedEntities:', detailedEntities);

    // 4. Format as Markdown table for the AI (instead of JSON)
    const entitiesMarkdown = arrayToMarkdownTable(detailedEntities);

    // 5. Render the prompt with detailed entity info
    const prompt = renderTemplate(promptObj.prompt_text, {
      columnName: args.columnName,
      entities: entitiesMarkdown
    });

    // Log prompt length and content for debugging prompt truncation/data loss
    console.log('[tagReporterColumnSummaryAction] Prompt length:', prompt.length);
    if (prompt.length > 2000) {
      console.log('[tagReporterColumnSummaryAction] Prompt (first 2000 chars):', prompt.slice(0, 2000));
      console.log('[tagReporterColumnSummaryAction] Prompt (last 1000 chars):', prompt.slice(-1000));
    } else {
      console.log('[tagReporterColumnSummaryAction] Prompt (full):', prompt);
    }

    // Call the LLM with the rendered prompt
    const response = await generateObject({
      model: google('gemini-2.5-flash-preview-04-17', { structuredOutputs: true }),
      schema: ColumnSummarySchema,
      messages: [{ role: 'user', content: prompt }]
    });

    // Log the prompt and the AI result for debugging
    // Log the prompt and the AI result for debugging
    console.log('[tagReporterColumnSummaryAction] AI Result:', response.object.report);
   
    return response.object;
  }
});

/**
 * Action: Compare/contrast Kanban column summaries using Google Flash LLM.
 * Args: { columnSummaries: string[] }
 * Returns: { comparison: string }
 */
export const tagReporterColumnComparisonAction = action({
  args: v.object({
    columnSummaries: v.array(v.string())
  }),
  handler: async (ctx, args) => {
    // Fetch the prompt template from the prompts table
    const promptObj = await ctx.runQuery(api.prompts.getPromptBySlug, {
      slug: 'tagreporter-column-comparison'
    });
    if (!promptObj || !promptObj.prompt_text) {
      throw new Error('Prompt template not found for tagreporter-column-comparison');
    }
    // Render the prompt with variables
    const prompt = renderTemplate(promptObj.prompt_text, {
      columnSummaries: args.columnSummaries
    });

    // Call the LLM with the rendered prompt
    const response = await generateObject({
      model: google('gemini-2.5-flash-preview-04-17', { structuredOutputs: true }),
      schema: ColumnComparisonSchema,
      messages: [{ role: 'user', content: prompt }]
    });

    return response.object;
  }
});

/**
 * Example: aiUpdateEntityFieldsAction (existing, unchanged)
 */
export async function aiUpdateEntityFieldsAction(args: {
  entityType: string,
  entityId: string,
  transcript: string,
}): Promise<any> {
  const { entityType, entityId, transcript } = args;
  let updatedFields: any = {};
  
  if (entityType === 'project') {
    // Simulation: use the first sentence of the transcript as the project description.
    const sentences = transcript.split('. ');
    const description = sentences[0].trim() + (sentences[0].slice(-1) === '.' ? '' : '.');
    updatedFields = { description };
    
    // In a real implementation, you would call a Convex mutation here, for example:
    // await api.projects.updateProject({ projectId: entityId, description });
  } else {
    // For other entity types, perform additional logic as needed.
    updatedFields = {};
  }
  
  return { status: 'success', updatedFields };
}
