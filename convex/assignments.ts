import { query, QueryCtx, MutationCtx } from './_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from './_generated/dataModel'; // Ensure Doc is imported
import { DatabaseReader, DatabaseWriter } from './_generated/server';

// Define types for our data structures
interface PersonDetails {
  _id: Id<'people'>; // Corrected: ID from people table
  name: string;
  image?: string;
  user_id?: Id<'users'>; // Link back to the user table
  initials?: string;
  // Removed placeholder fields, fetch real data if needed
}

// Define a more generic AssignmentMember type
interface AssignmentMember {
  id: Id<'users'> | Id<'teams'> | Id<'people'>; // Can be user, team, or people ID
  type: 'user' | 'team' | 'person';
  name: string;
  avatar?: string; // Only for users/people
  initials: string;
  dciRole: 'Driver' | 'Contributor' | 'Informed' | 'Requestor' | 'None';
}


// Remove local Task interface, rely on Doc<'tasks'> directly


// Helper function to get user details from people table - Simplified for this context
// This helper might not be needed if we fetch directly in getTeamForEntity
// const getUserDetails = async (ctx: { db: DatabaseReader }, userIds: Id<'users'>[]) => { ... };

export const getTeamForEntity = query({
  args: {
    entityId: v.string(), // Using string initially, might refine based on usage
    entityType: v.union(
      v.literal('task'),
      v.literal('project'), // Placeholder
      v.literal('decision') // Placeholder
      // Add other entity types as needed
    ),
  },
   // REMOVED output validation as we are using base 'query'
  handler: async (ctx, args): Promise<AssignmentMember[]> => { // Explicit return type
    let driverId: Id<'users'> | null = null;
    let requestorId: Id<'people'> | Id<'teams'> | null = null;
    const contributorIds = new Set<Id<'users'> | Id<'teams'>>();
    const informedIds = new Set<Id<'users'> | Id<'teams'>>();
    const allUserIds = new Set<Id<'users'>>(); // For fetching people details
    const allTeamIds = new Set<Id<'teams'>>(); // For fetching team details
    const allPeopleIds = new Set<Id<'people'>>(); // For fetching people directly

    let entity: Doc<'tasks'> | Doc<'projects'> | Doc<'decisions'> | null = null;

    // --- Logic to fetch entity and collect IDs ---
    if (args.entityType === 'task') {
      const taskId = ctx.db.normalizeId('tasks', args.entityId);
      if (!taskId) {
        console.error('Invalid Task ID provided:', args.entityId);
        return [];
      }
      entity = await ctx.db.get(taskId);
    } else if (args.entityType === 'project') {
      const projectId = ctx.db.normalizeId('projects', args.entityId);
       if (!projectId) {
         console.error('Invalid Project ID provided:', args.entityId);
          return [];
        }
        entity = await ctx.db.get(projectId);
        // Collect IDs from project DCI fields
        if (entity && 'driver' in entity && entity.driver) {
          driverId = entity.driver;
          if (driverId) allUserIds.add(driverId);
        }
        if (entity && 'contributors' in entity && entity.contributors) {
          entity.contributors.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return;
            contributorIds.add(id);
            if (ctx.db.normalizeId('users', id as Id<'users'>)) allUserIds.add(id as Id<'users'>);
            else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) allTeamIds.add(id as Id<'teams'>);
            else console.warn(`Project Contributor ID ${id} is neither user nor team.`);
          });
        }
        if (entity && 'informed' in entity && entity.informed) {
          entity.informed.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return;
            informedIds.add(id);
            if (ctx.db.normalizeId('users', id as Id<'users'>)) allUserIds.add(id as Id<'users'>);
            else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) allTeamIds.add(id as Id<'teams'>);
            else console.warn(`Project Informed ID ${id} is neither user nor team.`);
          });
        }
     } else if (args.entityType === 'decision') {
        const decisionId = ctx.db.normalizeId('decisions', args.entityId);
       if (!decisionId) {
         console.error('Invalid Decision ID provided:', args.entityId);
          return [];
        }
        entity = await ctx.db.get(decisionId);
        // Collect IDs from decision DCI fields
        if (entity && 'driver' in entity && entity.driver) {
          driverId = entity.driver;
          if (driverId) allUserIds.add(driverId);
        }
        if (entity && 'contributors' in entity && entity.contributors) {
          entity.contributors.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return;
            contributorIds.add(id);
            if (ctx.db.normalizeId('users', id as Id<'users'>)) allUserIds.add(id as Id<'users'>);
            else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) allTeamIds.add(id as Id<'teams'>);
            else console.warn(`Decision Contributor ID ${id} is neither user nor team.`);
          });
        }
        if (entity && 'informed' in entity && entity.informed) {
          entity.informed.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return;
            informedIds.add(id);
            if (ctx.db.normalizeId('users', id as Id<'users'>)) allUserIds.add(id as Id<'users'>);
            else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) allTeamIds.add(id as Id<'teams'>);
            else console.warn(`Decision Informed ID ${id} is neither user nor team.`);
          });
        }
     }

     // Only collect IDs for tasks if entityType is 'task'
     if (args.entityType === 'task' && entity) {
        // Collect IDs based on common DCI fields (adjust field names if different per entity type)
        if ('driver' in entity && entity.driver) {
          driverId = entity.driver;
          if (driverId) allUserIds.add(driverId); // Driver is always a user
        }
        if ('contributors' in entity && entity.contributors) {
          entity.contributors.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return; // Skip null/undefined IDs
            contributorIds.add(id);
            // Attempt to normalize both ways to determine type
            if (ctx.db.normalizeId('users', id as Id<'users'>)) {
              allUserIds.add(id as Id<'users'>);
            } else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) {
              allTeamIds.add(id as Id<'teams'>);
            } else {
              console.warn(`Task Contributor ID ${id} is neither a valid user nor team ID.`);
            }
          });
        }
        if ('informed' in entity && entity.informed) {
          entity.informed.forEach((id: Id<'users'> | Id<'teams'>) => {
            if (!id) return; // Skip null/undefined IDs
            informedIds.add(id);
            if (ctx.db.normalizeId('users', id as Id<'users'>)) {
              allUserIds.add(id as Id<'users'>);
            } else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) {
              allTeamIds.add(id as Id<'teams'>);
            } else {
              console.warn(`Task Informed ID ${id} is neither a valid user nor team ID.`);
            }
          });
        }
        // Handle requestor field for tasks
        if ('requestor' in entity && entity.requestor) {
          requestorId = entity.requestor;
          // Check if it's a person or team
          if (ctx.db.normalizeId('people', requestorId as Id<'people'>)) {
            allPeopleIds.add(requestorId as Id<'people'>);
          } else if (ctx.db.normalizeId('teams', requestorId as Id<'teams'>)) {
            allTeamIds.add(requestorId as Id<'teams'>);
          } else {
            console.warn(`Task Requestor ID ${requestorId} is neither a valid person nor team ID.`);
          }
        }
     }
     // --- End ID Collection ---

     if (!entity) { // Check entity existence after attempting to collect IDs for all types
      console.error(`${args.entityType} not found:`, args.entityId);
      return [];
    }

    // Collect IDs based on common DCI fields (adjust field names if different per entity type)
    if ('driver' in entity && entity.driver) {
      driverId = entity.driver;
      if (driverId) allUserIds.add(driverId); // Driver is always a user
    }
    if ('contributors' in entity && entity.contributors) {
      entity.contributors.forEach((id: Id<'users'> | Id<'teams'>) => {
        if (!id) return; // Skip null/undefined IDs
        contributorIds.add(id);
        // Attempt to normalize both ways to determine type
        if (ctx.db.normalizeId('users', id as Id<'users'>)) {
          allUserIds.add(id as Id<'users'>);
        } else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) {
          allTeamIds.add(id as Id<'teams'>);
        } else {
          console.warn(`Contributor ID ${id} is neither a valid user nor team ID.`);
        }
      });
    }
     if ('informed' in entity && entity.informed) {
        entity.informed.forEach((id: Id<'users'> | Id<'teams'>) => {
        if (!id) return; // Skip null/undefined IDs
        informedIds.add(id);
        if (ctx.db.normalizeId('users', id as Id<'users'>)) {
          allUserIds.add(id as Id<'users'>);
        } else if (ctx.db.normalizeId('teams', id as Id<'teams'>)) {
          allTeamIds.add(id as Id<'teams'>);
        } else {
           console.warn(`Informed ID ${id} is neither a valid user nor team ID.`);
        }
      });
    }
    // --- End ID Collection ---


    // --- Fetch details for collected IDs ---
    // Use Promise.allSettled to handle potential errors during fetching
    const results = await Promise.allSettled([
      // Fetch people details using the user IDs collected
      allUserIds.size > 0 ? ctx.db.query('people')
        .filter(q => q.or(...Array.from(allUserIds).map(id => q.eq(q.field('user_id'), id))))
        .collect() : Promise.resolve([]),
      // Fetch team details
      allTeamIds.size > 0 ? ctx.db.query('teams')
        .filter(q => q.or(...Array.from(allTeamIds).map(id => q.eq(q.field('_id'), id))))
        .collect() : Promise.resolve([]),
      // Fetch people details directly by ID (for requestor)
      allPeopleIds.size > 0 ? ctx.db.query('people')
        .filter(q => q.or(...Array.from(allPeopleIds).map(id => q.eq(q.field('_id'), id))))
        .collect() : Promise.resolve([])
    ]);

    const peopleDetails = results[0].status === 'fulfilled' ? results[0].value : [];
    const teamDetails = results[1].status === 'fulfilled' ? results[1].value : [];
    const directPeopleDetails = results[2].status === 'fulfilled' ? results[2].value : [];

    if (results[0].status === 'rejected') console.error("Failed to fetch people details:", results[0].reason);
    if (results[1].status === 'rejected') console.error("Failed to fetch team details:", results[1].reason);
    if (results[2].status === 'rejected') console.error("Failed to fetch direct people details:", results[2].reason);


    // --- Create lookup maps ---
    const peopleByUserIdMap = new Map(peopleDetails.map(p => [p.user_id, p]));
    const teamMap = new Map(teamDetails.map(t => [t._id, t]));
    const peopleByIdMap = new Map(directPeopleDetails.map(p => [p._id, p]));

    // --- Combine results into AssignmentMember structure ---
    const assignmentMembersMap = new Map<Id<'users'> | Id<'teams'> | Id<'people'>, AssignmentMember>();

    // Helper to add/update member in the map
    const addOrUpdateMember = (id: Id<'users'> | Id<'teams'> | Id<'people'>, type: 'user' | 'team' | 'person', name: string, avatar: string | undefined | null, role: 'Driver' | 'Contributor' | 'Informed' | 'Requestor') => {
      const existing = assignmentMembersMap.get(id);
      // Prioritize Driver role, then Contributor, then Informed, then Requestor
      const currentRolePriority = existing ? ['Driver', 'Contributor', 'Informed', 'Requestor', 'None'].indexOf(existing.dciRole) : 99;
      const newRolePriority = ['Driver', 'Contributor', 'Informed', 'Requestor'].indexOf(role);

      if (!existing || newRolePriority < currentRolePriority) {
        assignmentMembersMap.set(id, {
          id,
          type,
          name,
          avatar: avatar ?? undefined,
          initials: (name || '??').substring(0, 2).toUpperCase(),
          dciRole: role
        });
      }
    };

    // Process Driver
    if (driverId) {
      const person = peopleByUserIdMap.get(driverId);
      addOrUpdateMember(
        driverId, // Use the user ID as the key
        'user',
        person?.name ?? 'Unknown User (Driver)',
        person?.image,
        'Driver'
      );
    }

    // Process Contributors
    contributorIds.forEach(id => {
      const team = teamMap.get(id as Id<'teams'>);
      if (team) {
        addOrUpdateMember(id, 'team', team.name, null, 'Contributor');
      } else {
        const person = peopleByUserIdMap.get(id as Id<'users'>);
        addOrUpdateMember(
          id as Id<'users'>,
          'user',
          person?.name ?? 'Unknown User (Contributor)',
          person?.image,
          'Contributor'
        );
      }
    });

    // Process Informed
    informedIds.forEach(id => {
      const team = teamMap.get(id as Id<'teams'>);
      if (team) {
        addOrUpdateMember(id, 'team', team.name, null, 'Informed');
      } else {
        const person = peopleByUserIdMap.get(id as Id<'users'>);
         addOrUpdateMember(
          id as Id<'users'>,
          'user',
          person?.name ?? 'Unknown User (Informed)',
          person?.image,
          'Informed'
        );
      }
    });

    // Process Requestor (only for tasks)
    if (args.entityType === 'task' && requestorId) {
      const team = teamMap.get(requestorId as Id<'teams'>);
      if (team) {
        addOrUpdateMember(requestorId, 'team', team.name, null, 'Requestor');
      } else {
        const person = peopleByIdMap.get(requestorId as Id<'people'>);
        if (person) {
          addOrUpdateMember(
            requestorId as Id<'people'>,
            'person',
            person.name ?? 'Unknown Person (Requestor)',
            person.image,
            'Requestor'
          );
        } else {
          console.warn(`Requestor ID ${requestorId} not found in people table.`);
        }
      }
    }

    // TODO: Add logic to fetch and include users/teams currently assigned but not in DCI roles ('None') if needed

    return Array.from(assignmentMembersMap.values());
  },
});

// --- Mutation to assign a user OR TEAM to a DCI role for an entity ---
import { mutation } from './_generated/server';

export const assignRoleToEntity = mutation({
  args: {
    entityId: v.string(),
    entityType: v.union(
      v.literal('task'),
      v.literal('project'),
      v.literal('decision')
    ),
    // Allow userId, teamId, or peopleId
    assigneeId: v.union(v.id('users'), v.id('teams'), v.id('people')), 
    dciRole: v.union(
      v.literal('Driver'), // Note: Driver role might still only apply to users
      v.literal('Contributor'),
      v.literal('Informed'),
      v.literal('Requestor') // New role for tasks
    ),
  },
  handler: async (ctx, args) => {
    const { entityId, entityType, assigneeId, dciRole } = args;
    const isUser = ctx.db.normalizeId('users', assigneeId as Id<'users'>) !== null;
    const isTeam = !isUser && ctx.db.normalizeId('teams', assigneeId as Id<'teams'>) !== null;

    // Validate Driver role assignment
    if (dciRole === 'Driver' && !isUser) {
      throw new Error('Only users can be assigned the Driver role.');
    }
    
    // Validate Requestor role assignment
    // Allow: (a) any people record (regardless of user_id), (b) any team
    const isPeople = ctx.db.normalizeId('people', assigneeId as Id<'people'>) !== null;
    if (dciRole === 'Requestor' && !isPeople && !isTeam) {
      throw new Error('Only people or teams can be assigned the Requestor role.');
    }

    // --- Logic for Task ---
    if (entityType === 'task') {
      const taskId = ctx.db.normalizeId('tasks', entityId);
      if (!taskId) {
        throw new Error('Invalid Task ID provided');
      }
      const task = await ctx.db.get(taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Prepare updates based on role
      let updates: Partial<Doc<'tasks'>> = {}; // Use Doc type

      // Helper to safely add ID to an array if not present
      const addToArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToAdd: Id<'users'> | Id<'teams'> | Id<'people'>) => {
        // Only add user or team IDs to the array (not people IDs)
        if (ctx.db.normalizeId('users', idToAdd as Id<'users'>) || ctx.db.normalizeId('teams', idToAdd as Id<'teams'>)) {
          const currentSet = new Set(arr || []);
          currentSet.add(idToAdd as Id<'users'> | Id<'teams'>);
          return Array.from(currentSet);
        }
        return arr || []; // Return original array if ID is not a user or team
      };

      // Helper to safely remove ID from an array
      const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
        return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
      };

      if (dciRole === 'Driver') {
        // Assign driver (must be user)
        updates.driver = assigneeId as Id<'users'>;
        // Remove assignee from other roles
        updates.contributors = removeFromArray(task.contributors, assigneeId);
        updates.informed = removeFromArray(task.informed, assigneeId);
        // If the user was also a requestor (unlikely but possible), clear that too
        if (task.requestor === assigneeId) updates.requestor = undefined;
      } else if (dciRole === 'Contributor') {
        // Add to contributors
        updates.contributors = addToArray(task.contributors, assigneeId);
        // Remove from other roles
        if (task.driver === assigneeId) updates.driver = undefined;
        updates.informed = removeFromArray(task.informed, assigneeId);
        // If the user/team was also a requestor, clear that too
        if (task.requestor === assigneeId) updates.requestor = undefined;
      } else if (dciRole === 'Informed') {
        // Add to informed
        updates.informed = addToArray(task.informed, assigneeId);
        // Remove from other roles
        if (task.driver === assigneeId) updates.driver = undefined;
        updates.contributors = removeFromArray(task.contributors, assigneeId);
        // If the user/team was also a requestor, clear that too
        if (task.requestor === assigneeId) updates.requestor = undefined;
      } else if (dciRole === 'Requestor') {
        // Assign requestor (can be person or team)
        updates.requestor = assigneeId as Id<'people'> | Id<'teams'>;
        // Remove from other roles if it's a user or team
        if (isUser || isTeam) {
          if (task.driver === assigneeId) updates.driver = undefined;
          updates.contributors = removeFromArray(task.contributors, assigneeId);
          updates.informed = removeFromArray(task.informed, assigneeId);
        }
      }

      // Apply the patch
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(taskId, updates);
        console.log(`Assigned ${assigneeId} as ${dciRole} to task ${taskId}`);
      } else {
        console.warn(`No update needed for ${assigneeId} as ${dciRole} on task ${taskId}`);
       }
     }
     // --- Logic for Project ---
     else if (entityType === 'project') {
       const projectId = ctx.db.normalizeId('projects', entityId);
       if (!projectId) throw new Error('Invalid Project ID provided');
       const project = await ctx.db.get(projectId);
       if (!project) throw new Error('Project not found');

       let updates: Partial<Doc<'projects'>> = {};
       const addToArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToAdd: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         // Only add user or team IDs to the array (not people IDs)
         if (ctx.db.normalizeId('users', idToAdd as Id<'users'>) || ctx.db.normalizeId('teams', idToAdd as Id<'teams'>)) {
           const currentSet = new Set(arr || []);
           currentSet.add(idToAdd as Id<'users'> | Id<'teams'>);
           return Array.from(currentSet);
         }
         return arr || []; // Return original array if ID is not a user or team
       };
       const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
       };

       if (dciRole === 'Driver') {
         updates.driver = assigneeId as Id<'users'>;
         updates.contributors = removeFromArray(project.contributors, assigneeId);
         updates.informed = removeFromArray(project.informed, assigneeId);
       } else if (dciRole === 'Contributor') {
         updates.contributors = addToArray(project.contributors, assigneeId);
         if (project.driver === assigneeId) updates.driver = undefined;
         updates.informed = removeFromArray(project.informed, assigneeId);
       } else if (dciRole === 'Informed') {
         updates.informed = addToArray(project.informed, assigneeId);
         if (project.driver === assigneeId) updates.driver = undefined;
         updates.contributors = removeFromArray(project.contributors, assigneeId);
       }

       if (Object.keys(updates).length > 0) {
         await ctx.db.patch(projectId, updates);
         console.log(`Assigned ${assigneeId} as ${dciRole} to project ${projectId}`);
       } else {
         console.warn(`No update needed for ${assigneeId} as ${dciRole} on project ${projectId}`);
       }
     }
     // --- Logic for Decision ---
     else if (entityType === 'decision') {
       const decisionId = ctx.db.normalizeId('decisions', entityId);
       if (!decisionId) throw new Error('Invalid Decision ID provided');
       const decision = await ctx.db.get(decisionId);
       if (!decision) throw new Error('Decision not found');

       let updates: Partial<Doc<'decisions'>> = {};
       const addToArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToAdd: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         // Only add user or team IDs to the array (not people IDs)
         if (ctx.db.normalizeId('users', idToAdd as Id<'users'>) || ctx.db.normalizeId('teams', idToAdd as Id<'teams'>)) {
           const currentSet = new Set(arr || []);
           currentSet.add(idToAdd as Id<'users'> | Id<'teams'>);
           return Array.from(currentSet);
         }
         return arr || []; // Return original array if ID is not a user or team
       };
       const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
       };

       if (dciRole === 'Driver') {
         updates.driver = assigneeId as Id<'users'>;
         updates.contributors = removeFromArray(decision.contributors, assigneeId);
         updates.informed = removeFromArray(decision.informed, assigneeId);
       } else if (dciRole === 'Contributor') {
         updates.contributors = addToArray(decision.contributors, assigneeId);
         if (decision.driver === assigneeId) updates.driver = undefined;
         updates.informed = removeFromArray(decision.informed, assigneeId);
       } else if (dciRole === 'Informed') {
         updates.informed = addToArray(decision.informed, assigneeId);
         if (decision.driver === assigneeId) updates.driver = undefined;
         updates.contributors = removeFromArray(decision.contributors, assigneeId);
       }

       if (Object.keys(updates).length > 0) {
         await ctx.db.patch(decisionId, updates);
         console.log(`Assigned ${assigneeId} as ${dciRole} to decision ${decisionId}`);
       } else {
         console.warn(`No update needed for ${assigneeId} as ${dciRole} on decision ${decisionId}`);
       }
     }
     // --- Fallback for unhandled types ---
     else {
       console.error(`Role assignment not implemented for entity type: ${entityType}`);
       throw new Error(`Role assignment not implemented for: ${entityType}`);
     }
   },
 });


// --- Mutation to REMOVE a user, TEAM, or PERSON from their DCI role for an entity ---
export const removeRoleFromEntity = mutation({
  args: {
    entityId: v.string(),
    entityType: v.union(
      v.literal('task'),
      v.literal('project'), // Placeholder
      v.literal('decision') // Placeholder
    ),
    // Allow userId, teamId, or peopleId
    assigneeId: v.union(v.id('users'), v.id('teams'), v.id('people')), 
  },
  handler: async (ctx, args) => {
    const { entityId, entityType, assigneeId } = args;

    // --- Logic for Task ---
    if (entityType === 'task') {
      const taskId = ctx.db.normalizeId('tasks', entityId);
      if (!taskId) throw new Error('Invalid Task ID provided');
      const task = await ctx.db.get(taskId);
      if (!task) throw new Error('Task not found');

      let updates: Partial<Doc<'tasks'>> = {}; // Use Doc type
      let roleRemoved: string | null = null;

      // Helper to safely remove ID from an array
      const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
        return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
      };

      // Check and remove from driver
      if (task.driver === assigneeId) {
        updates.driver = undefined; // Set driver to undefined
        roleRemoved = 'Driver';
      }
      // Helper to check if an array includes an ID (comparing by string)
      const arrayIncludesId = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, id: Id<'users'> | Id<'teams'> | Id<'people'>) => {
        return arr?.some(item => item.toString() === id.toString()) ?? false;
      };
      
      // Check and remove from contributors
      if (arrayIncludesId(task.contributors, assigneeId)) {
        updates.contributors = removeFromArray(task.contributors, assigneeId);
        if (!roleRemoved) roleRemoved = 'Contributor';
      }
      // Check and remove from informed
      if (arrayIncludesId(task.informed, assigneeId)) {
        updates.informed = removeFromArray(task.informed, assigneeId);
        if (!roleRemoved) roleRemoved = 'Informed';
      }
      // Check and remove from requestor
      if (task.requestor === assigneeId) {
        updates.requestor = undefined; // Set requestor to undefined
        if (!roleRemoved) roleRemoved = 'Requestor';
      }

      // Apply the patch if changes were made
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(taskId, updates);
        console.log(`Removed ${assigneeId} from ${roleRemoved ?? 'roles'} on task ${taskId}`);
      } else {
        console.warn(`Assignee ${assigneeId} not found in any role on task ${taskId}. No update needed.`);
       }

     }
     // --- Logic for Project ---
     else if (entityType === 'project') {
       const projectId = ctx.db.normalizeId('projects', entityId);
       if (!projectId) throw new Error('Invalid Project ID provided');
       const project = await ctx.db.get(projectId);
       if (!project) throw new Error('Project not found');

       let updates: Partial<Doc<'projects'>> = {};
       let roleRemoved: string | null = null;
       const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
       };

       if (project.driver === assigneeId) {
         updates.driver = undefined;
         roleRemoved = 'Driver';
       }
       // Helper to check if an array includes an ID (comparing by string)
       const arrayIncludesId = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, id: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.some(item => item.toString() === id.toString()) ?? false;
       };
       
       if (arrayIncludesId(project.contributors, assigneeId)) {
         updates.contributors = removeFromArray(project.contributors, assigneeId);
         if (!roleRemoved) roleRemoved = 'Contributor';
       }
       if (arrayIncludesId(project.informed, assigneeId)) {
         updates.informed = removeFromArray(project.informed, assigneeId);
         if (!roleRemoved) roleRemoved = 'Informed';
       }

       // Apply the patch if changes were made
       if (Object.keys(updates).length > 0) {
         await ctx.db.patch(projectId, updates);
         console.log(`Removed ${assigneeId} from ${roleRemoved ?? 'roles'} on project ${projectId}`);
       } else {
         console.warn(`Assignee ${assigneeId} not found in any role on project ${projectId}. No update needed.`);
       }
     }
     // --- Logic for Decision ---
     else if (entityType === 'decision') {
       const decisionId = ctx.db.normalizeId('decisions', entityId);
       if (!decisionId) throw new Error('Invalid Decision ID provided');
       const decision = await ctx.db.get(decisionId);
       if (!decision) throw new Error('Decision not found');

       let updates: Partial<Doc<'decisions'>> = {};
       let roleRemoved: string | null = null;
       const removeFromArray = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, idToRemove: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.filter((id: Id<'users'> | Id<'teams'>) => id.toString() !== idToRemove.toString());
       };

       if (decision.driver === assigneeId) {
         updates.driver = undefined;
         roleRemoved = 'Driver';
       }
       
       // Helper to check if an array includes an ID (comparing by string)
       const arrayIncludesId = (arr: (Id<'users'> | Id<'teams'>)[] | undefined, id: Id<'users'> | Id<'teams'> | Id<'people'>) => {
         return arr?.some(item => item.toString() === id.toString()) ?? false;
       };
       
       if (arrayIncludesId(decision.contributors, assigneeId)) {
         updates.contributors = removeFromArray(decision.contributors, assigneeId);
         if (!roleRemoved) roleRemoved = 'Contributor';
       }
       if (arrayIncludesId(decision.informed, assigneeId)) {
         updates.informed = removeFromArray(decision.informed, assigneeId);
         if (!roleRemoved) roleRemoved = 'Informed';
       }

       // Apply the patch if changes were made
       if (Object.keys(updates).length > 0) {
         await ctx.db.patch(decisionId, updates);
         console.log(`Removed ${assigneeId} from ${roleRemoved ?? 'roles'} on decision ${decisionId}`);
       } else {
         console.warn(`Assignee ${assigneeId} not found in any role on decision ${decisionId}. No update needed.`);
       }
     }
     // --- Fallback for unhandled types ---
     else {
       console.error(`Role removal not implemented for entity type: ${entityType}`);
       throw new Error(`Role removal not implemented for: ${entityType}`);
     }
   },
 });
