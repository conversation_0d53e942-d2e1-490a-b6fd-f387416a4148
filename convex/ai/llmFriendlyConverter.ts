"use node";
import { internalAction } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";
import { Id } from "../_generated/dataModel";
import { generateText } from "ai";
import { google } from "@ai-sdk/google";

export const llmFriendlyConverter = internalAction({
  args: {
    documentId: v.id("documents"),
    source: v.union(
      v.object({ type: v.literal("storage"), storageId: v.id("_storage") }),
      v.object({ type: v.literal("url"), url: v.string() })
    ),
  },
  handler: async (ctx, { documentId, source }) => {
    // 1. Get document buffer
    let documentBuffer: Buffer;
    if (source.type === "storage") {
      const blob = await ctx.storage.get(source.storageId);
      if (!blob) {
        throw new Error(`Storage object not found: ${source.storageId}`);
      }
      documentBuffer = Buffer.from(await blob.arrayBuffer());
    } else { // source.type === "url"
      const response = await fetch(source.url);
      if (!response.ok) {
        throw new Error(`Failed to fetch URL: ${source.url}`);
      }
      documentBuffer = Buffer.from(await response.arrayBuffer());
    }

    // 2. Call AI SDK
    const { text } = await generateText({
      model: google("models/gemini-1.5-flash-latest"),
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: "Convert this document to clean, well-structured Markdown." },
            { type: 'image', image: documentBuffer, mimeType: 'application/pdf' }
          ]
        }
      ]
    });

    // 3. Save result
    await ctx.runMutation(internal.files.documentsMutations.saveMarkdown, {
      documentId,
      markdown: text,
    });
  },
});
