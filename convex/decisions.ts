import { zQuery, zMutation } from './functions';
import { z } from 'zod';
import {
  Decision,
  CreateDecisionInput,
  CreateMultipleDecisionsInput,
  UpdateDecisionInput,
  BulkDecisionUpdateSchema,
  DecisionFilterSchema,
  PaginationSchema,
  DecisionStatusEnum,
  DecisionTypeEnum,
  TimePeriodEnum,
  AmountTypeEnum,
  KanbanColumn, // Import the KanbanColumn schema
  SubtableTypeEnum // Import the SubtableTypeEnum
} from '../zod/decisions-schema';
import { zid } from 'convex-helpers/server/zod';
import { MutationCtx, QueryCtx, query, mutation } from './_generated/server'; // Add standard query import
import { v } from 'convex/values'; // Add v import
import { Id, Doc } from './_generated/dataModel';
import { ConvexError } from "convex/values";
import { api } from './_generated/api';
import { exportDecisionsByTags as exportDecisionsByTagsLogic } from "./downloads";

// Define a type for filter functions
// We use 'any' here because the exact Convex filter builder type is complex
// and changes between Convex versions
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type FilterFunction = (q: any) => boolean;

/**
 * Creates one or multiple decisions
 * Accepts either a single decision object or an array of decision objects
 * Returns an array of created decision IDs
 */
export const createDecisions = zMutation({
  args: {
    input: z.union([CreateDecisionInput, CreateMultipleDecisionsInput])
  },
  output: z.object({
    ids: z.array(z.string())
  }),
  handler: async (ctx, args) => {
    // If it's a single decision, convert to array for consistent handling
    const decisionsToCreate = Array.isArray(args.input)
      ? args.input
      : [args.input];

    // Validate the input before inserting
    if (decisionsToCreate.length === 0) {
      throw new Error('No decisions provided to create');
    }

    // Insert all decisions and collect their IDs
    const createdIds: string[] = []; // Explicitly type as string array
    for (const decision of decisionsToCreate) {
      const now = Date.now();

      // Get the category to determine allowed statuses and subtable
      const category = await ctx.db.get(decision.decision_category_id);
      if (!category) {
        throw new Error(`Decision category with ID ${decision.decision_category_id} not found`);
      }
      if (!category.statuses || !Array.isArray(category.statuses) || category.statuses.length === 0) {
        throw new Error(`Decision category with ID ${decision.decision_category_id} has no statuses defined`);
      }
      const allowedStatuses = category.statuses.map((s: any) => s.value);

      // Validate or default status
      let statusToUse: string;
      if (decision.status) {
        if (!allowedStatuses.includes(decision.status)) {
          throw new Error(`Status "${decision.status}" is not allowed for category "${category.name}"`);
        }
        statusToUse = decision.status;
      } else {
        statusToUse = allowedStatuses[0];
      }

      // Set default values if not provided
      const decisionWithDefaults = {
        ...decision,
        updated_at: now,
        status: statusToUse
      };

      // Insert the decision in the main table
      const id = await ctx.db.insert('decisions', decisionWithDefaults);
      createdIds.push(id);

      // Create the corresponding record in the appropriate subtable
      if (category.subtable_type === 'general_decisions') {
        await ctx.db.insert('general_decisions', {
          decision_id: id,
          description: '',
          updated_at: now
        });
      } else if (category.subtable_type === 'investment_decisions') {
        await ctx.db.insert('investment_decisions', {
          decision_id: id,
          description: '',
          is_active: true,
          updated_at: now
        });
      }
      // For custom_decisions, we don't need to create a record here
    }

    return { ids: createdIds };
  }
});

/**
 * Updates investment decision fields
 * This is a general mutation for updating any fields in the investment_decisions subtable
 * except for the research field which has its own specialized mutation due to TipTap integration
 */
export const updateInvestmentDecision = zMutation({
  args: {
    decisionId: zid('decisions'),
    // Zod schema should reflect the expected input *before* conversion
    updates: z.object({
      is_active: z.boolean().optional(),
      proposed_investment: z.string().optional(),
      // Allow null input for optional numbers, will convert to undefined later
      total_fundraise: z.union([z.number(), z.null()]).optional(),
      asset_class: z.string().optional(),
      structure: z.string().optional(),
      investment_entity: z.string().optional(),
      market: z.string().optional(),
      geography: z.string().optional(),
      // Allow null input for optional numbers, will convert to undefined later
      target_close_date: z.union([z.number(), z.null()]).optional(),
      close_timing_log: z.string().optional(),
      firm_name: z.string().optional(),
      due_source: z.string().optional(),
      gdrive_folder: z.string().optional(),
      stage_involvements: z.string().optional(),
      mentioned_by_pm: z.string().optional(),
      notes: z.string().optional(),
      // Exclude research field
    }),
  },
  output: z.object({ success: z.boolean() }),
  handler: async (ctx, args) => {
    const { decisionId, updates } = args;

    // 1. Fetch the core decision to get the category ID
    const coreDecision = await ctx.db.get(decisionId);
    if (!coreDecision) {
      throw new ConvexError(`Decision not found: ${decisionId}`);
    }

    // 2. Fetch the category to determine the subtable type
    const category = await ctx.db.get(coreDecision.decision_category_id);
    if (!category) {
      throw new ConvexError(`Decision category not found for decision: ${decisionId}`);
    }

    // 3. Verify this is an investment decision
    if (category.subtable_type !== 'investment_decisions') {
      throw new ConvexError(`Cannot update investment fields for non-investment decision: ${decisionId}`);
    }

    // Prepare updates for the database call.
    // Explicitly handle null -> undefined conversion for optional fields.
    const dbUpdates: Partial<Doc<'investment_decisions'>> = {};

    // Copy non-null values and convert nulls for specific optional fields
    for (const key in updates) {
      if (Object.prototype.hasOwnProperty.call(updates, key)) {
        const value = (updates as any)[key];

        if (key === 'total_fundraise' || key === 'target_close_date') {
          // For optional numbers, convert null to undefined
          dbUpdates[key] = value === null ? undefined : value;
        } else if (value !== null) {
          // For other fields, copy if not null
          (dbUpdates as any)[key] = value;
        }
        // If value is null and not handled above, it's omitted
      }
    }

    // 4. Find the investment_decisions record and update its fields
    try {
      // Use collect() instead of unique() to handle potential duplicate records
      const subRecords = await ctx.db
        .query('investment_decisions')
        .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
        .collect();

      if (subRecords.length > 0) {
        // If there are multiple records (which shouldn't happen but might), update all of them
        for (const record of subRecords) {
          await ctx.db.patch(record._id, {
            ...dbUpdates, // Use dbUpdates which has nulls converted to undefined
            updated_at: Date.now()
          });
        }
        
        // Log a warning if we found multiple records
        if (subRecords.length > 1) {
          console.warn(`Found ${subRecords.length} investment_decisions records for decision ID: ${decisionId}. This is unexpected and should be fixed.`);
        }
        
        return { success: true };
      } else {
        console.warn(`Investment decision subtable record not found for decision ID: ${decisionId}. Creating a new record.`);
        // Create a new record if none exists
        await ctx.db.insert('investment_decisions', {
          decision_id: decisionId,
          description: '', // Default empty description
          // Ensure is_active has a default if not provided in updates
          is_active: (dbUpdates.is_active as boolean | undefined) ?? true,
          ...dbUpdates, // Use dbUpdates which now correctly handles null -> undefined
          updated_at: Date.now()
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error(`Error updating investment decision ${decisionId}:`, error);
      throw new ConvexError("Failed to update investment decision.");
    }
  }
});

/**
 * Updates the investment decision research field
 * This is a specialized mutation for updating the research field in the investment_decisions subtable
 */
export const updateInvestmentDecisionResearch = zMutation({
  args: {
    decisionId: zid('decisions'),
    research: z.string().optional(),
  },
  output: z.object({ success: z.boolean() }),
  handler: async (ctx, args) => {
    const { decisionId, research } = args;

    // 1. Fetch the core decision to get the category ID
    const coreDecision = await ctx.db.get(decisionId);
    if (!coreDecision) {
      throw new ConvexError(`Decision not found: ${decisionId}`);
    }

    // 2. Fetch the category to determine the subtable type
    const category = await ctx.db.get(coreDecision.decision_category_id);
    if (!category) {
      throw new ConvexError(`Decision category not found for decision: ${decisionId}`);
    }

    // 3. Verify this is an investment decision
    if (category.subtable_type !== 'investment_decisions') {
      throw new ConvexError(`Cannot update research field for non-investment decision: ${decisionId}`);
    }

    // 4. Find the investment_decisions record and update its research field
    try {
      // Use collect() instead of unique() to handle potential duplicate records
      const subRecords = await ctx.db
        .query('investment_decisions')
        .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
        .collect();
      
      if (subRecords.length > 0) {
        // If there are multiple records (which shouldn't happen but might), update all of them
        for (const record of subRecords) {
          await ctx.db.patch(record._id, { 
            research: research ?? '', 
            updated_at: Date.now() 
          });
        }
        
        // Log a warning if we found multiple records
        if (subRecords.length > 1) {
          console.warn(`Found ${subRecords.length} investment_decisions records for decision ID: ${decisionId}. This is unexpected and should be fixed.`);
        }
        
        return { success: true };
      } else {
        console.warn(`Investment decision subtable record not found for decision ID: ${decisionId}. Cannot update research.`);
        // Create a new record if none exists
        await ctx.db.insert('investment_decisions', {
          decision_id: decisionId,
          description: '', // Default empty description
          research: research ?? '',
          is_active: true,
          updated_at: Date.now()
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error(`Error updating research for investment decision ${decisionId}:`, error);
      throw new ConvexError("Failed to update investment decision research.");
    }
  }
});

/**
 * Retrieves a single decision by ID
 * Returns the decision or throws an error if not found
 */
export const getDecision = zQuery({
  args: {
    id: zid('decisions')
  },
  output: Decision,
  handler: async (ctx, args) => {
    const decision = await ctx.db.get(args.id);
    if (!decision) {
      throw new Error(`Decision with ID ${args.id} not found`);
    }

    // Ensure badges is always an array, even if it's undefined in the database
    const decisionWithBadges = {
      ...decision,
      badges: decision.badges || []
    };

    // Validate and parse the decision using the Zod schema
    return Decision.parse(decisionWithBadges);
  }
});

/**
 * Search decisions by title using the search index.
 * Returns a limited list of matching decisions.
 */
export const searchDecisionsByTitle = query({
  args: {
    searchQuery: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const { searchQuery, limit = 10 } = args;

    if (!searchQuery) {
      return [];
    }

    const decisions = await ctx.db
      .query('decisions')
      .withSearchIndex('search_title', (q) =>
        q.search('title', searchQuery)
      )
      .take(limit);

    // Return raw documents
    return decisions;
  },
});

/**
 * Lists decisions with optional filtering and pagination
 * Returns an array of decisions and a continuation token for pagination
 */
export const listDecisions = zQuery({
  args: {
    filter: DecisionFilterSchema,
    pagination: PaginationSchema
  },
  output: z.object({
    decisions: z.array(Decision),
    continuation: z.string().nullable().optional()
  }),
  handler: async (ctx, args) => {
    const {
      filter,
      pagination = {
        sortBy: '_creationTime' as const,
        sortDirection: 'desc' as const,
        limit: 10
      }
    } = args;

    // Step 1: Initialize the query with the appropriate index
    let query;
    if (filter?.decision_category_id) {
      query = ctx.db
        .query('decisions')
        .withIndex('by_decision_category', (q) => 
          // Use type assertion to handle the possibly undefined value
          q.eq('decision_category_id', filter.decision_category_id as Id<'decision_categories'>)
        );
    } else if (filter?.project_ids) {
      query = ctx.db
        .query('decisions')
        .withIndex('by_project', (q) => q.eq('project_ids', filter.project_ids));
    } else if (filter?.status) {
      query = ctx.db
        .query('decisions')
        .withIndex('by_status', (q) => q.eq('status', filter.status));
    } else if (filter?.approver_id) {
      query = ctx.db
        .query('decisions')
        .withIndex('by_approver', (q) =>
          q.eq('approver_id', filter.approver_id)
        );
    } else if (filter?.driver) {
      // Handle driver field
      query = ctx.db
        .query('decisions')
        .withIndex('by_driver', (q) =>
          q.eq("driver", filter.driver)
        );
    } else {
      // No index needed, use the base query
      query = ctx.db.query('decisions');
    }

    // Step 2: Apply additional filters as a single operation
    const additionalFilters: FilterFunction[] = [];

    if (filter?.title) {
      additionalFilters.push((q) => q.eq(q.field('title'), filter.title));
    }

    if (filter?.due_date_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('due_date'), filter.due_date_before)
      );
    }

    if (filter?.due_date_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('due_date'), filter.due_date_after)
      );
    }

    if (filter?.decision_date_before) {
      additionalFilters.push((q) =>
        q.lt(q.field('decision_date'), filter.decision_date_before)
      );
    }

    if (filter?.decision_date_after) {
      additionalFilters.push((q) =>
        q.gt(q.field('decision_date'), filter.decision_date_after)
      );
    }

    // Apply the filters if there are any
    if (additionalFilters.length > 0) {
      query = query.filter((q) => {
        if (additionalFilters.length === 1) {
          return additionalFilters[0](q);
        }
        return q.and(...additionalFilters.map((filterFn) => filterFn(q)));
      });
    }

    // Step 3: Apply sorting and pagination in a single chain
    const sortField = pagination?.sortBy || '_creationTime';
    const sortDirection = pagination?.sortDirection || 'desc';

    const paginationResult = await query.order(sortDirection).paginate({
      cursor: pagination?.cursor || null,
      numItems: pagination?.limit || 10
    });

    // Validate the structure of each decision to ensure it matches our schema
    const validatedDecisions = paginationResult.page.map((decision) => {
      // Ensure badges is always an array, even if it's undefined in the database
      const decisionWithBadges = {
        ...decision,
        badges: decision.badges || []
      };
      return Decision.parse(decisionWithBadges);
    });

    // Return the paginated result with the correct structure
    return {
      decisions: validatedDecisions,
      continuation: paginationResult.continueCursor
    };
  }
});

/**
 * Counts decisions with optional filters
 * Returns the total count of matching decisions
 */
export const countDecisions = zQuery({
  args: {
    filter: DecisionFilterSchema
  },
  output: z.number(),
  handler: async (ctx, args) => {
    const { filter } = args;

    let decisions;

    // Use appropriate indexes based on filter criteria
    if (filter?.decision_category_id) {
      decisions = await ctx.db
        .query("decisions")
        .withIndex("by_decision_category", q => 
          // Use type assertion to handle the possibly undefined value
          q.eq("decision_category_id", filter.decision_category_id as Id<'decision_categories'>)
        )
        .collect();
    } else if (filter?.project_ids) {
      decisions = await ctx.db
        .query("decisions")
        .withIndex("by_project", q => q.eq("project_ids", filter.project_ids))
        .collect();
    } else if (filter?.driver) {
      decisions = await ctx.db
        .query("decisions")
        .withIndex("by_driver", q => q.eq("driver", filter.driver))
        .collect();
    } else if (filter?.approver_id) {
      decisions = await ctx.db
        .query("decisions")
        .withIndex("by_approver", q => q.eq("approver_id", filter.approver_id))
        .collect();
    } else if (filter?.status) {
      decisions = await ctx.db
        .query("decisions")
        .withIndex("by_status", q => q.eq("status", filter.status))
        .collect();
    } else if (filter?.title) {
      // Use the search index for title search
      decisions = await ctx.db
        .query("decisions")
        .withSearchIndex("search_title", q => q.search("title", filter.title || ""))
        .collect();
    } else {
      // No specific index, collect all
      decisions = await ctx.db.query("decisions").collect();
    }

    // Apply additional filters after collection
    if (decisions.length > 0) {
      if (filter?.due_date_before) {
        decisions = decisions.filter(d =>
          d.due_date !== undefined && d.due_date < filter.due_date_before!
        );
      }

      if (filter?.due_date_after) {
        decisions = decisions.filter(d =>
          d.due_date !== undefined && d.due_date > filter.due_date_after!
        );
      }

      if (filter?.decision_date_before) {
        decisions = decisions.filter(d =>
          d.decision_date !== undefined && d.decision_date < filter.decision_date_before!
        );
      }

      if (filter?.decision_date_after) {
        decisions = decisions.filter(d =>
          d.decision_date !== undefined && d.decision_date > filter.decision_date_after!
        );
      }
    }

    return decisions.length;
  },
});

/**
 * Updates one or multiple decisions
 * Returns an array of updated decision IDs
 */
export const updateDecisions = zMutation({
  args: {
    updates: BulkDecisionUpdateSchema
  },
  output: z.object({
    ids: z.array(z.string()),
    failedIds: z
      .array(
        z.object({
          id: z.string(),
          error: z.string()
    })
      )
      .optional()
  }),
  handler: async (ctx, args) => {
    const { id, ids, updates } = args.updates;

    // Handle bulk update
    if (ids && ids.length > 0) {
      const { updatedIds, failedIds } = await updateMultipleDecisions(
        ctx,
        ids,
        updates
      );
      return {
        ids: updatedIds,
        failedIds: failedIds.length > 0 ? failedIds : undefined
      };
    }
    // Handle single decision update
    else if (id) {
      try {
        const updatedId = await updateSingleDecision(ctx, id, updates);
        return { ids: [updatedId] };
      } catch (error) {
        return {
          ids: [],
          failedIds: [
            {
              id,
              error: error instanceof Error ? error.message : String(error)
            }
          ]
        };
      }
    } else {
      throw new Error(
        "Invalid arguments: must provide either 'id' or 'ids' with 'updates'"
      );
    }
  }
});

/**
 * Sets a decision's approval status
 * This is a specialized action to approve or reject a decision
 * The status must be allowed for the decision's category
 */
export const approveDecision = zMutation({
  args: {
    id: zid('decisions'),
    status: z.string(), // Now accepts any string (will be validated against category statuses)
    approver_id: zid('users')
  },
  output: z.object({
    id: z.string(),
    success: z.boolean(),
    error: z.string().optional()
  }),
  handler: async (ctx, args) => {
    const { id, status, approver_id } = args;

    try {
      // Update the decision status using the helper function
      // This will validate that the status is allowed for the category
      await updateSingleDecision(ctx, id, {
        status,
        approver_id,
        updated_at: Date.now()
      });

      return { id: id as string, success: true };
    } catch (error) {
      return { 
        id: id as string, 
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
});

/**
 * Deletes one or multiple decisions
 * Returns an array of deleted decision IDs
 */
export const deleteDecisions = zMutation({
  args: {
    decisionIds: z.array(zid('decisions'))
  },
  output: z.object({
    ids: z.array(z.string()),
    failedIds: z
      .array(
        z.object({
          id: z.string(),
          error: z.string()
        })
      )
      .optional()
  }),
  handler: async (ctx, args) => {
    // Convert single ID to array for consistent handling
    const decisionIds = Array.isArray(args.decisionIds)
      ? args.decisionIds
      : [args.decisionIds];

    if (decisionIds.length === 0) {
      throw new Error('No decision IDs provided for deletion');
    }

    const deletedIds = [];
    const failedIds = [];

    for (const decisionId of decisionIds) {
      try {
        // Check if decision exists
        const decision = await ctx.db.get(decisionId);
        if (!decision) {
          failedIds.push({
            id: decisionId,
            error: 'Decision not found'
          });
          continue;
        }

        // Delete from all possible subtables regardless of category
        // This ensures we clean up any potential duplicate or orphaned records
        
        // Delete from general_decisions
        const generalRecords = await ctx.db
          .query('general_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .collect();
        
        for (const record of generalRecords) {
          await ctx.db.delete(record._id);
        }
        
        // Delete from investment_decisions
        const investmentRecords = await ctx.db
          .query('investment_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .collect();
        
        for (const record of investmentRecords) {
          await ctx.db.delete(record._id);
        }
        
        // Add similar blocks for any future subtables
        
        // Delete the decision from the main table
        await ctx.db.delete(decisionId);
        deletedIds.push(decisionId);
      } catch (error) {
        failedIds.push({
          id: decisionId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      ids: deletedIds,
      failedIds: failedIds.length > 0 ? failedIds : undefined
    };
  }
});

/**
 * Optimized query for dashboard that returns pending decisions needing review
 * This query is tailored for dashboard views with basic filtering
 */
export const getDashboardDecisions = zQuery({
  args: {
    limit: z.number().default(5)
  },
  output: z.array(Decision),
  handler: async (ctx, args) => {
    // Get pending decisions, ordered by creation time (newest first)
    const decisions = await ctx.db
      .query('decisions')
      .withIndex('by_status', (q) => q.eq('status', 'in_review'))
      .order('desc')
      .take(args.limit);

    // Validate and transform the decisions to ensure they match our schema
    return decisions.map((decision) => {
      // Ensure badges is always an array, even if it's undefined in the database
      const decisionWithBadges = {
        ...decision,
        badges: decision.badges || []
      };
      return Decision.parse(decisionWithBadges);
    });
  }
});

// Helper function for single decision update
async function updateSingleDecision(
  ctx: MutationCtx,
  decisionId: Id<'decisions'>,
  updateFields: Partial<Doc<'decisions'>>
) {
  const decision = await ctx.db.get(decisionId);
  if (!decision) {
    throw new Error(`Decision with ID ${decisionId} not found`);
  }

  // If status is being updated, validate it against the category's allowed statuses
  if (updateFields.status !== undefined) {
    // Get the category to use (either the current one or the new one if it's being changed)
    const categoryId = updateFields.decision_category_id || decision.decision_category_id;
    const category = await ctx.db.get(categoryId);
    if (!category) {
      throw new Error(`Decision category with ID ${categoryId} not found`);
    }
    
    // Validate that the status is allowed for this category
    if (!category.statuses || !Array.isArray(category.statuses) || category.statuses.length === 0) {
      throw new Error(`Decision category with ID ${categoryId} has no statuses defined`);
    }
    
    const allowedStatuses = category.statuses.map((s: any) => s.value);
    if (!allowedStatuses.includes(updateFields.status)) {
      throw new Error(`Status "${updateFields.status}" is not allowed for category "${category.name}"`);
    }
  }

  // Check if we're changing the decision category
  if (updateFields.decision_category_id && updateFields.decision_category_id !== decision.decision_category_id) {
    // Get the old category
    const oldCategory = await ctx.db.get(decision.decision_category_id);
    if (!oldCategory) {
      throw new Error(`Old decision category with ID ${decision.decision_category_id} not found`);
    }

    // Get the new category
    const newCategory = await ctx.db.get(updateFields.decision_category_id);
    if (!newCategory) {
      throw new Error(`New decision category with ID ${updateFields.decision_category_id} not found`);
    }

    // Clean up taggings when category changes
    // 1. Get the tag_type_id from the new category
    const newCategoryTagTypeId = newCategory.tag_type_id;

    // 2. Find the general-tags tag_type by its immutable_slug
    const generalTagType = await ctx.db
      .query("tag_types")
      .filter((q) => q.eq(q.field("immutable_slug"), "general-tags"))
      .unique();
    
    const generalTagTypeId = generalTagType?._id;

    // 3. Get all taggings for the decision
    const taggings = await ctx.db
      .query("taggings")
      // Cast to 'any' to bypass potential type inference issue for the second .eq()
      .withIndex("by_taggable", q => (q.eq("taggable_type", "decision") as any).eq("taggable_id", decisionId))
      .collect();

    // 4. For each tagging, check if it should be deleted
    for (const tagging of taggings) {
      // Get the tag to check its tag_type
      const tag = await ctx.db.get(tagging.tag_id);
      if (!tag) continue;

      // Keep taggings if:
      // - Tag's tag_type is in the new category's tag_type_id array (if it's an array)
      // - Tag's tag_type is the general-tags tag_type
      const shouldKeep = 
        // If newCategoryTagTypeId is an array, check if tag.tag_type is in it
        (Array.isArray(newCategoryTagTypeId) && newCategoryTagTypeId.some(id => String(id) === String(tag.tag_type))) ||
        // If newCategoryTagTypeId is a single value, check if it matches
        (!Array.isArray(newCategoryTagTypeId) && newCategoryTagTypeId && String(newCategoryTagTypeId) === String(tag.tag_type)) ||
        // Or if it's a general tag
        (generalTagTypeId && String(tag.tag_type) === String(generalTagTypeId));

      // Delete the tagging if it shouldn't be kept
      if (!shouldKeep) {
        await ctx.db.delete(tagging._id);
      }
    }

    // Only proceed with data transfer if the subtable types are different
    if (oldCategory.subtable_type !== newCategory.subtable_type) {
      // Get the old subtable data
      let oldSubtableData: Doc<'general_decisions'> | Doc<'investment_decisions'> | null = null;
      
      if (oldCategory.subtable_type === 'general_decisions') {
        oldSubtableData = await ctx.db
          .query('general_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .unique();
      } else if (oldCategory.subtable_type === 'investment_decisions') {
        oldSubtableData = await ctx.db
          .query('investment_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .unique();
      }

      // Extract shared data (currently just description)
      const description = oldSubtableData?.description || '';
      const now = Date.now();

      // Create a new record in the target subtable
      if (newCategory.subtable_type === 'general_decisions') {
        await ctx.db.insert('general_decisions', {
          decision_id: decisionId,
          description,
          updated_at: now
        });
      } else if (newCategory.subtable_type === 'investment_decisions') {
        await ctx.db.insert('investment_decisions', {
          decision_id: decisionId,
          description,
          is_active: true, // Default value for investment decisions
          updated_at: now
        });
      }

      // Delete the old subtable record
      if (oldSubtableData) {
        await ctx.db.delete(oldSubtableData._id);
      }
    }
  }

  // Create updates object with the correct type
  const updates: Partial<Doc<'decisions'>> = {
    ...updateFields,
    updated_at: Date.now()
  };

  await ctx.db.patch(decisionId, updates);
  return decisionId;
}

// Helper function for bulk decision updates
async function updateMultipleDecisions(
  ctx: MutationCtx,
  decisionIds: Id<'decisions'>[],
  updateFields: Partial<Doc<'decisions'>>
) {
  const updatedIds = [];
  const failedIds = [];

  for (const id of decisionIds) {
    try {
      await updateSingleDecision(ctx, id, updateFields);
      updatedIds.push(id);
    } catch (error) {
      failedIds.push({
        id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  return { updatedIds, failedIds };
}

// ======================================================================
// NEW FUNCTIONS FOR HANDLING CATEGORIZED DECISION DETAILS & DESCRIPTION
// ======================================================================

/**
 * Retrieves detailed information for a single decision, including category
 * and data from the relevant subtable (general or investment).
 */
export const getDecisionDetails = query({
  args: { id: v.id('decisions') },
  handler: async (ctx, args) => {
    // 1. Fetch the core decision document
    const coreDecision = await ctx.db.get(args.id);
    if (!coreDecision) {
      console.error(`Core decision not found for ID: ${args.id}`);
      return null; // Or throw new ConvexError('Decision not found');
    }

    // 2. Fetch the decision category document
    const category = await ctx.db.get(coreDecision.decision_category_id);
    if (!category) {
      console.error(`Decision category not found for ID: ${coreDecision.decision_category_id}`);
      // Return core decision data even if category is missing? Or throw?
      // For now, return core data only. Frontend needs to handle this.
      return { core: coreDecision, category: null, subtableData: null };
    }

    // 3. Fetch data from the appropriate subtable based on category.subtable_type
    let subtableData: Doc<'general_decisions'> | Doc<'investment_decisions'> | null = null;
    try {
      // Only query the subtable that matches the decision's current category
      if (category.subtable_type === 'general_decisions') {
        try {
          // Try to get a unique record first
          subtableData = await ctx.db
            .query('general_decisions')
            .withIndex('by_decision', (q) => q.eq('decision_id', coreDecision._id))
            .unique();
        } catch (error) {
          // If unique() fails because of multiple records, handle it gracefully
          if (error instanceof Error && error.message.includes('more than one result')) {
            console.warn(`Multiple general_decisions records found for decision ID: ${coreDecision._id}. This is unexpected.`);
            
            // Get all records and use the first one
            const records = await ctx.db
              .query('general_decisions')
              .withIndex('by_decision', (q) => q.eq('decision_id', coreDecision._id))
              .collect();
            
            if (records.length > 0) {
              subtableData = records[0]; // Use the first record
              console.warn(`Found ${records.length} general_decisions records for decision ID: ${coreDecision._id}. Using the first one. Run cleanupDuplicateSubtableRecords mutation to fix.`);
            }
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      } else if (category.subtable_type === 'investment_decisions') {
        try {
          // Try to get a unique record first
          subtableData = await ctx.db
            .query('investment_decisions')
            .withIndex('by_decision', (q) => q.eq('decision_id', coreDecision._id))
            .unique();
        } catch (error) {
          // If unique() fails because of multiple records, handle it gracefully
          if (error instanceof Error && error.message.includes('more than one result')) {
            console.warn(`Multiple investment_decisions records found for decision ID: ${coreDecision._id}. This is unexpected.`);
            
            // Get all records and use the first one
            const records = await ctx.db
              .query('investment_decisions')
              .withIndex('by_decision', (q) => q.eq('decision_id', coreDecision._id))
              .collect();
            
            if (records.length > 0) {
              subtableData = records[0]; // Use the first record
              console.warn(`Found ${records.length} investment_decisions records for decision ID: ${coreDecision._id}. Using the first one. Run cleanupDuplicateSubtableRecords mutation to fix.`);
            }
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      }
      
      // If no subtable data was found, log a warning
      if (!subtableData) {
        console.warn(`No ${category.subtable_type} record found for decision ID: ${coreDecision._id}`);
      }
      // Note: 'decision_custom_data' subtable_type currently doesn't fetch specific subtable data here.
      // Custom fields would need separate handling if required on overview.

    } catch (error) {
       console.error(`Error fetching subtable data for decision ${args.id} and category ${category._id} (${category.subtable_type}):`, error);
       // Proceed without subtable data if fetch fails
    }


    // 4. Return the combined data structure
    return {
      core: coreDecision,
      category: category,
      subtableData: subtableData,
    };
  },
});


/**
 * Updates the description field for a decision within its specific subtable
 * (general_decisions or investment_decisions).
 */
/**
 * Lists all decision categories
 * Returns an array of decision categories with their IDs and names
 */
export const listDecisionCategories = query({
  args: {},
  handler: async (ctx) => {
    const categories = await ctx.db
      .query('decision_categories')
      .collect();
    
    // Return full category objects including statuses
    return categories;
  }
});

/**
 * Retrieves a decision category by its subtable type.
 * Assumes subtable_type is unique or returns the first match.
 */
export const getDecisionCategoryBySubtableType = query({
  // Use Convex validator v.union(v.literal(...)) instead of Zod enum
  args: { 
    subtableType: v.union(
      v.literal('general_decisions'), 
      v.literal('investment_decisions'), 
      v.literal('custom_decisions')
    ) 
  },
  handler: async (ctx, args) => {
    const category = await ctx.db
      .query('decision_categories')
      .withIndex('by_subtable_type', (q) => q.eq('subtable_type', args.subtableType))
      .first(); // Use first() to get the first match or null

    return category; // Returns the category document or null if not found
  },
});

/**
 * Updates the Kanban columns for a specific decision category.
 */
export const updateKanbanColumns = zMutation({
  args: {
    categoryId: zid('decision_categories'),
    kanbanColumns: z.array(KanbanColumn) // Use the imported Zod schema
  },
  output: z.object({ success: z.boolean() }),
  handler: async (ctx, args) => {
    const { categoryId, kanbanColumns } = args;

    // Validate the category exists
    const category = await ctx.db.get(categoryId);
    if (!category) {
      throw new ConvexError(`Decision category with ID ${categoryId} not found`);
    }

    try {
      // Patch the kanban_columns field
      await ctx.db.patch(categoryId, {
        kanban_columns: kanbanColumns,
        updated_at: Date.now() // Update timestamp
      });
      return { success: true };
    } catch (error) {
      console.error(`Error updating Kanban columns for category ${categoryId}:`, error);
      throw new ConvexError("Failed to update Kanban columns.");
    }
  }
});

/**
 * Utility mutation to clean up duplicate subtable records for a decision
 * This is useful when a decision has multiple records in a subtable, which shouldn't happen
 */
export const cleanupDuplicateSubtableRecords = zMutation({
  args: {
    decisionId: zid('decisions')
  },
  output: z.object({
    success: z.boolean(),
    cleanedUp: z.object({
      general_decisions: z.number(),
      investment_decisions: z.number()
    })
  }),
  handler: async (ctx, args) => {
    const { decisionId } = args;
    
    // Get the decision to check if it exists
    const decision = await ctx.db.get(decisionId);
    if (!decision) {
      throw new Error(`Decision with ID ${decisionId} not found`);
    }
    
    // Track how many records were cleaned up
    const cleanedUp = {
      general_decisions: 0,
      investment_decisions: 0
    };
    
    // Clean up general_decisions
    const generalRecords = await ctx.db
      .query('general_decisions')
      .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
      .collect();
    
    if (generalRecords.length > 1) {
      // Keep the first record and delete the rest
      for (let i = 1; i < generalRecords.length; i++) {
        await ctx.db.delete(generalRecords[i]._id);
        cleanedUp.general_decisions++;
      }
    }
    
    // Clean up investment_decisions
    const investmentRecords = await ctx.db
      .query('investment_decisions')
      .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
      .collect();
    
    if (investmentRecords.length > 1) {
      // Keep the first record and delete the rest
      for (let i = 1; i < investmentRecords.length; i++) {
        await ctx.db.delete(investmentRecords[i]._id);
        cleanedUp.investment_decisions++;
      }
    }
    
    return {
      success: true,
      cleanedUp
    };
  }
});

export const updateDecisionDescription = zMutation({
  args: {
    decisionId: zid('decisions'),
    description: z.string().optional(), // Allow optional description
  },
  output: z.object({ success: z.boolean() }),
  handler: async (ctx, args) => {
    const { decisionId, description } = args;

    // 1. Fetch the core decision to get the category ID
    const coreDecision = await ctx.db.get(decisionId);
    if (!coreDecision) {
      throw new ConvexError(`Decision not found: ${decisionId}`);
    }

    // 2. Fetch the category to determine the subtable type
    const category = await ctx.db.get(coreDecision.decision_category_id);
    if (!category) {
      throw new ConvexError(`Decision category not found for decision: ${decisionId}`);
    }

    // 3. Find the subtable record and update its description
    let updateSuccess = false;

    try {
      if (category.subtable_type === 'general_decisions') {
        // Use collect() instead of unique() to handle potential duplicate records
        const subRecords = await ctx.db
          .query('general_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .collect();
        
        if (subRecords.length > 0) {
          // If there are multiple records (which shouldn't happen but might), update all of them
          for (const record of subRecords) {
            await ctx.db.patch(record._id, { description: description ?? '', updated_at: Date.now() });
          }
          updateSuccess = true;
        } else {
          // Create a new record if none exists
          await ctx.db.insert('general_decisions', {
            decision_id: decisionId,
            description: description ?? '',
            updated_at: Date.now()
          });
          updateSuccess = true;
        }
      } else if (category.subtable_type === 'investment_decisions') {
        // Use collect() instead of unique() to handle potential duplicate records
        const subRecords = await ctx.db
          .query('investment_decisions')
          .withIndex('by_decision', (q) => q.eq('decision_id', decisionId))
          .collect();
        
        if (subRecords.length > 0) {
          for (const record of subRecords) {
            await ctx.db.patch(record._id, { description: description ?? '', updated_at: Date.now() });
          }
          updateSuccess = true;
        } else {
          await ctx.db.insert('investment_decisions', {
            decision_id: decisionId,
            description: description ?? '',
            is_active: true,
            updated_at: Date.now()
          });
          updateSuccess = true;
        }
      } else {
        // Handle 'decision_custom_data' or other types if needed
        // Currently, description is assumed to be in general/investment subtables
        return { success: false };
      }
    } catch (error) {
      throw new ConvexError("Failed to update decision description.");
    }

    return { success: updateSuccess };
  }
});

/**
 * Fetch a decision by its exact title (name).
 * Returns the first matching decision or null if not found.
 */
export const getDecisionByName = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    const result = await ctx.db
      .query('decisions')
      .withIndex('by_title', (q) => q.eq('title', args.name))
      .collect();
    // Return the first exact match, or null if not found
    return result.find(d => d.title === args.name) || null;
  },
});

/**
 * Export all decisions (with joined general/investment data) for a set of tagIds.
 */
export const exportDecisionsByTags = query({
  args: {
    tagIds: v.array(v.id('tags')),
  },
  handler: async (ctx, { tagIds }) => {
    return exportDecisionsByTagsLogic(ctx, tagIds);
  },
});

//  Query: Get general_decisions record by decisionId
export const getGeneralDecisionByDecisionId = query({
  args: { decisionId: v.id('decisions') },
  handler: async (ctx, args) => {
    // Returns the matching general_decisions record or undefined
    return await ctx.db
      .query('general_decisions')
      .withIndex('by_decision', (q) => q.eq('decision_id', args.decisionId))
      .unique()
      .catch(() => undefined);
  },
});

// 🤖 Query: Get investment_decisions record by decisionId
export const getInvestmentDecisionByDecisionId = query({
  args: { decisionId: v.id('decisions') },
  handler: async (ctx, args) => {
    // Returns the matching investment_decisions record or undefined
    return await ctx.db
      .query('investment_decisions')
      .withIndex('by_decision', (q) => q.eq('decision_id', args.decisionId))
      .unique()
      .catch(() => undefined);
  },
});
