import { action, internalQuery, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { ConvexError } from "convex/values";
import { openai } from "@ai-sdk/openai";
import { embed, embedMany } from "ai";

// Define the tables and their embedding fields
const EMBEDDING_TABLES = [
  {
    table: "people",
    sourceField: "short_description",
    embeddingField: "short_description_embedding",
  },
  {
    table: "organizations",
    sourceField: "short_description",
    embeddingField: "short_description_embedding",
  },
  {
    table: "knowledge_base",
    sourceField: "content",
    embeddingField: "content_embedding",
  },
  {
    table: "meeting_notes",
    sourceField: "content",
    embeddingField: "content_embedding",
  },
  {
    table: "projects",
    sourceField: "description",
    embeddingField: "description_embedding",
  },
  {
    table: "tasks",
    sourceField: "description",
    embeddingField: "description_embedding",
  },
  {
    table: "decisions",
    sourceField: "short_description",
    embeddingField: "short_description_embedding",
  },
  {
    table: "general_decisions",
    sourceField: "description",
    embeddingField: "description_embedding",
  },
  {
    table: "investment_decisions",
    sourceField: "description",
    embeddingField: "description_embedding",
  },
  {
    table: "projectUpdates",
    sourceField: "content",
    embeddingField: "content_embedding",
  },
];

// Maximum batch size for embedding generation
const BATCH_SIZE = 100;

// Internal query to get the cron state
export const getCronState = internalQuery({
  handler: async (ctx) => {
    const existingState = await ctx.db
      .query("cronStates")
      .withIndex("by_jobName", (q) => q.eq("jobName", "generateEmbeddings"))
      .first();
    
    return existingState;
  },
});

// Internal mutation to create a new cron state
export const createCronState = internalMutation({
  handler: async (ctx) => {
    const newStateId = await ctx.db.insert("cronStates", {
      jobName: "generateEmbeddings",
      lastRun: 0, // Start from the beginning of time
    });
    
    return await ctx.db.get(newStateId);
  },
});

// Internal mutation to update the cron state
export const updateCronState = internalMutation({
  args: { id: v.id("cronStates"), lastRun: v.number() },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, {
      lastRun: args.lastRun,
    });
  },
});

// Internal query to get rows that need embedding updates
export const getRowsToUpdate = internalQuery({
  args: { 
    table: v.string(),
    lastRun: v.number()
  },
  handler: async (ctx, args) => {
    // We need to use any here because we're dynamically accessing tables
    return await ctx.db
      .query(args.table as any)
      .withIndex("by_updated", (q) => q.gt("updated_at", args.lastRun))
      .collect();
  },
});

// Internal mutation to update a row with its embedding
export const updateRowEmbedding = internalMutation({
  args: {
    table: v.string(),
    id: v.string(),
    embeddingField: v.string(),
    embedding: v.array(v.float64()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id as any, {
      [args.embeddingField]: args.embedding,
    });
  },
});



// Cron job handler for generating embeddings
export const generateEmbeddings = action({
  handler: async (ctx) => {
    try {
      // Get or create the cron state record
      let cronState = await ctx.runQuery(internal.generateEmbeddings.getCronState);
      
      if (!cronState) {
        cronState = await ctx.runMutation(internal.generateEmbeddings.createCronState);
      }

      if (!cronState) {
        throw new Error("Failed to get or create cron state");
      }

      const lastRun = cronState.lastRun;
      const currentTime = Date.now();
      let totalProcessed = 0;

      // Process each table
      for (const tableConfig of EMBEDDING_TABLES) {
        try {
          const { table, sourceField, embeddingField } = tableConfig;
          console.log(`Processing table: ${table}`);

          // Query rows that have been updated since the last run
          const rows = await ctx.runQuery(internal.generateEmbeddings.getRowsToUpdate, { 
            table, 
            lastRun 
          });

          console.log(`Found ${rows.length} rows to process in ${table}`);
          
          if (rows.length === 0) {
            continue;
          }

          // Group rows into batches
          const batches = [];
          let currentBatch: { id: Id<any>; text: string }[] = [];
          
          for (const row of rows) {
            const sourceText = (row as any)[sourceField];
            
            // Skip if the source field is empty or not a string
            if (!sourceText || typeof sourceText !== "string") {
              continue;
            }
            
            currentBatch.push({
              id: row._id,
              text: sourceText,
            });
            
            if (currentBatch.length >= BATCH_SIZE) {
              batches.push([...currentBatch]);
              currentBatch = [];
            }
          }
          
          // Add the remaining items as a batch
          if (currentBatch.length > 0) {
            batches.push(currentBatch);
          }

          // Process each batch
          await Promise.all(
            batches.map(async (batch) => {
              const texts = batch.map((i) => i.text);
              const embeddings = await generateOpenAIEmbeddings(texts);
              await Promise.all(
                batch.map((item, i) =>
                  ctx.runMutation(internal.generateEmbeddings.updateRowEmbedding, {
                    table,
                    id: item.id,
                    embeddingField,
                    embedding: embeddings[i],
                  })
                )
              );
              totalProcessed += batch.length;
            })
          );
        } catch (tableError) {
          console.error(`Error processing table ${tableConfig.table}:`, tableError);
          // Continue with the next table
        }
      }

      // Update the cronState with the current time
      await ctx.runMutation(internal.generateEmbeddings.updateCronState, {
        id: cronState._id as Id<"cronStates">,
        lastRun: currentTime,
      });

      console.log(`Embedding generation completed. Processed ${totalProcessed} items.`);
      return { success: true, processed: totalProcessed };
    } catch (error) {
      console.error("Error in generateEmbeddings action:", error);
      return { success: false, error: String(error) };
    }
  },
});

/**
 * Generate embeddings using OpenAI's text-embedding-3-large model
 * @param input - A string or array of strings to generate embeddings for
 * @param dimensions - Optional parameter to specify the dimensions of the embedding vectors
 * @returns An array of numbers for a single input, or an array of embedding arrays for multiple inputs
 */
export async function generateOpenAIEmbeddings(input: string, dimensions?: number): Promise<number[]>;
export async function generateOpenAIEmbeddings(input: string[], dimensions?: number): Promise<number[][]>;
export async function generateOpenAIEmbeddings(
  input: string | string[],
  dimensions?: number
): Promise<number[] | number[][]> {
  try {
    // Configure the model with dimensions and encoding_format
    const modelOptions = {
      ...(dimensions ? { dimensions } : {}),
      encoding_format: "float"
    };
    const model = openai.embedding('text-embedding-3-large', modelOptions);
    
    // Use different methods based on input type
    if (Array.isArray(input)) {
      // For array inputs, use embedMany and return all embeddings
      const { embeddings } = await embedMany({
        model,
        values: input
      });
      
      return embeddings;
    } else {
      // For single string input, use embed
      const { embedding } = await embed({
        model,
        value: input
      });
      
      return embedding;
    }
  } catch (error) {
    console.error("Error generating OpenAI embeddings:", error);
    throw new ConvexError(`OpenAI API error: ${String(error)}`);
  }
};