import { v } from "convex/values";
import { <PERSON>Reader, QueryCtx } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import {
  DECISION_FIELDS,
  GENERAL_DECISION_FIELDS,
  INVESTMENT_DECISION_FIELDS,
  pickFields,
} from "./entityFieldHelpers";

// Main export function
export async function exportDecisionsByTags(ctx: QueryCtx, tagIds: string[]) {
  // 1. Find all taggings for these tags, type 'decision'
  const allTaggings = await ctx.db.query("taggings").collect();
  const taggings = allTaggings.filter(
    (t: any) => tagIds.some((id) => id === t.tag_id) && t.taggable_type === "decision"
  );
  const decisionIds = Array.from(new Set(taggings.map((t: any) => t.taggable_id)));
  if (decisionIds.length === 0) return [];

  // 2. Fetch all decisions
  const decisions = await Promise.all(
    decisionIds.map((id) => ctx.db.get(id as Id<"decisions">))
  );

  // 3. Fetch all general_decisions and investment_decisions for these decision_ids
  const allGeneralRows = await ctx.db.query("general_decisions").collect();
  const generalRows = allGeneralRows.filter((row: any) => decisionIds.includes(row.decision_id));
  const allInvestmentRows = await ctx.db.query("investment_decisions").collect();
  const investmentRows = allInvestmentRows.filter((row: any) => decisionIds.includes(row.decision_id));

  // 4. Build lookup maps
  const generalById: Record<string, any> = Object.fromEntries(
    generalRows.map((row: any) => [row.decision_id, row])
  );
  const investmentById: Record<string, any> = Object.fromEntries(
    investmentRows.map((row: any) => [row.decision_id, row])
  );

  // 5. Join and flatten for CSV export
  return decisions.filter(Boolean).map((decision: any) => {
    const flat: Record<string, any> = {
      ...pickFields(decision, DECISION_FIELDS),
    };
    const general = generalById[decision._id];
    const investment = investmentById[decision._id];
    for (const field of GENERAL_DECISION_FIELDS) {
      flat[`general_${field}`] = general ? general[field] ?? null : null;
    }
    for (const field of INVESTMENT_DECISION_FIELDS) {
      flat[`investment_${field}`] = investment ? investment[field] ?? null : null;
    }
    return flat;
  });
}
