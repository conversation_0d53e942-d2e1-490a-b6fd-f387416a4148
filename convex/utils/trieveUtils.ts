import { ActionCtx, MutationCtx, QueryCtx } from "../_generated/server";
import { Id } from "../_generated/dataModel";
import { ConvexError } from "convex/values";
import { trieve, isTrieveConfigured } from "../trieve/trieve";
import { getSubTableData, validateFileExists } from "./fileUtils";
import { api } from "../_generated/api";

/**
 * Standard delay for Trieve operations in milliseconds
 * Used across all Trieve scheduling to ensure consistency
 */
export const TRIEVE_OPERATION_DELAY = 20000;

/**
 * Prepares document data for Trieve operations
 * Note: This function requires database access and cannot be used in actions
 * 
 * @param ctx - The Convex context (Query or Mutation only).
 * @param fileId - The ID of the file record.
 * @param overrides - Optional fields to override (title, content, docType).
 * @returns The prepared document data ready for Trieve.
 */
export async function prepareTreivedDocData(
  ctx: QueryCtx | MutationCtx,
  fileId: Id<"files">, 
  overrides?: {
    title?: string;
    content?: string;
    docType?: string;
  }
) {
  // Get and validate file record
  const fileRecord = await validateFileExists(ctx, fileId);
  
  // Determine final docType
  const finalDocType = overrides?.docType ?? fileRecord.docType;
  
  // Get category and content from appropriate sub-table
  const { category, content } = await getSubTableData(ctx, fileId, finalDocType);
  
  // Use overrides if provided, otherwise use fetched data
  const finalTitle = overrides?.title ?? fileRecord.title ?? "Untitled Document";
  const finalContent = overrides?.content ?? content ?? "";
  
  // TODO: Determine how to get the correct orgId for multi-tenancy filtering
  const orgId = "PLACEHOLDER_ORG_ID"; // Replace with actual orgId logic
  // TODO: Determine if projectId is relevant/available here
  const projectId = fileRecord.parentEntityId as Id<'projects'> | null;

  return {
    id: fileId,
    title: finalTitle,
    short_description: fileRecord.short_description ?? null,
    content: finalContent,
    category: category ?? null,
    docType: finalDocType,
    projectId: projectId,
    orgId: orgId,
    createdAt: fileRecord._creationTime,
  };
}

/**
 * Verifies if Trieve is properly configured and available
 * Note: This function can be used in any context as it doesn't require database access
 * 
 * @returns An object indicating if Trieve is configured, with error details if not.
 */
export function checkTrieveConfiguration() {
  if (!isTrieveConfigured() || !trieve) {
    return {
      isConfigured: false,
      error: "Trieve not configured",
      details: "The Trieve client is not properly configured with API keys."
    };
  }
  return { isConfigured: true };
}

/**
 * Standard error handler for Trieve operations
 * Note: This function can be used in any context as it doesn't require database access
 * 
 * @param operation - The name of the Trieve operation that failed.
 * @param fileId - The file ID being processed.
 * @param error - The error that occurred.
 * @returns A standardized error response object.
 */
export function handleTrieveError(operation: string, fileId: Id<"files">, error: unknown) {
  console.error(`Error during Trieve ${operation} for document ${fileId}:`, error);
  const errorMessage = error instanceof Error ? error.message : String(error);
  return {
    status: "error" as const,
    documentId: fileId,
    error: errorMessage,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Helper for chunking content into paragraphs for Trieve
 * Note: This function can be used in any context as it doesn't require database access
 * 
 * @param content - The document content to chunk.
 * @returns An array of content chunks.
 */
export function chunkContentByParagraphs(content: string): string[] {
  // Basic split by double newline, filter empty paragraphs
  const paragraphs = content.split(/\n\s*\n/).map(p => p.trim()).filter(p => p.length > 0);

  // If no paragraphs were extracted (e.g., single paragraph without newlines),
  // treat the entire content as one chunk
  if (paragraphs.length === 0 && content.trim().length > 0) {
    return [content.trim()];
  }

  return paragraphs;
}

/**
 * Schedules a Trieve operation (index, reindex, delete)
 * Note: This function requires mutation or action context for scheduling
 * 
 * @param ctx - The Convex context (Mutation or Action).
 * @param params - Parameters including fileId and operation type.
 * @returns A result object indicating success or failure.
 */
export async function scheduleTrieveOperation(
  ctx: MutationCtx | ActionCtx,
  params: {
    fileId: Id<"files">;
    operationType: 'index' | 'reindex' | 'delete';
    forceDelete?: boolean;
  }
) {
  const { fileId, operationType, forceDelete = false } = params;
  
  try {
    // For delete operations, call the delete mutation directly
    if (operationType === 'delete') {
      await ctx.runMutation(api.trieve.deleteDocument.default, { docId: fileId });
      return {
        status: "scheduled" as const,
        documentId: fileId,
        message: `Successfully scheduled deletion for document ${fileId}`
      };
    }

    // For index/reindex operations, schedule the appropriate action
    if (operationType === 'reindex') {
      await ctx.scheduler.runAfter(
        TRIEVE_OPERATION_DELAY,
        api.actions.trieveActions.performTrieveReindex,
        { fileId, forceDelete }
      );
    } else {
      // For index operations, schedule the document preparation and indexing
      // Note: We pass just the fileId and let the action handle the database operations
      await ctx.scheduler.runAfter(
        TRIEVE_OPERATION_DELAY,
        api.actions.trieveActions.trieveUpsertDocumentChunks,
        { doc: { 
            id: fileId, 
            title: "Placeholder Title", 
            content: "Placeholder Content", 
            docType: "DOCUMENT", 
            orgId: "PLACEHOLDER_ORG_ID", 
            createdAt: Date.now() 
          } 
        }
      );
    }
    
    console.log(`Successfully scheduled Trieve ${operationType} for document: ${fileId}`);
    return {
      status: "scheduled" as const,
      documentId: fileId,
      message: `Successfully scheduled ${operationType} for document ${fileId}`
    };
  } catch (error) {
    return handleTrieveError(operationType, fileId, error);
  }
}

/**
 * Helper function to check if a file has content for Trieve
 * Note: This function requires database access and cannot be used in actions
 * 
 * @param ctx - The Convex context (Query or Mutation only).
 * @param fileId - The ID of the file to check.
 * @returns A boolean indicating if the file has content.
 */
export async function fileHasContentForTrieve(ctx: QueryCtx | MutationCtx, fileId: Id<"files">) {
  const fileRecord = await ctx.db.get(fileId);
  if (!fileRecord) return false;
  
  const { content } = await getSubTableData(ctx, fileId, fileRecord.docType);
  return !!content && content.trim().length > 0;
}