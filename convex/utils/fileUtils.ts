import { Id } from "../_generated/dataModel";
import { MutationCtx, QueryCtx } from "../_generated/server";
import { ConvexError } from "convex/values";

/**
 * Helper function to get category and content from document sub-tables.
 * 
 * @param ctx - The Convex query or mutation context.
 * @param fileId - The ID of the main file record in the 'files' table.
 * @param docType - The document type (e.g., 'KNOWLEDGE_BASE', 'MEETING_NOTES').
 * @returns An object containing the category and content, or null/undefined if not found.
 */
export async function getSubTableData(ctx: QueryCtx | MutationCtx, fileId: Id<"files">, docType: string) {
  let category: string | null | undefined = null;
  let content: string | null | undefined = null;

  switch (docType) {
    case 'KNOWLEDGE_BASE':
      const kbRecord = await ctx.db.query('knowledge_base').withIndex('by_file', q => q.eq('fileId', fileId)).first();
      category = kbRecord?.category;
      content = kbRecord?.content;
      break;
    case 'MEETING_NOTES':
      const mnRecord = await ctx.db.query('meeting_notes').withIndex('by_file', q => q.eq('fileId', fileId)).first();
      category = mnRecord?.category;
      content = mnRecord?.content;
      break;
    // Add cases for 'CONTRACT', 'DOCUMENT' if they have category/content fields
    default:
      console.warn(`No specific sub-table logic for docType: ${docType} in getSubTableData`);
  }
  return { category, content };
}

/**
 * Helper to get a complete file record with its sub-table data
 * 
 * @param ctx - The Convex query or mutation context.
 * @param fileId - The ID of the main file record in the 'files' table.
 * @returns The combined file record with content and category, or null if not found.
 */
export async function getCompleteFileRecord(ctx: QueryCtx | MutationCtx, fileId: Id<"files">) {
  const fileRecord = await ctx.db.get(fileId);
  if (!fileRecord) return null;
  
  const { category, content } = await getSubTableData(ctx, fileId, fileRecord.docType);
  
  return {
    ...fileRecord,
    category,
    content
  };
}

/**
 * Helper to check if a file exists and has content
 * 
 * @param ctx - The Convex query or mutation context.
 * @param fileId - The ID of the main file record in the 'files' table.
 * @returns Boolean indicating if the file exists and has non-empty content.
 */
export async function fileHasContent(ctx: QueryCtx | MutationCtx, fileId: Id<"files">) {
  const fileRecord = await ctx.db.get(fileId);
  if (!fileRecord) return false;
  
  const { content } = await getSubTableData(ctx, fileId, fileRecord.docType);
  return !!content && content.trim().length > 0;
}

/**
 * Helper to validate that a file exists and throw a standardized error if not
 * 
 * @param ctx - The Convex query or mutation context.
 * @param fileId - The ID of the main file record in the 'files' table.
 * @returns The file record if it exists.
 * @throws ConvexError if the file doesn't exist.
 */
export async function validateFileExists(ctx: QueryCtx | MutationCtx, fileId: Id<"files">) {
  const fileRecord = await ctx.db.get(fileId);
  if (!fileRecord) {
    throw new ConvexError(`File record not found: ${fileId}`);
  }
  return fileRecord;
}
