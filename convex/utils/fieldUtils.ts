import { z } from 'zod';
import { zMutation } from '../functions';
import { ConvexError } from 'convex/values';
import { Id } from '../_generated/dataModel';


/**
 * Updates a specific field of any document in any table.
 * This is a generic utility mutation that can be used by actions to update document fields.
 */
export const updateAnyField = zMutation({
  args: {
    tableName: z.string().min(1),
    documentId: z.string().min(1),
    field: z.string().min(1).refine(name => !name.startsWith('_'), {
      message: "Field cannot be an internal field starting with '_'",
    }),
    value: z.any(),
    updateTimestamp: z.boolean().default(true),
  },
  output: z.object({
    success: z.boolean(),
    message: z.string(),
  }),
  handler: async (ctx, args) => {
    try {
      const { tableName, documentId, field, value, updateTimestamp } = args;

      try {
        // Convert string ID to Id type and get the document
        const id = documentId as Id<any>;
        const document = await ctx.db.get(id);
        
        // Check if the document exists
        if (!document) {
          throw new ConvexError({
            message: `Document with ID ${documentId} not found`,
            code: "NOT_FOUND",
          });
        }

        // Create an update object with the field and value
        // Special handling: If the field ends with "JobId" and value is null, 
        // use undefined to clear the field (for optional Id fields)
        const update: Record<string, any> = {};
        if (field.endsWith("JobId") && value === null) {
          // For job ID fields, use undefined instead of null to correctly clear them
          update[field] = undefined;
        } else {
          update[field] = value;
        }
        
        // Add updated_at timestamp if requested
        if (updateTimestamp) {
          update.updated_at = Date.now();
        }
        
        // Log the update
        console.log(`Updating ${tableName} document ${documentId} field '${field}' with value: ${typeof value === 'string' ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : JSON.stringify(value)}`);
        
        // Update the document using the ID directly
        await ctx.db.patch(id, update);
        
        console.log(`Successfully updated ${tableName} document ${documentId} field '${field}'`);
        
        return {
          success: true,
          message: `Field '${field}' updated successfully in ${tableName} document ${documentId}`,
        };
      } catch (error) {
        console.error(`Database error in updateAnyField:`, error);
        throw new ConvexError({
          message: `Could not update document ${documentId}: ${error instanceof Error ? error.message : "Unknown error"}`,
          code: "UPDATE_FAILED",
        });
      }
    } catch (error: unknown) {
      console.error(`Error updating field:`, error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

