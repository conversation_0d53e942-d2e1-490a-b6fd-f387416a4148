import { Id } from '../_generated/dataModel';

/**
 * Represents a simple mention extracted from text, containing only display name and ID.
 * The type ('user' or 'team') needs to be determined separately.
 */
export interface SimpleMention {
  id: string; // Raw ID extracted from markup
  displayName: string;
}

/**
 * Represents a mention extracted from text, including its type (DEPRECATED - Use SimpleMention and determine type separately)
 */
export interface TypedMention {
  id: Id<'people'> | Id<'teams'>;
  displayName: string;
  type: 'user' | 'team';
}


/**
 * Extracts mentioned entity IDs (people or teams) from text containing react-mentions markup. (DEPRECATED)
 * @deprecated Use extractFullMentionsFromText which returns SimpleMention objects.
 */
export function extractMentionsFromText(text: string): (Id<'people'> | Id<'teams'>)[] {
  // This function remains for backward compatibility if needed, but is deprecated.
  console.warn("extractMentionsFromText is deprecated. Use extractFullMentionsFromText instead.");
  if (!text) return [];
  const mentionRegex = /@\[[^\]]+\]\(([^)]+)\)/g; // Simplified regex for @[display](id)
  const mentions: (Id<'people'> | Id<'teams'>)[] = [];
  let match;
  while ((match = mentionRegex.exec(text)) !== null) {
    // Cannot determine type here, casting is unsafe.
    mentions.push(match[1] as Id<'people'> | Id<'teams'>);
  }
  return mentions;
}


/**
 * Extracts simple mention objects (displayName and id) from text containing react-mentions markup.
 * Expects markup format: @[displayName](id)
 * The type ('user' or 'team') must be determined separately by looking up the ID.
 * @param text Text containing mentions
 * @returns Array of SimpleMention objects
 */
export function extractFullMentionsFromText(text: string): SimpleMention[] {
  if (!text) return [];

  // Simplified regex to capture display name (group 1) and the ID (group 2)
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g; 
  const mentions: SimpleMention[] = [];

  let match;
  while ((match = mentionRegex.exec(text)) !== null) {
    const displayName = match[1];
    const id = match[2]; // This is the raw ID string

    // Push the simple object without type information
    mentions.push({
      displayName: displayName,
      id: id // Store the raw ID string
    });
  }

  // Logging removed as type determination is moved to frontend

  return mentions;
}
