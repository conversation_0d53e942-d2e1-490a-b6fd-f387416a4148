import { type MutationCtx, internalMutation } from "../_generated/server";
import { type FunctionReference } from "convex/server";
import { type Id, type Doc } from "../_generated/dataModel";
import { ConvexError } from "convex/values";
import { v } from "convex/values";
import { api } from "../_generated/api"; // Import api for internal mutation reference

/**
 * Schedules a Convex job (action or mutation) with debouncing using explicit cancellation.
 * Stores the scheduled job ID in a specified field on the document.
 *
 * @param ctx - The Convex mutation context.
 * @param args - Arguments for scheduling the debounced job.
 * @param args.tableName - The name of the table containing the document.
 * @param args.documentId - The ID of the document to associate the job with.
 * @param args.jobIdFieldName - The name of the field in the document where the job ID is stored (e.g., "shortDescriptionJobId").
 * @param args.jobToSchedule - A reference to the Convex action or mutation to schedule.
 * @param args.jobArgs - The arguments to pass to the scheduled job.
 * @param args.delayMs - The debounce delay in milliseconds.
 * @returns The ID of the newly scheduled job.
 * @throws ConvexError if the document is not found.
 */
export async function scheduleDebouncedJob(
  ctx: MutationCtx,
  args: {
    tableName: string; // Added tableName parameter
    documentId: Id<any>; // Generic document ID
    jobIdFieldName: string; // Generic field name
    jobToSchedule: FunctionReference<"action" | "mutation">;
    jobArgs: any; // Arguments for the job being scheduled
    delayMs: number;
  }
): Promise<Id<"_scheduled_functions">> {
  const { tableName, documentId, jobIdFieldName, jobToSchedule, jobArgs, delayMs } = args;

  console.log(`[Debounce] Scheduling job for ${tableName} ${documentId}, field ${jobIdFieldName}, delay ${delayMs}ms`);

  // 1. Fetch the document record
  const record = await ctx.db.get(documentId);
  if (!record) {
    throw new ConvexError(`${tableName} record ${documentId} not found.`);
  }

  // 2. Read the existing job ID
  const existingJobId = record[jobIdFieldName] as Id<"_scheduled_functions"> | undefined;

  // 3. Cancel the previous job if it exists
  if (existingJobId) {
    console.log(`[Debounce] Attempting to cancel previous job: ${existingJobId} for field ${jobIdFieldName}`);
    try {
      await ctx.scheduler.cancel(existingJobId);
      console.log(`[Debounce] Successfully cancelled previous job: ${existingJobId}`);
    } catch (error) {
      // Log error if cancellation fails (e.g., job already ran or was cancelled)
      console.warn(`[Debounce] Failed to cancel job ${existingJobId}, it might have already completed or been cancelled:`, error);
    }
  } else {
    console.log(`[Debounce] No previous job ID found for field ${jobIdFieldName} on ${tableName} ${documentId}.`);
  }

  // 4. Schedule the new job
  console.log(`[Debounce] Scheduling new job for ${jobIdFieldName} on ${tableName} ${documentId}`);
  const newJobId = await ctx.scheduler.runAfter(delayMs, jobToSchedule, jobArgs);
  console.log(`[Debounce] Scheduled new job with ID: ${newJobId}`);

  // 5. Patch the record with the new job ID
  // Use a dynamic update object with the field name
  await ctx.db.patch(documentId, { [jobIdFieldName]: newJobId });
  console.log(`[Debounce] Stored new job ID ${newJobId} in field ${jobIdFieldName} for ${tableName} ${documentId}`);

  // 6. Return the new job ID
  return newJobId;
}


/**
 * INTERNAL MUTATION: Clears a specified scheduled job ID field from a document.
 * Called by the scheduled job (action/mutation) itself upon successful completion.
 */
export const internalClearScheduledJobId = internalMutation({
  args: {
    tableName: v.string(), // Added tableName parameter
    documentId: v.id("any"), // Generic document ID
    jobIdFieldName: v.string() // Generic field name
  },
  handler: async (ctx: MutationCtx, args: { tableName: string, documentId: Id<any>, jobIdFieldName: string }) => {
    const { tableName, documentId, jobIdFieldName } = args;

    const record = await ctx.db.get(documentId);

    if (record && record[jobIdFieldName] !== undefined) {
      // Use dynamic field name in the patch object
      await ctx.db.patch(documentId, { [jobIdFieldName]: undefined });
      console.log(`[Debounce Internal] Cleared job ID field '${jobIdFieldName}' for ${tableName} ${documentId}`);
    } else if (record) {
      console.log(`[Debounce Internal] No job ID found to clear for field '${jobIdFieldName}' on ${tableName} ${documentId}`);
    } else {
      console.warn(`[Debounce Internal] ${tableName} record not found for ID ${documentId} when trying to clear job ID field '${jobIdFieldName}'.`);
    }
  },
});
