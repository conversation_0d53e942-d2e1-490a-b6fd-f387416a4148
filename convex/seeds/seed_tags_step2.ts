// @ts-nocheck

import { z } from 'zod';
import { Id } from '../_generated/dataModel';
import { zMutation } from '../functions';

/**
 * Creates initial tags in bulk
 * This is a utility function for initial setup
 * Checks for existing tags to prevent duplication
 *
 * @returns Array of created tag IDs
 */
export const createInitialTags = zMutation({
  args: {},
  output: z.array(z.string()),
  handler: async (ctx) => {
    const now = Date.now();
    const tagIds: Record<string, Id<'tags'>> = {};

    // First, get the expense-categories tag type ID
    const expenseCategoryType = await ctx.db
      .query('tag_types')
      .withIndex('by_name', (q) => q.eq('name', 'Expense Category'))
      .unique();

    if (!expenseCategoryType) {
      throw new Error('Expense Category tag type not found. Please run createInitialTagTypes first.');
    }

    // Define tag types
    const tagTypes = {
      EXPENSE: expenseCategoryType._id,
      INVESTMENT: 'Investment Themes',
      PHILANTHROPY: 'Philanthropy Themes',
      GENERAL: 'Tags'
    };

    // First, create all parent tags
    const parentTags = [
      { name: 'Education', tag_type: tagTypes.EXPENSE },
      { name: 'Transportation', tag_type: tagTypes.EXPENSE },
      { name: 'Philanthropy & Gifts', tag_type: tagTypes.EXPENSE },
      { name: 'Healthcare', tag_type: tagTypes.EXPENSE },
      { name: 'Retail & Shopping', tag_type: tagTypes.EXPENSE },
      { name: 'Insurance & Taxes', tag_type: tagTypes.EXPENSE },
      { name: 'Travel', tag_type: tagTypes.EXPENSE },
      { name: 'Personal Care & Lifestyle', tag_type: tagTypes.EXPENSE },
      { name: 'Financial Transactions', tag_type: tagTypes.EXPENSE },
      { name: 'Investments & Property', tag_type: tagTypes.EXPENSE },
      { name: 'Professional Fees & Services', tag_type: tagTypes.EXPENSE },
      { name: 'Technology & Communication', tag_type: tagTypes.EXPENSE },
      { name: 'Dining & Groceries', tag_type: tagTypes.EXPENSE },
      { name: 'Needs Review', immutable_slug: 'needs-review', tag_type: tagTypes.EXPENSE }
    ];

    for (const { name, tag_type, immutable_slug } of parentTags) {
      const existingTag = await ctx.db
        .query('tags')
        .withIndex('by_name', (q) => q.eq('name', name))
        .filter(
          (q) =>
            q.eq(q.field('tag_type'), tag_type) && q.eq(q.field('parent_id'), undefined)
        )
        .unique();

      let id: Id<'tags'>;
      if (existingTag) {
        id = existingTag._id;
        await ctx.db.patch(existingTag._id, { 
          updated_at: now,
          immutable_slug 
        });
      } else {
        id = await ctx.db.insert('tags', {
          name,
          tag_type,
          parent_id: undefined,
          immutable_slug,
          updated_at: now
        });
      }

      tagIds[name] = id;
    }

    // Then create all subtags with their parent references
    const subtags = [
      { name: 'Auto', parent: 'Financial Transactions' },
      { name: 'Boat Repairs & Maintenance', parent: 'Transportation' },
      { name: 'General', parent: 'Transportation' },
      { name: 'Charitable Donations', parent: 'Philanthropy & Gifts' },
      { name: 'Gifts', parent: 'Philanthropy & Gifts' },
      { name: 'Medical & Dental', parent: 'Healthcare' },
      { name: 'Health & Fitness', parent: 'Personal Care & Lifestyle' },
      { name: 'Medical Reimbursement', parent: 'Healthcare' },
      { name: 'Amazon', parent: 'Retail & Shopping' },
      { name: 'Books & Subscriptions', parent: 'Personal Care & Lifestyle' },
      { name: 'Memberships', parent: 'Personal Care & Lifestyle' },
      { name: 'Laundry/Drycleaning', parent: 'Personal Care & Lifestyle' },
      { name: 'Pet Care', parent: 'Personal Care & Lifestyle' },
      { name: 'Recreation', parent: 'Personal Care & Lifestyle' },
      { name: 'Wardrobe & Clothing', parent: 'Investments & Property' },
      { name: 'Wine', parent: 'Personal Care & Lifestyle' },
      { name: 'Cash Draw', parent: 'Financial Transactions' },
      { name: 'Digital Payments', parent: 'Investments & Property' },
      { name: 'Computer', parent: 'Technology & Communication' },
      { name: "Children's Education", parent: 'Education' },
      { name: 'Filing Fees', parent: 'Professional Fees & Services' },
      { name: 'Accounting', parent: 'Personal Care & Lifestyle' },
      { name: 'Legal', parent: 'Professional Fees & Services' },
      {
        name: 'Other Professional Fees',
        parent: 'Professional Fees & Services'
      },
      { name: 'Insurance', parent: 'Insurance & Taxes' },
      { name: 'Taxes', parent: 'Insurance & Taxes' },
      { name: 'Lodging', parent: 'Travel' },
      { name: 'E. 57th Street', parent: 'Investments & Property' },
      { name: 'Flat Rocks', parent: 'Investments & Property' },
      { name: 'Morrison', parent: 'Investments & Property' },
      { name: 'Groceries', parent: 'Dining & Groceries' },
      { name: 'AI Uncertain', immutable_slug: 'ai-uncertain', parent: 'Needs Review' },
      { name: 'Mortgage', parent: 'Investments & Property' },
      { name: 'Dining', parent: 'Dining & Groceries' },
      { name: 'Internet', parent: 'Technology & Communication' },
      { name: 'Rideshare', parent: 'Transportation' }
    ];

    for (const { name, parent, immutable_slug } of subtags) {
      const parentId = tagIds[parent];
      if (!parentId) {
        throw new Error(
          `Parent tag "${parent}" not found for subtag "${name}"`
        );
      }

      // Get parent tag to inherit type
      const parentTag = await ctx.db.get(parentId);
      if (!parentTag) {
        throw new Error(`Parent tag "${parent}" not found`);
      }

      // Check if this subtag already exists with the same parent
      const existingSubtag = await ctx.db
        .query('tags')
        .withIndex('by_parent_name', (q) =>
          q.eq('parent_id', parentId).eq('name', name)
        )
        .unique();

      // Only insert if the subtag doesn't already exist
      if (!existingSubtag) {
        await ctx.db.insert('tags', {
          name,
          tag_type: parentTag.tag_type,
          parent_id: parentId,
          immutable_slug,
          updated_at: now
        });
      } else {
        // Update timestamp if needed
        await ctx.db.patch(existingSubtag._id, { updated_at: now });
      }
    }

    // Return all parent tag IDs in an array
    return Object.values(tagIds).map((id) => id.toString());
  }
});
