import { mutation } from "../_generated/server";
import { v } from "convex/values";

/**
 * Creates or updates a prompt in the database.
 * This is used for seeding prompts for AI-generated content.
 */
export const createOrUpdatePrompt = mutation({
  args: {
    immutable_slug: v.string(),
    name: v.string(),
    prompt_text: v.string(),
  },
  handler: async (ctx, args) => {
    const { immutable_slug, name, prompt_text } = args;

    // Check if the prompt already exists
    const existingPrompt = await ctx.db
      .query("prompts")
      .filter((q) => q.eq(q.field("immutable_slug"), immutable_slug))
      .first();

    if (existingPrompt) {
      // Update the existing prompt
      await ctx.db.patch(existingPrompt._id, {
        name,
        prompt_text,
        updated_at: Date.now(),
      });
      console.log(`Updated prompt: ${immutable_slug}`);
      return existingPrompt._id;
    } else {
      // Create a new prompt
      const promptId = await ctx.db.insert("prompts", {
        immutable_slug,
        name,
        prompt_text,
        updated_at: Date.now(),
      });
      console.log(`Created prompt: ${immutable_slug}`);
      return promptId;
    }
  },
});

/**
 * Seeds the person short description prompt.
 * This prompt is used to generate a concise description of a person.
 */
export const seedPersonShortDescriptionPrompt = mutation({
  args: {},
  handler: async (ctx) => {
    const promptText = `You are an expert at creating concise, professional summaries of people based on their descriptions and research information. 

Your task is to create a short, one-paragraph summary (2-3 sentences) that captures the essence of the person. Focus on their professional role, expertise, and notable achievements.

The summary should be:
- Professional and factual
- Concise (maximum 2-3 sentences)
- Focused on key information
- Written in third person
- Free of subjective opinions or marketing language

Example format:
"[Name] is a [role/profession] with expertise in [key areas]. They have [notable achievement or experience] and [another key point if relevant]."

Use the information provided below to create this summary. If insufficient information is provided, create a summary based on what is available without making assumptions.`;

    return await ctx.db.insert("prompts", {
      immutable_slug: "person-short-description",
      name: "Person Short Description",
      prompt_text: promptText,
      updated_at: Date.now(),
    });
  },
});

/**
 * Seeds the organization short description prompt.
 * This prompt is used to generate a concise description of an organization.
 */
export const seedOrganizationShortDescriptionPrompt = mutation({
  args: {},
  handler: async (ctx) => {
    const promptText = `You are an expert at creating concise, professional summaries of organizations based on their descriptions and research information.

Your task is to create a short, one-paragraph summary (2-3 sentences) that captures the essence of the organization. Focus on the organization's industry, main offerings, and notable characteristics.

The summary should be:
- Professional and factual
- Concise (maximum 2-3 sentences)
- Focused on key information
- Written in third person
- Free of subjective opinions or marketing language

Example format:
"[Organization Name] is a [industry/type] organization that [main function/offering]. They specialize in [key specialization] and [another key point if relevant]."

Use the information provided below to create this summary. If insufficient information is provided, create a summary based on what is available without making assumptions.`;

    return await ctx.db.insert("prompts", {
      immutable_slug: "organization-short-description",
      name: "Organization Short Description",
      prompt_text: promptText,
      updated_at: Date.now(),
    });
  },
});
