// @ts-nocheck

import { z } from 'zod';
import { Id } from '../_generated/dataModel';
import { zMutation } from '../functions';

/**
 * Creates initial tag types in bulk
 * This is a utility function for initial setup
 * Checks for existing tag types to prevent duplication
 *
 * @returns Array of created tag type IDs
 */
export const createInitialTagTypes = zMutation({
  args: {},
  output: z.array(z.string()),
  handler: async (ctx) => {
    const now = Date.now();
    const tagTypeIds: Id<'tag_types'>[] = [];

    // Define tag types with their descriptions
    const tagTypesToCreate = [
      { name: 'Expense Category', description: 'Used to categorize spending', immutable_slug: 'expense-categories' },
      { name: 'Tags', description: 'General tags used throughout the system', immutable_slug: 'general-tags' },
      {
        name: 'Investment Themes',
        description: 'Themes used to categorize investments'
      },
      {
        name: 'Philanthropy Themes',
        description: 'Themes used to categorize philanthropy'
      }
    ];

    for (const { name, description, immutable_slug } of tagTypesToCreate) {
      const existingTagType = await ctx.db
        .query('tag_types')
        .withIndex('by_name', (q) => q.eq('name', name))
        .unique();

      let id: Id<'tag_types'>;
      if (existingTagType) {
        id = existingTagType._id;
        await ctx.db.patch(existingTagType._id, {
          description,
          immutable_slug,
          updated_at: now
        });
      } else {
        id = await ctx.db.insert('tag_types', {
          name,
          description,
          immutable_slug,
          updated_at: now
        });
      }
      tagTypeIds.push(id);
    }

    return tagTypeIds.map((id) => id.toString());
  }
});
