// @ts-nocheck

import { z } from 'zod';
import { Id } from '../_generated/dataModel';
import { zMutation } from '../functions';

/**
 * Creates initial badges across all domains
 * This is a utility function for initial setup
 * Checks for existing badges to prevent duplication
 *
 * @returns Array of created badge IDs
 */
export const createInitialBadges = zMutation({
  args: {},
  output: z.array(z.string()),
  handler: async (ctx) => {
    const badgeIds: Id<"badges">[] = [];

    // Define initial badges across all domains
    const badgesToCreate = [
      // Task badges
      { name: "Task - To Do", color: "#FFA500", description: "Task is pending.", badgeDomain: "task" },
      { name: "Task - In Progress", color: "#0000FF", description: "Task is in progress.", badgeDomain: "task" },
      { name: "Task - Completed", color: "#008000", description: "Task is completed.", badgeDomain: "task" },
      
      // Project badges
      { name: "Project - Not Started", color: "#808080", description: "Project has not started yet.", badgeDomain: "project" },
      { name: "Project - In Progress", color: "#0000FF", description: "Project is in progress.", badgeDomain: "project" },
      { name: "Project - Completed", color: "#008000", description: "Project is completed.", badgeDomain: "project" },
      
      // Decision badges
      { name: "Decision - Pending", color: "#FFA500", description: "Decision is pending review.", badgeDomain: "decision" },
      { name: "Decision - Approved", color: "#008000", description: "Decision has been approved.", badgeDomain: "decision" },
      { name: "Decision - Rejected", color: "#FF0000", description: "Decision has been rejected.", badgeDomain: "decision" },
      
      // Organization badges
      { name: "Organization - Vendor", color: "#008000", description: "Organization is Vendor.", badgeDomain: "organization" },
      
      // User badges
      { name: "User - Active", color: "#008000", description: "User is active.", badgeDomain: "users" },
      { name: "User - Inactive", color: "#808080", description: "User is inactive.", badgeDomain: "users" },
      
      // People badges
      { name: "Person - Verified", color: "#008000", description: "Person is verified.", badgeDomain: "people" },
      { name: "Person - Unverified", color: "#FF0000", description: "Person is unverified.", badgeDomain: "people" },
    ];

    // For each badge definition, check if it already exists and patch or insert accordingly.
    for (const { name, color, description, badgeDomain } of badgesToCreate) {
      const existingBadge = await ctx.db
        .query("badges")
        .withIndex("by_name", (q) => q.eq("name", name))
        .unique();

      let id: Id<"badges">;
      if (existingBadge) {
        id = existingBadge._id;
        await ctx.db.patch(existingBadge._id, { color, description, badgeDomain });
      } else {
        id = await ctx.db.insert("badges", { name, color, description, badgeDomain });
      }
      badgeIds.push(id);
    }

    return badgeIds.map((id) => id.toString());
  }
});
