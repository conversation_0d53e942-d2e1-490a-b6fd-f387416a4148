// @ts-nocheck

import { z } from 'zod';
import { Id } from '../_generated/dataModel';
import { zMutation } from '../functions';

/**
 * Regenerates people records from users table
 * This mutation will:
 * 1. Get all users from the users table
 * 2. Create people records for users that don't have one
 * 3. Skip users that already have a person record
 * 
 * @returns Object containing count of created records and skipped records
 */
export const regeneratePeople = zMutation({
  args: {},
  output: z.object({
    created: z.number(),
    skipped: z.number(),
  }),
  handler: async (ctx) => {
    const now = Date.now();
    let created = 0;
    let skipped = 0;

    // Get all users
    const users = await ctx.db.query("users").collect();

    for (const user of users) {
      if (!user.email) continue; // Skip users without email

      // Check if person already exists with this email
      const existingPerson = await ctx.db
        .query("people")
        .withIndex("by_email", (q) => q.eq("email", user.email))
        .unique();

      if (existing<PERSON>erson) {
        // Skip if person already exists
        skipped++;
        continue;
      }

      // Create new person only if they don't exist
      await ctx.db.insert("people", {
        name: user.name || "Unknown",
        email: user.email,
        user_id: user._id,
        image: user.image,
        updated_at: now
      });
      created++;
    }

    return { created, skipped };
  }
});
