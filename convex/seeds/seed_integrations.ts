// @ts-nocheck

import { z } from 'zod';
import { Id } from '../_generated/dataModel';
import { zMutation } from '../functions';

/**
 * Creates canonical integrations in bulk
 * This is a utility function for initial setup
 * Checks for existing integrations to prevent duplication
 * All integrations start with status NEEDS_SETUP
 *
 * @returns Array of created integration IDs
 */
export const createCanonicalIntegrations = zMutation({
  args: {},
  output: z.array(z.string()),
  handler: async (ctx) => {
    const now = Date.now();
    const integrationIds: Id<'integrations'>[] = [];

    // Define canonical integrations
    const integrationsToCreate = [
      { 
        immutable_slug: 'slack', 
        display_name: 'Slack', 
        description: 'Connect to Slack for team communication and notifications',
        expected_config: {
          client_id: {
            type: "string",
            required: true,
            description: "Your Slack client ID"
          },
          client_secret: {
            type: "string",
            required: true,
            description: "Your Slack client secret",
            sensitive: true
          },
          environment: {
            type: "string",
            required: false,
            description: "Environment (production or development)"
          }
        },
        status: 'NEEDS_SETUP' as const
      },
      { 
        immutable_slug: 'jira', 
        display_name: '<PERSON><PERSON>', 
        description: 'Integrate with Jira for project and issue tracking',
        expected_config: {
          client_id: {
            type: "string",
            required: true,
            description: "Your Jira client ID"
          },
          client_secret: {
            type: "string",
            required: true,
            description: "Your Jira client secret",
            sensitive: true
          },
          environment: {
            type: "string",
            required: false,
            description: "Environment (production or development)"
          }
        },
        status: 'NEEDS_SETUP' as const
      },
      { 
        immutable_slug: 'bill-com', 
        display_name: 'Bill.com', 
        description: 'Connect to Bill.com for invoice and payment management',
        expected_config: {
          // Bill.com specific configuration fields
          environmentURL: {
            type: "string",
            required: true,
            description: "The Bill.com environment URL (sandbox or production)",
            default: ""
          },
          devKey: {
            type: "string",
            required: true,
            description: "Your Bill.com developer key",
            default: ""
          },
          orgId: {
            type: "string",
            required: true,
            description: "Your Bill.com organization ID",
            default: ""
          },
          apiUserName: {
            type: "string",
            required: true,
            description: "Your Bill.com API username",
            default: ""
          },
          apiPasswordOrToken: {
            type: "string",
            required: true,
            description: "Your Bill.com API password or token",
            sensitive: true,
            default: ""
          }
        },
        status: 'NEEDS_SETUP' as const
      },
      { 
        immutable_slug: 'github', 
        display_name: 'GitHub', 
        description: 'Integrate with GitHub for code repository management',
        expected_config: {
          client_id: {
            type: "string",
            required: true,
            description: "Your GitHub OAuth App client ID"
          },
          client_secret: {
            type: "string",
            required: true,
            description: "Your GitHub OAuth App client secret",
            sensitive: true
          },
          environment: {
            type: "string",
            required: false,
            description: "Environment (production or development)"
          }
        },
        status: 'NEEDS_SETUP' as const
      },
      { 
        immutable_slug: 'addepar', 
        display_name: 'Addepar', 
        description: 'Connect to Addepar for investment portfolio management',
        expected_config: {
          client_id: {
            type: "string",
            required: true,
            description: "Your Addepar client ID"
          },
          client_secret: {
            type: "string",
            required: true,
            description: "Your Addepar client secret",
            sensitive: true
          },
          environment: {
            type: "string",
            required: false,
            description: "Environment (production or development)"
          }
        },
        status: 'NEEDS_SETUP' as const
      },
      { 
        immutable_slug: 'quickbooks',  
        display_name: 'QuickBooks',  
        description: 'Integrate with QuickBooks for accounting and financial management',
        expected_config: {
          client_id: {
            type: "string",
            required: true,
            description: "Your QuickBooks client ID"
          },
          client_secret: {
            type: "string",
            required: true,
            description: "Your QuickBooks client secret",
            sensitive: true
          },
          environment: {
            type: "string",
            required: false,
            description: "Environment (production or development)"
          }
        },
        status: 'NEEDS_SETUP' as const
      }
    ];

    for (const integration of integrationsToCreate) {
      // Check if integration already exists by immutable_slug
      const existingIntegration = await ctx.db
        .query('integrations')
        .filter(q => q.eq(q.field('immutable_slug'), integration.immutable_slug))
        .unique();

      let id: Id<'integrations'>;
      if (existingIntegration) {
        // Update existing integration
        id = existingIntegration._id;
        await ctx.db.patch(existingIntegration._id, {
          display_name: integration.display_name,
          description: integration.description,
          expected_config: integration.expected_config, 
          updated_at: now
        });
      } else {
        // Create new integration
        id = await ctx.db.insert('integrations', {
          immutable_slug: integration.immutable_slug,
          display_name: integration.display_name,
          description: integration.description,
          expected_config: integration.expected_config, 
          status: integration.status,
          updated_at: now
        });
      }
      integrationIds.push(id);
    }

    return integrationIds.map((id) => id.toString());
  }
});
