import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { auth } from "./auth";
import { api, internal } from "./_generated/api";
import { CoreMessage, Message, streamText } from "ai";
import { google } from "@ai-sdk/google";
import { ConvexError } from "convex/values";

// Define our message types for better type safety
interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string | { text: string } | Array<{ type: string; text: string }>;
}

interface ChatRequestBody {
  messages: ChatMessage[];
  filters?: string[];
  data?: {
    filters?: string[];
  };
  config?: {
    filters?: string[];
  };
}

// Helper to transform messages to CoreMessage format
const transformToCoreMessage = (message: ChatMessage): CoreMessage => ({
  role: message.role,
  content: typeof message.content === "string" 
    ? message.content 
    : Array.isArray(message.content)
    ? message.content.map(part => part.text).join(" ")
    : message.content.text || ""
});

const http = httpRouter();

// Add authentication routes
auth.addHttpRoutes(http);

// ==================== CORS Handling ====================

// Helper function to create CORS headers
const createCorsHeaders = (requestOrigin: string | null): Headers => {
  // Verify that required environment variables are set
  if (!process.env.NEXT_PUBLIC_CONVEX_SITE_URL) {
    console.error("NEXT_PUBLIC_CONVEX_SITE_URL environment variable is not set");
  }
  
  // Define allowed origins
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:5174',
    'https://fojo-vision.vercel.app',
    'https://fojo.vision',
    process.env.NEXT_PUBLIC_CONVEX_SITE_URL,
    process.env.NEXT_PUBLIC_CONVEX_URL,
  ].filter(Boolean) as string[]; // Filter out null/undefined

  let allowedOrigin = allowedOrigins[0]; // Default to the first allowed origin

  // Check if the request origin is in the allowed list
  if (requestOrigin && allowedOrigins.includes(requestOrigin)) {
    allowedOrigin = requestOrigin; // If allowed, use the request's origin
  } else if (process.env.NEXT_PUBLIC_CONVEX_SITE_URL) {
    // Fallback to NEXT_PUBLIC_CONVEX_SITE_URL if set and request origin is not explicitly allowed
    allowedOrigin = process.env.NEXT_PUBLIC_CONVEX_SITE_URL;
  }

  console.log("CORS request from origin:", requestOrigin);
  console.log("Using allowed origin:", allowedOrigin);

  const headers = new Headers();
  // Use the determined allowedOrigin
  headers.set("Access-Control-Allow-Origin", allowedOrigin);
  headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, Accept, Origin, X-Requested-With"); 
  headers.set("Access-Control-Max-Age", "86400"); // Cache preflight for 1 day
  headers.set("Vary", "Origin"); // Important for caching
  return headers;
};

// ==================== Google AI Chat with Trieve Integration ====================

// Define the handler for the chat endpoint
const handleChatRequest = httpAction(async (ctx, request): Promise<Response> => {
  const requestOrigin = request.headers.get("Origin");
  const corsHeaders = createCorsHeaders(requestOrigin);

  try {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      throw new Error("AI service configuration error.");
    }

    const requestText = await request.text();
    let requestBody: ChatRequestBody;
    
    try {
      requestBody = JSON.parse(requestText);
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      throw new Error("Invalid JSON in request body");
    }

    // Extract and validate messages with type safety
    const messages: ChatMessage[] = Array.isArray(requestBody.messages) 
      ? requestBody.messages
      : [];

    // Extract filters with type safety
    let filters: string[] = [];
    if (Array.isArray(requestBody.filters)) {
      filters = requestBody.filters;
    } else if (Array.isArray(requestBody.data?.filters)) {
      filters = requestBody.data.filters;
    } else if (Array.isArray(requestBody.config?.filters)) {
      filters = requestBody.config.filters;
    } else {
      filters = ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT'];
    }

    // Ensure filters contains only strings
    filters = filters.filter((f): f is string => typeof f === 'string' && f.length > 0);

    // If filters is empty, use defaults
    if (filters.length === 0) {
      console.log("Empty filters array, using defaults");
      filters = ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT'];
    }

    // Log detailed info about extracted filters
    console.log("HTTP Chat Request - Filter Details:", {
      originalType: typeof requestBody.filters,
      isOriginalArray: Array.isArray(requestBody.filters),
      originalLength: Array.isArray(requestBody.filters) ? requestBody.filters.length : 0,
      finalFilters: filters,
      finalLength: filters.length,
      timestamp: new Date().toISOString()
    });
    
    // Enhanced logging of the parsed request
    console.log("HTTP Chat Request - Parsed Input:", {
      hasMessages: !!messages && Array.isArray(messages) && messages.length > 0,
      messageCount: messages?.length || 0,
      rawFilters: filters, // Log the raw filters array
      filtersType: filters ? typeof filters : 'undefined',
      filtersIsArray: Array.isArray(filters),
      filtersLength: filters?.length || 0,
      timestamp: new Date().toISOString()
    });
    
    // Define variables we need outside of the conditional blocks
    let augmentedMessages = [...messages];
    let retrievedContext = "";

    // Only attempt Trieve integration if we have credentials
    console.log("Input to handleChatRequest:", JSON.stringify({ messages, filters }, null, 2));

    const sources: { title: string; link?: string }[] = []; // To store source info if needed later

    // Map frontend filter IDs to Trieve tags (Type:Value format)
    const mapFilterToTag = (filterId: string): string | null => {
      // Log each filter mapping for debugging
      console.log(`Mapping filter ID: ${filterId}`);
      
      switch (filterId) {
        case 'KNOWLEDGE_BASE': return 'Type:KNOWLEDGE_BASE';
        case 'MEETING_NOTES': return 'Type:MEETING_NOTES';
        case 'CONTRACT': return 'Type:CONTRACT';
        case 'DOCUMENT': return 'Type:DOCUMENT'; // Assuming maps to general document
        case 'BILL': return 'Type:BILL';
        default: 
          console.warn(`Unknown filter ID: ${filterId}`);
          return null; // Ignore unknown filters
      }
    };

    // Safely map filters to tags with detailed logging
    let trieveTags: string[] = [];
    if (filters && Array.isArray(filters)) {
      console.log(`Processing ${filters.length} filters`);
      // Only map filters if there are any
      if (filters.length > 0) {
        trieveTags = filters
          .map(filterId => {
            const tag = mapFilterToTag(filterId);
            console.log(`Mapped ${filterId} to ${tag}`);
            return tag;
          })
          .filter((tag): tag is string => tag !== null);
        
        console.log(`Resulting Trieve tags (${trieveTags.length}):`, trieveTags);
      } else {
        console.log("Filters array is empty - no document types selected");
      }
    } else {
      console.error("Filters not provided or not an array:", filters);
    }

    // Only perform Trieve search if there are valid filters selected
    if (trieveTags.length > 0 && messages.length > 0) {
      // Extract the actual query text from the last message
      const lastMessage = messages[messages.length - 1];
      let searchQuery = "";

      if (lastMessage && lastMessage.content) {
        if (typeof lastMessage.content === 'string') {
          // If content is a string, use it directly
          searchQuery = lastMessage.content;
        } else if (Array.isArray(lastMessage.content)) {
          // If content is an array, extract text from text parts
          searchQuery = lastMessage.content
            .filter((part: any) => part && typeof part === 'object' && part.type === 'text')
            .map((part: any) => part.text)
            .join(' ');
        } else if (typeof lastMessage.content === 'object' && lastMessage.content !== null) {
          // If content is an object with a text property, use that
          searchQuery = (lastMessage.content as any).text || JSON.stringify(lastMessage.content);
        } else {
          // Fallback to stringifying the content
          searchQuery = JSON.stringify(lastMessage.content);
        }
      }

      console.log("Extracted search query for Trieve:", {
        searchQuery,
        messageContentType: typeof lastMessage.content,
        isContentArray: Array.isArray(lastMessage.content),
      });

      const trieveSearchBody = {
        query: searchQuery,
        search_type: "hybrid", // Use hybrid search as requested
        page_size: 10, // Retrieve top 10 chunks for context
        filters: {
          must: [
            {
              field: "tag_set",
              match_any: trieveTags, // Use the mapped tags
            },
          ],
        },
        highlight_results: false, // Don't need highlights for context
        slim_chunks: false, // Need chunk_html for context
      };

      console.log("Calling Trieve Search with body:", JSON.stringify(trieveSearchBody, null, 2));

      try {
        const trieveResponse = await fetch("https://api.trieve.ai/api/chunk/search", {
          method: "POST",
          headers: new Headers({
            "Content-Type": "application/json",
            "TR-Dataset": process.env.TRIEVE_DATASET_ID || "",
            "Authorization": process.env.TRIEVE_API_KEY || "",
          }),
          body: JSON.stringify(trieveSearchBody),
        });

        if (!trieveResponse.ok) {
          const errorBody = await trieveResponse.text();
          console.error(`Trieve API Error (${trieveResponse.status}): ${errorBody}`);
          // Don't throw, just proceed without context
        } else {
          const trieveResult = await trieveResponse.json();
          console.log("Trieve Search Result:", JSON.stringify(trieveResult, null, 2));

          if (trieveResult?.chunks && Array.isArray(trieveResult.chunks) && trieveResult.chunks.length > 0) {
            // Format context
            retrievedContext = "Context from relevant documents:\n---\n";
            trieveResult.chunks.forEach((chunk: any, index: number) => {
              if (chunk && typeof chunk === 'object' && chunk.chunk) {
                // Get the chunk data and metadata
                const chunkData = chunk.chunk;
                const metadata = chunkData.metadata || {};
                
                // Extract content properly from chunk_html
                const chunkHtml = chunkData.chunk_html || '';
                
                // Find the title
                const titleMatch = chunkHtml.match(/Title:\s*(.*?)(?=\nShort|$)/);
                const title = titleMatch && titleMatch[1] ? titleMatch[1].trim() : `Document ${index + 1}`;
                
                // Add the chunk to the context with its title
                retrievedContext += `\n[${title}]\n${chunkHtml}\n---\n`;
                
                // Store source info for potential future use
                sources.push({ title, link: metadata.url });
              }
            });
          }
        }
      } catch (trieveError) {
        console.error("Error in Trieve search:", trieveError);
        // Don't throw, just proceed without context
      }
    }

    // Define static system instruction with proper typing
    const staticSystemInstruction: CoreMessage = {
      role: "system",
      content: "You are a helpful assistant that has access to all of a user's documents, this includes knowledge base, meeting notes, general docs, and contracts. Your task is to take the user's query and answer it while quoting the document it came from. You want to provide as much context as possible."
    };

    // Start with the static instruction
    let finalMessages: CoreMessage[] = [staticSystemInstruction];

    // Add context if available
    if (retrievedContext) {
      finalMessages.push({
        role: "system",
        content: retrievedContext
      });
    }

    // Transform and add user messages with type safety
    const nonSystemMessages = messages
      .filter((msg): msg is ChatMessage => msg.role !== "system")
      .map(transformToCoreMessage);
    
    finalMessages = [...finalMessages, ...nonSystemMessages];

    // Call streamText with properly typed messages
    const streamResult = streamText({
      model: google("gemini-2.5-flash-preview-04-17"),
      messages: finalMessages,
    });

    const response = streamResult.toDataStreamResponse();
    corsHeaders.forEach((value, key) => response.headers.set(key, value));
    return response;

  } catch (error: unknown) {
    console.error("Detailed error in chat HTTP handler:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to process chat request.";
    const errorHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    corsHeaders.forEach((value, key) => {
      errorHeaders[key] = value;
    });

    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: errorHeaders,
    });
  }
});

// Route for the chat API
http.route({
  path: "/api/chat",
  method: "POST",
  handler: handleChatRequest,
});

// OPTIONS handler for CORS
http.route({
  path: "/api/chat",
  method: "OPTIONS",
  handler: httpAction(async (_, request): Promise<Response> => {
    const requestOrigin = request.headers.get("Origin");
    const corsHeaders = createCorsHeaders(requestOrigin);
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }),
});

// ==================== OpenAI Realtime Sessions ====================

/**
 * HTTP endpoint for requesting an OpenAI realtime session (for direct access).
 * This is a more generic endpoint that can serve both conversation and transcription use cases.
 */
http.route({
  path: "/openai/realtime/session",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      console.log("Received request to /openai/realtime/session");
      const url = 'https://api.openai.com/v1/realtime/sessions';

      // Parse the request body to get client-provided configuration
      const clientConfig = await request.json().catch(() => ({}));
      console.log("Client-provided config:", clientConfig);

      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        console.error("OPENAI_API_KEY environment variable is not set.");
        throw new Error("OPENAI_API_KEY environment variable is not set.");
      }

      // Log that we're about to make the OpenAI API request
      console.log("Making request to OpenAI API:", url);

      // Ensure required fields for OpenAI API
      // We only need to add model if not provided by client
      const sessionConfig = {
        // Default model if not specified by client
        model: clientConfig.model || 'gpt-4o-mini-transcribe',

        // Pass through all valid client configuration
        ...clientConfig
      };

      console.log("Using final session config:", sessionConfig);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify(sessionConfig),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenAI session request failed:", errorText, "Status:", response.status);
        // Add CORS headers to the error response
        const requestOrigin = request.headers.get("Origin");
        const headers = createCorsHeaders(requestOrigin);
        headers.set('Content-Type', 'application/json');
        return new Response(
          JSON.stringify({ error: `OpenAI session request failed: ${errorText}` }),
          { status: response.status, headers: headers }
        );
      }

      const data = await response.json();
      console.log("Successfully got session from OpenAI");

      // Return success response with CORS headers
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');

      console.log("Returning success response with CORS headers");

      return new Response(JSON.stringify(data), {
        status: 200,
        headers: headers,
      });
    } catch (error: any) {
      console.error("Error in Convex HTTP action:", error);
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');
      return new Response(
        JSON.stringify({ error: error.message || 'Internal Server Error' }),
        { status: 500, headers: headers }
      );
    }
  }),
});

// Pre-flight OPTIONS handler for the generic session endpoint
http.route({
  path: "/openai/realtime/session",
  method: "OPTIONS",
  handler: httpAction(async (_, request) => {
    const requestOrigin = request.headers.get("Origin");
    const corsHeaders = createCorsHeaders(requestOrigin);
    return new Response(null, { 
      status: 204,
      headers: corsHeaders
    });
  }),
});

/**
 * HTTP endpoint for requesting an OpenAI realtime **conversation** session.
 */
http.route({
  path: "/openai/realtime/conversation/session",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      const url = 'https://api.openai.com/v1/realtime/sessions'; // Correct endpoint for conversation sessions

      // Parse the client config
      const clientConfig = await request.json().catch(() => ({}));
      console.log("Client-provided conversation config:", clientConfig);

      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error("OPENAI_API_KEY environment variable is not set.");
      }

      // Ensure we have the required model parameter and merge with client config
      const sessionConfig = {
        // Default model if not provided
        model: clientConfig.model || 'gpt-4o-realtime-preview',

        // Pass through all client configuration
        ...clientConfig
      };

      // Log tools configuration specifically for debugging
      if (sessionConfig.tools && sessionConfig.tools.length > 0) {
        console.log("Tools configuration detected:", JSON.stringify(sessionConfig.tools, null, 2));
        console.log("Tool choice setting:", sessionConfig.tool_choice || "default");
      } else {
        console.log("No tools configuration found in the request");
      }

      console.log("Using final conversation session config:", sessionConfig);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`, // Use the validated apiKey
        },
        body: JSON.stringify(sessionConfig),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenAI session request failed:", errorText); // Log error details
        console.error("Request payload was:", JSON.stringify(sessionConfig, null, 2));
        console.error("Response status:", response.status);

        // Return a proper Convex Response object for errors, ensuring CORS headers are set
        const requestOrigin = request.headers.get("Origin");
        const errorHeaders = createCorsHeaders(requestOrigin);
        errorHeaders.set('Content-Type', 'application/json');
        return new Response(
          JSON.stringify({
            error: `OpenAI session request failed: ${errorText}`,
            requestPayload: sessionConfig,
            status: response.status
          }),
          { status: response.status, headers: errorHeaders }
        );
      }

      const data = await response.json();
      // Return success response with CORS headers
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: headers,
      });
    } catch (error: any) {
      console.error("Error in Convex HTTP action (conversation):", error); // Log internal errors
      // Return error response with CORS headers
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');
      return new Response(
        JSON.stringify({ error: error.message || 'Internal Server Error' }),
        { status: 500, headers: headers }
      );
    }
  }),
});

// Pre-flight OPTIONS handler for conversation session endpoint
http.route({
  path: "/openai/realtime/conversation/session",
  method: "OPTIONS",
  handler: httpAction(async (_, request) => {
    const requestOrigin = request.headers.get("Origin");
    const corsHeaders = createCorsHeaders(requestOrigin);
    return new Response(null, { 
      status: 204,
      headers: corsHeaders
    });
  }),
});

/**
 * HTTP endpoint for requesting an OpenAI realtime **transcription** session.
 */
http.route({
  path: "/openai/realtime/transcription/session", // Distinct path for transcription
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      const url = 'https://api.openai.com/v1/realtime/transcription_sessions'; // Correct endpoint for transcription

      // Parse the client config
      const clientConfig = await request.json().catch(() => ({}));
      console.log("Client-provided transcription config:", clientConfig);

      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error("OPENAI_API_KEY environment variable is not set.");
      }

      // Construct the session config, passing through client config
      // DO NOT include 'model' here, as the transcription endpoint doesn't accept it.
      const sessionConfig = { ...clientConfig };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(sessionConfig)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenAI transcription session request failed:", errorText);
        // Add CORS headers to the error response
        const requestOrigin = request.headers.get("Origin");
        const headers = createCorsHeaders(requestOrigin);
        headers.set('Content-Type', 'application/json');
        return new Response(
          JSON.stringify({ error: `OpenAI transcription session request failed: ${errorText}` }),
          { status: response.status, headers: headers }
        );
      }

      const data = await response.json();
      // Return success response with CORS headers
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: headers,
      });
    } catch (error: any) {
      console.error("Error in Convex HTTP action (transcription):", error);
      // Return error response with CORS headers
      const requestOrigin = request.headers.get("Origin");
      const headers = createCorsHeaders(requestOrigin);
      headers.set('Content-Type', 'application/json');
      return new Response(
        JSON.stringify({ error: error.message || 'Internal Server Error' }),
        { status: 500, headers: headers }
      );
    }
  }),
});

// Pre-flight OPTIONS handler for transcription session endpoint
http.route({
  path: "/openai/realtime/transcription/session",
  method: "OPTIONS",
  handler: httpAction(async (_, request) => {
    const requestOrigin = request.headers.get("Origin");
    const corsHeaders = createCorsHeaders(requestOrigin);
    return new Response(null, { 
      status: 204,
      headers: corsHeaders
    });
  }),
});

export default http;