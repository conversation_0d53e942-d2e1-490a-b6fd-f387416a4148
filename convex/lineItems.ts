import { z } from "zod";
import { zQ<PERSON>y, zMutation } from "./functions";
import { QueryCtx, MutationCtx } from "./_generated/server";
import { Id, <PERSON> } from "./_generated/dataModel";
import { zid } from "convex-helpers/server/zod";

// ------------------------------------------------------------------
// Import Zod schemas and types from your lineitems-schema.ts
// ------------------------------------------------------------------
import {
  LineItemFilterSchema,
  PaginationOptionsSchema,
  PaginatedResultSchema,
  LineItemSchema,
  LineItemInputSchema,
  LineItemUpdateSchema,
  LineItemBulkUpdateSchema,
  BulkUpdateResultSchema,
  GetByBillSchema,
  AIInsightSchema,
  BulkUpdateResult,
  SpendingAggregationSchema
} from "../zod/lineItems-schema";

// ------------------------------------------------------------------
// (Optional) Shared helper to apply common line item filters
// *Note: This function is not used in the queries below, but is
// rewritten here to preserve the original file's structure.
// ------------------------------------------------------------------
function applyCommonFilters<T extends { filter: (fn: (q: any) => any) => T }>(
  queryBuilder: T,
  args: {
    startDate?: number;
    endDate?: number;
    category?: Id<"tags">;
    categorySlug?: string;
    minAmount?: number;
    maxAmount?: number;
    onlyUncategorized?: boolean;
  }
): T {
  return queryBuilder.filter((q: any) => {
    let filter = q.eq(q.field("_id"), q.field("_id")); // Always-true base

    if (args.startDate !== undefined) {
      filter = q.and(filter, q.gte(q.field("post_date"), args.startDate));
    }
    if (args.endDate !== undefined) {
      filter = q.and(filter, q.lte(q.field("post_date"), args.endDate));
    }
    if (args.category !== undefined) {
      filter = q.and(filter, q.eq(q.field("spending_category"), args.category));
    }
    if (args.onlyUncategorized) {
      filter = q.and(filter, q.eq(q.field("spending_category"), undefined));
    }
    if (args.minAmount !== undefined) {
      filter = q.and(filter, q.gte(q.field("amount"), args.minAmount));
    }
    if (args.maxAmount !== undefined) {
      filter = q.and(filter, q.lte(q.field("amount"), args.maxAmount));
    }
    return filter;
  });
}

// ------------------------------------------------------------------
// 1) listLineItems - A consolidated line item query
//    Uses optional filters + optional pagination
// ------------------------------------------------------------------
export const listLineItems = zQuery({
  args: {
    filter: LineItemFilterSchema.optional(),
    paginationOpts: PaginationOptionsSchema.optional()
  },
  output: PaginatedResultSchema,
  handler: async (ctx: QueryCtx, { filter, paginationOpts }) => {
    const { db } = ctx;

    // Default to sorting by "date" if not provided
    const sortBy = filter?.sortBy || "date";
    const sortDirection = filter?.sortDirection || "desc";

    // Build complete query chain based on sort field
    if (sortBy === "date") {
      // Complete chain for date-sorted query with all filters
      const result = await db
        .query("lineItems")
        .withIndex("by_post_date", q => {
          // Apply date range using the index
          if (filter?.startDate !== undefined) {
            return q.gte("post_date", filter.startDate);
          }
          if (filter?.endDate !== undefined) {
            return q.lte("post_date", filter.endDate);
          }
          return q;
        })
        // Apply additional filters after the index
        .filter(q => {
          let conditions = [];
          
          // Apply category filters
          if (filter?.category !== undefined) {
            conditions.push(q.eq(q.field("spending_category"), filter.category));
          }
          if (filter?.onlyUncategorized) {
            conditions.push(q.eq(q.field("spending_category"), undefined));
          }
          
          // Apply amount filters
          if (filter?.minAmount !== undefined) {
            conditions.push(q.gte(q.field("amount"), filter.minAmount || 0));
          }
          if (filter?.maxAmount !== undefined) {
            conditions.push(q.lte(q.field("amount"), filter.maxAmount || 0));
          }
          
          // Return true if no conditions (no filtering)
          return conditions.length > 0 ? q.and(...conditions) : q.eq(q.field("_id"), q.field("_id"));
        })
        .order(sortDirection)
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });

      return {
        page: result.page,
        isDone: result.isDone,
        continueCursor: result.continueCursor ?? null
      };
    } else {
      // Default case for other sort fields
      const result = await db
        .query("lineItems")
        .filter(q => {
          let conditions = [];
          
          // Apply all filters in a single filter chain
          if (filter?.category !== undefined) {
            conditions.push(q.eq(q.field("spending_category"), filter.category));
          }
          if (filter?.onlyUncategorized) {
            conditions.push(q.eq(q.field("spending_category"), undefined));
          }
          if (filter?.startDate !== undefined) {
            conditions.push(q.gte(q.field("post_date"), filter.startDate || 0));
          }
          if (filter?.endDate !== undefined) {
            conditions.push(q.lte(q.field("post_date"), filter.endDate || 0));
          }
          if (filter?.minAmount !== undefined) {
            conditions.push(q.gte(q.field("amount"), filter.minAmount || 0));
          }
          if (filter?.maxAmount !== undefined) {
            conditions.push(q.lte(q.field("amount"), filter.maxAmount || 0));
          }
          
          // Return true if no conditions (no filtering)
          return conditions.length > 0 ? q.and(...conditions) : q.eq(q.field("_id"), q.field("_id"));
        })
        .order(sortDirection)
        .paginate({
          cursor: paginationOpts?.cursor ?? null,
          numItems: paginationOpts?.numItems || 50
        });

      return {
        page: result.page,
        isDone: result.isDone,
        continueCursor: result.continueCursor ?? null
      };
    }
  }
});

// ------------------------------------------------------------------
// 2) getLineItemsByBill - specialized query for a single bill
// ------------------------------------------------------------------
export const getLineItemsByBill = zQuery({
  args: {
    bill_id: zid("bills")
  },
  output: z.array(LineItemSchema),
  handler: async (ctx: QueryCtx, { bill_id }) => {
    const { db } = ctx;
    const items = await db
      .query("lineItems")
      .withIndex("by_bill", (q) => q.eq("bill_id", bill_id))
      .collect();
    
    // Ensure all required fields are present with correct types
    return items.map((item: Doc<'lineItems'>) => ({
      _id: item._id as Id<'lineItems'>,
      _creationTime: item._creationTime as number,
      bill_id: item.bill_id as Id<'bills'>,
      amount: item.amount as number,
      post_date: item.post_date as number,
      merchant_name: item.merchant_name as string,
      description: item.description as string,
      vendor_id: item.vendor_id as Id<'organizations'>,
      updated_at: item.updated_at as number,
      spending_category: item.spending_category as Id<'tags'> | undefined
    }));
  }
});

// ------------------------------------------------------------------
// 3) getAiInsights - returns an AI insight about spending in a date range
// ------------------------------------------------------------------
export const getAiInsights = zQuery({
  args: {
    startDate: z.number().optional(),
    endDate: z.number().optional()
  },
  output: AIInsightSchema,
  handler: async (ctx: QueryCtx, { startDate, endDate }) => {
    const { db } = ctx;
    let queryBuilder = db.query("lineItems");

    // Apply date range filters if provided
    if (startDate !== undefined) {
      queryBuilder = queryBuilder.filter((q) =>
        q.gte(q.field("post_date"), startDate || 0)
      );
    }
    if (endDate !== undefined) {
      queryBuilder = queryBuilder.filter((q) =>
        q.lte(q.field("post_date"), endDate || 0)
      );
    }

    const items = await queryBuilder.collect();
    if (items.length === 0) {
      // No transactions found in this period
      return {
        // Using a specific enum value as defined in AIInsightSchema
        insightType: "spending_trend" as const,
        title: "Current Period Spending Summary",
        description: "No Spending Data Available",
        category: {
          name: "No Data",
          percentage: 0,
          amount: 0
        }
      };
    }

    // Calculate total spending and return with specific enum value to match the schema
    const totalSpending = items.reduce((sum, item) => sum + item.amount, 0);
    
    // Use a specific enum value that matches AIInsightSchema
    return {
      // Using a specific enum value as defined in AIInsightSchema
      insightType: "spending_trend" as const,
      title: "Spending Summary",
      description: `Total spending in this period: $${totalSpending.toFixed(2)}`,
      category: {
        name: "Overall",
        percentage: 100,
        amount: totalSpending
      }
    };
  }
});

// ------------------------------------------------------------------
// 4) createLineItem - single-item create
// ------------------------------------------------------------------
export const createLineItem = zMutation({
  args: {
    input: LineItemInputSchema
  },
  // Return the newly inserted ID
  output: z.object({
    id: z.string()
  }),
  handler: async (ctx: MutationCtx, { input }) => {
    const { db } = ctx;
    // Insert the item; ensure spending_category is undefined if not provided
    const id = await db.insert("lineItems", {
      ...input,
      spending_category: input.spending_category ?? undefined
    });
    return { id: id.toString() };
  }
});

// ------------------------------------------------------------------
// 5) updateLineItem - single-item update
// ------------------------------------------------------------------
export const updateLineItem = zMutation({
  args: {
    id: z.string(),
    updates: LineItemUpdateSchema
  },
  output: z.object({
    success: z.boolean(),
    updated: z.array(z.string()),
    debug: z.object({
      before: z.any(),
      after: z.any(),
      updatePayload: z.any()
    })
  }),
  handler: async (ctx: MutationCtx, { id, updates }) => {
    const { db } = ctx;
    const lineItemId = id as Id<"lineItems">;

    // Get the current state for debugging
    const before = await db.get(lineItemId);
    
    // Create update payload
    const updatePayload: Partial<{
      updated_at: number;
      amount: number;
      vendor_id: Id<"organizations">;
      description: string;
      post_date: number;
      merchant_name: string;
      spending_category: Id<"tags"> | undefined;  // Only allow undefined or valid ID
    }> = {
      updated_at: Date.now()
    };

    // Only add fields that are actually present in updates
    if ('amount' in updates) updatePayload.amount = updates.amount;
    if ('vendor_id' in updates) updatePayload.vendor_id = updates.vendor_id;
    if ('description' in updates) updatePayload.description = updates.description;
    if ('post_date' in updates) updatePayload.post_date = updates.post_date;
    if ('merchant_name' in updates) updatePayload.merchant_name = updates.merchant_name;
    
    // Handle spending_category - convert null to undefined
    if ('spending_category' in updates) {
      // Explicitly check for null to handle uncategorized selections
      // JSON serialization converts undefined to null, so we need to handle it here
      updatePayload.spending_category = updates.spending_category === null ? undefined : updates.spending_category;
      
      console.log('Convex updateLineItem - spending_category processing:', {
        originalValue: updates.spending_category,
        originalType: typeof updates.spending_category,
        isNull: updates.spending_category === null,
        processedValue: updatePayload.spending_category,
        processedType: typeof updatePayload.spending_category,
        isUndefined: updatePayload.spending_category === undefined
      });
    }

    // Log the update operation
    console.log('Convex updateLineItem:', {
      operation: 'update',
      lineItemId,
      before: {
        value: before,
        spending_category: before?.spending_category,
        spending_category_type: before?.spending_category ? typeof before.spending_category : 'undefined'
      },
      update: {
        payload: updatePayload,
        spending_category: updatePayload.spending_category,
        spending_category_type: updatePayload.spending_category ? typeof updatePayload.spending_category : 'undefined',
        raw_update_value: updates.spending_category,
        raw_update_type: typeof updates.spending_category
      }
    });

    // Apply the update
    await db.patch(lineItemId, updatePayload);

    // Get the after state for verification
    const after = await db.get(lineItemId);

    // Log the final state
    console.log('Convex updateLineItem result:', {
      operation: 'complete',
      before: before?.spending_category,
      after: after?.spending_category,
      expected: updates.spending_category
    });

    return {
      success: true,
      updated: [id],
      debug: { before, after, updatePayload }
    };
  }
});

// ------------------------------------------------------------------
// 6) updateLineItemsBulk - bulk update
// ------------------------------------------------------------------
export const updateLineItemsBulk = zMutation({
  args: {
    items: LineItemBulkUpdateSchema
  },
  output: BulkUpdateResultSchema,
  handler: async (ctx: MutationCtx, { items }) => {
    const { db } = ctx;
    const results: BulkUpdateResult = {
      updated: [],
      failed: [],
      errors: []
    };

    // Find the AI Uncertain tag once at the start
    let aiUncertainTag;
    try {
      // First find all tags with immutable_slug 'ai-uncertain'
      const aiUncertainTags = await db
        .query("tags")
        .filter((q) => q.eq(q.field("immutable_slug"), "ai-uncertain"))
        .collect();

      if (aiUncertainTags.length > 0) {
        aiUncertainTag = aiUncertainTags[0];
      } else {
        console.error("AI Uncertain tag not found");
      }
    } catch (error) {
      console.error("Error finding AI Uncertain tag:", error);
    }

    for (const { _id, updates } of items) {
      const lineItemId = _id as Id<"lineItems">;
      try {
        // Log incoming update
        console.log('Processing line item update:', {
          itemId: lineItemId,
          updates,
          spendingCategory: updates.spending_category
        });

        let finalSpendingCategory = undefined;

        // Validate spending_category if provided
        if (updates.spending_category !== undefined && updates.spending_category !== null) {
          const categoryId = updates.spending_category as Id<"tags">;
          // Verify the category exists
          const categoryExists = await db.get(categoryId);
          if (!categoryExists) {
            console.log(`Category ${categoryId} not found, falling back to AI Uncertain`);
            // Fall back to AI Uncertain tag if available
            if (aiUncertainTag) {
              finalSpendingCategory = aiUncertainTag._id;
            }
          } else {
            finalSpendingCategory = categoryId;
          }
        }

        const patchUpdates = {
          ...updates,
          updated_at: Date.now(),
          spending_category: finalSpendingCategory
        };

        // Log the final patch updates
        console.log('Applying patch:', {
          itemId: lineItemId,
          patchUpdates,
          finalSpendingCategory: patchUpdates.spending_category
        });

        // Apply the patch
        await db.patch(lineItemId, patchUpdates);

        // Verify the update was successful
        const verifyUpdate = await db.get(lineItemId);
        if (!verifyUpdate || verifyUpdate.spending_category !== finalSpendingCategory) {
          console.error('Update verification failed:', {
            itemId: lineItemId,
            expected: finalSpendingCategory,
            actual: verifyUpdate?.spending_category,
            fullItem: verifyUpdate
          });
          throw new Error(`Failed to update line item ${lineItemId}`);
        }

        // Log the successful update
        console.log('Successfully updated line item:', {
          itemId: lineItemId,
          newCategory: patchUpdates.spending_category
        });

        results.updated.push(lineItemId);
      } catch (error) {
        // Log detailed error information
        console.error('Failed to update line item:', {
          itemId: lineItemId,
          error: error instanceof Error ? {
            message: error.message,
            stack: error.stack
          } : String(error),
          updates
        });

        results.failed.push(lineItemId);
        results.errors.push({
          _id: lineItemId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // Log final results
    console.log('Bulk update completed:', {
      totalProcessed: items.length,
      succeeded: results.updated.length,
      failed: results.failed.length,
      errors: results.errors
    });

    return results;
  }
});

// ------------------------------------------------------------------
// getSpendingByCategory - Aggregates spending data by category
// ------------------------------------------------------------------
export const getSpendingByCategory = zQuery({
  args: {
    filter: LineItemFilterSchema.optional()
  },
  output: SpendingAggregationSchema,
  handler: async (ctx: QueryCtx, { filter }) => {
    const { db } = ctx;

    // Build query with filters
    let queryBuilder = db.query("lineItems");

    // Apply date filters if provided
    if (filter?.startDate !== undefined) {
      queryBuilder = queryBuilder.filter(q => 
        q.gte(q.field("post_date"), filter.startDate!)
      );
    }
    if (filter?.endDate !== undefined) {
      queryBuilder = queryBuilder.filter(q => 
        q.lte(q.field("post_date"), filter.endDate!)
      );
    }

    // Apply category filter if provided
    if (filter?.category !== undefined) {
      queryBuilder = queryBuilder.filter(q => 
        q.eq(q.field("spending_category"), filter.category)
      );
    }

    // Apply amount filters if provided
    if (filter?.minAmount !== undefined) {
      queryBuilder = queryBuilder.filter(q => 
        q.gte(q.field("amount"), filter.minAmount!)
      );
    }
    if (filter?.maxAmount !== undefined) {
      queryBuilder = queryBuilder.filter(q => 
        q.lte(q.field("amount"), filter.maxAmount!)
      );
    }

    // Get all matching line items
    const lineItems = await queryBuilder.collect();

    // Group by category and calculate totals
    const categoryMap = new Map<Id<"tags">, { amount: number; count: number }>();
    let uncategorizedAmount = 0;
    let uncategorizedCount = 0;
    let totalSpent = 0;

    // Calculate totals and group by category
    for (const item of lineItems) {
      const amount = item.amount || 0;
      totalSpent += amount;

      if (item.spending_category) {
        const existing = categoryMap.get(item.spending_category) || { amount: 0, count: 0 };
        categoryMap.set(item.spending_category, {
          amount: existing.amount + amount,
          count: existing.count + 1
        });
      } else {
        uncategorizedAmount += amount;
        uncategorizedCount++;
      }
    }

    // Fetch category details for all used categories
    const categoryIds = Array.from(categoryMap.keys());
    const categories = categoryIds.length > 0 
      ? await Promise.all(categoryIds.map(id => db.get(id)))
      : [];

    // Filter out any null values and build the response array
    const categoriesResponse = categories
      .filter((category): category is NonNullable<Doc<"tags">> => category !== null)
      .map(category => {
        // Get the category data with proper typing
        const categoryData = categoryMap.get(category._id as Id<"tags">);
        return {
          id: category._id as Id<"tags">, // Ensure correct typing
          name: category.name as string, // Ensure string type
          amount: categoryData?.amount || 0,
          count: categoryData?.count || 0,
          color: "#CBD5E1" as string // Ensure string type
        };
      });

    // Add uncategorized if there are any
    if (uncategorizedCount > 0) {
      const uncategorizedId = "uncategorized" as unknown as Id<"tags">;
      categoriesResponse.push({
        id: uncategorizedId,
        name: "Uncategorized" as string,
        amount: uncategorizedAmount,
        count: uncategorizedCount,
        color: "#94A3B8" as string
      });
    }

    // Sort categories by amount (highest first)
    categoriesResponse.sort((a, b) => b.amount - a.amount);

    // Ensure the return type matches exactly what's expected by SpendingAggregationSchema
    return {
      categories: categoriesResponse as Array<{
        id: Id<"tags">;
        name: string;
        amount: number;
        count: number;
        color: string;
      }>,
      totalSpent
    };
  }
});