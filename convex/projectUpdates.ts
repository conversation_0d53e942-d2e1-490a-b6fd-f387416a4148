import { zQ<PERSON>y, zMutation } from './functions';
import { z } from 'zod';
import {
  CreateManualUpdateInput,
  UpdateProjectUpdateContentInput,
  UpdateProjectUpdateDateInput,
  ListProjectUpdatesInput,
  ListProjectUpdatesOutput
} from '../zod/projectUpdates-schema';
import { ConvexError } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from './_generated/dataModel';
import { api } from './_generated/api';

/**
 * Creates a manual project update
 * Returns the ID of the created update
 */
export const createManualUpdate = zMutation({
  args: {
    input: CreateManualUpdateInput
  },
  output: z.object({
    id: z.string()
  }),
  handler: async (ctx, args) => {
    const { input } = args;
    const now = Date.now();

    // Get the current user ID using the auth helper
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Unauthorized: You must be logged in to create a project update");
    }

    // Get the user document
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Verify the project exists
    const project = await ctx.db.get(input.project_id);
    if (!project) {
      throw new ConvexError(`Project with ID ${input.project_id} not found`);
    }

    // Create the project update
    const updateId = await ctx.db.insert('projectUpdates', {
      project_id: input.project_id,
      content: input.content,
      date_of_update: input.date_of_update || now,
      update_type: 'MANUAL',
      source_id: input.project_id, // For manual updates, source is the project itself
      created_by: user._id,
      updated_at: now
    });

    return { id: updateId };
  }
});

/**
 * Creates a project update linked to a meeting note
 * Returns the ID of the created update
 */
export const createMeetingLinkUpdate = zMutation({
  args: {
    projectId: z.string().transform((id) => id as Id<'projects'>),
    meetingNoteFileId: z.string().transform((id) => id as Id<'files'>)
  },
  output: z.object({
    id: z.string()
  }),
  handler: async (ctx, args): Promise<{ id: string }> => {
    const { projectId, meetingNoteFileId } = args;
    const now = Date.now();

    // Get the current user ID using the auth helper
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Unauthorized: You must be logged in to create a project update");
    }

    // Get the user document
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Verify the project exists
    const project = await ctx.db.get(projectId);
    if (!project) {
      throw new ConvexError(`Project with ID ${projectId} not found`);
    }

    // Get the meeting note details
    const meetingNote: any = await ctx.runQuery(api.files.files.getMeetingNote, { fileId: meetingNoteFileId });
    if (!meetingNote) {
      throw new ConvexError(`Meeting note with ID ${meetingNoteFileId} not found`);
    }

    // Use the meeting note's short_description for content, or a fallback
    const content: string = meetingNote.short_description || `Linked Meeting Note: ${meetingNote.title || 'Untitled Meeting'}`;

    // Use the meeting note's meeting date if available, otherwise use current time
    const meetingDate = meetingNote.mnMeetingDate || now;

    // Create the project update
    const updateId: Id<'projectUpdates'> = await ctx.db.insert('projectUpdates', {
      project_id: projectId,
      content: content,
      date_of_update: meetingDate,
      update_type: 'MEETING',
      source_id: meetingNoteFileId, // Link to the meeting note file
      created_by: user._id,
      updated_at: now
    });

    return { id: updateId };
  }
});

/**
 * Deletes a project update
 */
export const deleteProjectUpdate = zMutation({
  args: {
    updateId: z.string().transform((id) => id as Id<'projectUpdates'>)
  },
  output: z.object({
    success: z.boolean()
  }),
  handler: async (ctx, args) => {
    const { updateId } = args;

    // Get the current user ID using the auth helper
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Unauthorized: You must be logged in to delete a project update");
    }

    // Get the user document
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Get the project update
    const projectUpdate = await ctx.db.get(updateId);
    if (!projectUpdate) {
      throw new ConvexError(`Project update with ID ${updateId} not found`);
    }

    // Verify the user is the creator of the update (or potentially an admin/owner in the future)
    if (projectUpdate.created_by !== user._id) {
      // TODO: Add role-based access control checks here if needed
      throw new ConvexError("Unauthorized: You can only delete your own project updates");
    }

    // Delete the project update
    await ctx.db.delete(updateId);

    return { success: true };
  }
});

/**
 * Updates the content of an existing project update
 */
export const updateProjectUpdateContent = zMutation({
  args: {
    input: UpdateProjectUpdateContentInput
  },
  output: z.object({
    success: z.boolean()
  }),
  handler: async (ctx, args) => {
    const { input } = args;
    const now = Date.now();

    // Get the current user ID using the auth helper
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Unauthorized: You must be logged in to update a project update");
    }

    // Get the user document
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Get the project update
    const projectUpdate = await ctx.db.get(input.update_id);
    if (!projectUpdate) {
      throw new ConvexError(`Project update with ID ${input.update_id} not found`);
    }

    // Verify the user is the creator of the update
    if (projectUpdate.created_by !== user._id) {
      throw new ConvexError("Unauthorized: You can only update your own project updates");
    }

    // Update the project update
    await ctx.db.patch(input.update_id, {
      content: input.content,
      updated_at: now
    });

    return { success: true };
  }
});

/**
 * Updates the date of an existing project update
 */
export const updateProjectUpdateDate = zMutation({
  args: {
    input: UpdateProjectUpdateDateInput
  },
  output: z.object({
    success: z.boolean()
  }),
  handler: async (ctx, args) => {
    const { input } = args;
    const now = Date.now();

    // Get the current user ID using the auth helper
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Unauthorized: You must be logged in to update a project update");
    }

    // Get the user document
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new ConvexError("User not found");
    }

    // Get the project update
    const projectUpdate = await ctx.db.get(input.update_id);
    if (!projectUpdate) {
      throw new ConvexError(`Project update with ID ${input.update_id} not found`);
    }

    // Verify the user is the creator of the update
    if (projectUpdate.created_by !== user._id) {
      throw new ConvexError("Unauthorized: You can only update your own project updates");
    }

    // Update the project update date
    await ctx.db.patch(input.update_id, {
      date_of_update: input.date_of_update,
      updated_at: now
    });

    return { success: true };
  }
});

/**
 * Lists project updates for a specific project
 * Returns an array of project updates and a continuation token for pagination
 */
export const listByProject = zQuery({
  args: {
    input: ListProjectUpdatesInput
  },
  output: ListProjectUpdatesOutput,
  handler: async (ctx, args) => {
    const { input } = args;
    const { project_id, limit = 10, cursor } = input;

    // Verify the project exists
    const project = await ctx.db.get(project_id);
    if (!project) {
      throw new ConvexError(`Project with ID ${project_id} not found`);
    }

    // Query project updates for this project, sorted by date_of_update (most recent first)
    const paginationOpts = { numItems: limit, cursor };
    const paginationResult = await ctx.db
      .query('projectUpdates')
      .withIndex('by_project_date', (q) => q.eq('project_id', project_id))
      .order('desc')
      .paginate(paginationOpts);

    // Map the results to match the ProjectUpdate schema
    const projectUpdates = paginationResult.page.map(update => ({
      _id: update._id,
      _creationTime: update._creationTime,
      project_id: update.project_id,
      short_description: update.short_description,
      content: update.content,
      date_of_update: update.date_of_update,
      update_type: update.update_type,
      source_id: update.source_id,
      created_by: update.created_by,
      updated_at: update.updated_at
    }));

    return {
      projectUpdates,
      continuation: paginationResult.continueCursor
    };
  }
});
