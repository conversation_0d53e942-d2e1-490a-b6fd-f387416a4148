import { query } from "./_generated/server";
import { v } from "convex/values";

/**
 * Retrieves a prompt by its immutable slug.
 *
 * @param {string} slug - The immutable slug of the prompt.
 * @returns {object | null} The prompt object if found, null otherwise.
 */
export const getPromptBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const prompt = await ctx.db
      .query("prompts")
      .filter((q) => q.eq(q.field("immutable_slug"), args.slug))
      .first();
    return prompt ?? null;
  },
});
