/**
 * Centralized field definitions and helpers for entity data selection.
 * Update these lists to control which fields are included for each entity type.
 */

export const DECISION_FIELDS = [
  "_id",
  "title",
  "decision_category_id",
  "short_description",
  "summary",
  "project_ids",
  "decision_date",
  "due_date",
  "status",
  "importance",
  "priority",
  "approver_id",
  "badges",
  "amount",
  "amountType",
  "currencyCode",
  "updated_at",
  "driver",
  "contributors",
  "informed",
  "decision_type",
  "has_financial_implications",
  "financial_time_period",
  "financial_years",
  "yes_means",
  "no_means"
];

export const GENERAL_DECISION_FIELDS = [
  "description",
  "approver_id",
  "approval_date",
  "updated_at"
];

export const INVESTMENT_DECISION_FIELDS = [
  "description",
  "is_active",
  "proposed_investment",
  "total_fundraise",
  "asset_class",
  "structure",
  "investment_entity",
  "market",
  "geography"
];

/**
 * Helper to pick only allowed fields from an object.
 * Returns an object with all fields in the list, using null for missing values.
 */
export function pickFields(obj: any, fields: string[]) {
  const result: Record<string, any> = {};
  for (const field of fields) {
    result[field] = obj?.[field] ?? null;
  }
  return result;
}
