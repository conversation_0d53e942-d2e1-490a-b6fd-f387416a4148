import { v } from "convex/values";
import { query, action, internalQuery } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";
import { ConvexError } from "convex/values";

// Define types for search results
type SearchResult = {
  id: string;
  type: string;
  title: string;
  short_description?: string;
  score: number;
  item: any;
  source?: 'keyword' | 'contextual';
};

// Helper internal queries to fetch documents by IDs
export const fetchPeople = internalQuery({
  args: { ids: v.array(v.id("people")) },
  handler: async (ctx, args) => {
    const docs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    return docs.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});

export const fetchOrganizations = internalQuery({
  args: { ids: v.array(v.id("organizations")) },
  handler: async (ctx, args) => {
    const docs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    return docs.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});

export const fetchProjects = internalQuery({
  args: { ids: v.array(v.id("projects")) },
  handler: async (ctx, args) => {
    const docs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    return docs.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});

export const fetchDecisions = internalQuery({
  args: { ids: v.array(v.id("decisions")) },
  handler: async (ctx, args) => {
    const docs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    return docs.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});

export const fetchTasks = internalQuery({
  args: { ids: v.array(v.id("tasks")) },
  handler: async (ctx, args) => {
    const docs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    return docs.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});

export const fetchKnowledgeBase = internalQuery({
  args: { ids: v.array(v.id("knowledge_base")) },
  handler: async (ctx, args) => {
    const kbs = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    const enriched = await Promise.all(
      kbs.filter((kb): kb is NonNullable<typeof kb> => kb !== null)
         .map(async (kb) => {
           const file = kb.fileId ? await ctx.db.get(kb.fileId) : null;
           return { ...kb, file };
         })
    );
    return enriched;
  },
});

export const fetchMeetingNotes = internalQuery({
  args: { ids: v.array(v.id("meeting_notes")) },
  handler: async (ctx, args) => {
    const notes = await Promise.all(args.ids.map((id) => ctx.db.get(id)));
    const enriched = await Promise.all(
      notes.filter((note): note is NonNullable<typeof note> => note !== null)
           .map(async (note) => {
             const file = note.fileId ? await ctx.db.get(note.fileId) : null;
             return { ...note, file };
           })
    );
    return enriched;
  },
});

// Define the OpenAI embedding function
async function generateEmbedding(text: string): Promise<number[]> {
  // Use OpenAI API to generate embedding
  const response = await fetch("https://api.openai.com/v1/embeddings", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      input: text,
      model: "text-embedding-3-large",
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new ConvexError(`OpenAI API error: ${JSON.stringify(error)}`);
  }

  const result = await response.json();
  return result.data[0].embedding;
}

// Vector search action - required because vector search can only be performed in actions
export const vectorSearch = action({
  args: { 
    searchTerm: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    if (!args.searchTerm.trim()) {
      return [];
    }

    const limit = args.limit || 5;
    const results: SearchResult[] = [];
    const seenIds = new Set<string>();

    try {
      // Generate embedding for the search term
      const embedding = await generateEmbedding(args.searchTerm);
      
      // All vector search results will be marked as contextual
      const sourceType = 'contextual';

      // Search people with vector search across all vector fields
      const peopleByDescription = await ctx.vectorSearch("people", "description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const peopleByShortDescription = await ctx.vectorSearch("people", "short_description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const peopleByResearch = await ctx.vectorSearch("people", "research_embedding_idx", {
        vector: embedding,
        limit,
      });

      // Fetch the actual people documents
      const peopleIds = [
        ...peopleByDescription.map(r => r._id),
        ...peopleByShortDescription.map(r => r._id),
        ...peopleByResearch.map(r => r._id)
      ];

      // Create a map of id -> score for ranking
      const peopleScores = new Map<Id<"people">, number>();
      [...peopleByDescription, ...peopleByShortDescription, ...peopleByResearch].forEach(r => {
        if (peopleScores.has(r._id)) {
          peopleScores.set(r._id, Math.max(peopleScores.get(r._id)!, r._score));
        } else {
          peopleScores.set(r._id, r._score);
        }
      });

      // Fetch the actual people documents
      const people = await ctx.runQuery(internal.universalSearch.fetchPeople, { ids: peopleIds });

      // Add people to results
      for (const person of people) {
        if (!seenIds.has(person._id.toString())) {
          seenIds.add(person._id.toString());
          results.push({
            id: person._id.toString(),
            type: 'person',
            title: person.name,
            short_description: person.short_description || '',
            score: peopleScores.get(person._id as Id<"people">) || 0,
            item: person,
            source: sourceType,
          });
        }
      }

      // Similar pattern for organizations
      const orgsByDescription = await ctx.vectorSearch("organizations", "description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const orgsByShortDescription = await ctx.vectorSearch("organizations", "short_description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const orgsByResearch = await ctx.vectorSearch("organizations", "research_embedding_idx", {
        vector: embedding,
        limit,
      });

      const orgIds = [
        ...orgsByDescription.map(r => r._id),
        ...orgsByShortDescription.map(r => r._id),
        ...orgsByResearch.map(r => r._id)
      ];

      const orgScores = new Map<Id<"organizations">, number>();
      [...orgsByDescription, ...orgsByShortDescription, ...orgsByResearch].forEach(r => {
        if (orgScores.has(r._id)) {
          orgScores.set(r._id, Math.max(orgScores.get(r._id)!, r._score));
        } else {
          orgScores.set(r._id, r._score);
        }
      });

      const orgs = await ctx.runQuery(internal.universalSearch.fetchOrganizations, { ids: orgIds });

      for (const org of orgs) {
        if (!seenIds.has(org._id.toString())) {
          seenIds.add(org._id.toString());
          results.push({
            id: org._id.toString(),
            type: 'organization',
            title: org.name,
            short_description: org.short_description || '',
            score: orgScores.get(org._id as Id<"organizations">) || 0,
            item: org,
            source: sourceType,
          });
        }
      }

      // Projects
      const projectsByDescription = await ctx.vectorSearch("projects", "description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const projectsByShortDescription = await ctx.vectorSearch("projects", "short_description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const projectIds = [
        ...projectsByDescription.map(r => r._id),
        ...projectsByShortDescription.map(r => r._id)
      ];

      const projectScores = new Map<Id<"projects">, number>();
      [...projectsByDescription, ...projectsByShortDescription].forEach(r => {
        if (projectScores.has(r._id)) {
          projectScores.set(r._id, Math.max(projectScores.get(r._id)!, r._score));
        } else {
          projectScores.set(r._id, r._score);
        }
      });

      const projects = await ctx.runQuery(internal.universalSearch.fetchProjects, { ids: projectIds });

      for (const project of projects) {
        if (!seenIds.has(project._id.toString())) {
          seenIds.add(project._id.toString());
          results.push({
            id: project._id.toString(),
            type: 'project',
            title: project.name || '',
            short_description: project.short_description || '',
            score: projectScores.get(project._id as Id<"projects">) || 0,
            item: project,
            source: sourceType,
          });
        }
      }

      // Decisions
      const decisionsByShortDescription = await ctx.vectorSearch("decisions", "short_description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const decisionsBySummary = await ctx.vectorSearch("decisions", "summary_embedding_idx", {
        vector: embedding,
        limit,
      });

      const decisionIds = [
        ...decisionsByShortDescription.map(r => r._id),
        ...decisionsBySummary.map(r => r._id)
      ];

      const decisionScores = new Map<Id<"decisions">, number>();
      [...decisionsByShortDescription, ...decisionsBySummary].forEach(r => {
        if (decisionScores.has(r._id)) {
          decisionScores.set(r._id, Math.max(decisionScores.get(r._id)!, r._score));
        } else {
          decisionScores.set(r._id, r._score);
        }
      });

      const decisions = await ctx.runQuery(internal.universalSearch.fetchDecisions, { ids: decisionIds });

      for (const decision of decisions) {
        if (!seenIds.has(decision._id.toString())) {
          seenIds.add(decision._id.toString());
          results.push({
            id: decision._id.toString(),
            type: 'decision',
            title: decision.title || '',
            short_description: decision.short_description || '',
            score: decisionScores.get(decision._id as Id<"decisions">) || 0,
            item: decision,
            source: sourceType,
          });
        }
      }

      // Tasks
      const tasksByDescription = await ctx.vectorSearch("tasks", "description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const tasksByShortDescription = await ctx.vectorSearch("tasks", "short_description_embedding_idx", {
        vector: embedding,
        limit,
      });

      const tasksByCompletedNotes = await ctx.vectorSearch("tasks", "completed_notes_embedding_idx", {
        vector: embedding,
        limit,
      });

      const taskIds = [
        ...tasksByDescription.map(r => r._id),
        ...tasksByShortDescription.map(r => r._id),
        ...tasksByCompletedNotes.map(r => r._id)
      ];

      const taskScores = new Map<Id<"tasks">, number>();
      [...tasksByDescription, ...tasksByShortDescription, ...tasksByCompletedNotes].forEach(r => {
        if (taskScores.has(r._id)) {
          taskScores.set(r._id, Math.max(taskScores.get(r._id)!, r._score));
        } else {
          taskScores.set(r._id, r._score);
        }
      });

      const tasks = await ctx.runQuery(internal.universalSearch.fetchTasks, { ids: taskIds });

      for (const task of tasks) {
        if (!seenIds.has(task._id.toString())) {
          seenIds.add(task._id.toString());
          results.push({
            id: task._id.toString(),
            type: 'task',
            title: task.name,
            short_description: task.short_description || '',
            score: taskScores.get(task._id as Id<"tasks">) || 0,
            item: task,
            source: sourceType,
          });
        }
      }

      // Knowledge base
      const knowledgeBaseByContent = await ctx.vectorSearch("knowledge_base", "content_embedding_idx", {
        vector: embedding,
        limit,
      });

      const kbIds = knowledgeBaseByContent.map(r => r._id);
      const kbScores = new Map<Id<"knowledge_base">, number>();
      knowledgeBaseByContent.forEach(r => {
        kbScores.set(r._id, r._score);
      });

      const kbEntries = await ctx.runQuery(internal.universalSearch.fetchKnowledgeBase, { ids: kbIds });

      for (const kb of kbEntries) {
        if (!seenIds.has(kb._id.toString())) {
          seenIds.add(kb._id.toString());
          results.push({
            id: kb._id.toString(),
            type: 'knowledge_base',
            title: kb.file?.title || kb.file?.fileName || '',
            short_description: kb.file?.short_description || '',
            score: kbScores.get(kb._id as Id<"knowledge_base">) || 0,
            item: kb,
            source: sourceType,
          });
        }
      }

      // Meeting notes
      const meetingNotesByContent = await ctx.vectorSearch("meeting_notes", "content_embedding_idx", {
        vector: embedding,
        limit,
      });

      const meetingNotesByManualNotes = await ctx.vectorSearch("meeting_notes", "manualNotes_embedding_idx", {
        vector: embedding,
        limit,
      });

      const meetingNotesByTranscript = await ctx.vectorSearch("meeting_notes", "transcript_embedding_idx", {
        vector: embedding,
        limit,
      });

      const mnIds = [
        ...meetingNotesByContent.map(r => r._id),
        ...meetingNotesByManualNotes.map(r => r._id),
        ...meetingNotesByTranscript.map(r => r._id)
      ];

      const mnScores = new Map<Id<"meeting_notes">, number>();
      [...meetingNotesByContent, ...meetingNotesByManualNotes, ...meetingNotesByTranscript].forEach(r => {
        if (mnScores.has(r._id)) {
          mnScores.set(r._id, Math.max(mnScores.get(r._id)!, r._score));
        } else {
          mnScores.set(r._id, r._score);
        }
      });

      const meetingNotes = await ctx.runQuery(internal.universalSearch.fetchMeetingNotes, { ids: mnIds });

      for (const note of meetingNotes) {
        if (!seenIds.has(note._id.toString())) {
          seenIds.add(note._id.toString());
          results.push({
            id: note._id.toString(),
            type: 'meeting_notes',
            title: note.file?.title || note.file?.fileName || '',
            short_description: note.file?.short_description || '',
            score: mnScores.get(note._id as Id<"meeting_notes">) || 0,
            item: note,
            source: sourceType,
          });
        }
      }

      // Sort results by score (highest first)
      results.sort((a, b) => b.score - a.score);

      return results;
    } catch (error) {
      console.error("Error in vector search:", error);
      throw new ConvexError(`Vector search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

// Full-text search query
export const textSearch = query({
  args: { 
    searchTerm: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    if (!args.searchTerm.trim()) {
      return [];
    }

    const limit = args.limit || 10;
    const results: SearchResult[] = [];
    const seenIds = new Set<string>();

    // People by name only
    const peopleByName = await ctx.db
      .query("people")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    // Add people results
    for (const person of peopleByName) {
      if (!seenIds.has(person._id.toString())) {
        seenIds.add(person._id.toString());
        results.push({
          id: person._id.toString(),
          type: 'person',
          title: person.name,
          short_description: person.short_description || '',
          score: 1.0,
          item: person,
        });
      }
    }

    // Organizations by name
    const orgsByName = await ctx.db
      .query("organizations")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const org of orgsByName) {
      if (!seenIds.has(org._id.toString())) {
        seenIds.add(org._id.toString());
        results.push({
          id: org._id.toString(),
          type: 'organization',
          title: org.name,
          short_description: org.short_description || '',
          score: 1.0,
          item: org,
        });
      }
    }

    // Projects by name
    const projectsByName = await ctx.db
      .query("projects")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const project of projectsByName) {
      if (!seenIds.has(project._id.toString())) {
        seenIds.add(project._id.toString());
        results.push({
          id: project._id.toString(),
          type: 'project',
          title: project.name || '',
          short_description: project.short_description || '',
          score: 1.0,
          item: project,
        });
      }
    }

    // Decisions by title
    const decisionsByTitle = await ctx.db
      .query("decisions")
      .withSearchIndex("search_title", (q) => q.search("title", args.searchTerm))
      .take(limit);

    // Decisions by summary
    const decisionsBySummary = await ctx.db
      .query("decisions")
      .withSearchIndex("search_summary", (q) => q.search("summary", args.searchTerm))
      .take(limit);

    for (const decision of [...decisionsByTitle, ...decisionsBySummary]) {
      if (!seenIds.has(decision._id.toString())) {
        seenIds.add(decision._id.toString());
        results.push({
          id: decision._id.toString(),
          type: 'decision',
          title: decision.title || '',
          short_description: decision.short_description || '',
          score: 1.0,
          item: decision,
        });
      }
    }

    // Tasks by name
    const tasksByName = await ctx.db
      .query("tasks")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const task of tasksByName) {
      if (!seenIds.has(task._id.toString())) {
        seenIds.add(task._id.toString());
        results.push({
          id: task._id.toString(),
          type: 'task',
          title: task.name,
          short_description: task.short_description || '',
          score: 1.0,
          item: task,
        });
      }
    }

    // Knowledge base by content
    const knowledgeBaseByContent = await ctx.db
      .query("knowledge_base")
      .withSearchIndex("search_content", (q) => q.search("content", args.searchTerm))
      .take(limit);

    // Batch fetch files for knowledge base entries
    const kbFileIds = knowledgeBaseByContent
      .filter(kb => kb.fileId)
      .map(kb => kb.fileId);
    
    const kbFiles = await Promise.all(
      kbFileIds.map(fileId => ctx.db.get(fileId))
    );
    
    // Create a map for quick file lookups
    const fileMap = new Map();
    kbFiles.forEach(file => {
      if (file) fileMap.set(file._id.toString(), file);
    });

    for (const kb of knowledgeBaseByContent) {
      if (!seenIds.has(kb._id.toString())) {
        seenIds.add(kb._id.toString());
        const file = kb.fileId ? fileMap.get(kb.fileId.toString()) : null;
        if (file) {
          results.push({
            id: kb._id.toString(),
            type: 'knowledge_base',
            title: file.title || file.fileName || '',
            short_description: file.short_description || '',
            score: 1.0,
            item: { ...kb, file },
          });
        }
      }
    }

    // Files by title
    const filesByTitle = await ctx.db
      .query("files")
      .withSearchIndex("search_title", (q) => q.search("title", args.searchTerm))
      .take(limit);

    for (const file of filesByTitle) {
      if (!seenIds.has(file._id.toString())) {
        seenIds.add(file._id.toString());
        results.push({
          id: file._id.toString(),
          type: 'file',
          title: file.title || file.fileName || '',
          short_description: file.short_description || '',
          score: 1.0,
          item: file,
        });
      }
    }

    return results;
  },
});

// Universal search that combines vector and text search
export const universalSearch = query({
  args: { 
    searchTerm: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    if (!args.searchTerm.trim()) {
      return [];
    }

    // Perform text search directly in this query
    const limit = args.limit || 10;
    const textResults: SearchResult[] = [];
    const textSearchSeenIds = new Set<string>();

    // People by name only
    const peopleByName = await ctx.db
      .query("people")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    // Add people results
    for (const person of peopleByName) {
      if (!textSearchSeenIds.has(person._id.toString())) {
        textSearchSeenIds.add(person._id.toString());
        textResults.push({
          id: person._id.toString(),
          type: 'person',
          title: person.name,
          short_description: person.short_description || '',
          score: 1.0,
          item: person,
          source: 'keyword',
        });
      }
    }

    // Organizations by name
    const orgsByName = await ctx.db
      .query("organizations")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const org of orgsByName) {
      if (!textSearchSeenIds.has(org._id.toString())) {
        textSearchSeenIds.add(org._id.toString());
        textResults.push({
          id: org._id.toString(),
          type: 'organization',
          title: org.name,
          short_description: org.short_description || '',
          score: 1.0,
          item: org,
          source: 'keyword',
        });
      }
    }

    // Projects by name
    const projectsByName = await ctx.db
      .query("projects")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const project of projectsByName) {
      if (!textSearchSeenIds.has(project._id.toString())) {
        textSearchSeenIds.add(project._id.toString());
        textResults.push({
          id: project._id.toString(),
          type: 'project',
          title: project.name || '',
          short_description: project.short_description || '',
          score: 1.0,
          item: project,
          source: 'keyword',
        });
      }
    }

    // Decisions by title
    const decisionsByTitle = await ctx.db
      .query("decisions")
      .withSearchIndex("search_title", (q) => q.search("title", args.searchTerm))
      .take(limit);

    // Decisions by summary
    const decisionsBySummary = await ctx.db
      .query("decisions")
      .withSearchIndex("search_summary", (q) => q.search("summary", args.searchTerm))
      .take(limit);

    for (const decision of [...decisionsByTitle, ...decisionsBySummary]) {
      if (!textSearchSeenIds.has(decision._id.toString())) {
        textSearchSeenIds.add(decision._id.toString());
        textResults.push({
          id: decision._id.toString(),
          type: 'decision',
          title: decision.title || '',
          short_description: decision.short_description || '',
          score: 1.0,
          item: decision,
          source: 'keyword',
        });
      }
    }

    // Tasks by name
    const tasksByName = await ctx.db
      .query("tasks")
      .withSearchIndex("search_name", (q) => q.search("name", args.searchTerm))
      .take(limit);

    for (const task of tasksByName) {
      if (!textSearchSeenIds.has(task._id.toString())) {
        textSearchSeenIds.add(task._id.toString());
        textResults.push({
          id: task._id.toString(),
          type: 'task',
          title: task.name,
          short_description: task.short_description || '',
          score: 1.0,
          item: task,
          source: 'keyword',
        });
      }
    }

    // Track file IDs that are already included as part of meeting_notes or knowledge_base
    const includedFileIds = new Set<string>();

    // Meeting notes keyword search via configured search indexes
    const meetingNotesByContent = await ctx.db
      .query("meeting_notes")
      .withSearchIndex("search_content", (q) => q.search("content", args.searchTerm))
      .take(limit);
    
    const meetingNotesByManual = await ctx.db
      .query("meeting_notes")
      .withSearchIndex("search_manualNotes", (q) => q.search("manualNotes", args.searchTerm))
      .take(limit);

    // Combine meeting notes results
    const allMeetingNotes = [...meetingNotesByContent, ...meetingNotesByManual];
    
    // Batch fetch files for meeting notes
    const meetingNotesFileIds = allMeetingNotes
      .filter(note => note.fileId)
      .map(note => note.fileId);
    
    const meetingNotesFiles = await Promise.all(
      meetingNotesFileIds.map(fileId => ctx.db.get(fileId))
    );
    
    // Create a map for quick file lookups
    const fileMap = new Map();
    meetingNotesFiles.forEach(file => {
      if (file) fileMap.set(file._id.toString(), file);
    });

    for (const note of allMeetingNotes) {
      if (!textSearchSeenIds.has(note._id.toString())) {
        textSearchSeenIds.add(note._id.toString());
        const file = note.fileId ? fileMap.get(note.fileId.toString()) : null;
        if (file) {
          // Mark this file ID as included
          includedFileIds.add(note.fileId.toString());
          textResults.push({
            id: note._id.toString(),
            type: 'meeting_notes',
            title: file.title || file.fileName || '',
            short_description: file.short_description || '',
            score: 1.0,
            item: { ...note, fileId: note.fileId, file },
            source: 'keyword',
          });
        }
      }
    }

    // Knowledge base keyword search via configured search index
    const knowledgeBaseByContent = await ctx.db
      .query("knowledge_base")
      .withSearchIndex("search_content", (q) => q.search("content", args.searchTerm))
      .take(limit);

    // Batch fetch files for knowledge base entries
    const kbFileIds = knowledgeBaseByContent
      .filter(kb => kb.fileId)
      .map(kb => kb.fileId);
    
    const kbFiles = await Promise.all(
      kbFileIds.map(fileId => ctx.db.get(fileId))
    );
    
    // Add knowledge base files to the map for quick lookups
    kbFiles.forEach(file => {
      if (file) fileMap.set(file._id.toString(), file);
    });

    for (const kb of knowledgeBaseByContent) {
      if (!textSearchSeenIds.has(kb._id.toString())) {
        textSearchSeenIds.add(kb._id.toString());
        const file = kb.fileId ? fileMap.get(kb.fileId.toString()) : null;
        if (file) {
          // Mark this file ID as included
          includedFileIds.add(kb.fileId.toString());
          textResults.push({
            id: kb._id.toString(),
            type: 'knowledge_base',
            title: file.title || file.fileName || '',
            short_description: file.short_description || '',
            score: 1.0,
            item: { ...kb, fileId: kb.fileId, file },
            source: 'keyword',
          });
        }
      }
    }

    // Files by title - only include files that aren't already part of meeting_notes or knowledge_base
    const filesByTitle = await ctx.db
      .query("files")
      .withSearchIndex("search_title", (q) => q.search("title", args.searchTerm))
      .take(limit);

    // Fetch related knowledge_base and meeting_notes in batches
    const fileIdsToCheck = filesByTitle.map(file => file._id);
    
    // Batch fetch knowledge_base entries
    const kbEntriesByFiles = await Promise.all(
      fileIdsToCheck.map(fileId => 
        ctx.db
          .query("knowledge_base")
          .withIndex("by_file", (q) => q.eq("fileId", fileId))
          .first()
      )
    );
    
    // Batch fetch meeting_notes entries
    const noteEntriesByFiles = await Promise.all(
      fileIdsToCheck.map(fileId => 
        ctx.db
          .query("meeting_notes")
          .withIndex("by_file", (q) => q.eq("fileId", fileId))
          .first()
      )
    );

    // Create maps for quick lookups
    const kbEntriesByFileId = new Map();
    const noteEntriesByFileId = new Map();
    
    fileIdsToCheck.forEach((fileId, index) => {
      if (kbEntriesByFiles[index]) {
        kbEntriesByFileId.set(fileId.toString(), kbEntriesByFiles[index]);
      }
      if (noteEntriesByFiles[index]) {
        noteEntriesByFileId.set(fileId.toString(), noteEntriesByFiles[index]);
      }
    });

    for (const file of filesByTitle) {
      // Skip if this file is already included as part of a meeting note or knowledge base
      if (!includedFileIds.has(file._id.toString()) && !textSearchSeenIds.has(file._id.toString())) {
        // Check if this file is referenced by a knowledge_base
        const kb = kbEntriesByFileId.get(file._id.toString());
        if (kb) {
          textSearchSeenIds.add(kb._id.toString());
          includedFileIds.add(file._id.toString());
          textResults.push({
            id: kb._id.toString(),
            type: 'knowledge_base',
            title: file.title || file.fileName || '',
            short_description: file.short_description || '',
            score: 1.0,
            item: { ...kb, file },
            source: 'keyword',
          });
          continue;
        }

        // Check for meeting_notes referencing this file
        const note = noteEntriesByFileId.get(file._id.toString());
        if (note) {
          textSearchSeenIds.add(note._id.toString());
          includedFileIds.add(file._id.toString());
          textResults.push({
            id: note._id.toString(),
            type: 'meeting_notes',
            title: file.title || file.fileName || '',
            short_description: file.short_description || '',
            score: 1.0,
            item: { ...note, file },
            source: 'keyword',
          });
          continue;
        }

        // If not referenced, return as a file
        textSearchSeenIds.add(file._id.toString());
        textResults.push({
          id: file._id.toString(),
          type: 'file',
          title: file.title || file.fileName || '',
          short_description: file.short_description || '',
          score: 1.0,
          item: file,
          source: 'keyword',
        });
      }
    }

    // We can't call the vector search action directly from a query
    const vectorResults: SearchResult[] = [];

    // Combine and deduplicate results
    const combinedResults: SearchResult[] = [];
    const combinedSeenIds = new Set<string>();

    // Add vector results first (they typically have better relevance)
    for (const result of vectorResults) {
      if (!combinedSeenIds.has(`${result.type}:${result.id}`)) {
        combinedSeenIds.add(`${result.type}:${result.id}`);
        combinedResults.push(result);
      }
    }

    // Add text results
    for (const result of textResults) {
      if (!combinedSeenIds.has(`${result.type}:${result.id}`)) {
        combinedSeenIds.add(`${result.type}:${result.id}`);
        combinedResults.push(result);
      }
    }

    // Sort by score (highest first)
    combinedResults.sort((a, b) => b.score - a.score);

    // Limit results if needed
    const finalLimit = args.limit || 20;
    return combinedResults.slice(0, finalLimit);
  },
});

// Query to fetch the most recently updated entities across all collections
export const recentEntities = query({
  args: { limit: v.number() },
  handler: async (ctx, args) => {
    const { limit } = args;
    const results: Array<{
      id: string;
      title: string;
      type: string;
      short_description?: string;
      item?: any;
      updated_at: number;
    }> = [];

    // Fetch recent projects
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    for (const project of projects) {
      results.push({
        id: project._id.toString(),
        type: 'project',
        title: project.name || '',
        short_description: project.short_description || '',
        item: project,
        updated_at: project.updated_at ?? project._creationTime,
      });
    }

    // Fetch recent tasks
    const tasks = await ctx.db
      .query("tasks")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    for (const task of tasks) {
      results.push({
        id: task._id.toString(),
        type: 'task',
        title: task.name,
        short_description: task.short_description || '',
        item: task,
        updated_at: task.updated_at ?? task._creationTime,
      });
    }

    // Fetch recent decisions
    const decisions = await ctx.db
      .query("decisions")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    for (const decision of decisions) {
      results.push({
        id: decision._id.toString(),
        type: 'decision',
        title: decision.title || '',
        short_description: decision.short_description || '',
        item: decision,
        updated_at: decision.updated_at ?? decision._creationTime,
      });
    }

    // Fetch recent people
    const people = await ctx.db
      .query("people")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    for (const person of people) {
      results.push({
        id: person._id.toString(),
        type: 'person',
        title: person.name,
        short_description: person.short_description || '',
        item: person,
        updated_at: person.updated_at ?? person._creationTime,
      });
    }

    // Fetch recent organizations
    const organizations = await ctx.db
      .query("organizations")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    for (const organization of organizations) {
      results.push({
        id: organization._id.toString(),
        type: 'organization',
        title: organization.name,
        short_description: organization.short_description || '',
        item: organization,
        updated_at: organization.updated_at ?? organization._creationTime,
      });
    }

    // Fetch recent meeting notes
    const meetingNotes = await ctx.db
      .query("meeting_notes")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    // Batch fetch files for meeting notes
    const meetingNotesFileIds = meetingNotes
      .filter(note => note.fileId)
      .map(note => note.fileId);
    
    const meetingNotesFiles = await Promise.all(
      meetingNotesFileIds.map(fileId => ctx.db.get(fileId))
    );
    
    // Create a map for quick file lookups
    const fileMap = new Map();
    meetingNotesFiles.forEach(file => {
      if (file) fileMap.set(file._id.toString(), file);
    });

    for (const note of meetingNotes) {
      const file = note.fileId ? fileMap.get(note.fileId.toString()) : null;
      results.push({
        id: note._id.toString(),
        type: 'meeting_notes',
        title: file?.title || file?.fileName || '',
        short_description: file?.short_description || '',
        item: { ...note, file },
        updated_at: note.updated_at ?? note._creationTime,
      });
    }

    // Fetch recent knowledge base entries
    const knowledgeBase = await ctx.db
      .query("knowledge_base")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    // Batch fetch files for knowledge base entries
    const kbFileIds = knowledgeBase
      .filter(kb => kb.fileId)
      .map(kb => kb.fileId);
    
    const kbFiles = await Promise.all(
      kbFileIds.map(fileId => ctx.db.get(fileId))
    );
    
    // Add new files to the map for quick lookups
    kbFiles.forEach(file => {
      if (file) fileMap.set(file._id.toString(), file);
    });

    for (const kb of knowledgeBase) {
      const file = kb.fileId ? fileMap.get(kb.fileId.toString()) : null;
      results.push({
        id: kb._id.toString(),
        type: 'knowledge_base',
        title: file?.title || file?.fileName || '',
        short_description: file?.short_description || '',
        item: { ...kb, file },
        updated_at: kb.updated_at ?? kb._creationTime,
      });
    }

    // Fetch recent files - but only those not already included as meeting_notes or knowledge_base
    const files = await ctx.db
      .query("files")
      .withIndex("by_creation_time", (q) => q)
      .order("desc")
      .take(limit);

    // Track file IDs that are already included as part of meeting_notes or knowledge_base
    const includedFileIds = new Set<string>();
    
    // Collect file IDs from meeting notes and knowledge base entries
    for (const result of results) {
      if (result.type === 'meeting_notes' && result.item?.fileId) {
        includedFileIds.add(result.item.fileId.toString());
      } else if (result.type === 'knowledge_base' && result.item?.fileId) {
        includedFileIds.add(result.item.fileId.toString());
      }
    }

    // Only add files that aren't already included as meeting_notes or knowledge_base
    for (const file of files) {
      if (!includedFileIds.has(file._id.toString())) {
        results.push({
          id: file._id.toString(),
          type: 'file',
          title: file.title || file.fileName || '',
          short_description: file.short_description || '',
          item: file,
          updated_at: file.updated_at ?? file._creationTime,
        });
      }
    }

    // Sort all results by updated_at in descending order
    results.sort((a, b) => b.updated_at - a.updated_at);

    // Return only the requested number of items
    return results.slice(0, limit);
  },
});