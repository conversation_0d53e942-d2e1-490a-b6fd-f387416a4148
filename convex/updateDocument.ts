import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
import { ConvexError } from "convex/values";
import { Id } from "./_generated/dataModel";
import { MutationCtx, QueryCtx } from "./_generated/server"; // Import MutationCtx and QueryCtx for typing
import { getSubTableData } from "./utils/fileUtils"; // Corrected import path

// Define the expected input structure, adding fields needed for the new chunking strategy
const documentDataValidator = v.object({
  id: v.id('files'),
  title: v.string(),
  content: v.string(),
  docType: v.string(),
  short_description: v.optional(v.union(v.string(), v.null())), // Added
  category: v.optional(v.union(v.string(), v.null())),        // Added
  projectId: v.optional(v.union(v.id('projects'), v.null())),
  orgId: v.string(),
  createdAt: v.number(), // Pass original creation time for timestamp consistency
});

// Define return type for this mutation (now schedules an action)
type UpdateResult = {
  status: "scheduled" | "error";
  documentId: Id<"files">;
  message?: string;
};

// Mutation to update a document in Trieve by deleting old chunks and re-ingesting
export default mutation({
  // Args now only needs the fileId, other data will be fetched
  args: { fileId: v.id('files') },
  handler: async (ctx: MutationCtx, args): Promise<UpdateResult> => { // Add explicit types
    const { fileId } = args;

    console.log(`Starting Trieve re-index process for document ID: ${fileId}`);

    try {
      // Step 1: Fetch required data from Convex
      const fileRecord = await ctx.db.get(fileId);
      if (!fileRecord) {
        throw new ConvexError(`File record not found: ${fileId}`);
      }

      const { category, content } = await getSubTableData(ctx, fileId, fileRecord.docType);

      // Ensure we have content to ingest
      if (!content || content.trim().length === 0) {
        console.warn(`Document ${fileId} has empty content. Skipping re-index.`);
        return { status: "scheduled", documentId: fileId, message: "Skipped: empty content" }; // Return scheduled but note skipped reason
      }

      // Prepare the combined data object for the action
      // TODO: Determine how to get the correct orgId for multi-tenancy filtering
      const orgId = "PLACEHOLDER_ORG_ID"; // Replace with actual orgId logic
      // TODO: Determine if projectId is relevant/available here
      // Simplified check: If parentEntityId exists, pass it. The action might need more robust type checking if needed.
      const projectId = fileRecord.parentEntityId ? fileRecord.parentEntityId as Id<'projects'> : null;


      const combinedDocData = {
        id: fileId,
        title: fileRecord.title ?? 'Untitled',
        short_description: fileRecord.short_description ?? null,
        content: content, // Use fetched content
        category: category ?? null, // Use fetched category
        docType: fileRecord.docType,
        projectId: projectId,
        orgId: orgId,
        createdAt: fileRecord._creationTime,
      };

      // Step 2: Remove existing chunks/group for this document from Trieve
      console.log(`Calling deleteDocument mutation for Trieve group: ${fileId}`);
      // Correctly reference the default export
      const deleteResult = await ctx.runMutation(api.trieve.deleteDocument.default, { docId: fileId });
      console.log(`Trieve deletion result for ${fileId}:`, deleteResult);
      // We proceed even if status is 'not_found'

      // Step 3: Schedule the re-ingestion action
      console.log(`Scheduling Trieve update for document ${fileId}`);
      await ctx.scheduler.runAfter(3000, api.actions.trieveActions.trieveUpsertDocumentChunks, { doc: combinedDocData });

      console.log(`Successfully scheduled re-index for document ID: ${fileId} in Trieve`);
      // Ensure return type matches UpdateResult
      return { status: "scheduled", documentId: fileId };

    } catch (error: unknown) {
      console.error(`Error during Trieve re-index scheduling for document ID ${args.fileId}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      // Consider if specific errors need different handling
      throw new ConvexError(`Trieve re-index failed for ${args.fileId}: ${errorMessage}`);
      // return { status: "error", message: errorMessage, documentId: args.fileId };
    }
  },
});
