import { query, mutation } from "../_generated/server";
import { api } from "../_generated/api";
import { v } from "convex/values";
import { z } from "zod";
import { zQuery } from "../functions";

const BillComConfig = z.object({
  apiUserName: z.string(),
  devKey: z.string(),
  orgId: z.string(),
  environmentURL: z.string(),
  apiPasswordOrToken: z.optional(z.string()),
  sessionId: z.optional(z.string()),
  rememberMeId: z.optional(z.string()),
  device: z.optional(z.string())
});

/**
 * Query to get Bill.com configuration from integrations table
 * Retrieves credentials needed for Bill.com API calls
 * Only returns config if integration is ACTIVE
 */
export const getBillComConfig = zQuery({
  args: {},
  handler: async (ctx) => {
    // Fetch the integration with the slug "bill-com"
    const integration = await ctx.db
      .query("integrations")
      .filter((q) => q.eq(q.field("immutable_slug"), "bill-com"))
      .first();

    // Return null if integration doesn't exist or is not active
    if (!integration || integration.status !== "ACTIVE") {
      return null;
    }

    try {
      // Return the user_config validated by Zod, or null if validation fails
      return BillComConfig.parse(integration.user_config) ?? null;
    } catch (error) {
      // Log validation error but return null to fail gracefully
      console.error("Bill.com config validation failed:", error);
      return null;
    }
  },
});

/**
 * Mutation to update the Bill.com integration's user_config
 */
export const updateBillComConfig = mutation({
  args: {
    sessionId: v.string(),
    rememberMeId: v.optional(v.string()),
    device: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Fetch the integration with the slug "bill-com"
    const integration = await ctx.db
      .query("integrations")
      .filter((q) => q.eq(q.field("immutable_slug"), "bill-com"))
      .first();

    if (!integration) {
      console.warn("Bill.com integration not found");
      return;
    }

    // Update the user_config with the new sessionId, rememberMeId, and device
    await ctx.db.patch(integration._id, {
      user_config: {
        ...integration.user_config,
        sessionId: args.sessionId,
        rememberMeId: args.rememberMeId,
        device: args.device,
      },
    });
  },
});
