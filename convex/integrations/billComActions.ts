"use node";

import { action } from "../_generated/server";
import { api } from "../_generated/api";
import { v } from "convex/values";

// Define response types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Action to get bills from Bill.com
 * Retrieves a list of bills from the Bill.com API
 */
export const getBills = action({
  args: {
    sessionId: v.string(),
    devKey: v.string(),
    max: v.optional(v.number()),
    sort: v.optional(v.string()),
    filters: v.optional(v.string()),
    page: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<ApiResponse> => {
    try {
      // Construct query parameters
      const queryParams = new URLSearchParams();
      if (args.max !== undefined) {
        if (args.max >= 1 && args.max <= 100) {
          queryParams.append("max", args.max.toString());
        } else {
          return {
            success: false,
            error: "Invalid 'max' parameter. Must be between 1 and 100.",
          };
        }
      }
      if (args.sort !== undefined) {
        queryParams.append("sort", args.sort);
      }
      
      // Use the correct format for Bill.com API filters: field:operator:value
      const filterStr = `archived:eq:false`;
      
      // Add the filter to the query parameters
      queryParams.append("filters", filterStr);
      
      // Log the filter being applied
      console.log("Applying filter for non-archived bills:", filterStr);
      
      if (args.page !== undefined) {
        queryParams.append("page", args.page);
      }

      // Make request to Bill.com API
      const url = `https://gateway.stage.bill.com/connect/v3/bills${
        queryParams.toString().length > 0 ? `?${queryParams.toString()}` : ""
      }`;

      const headers: Record<string, string> = {
        accept: "application/json",
        devKey: args.devKey,
        sessionId: args.sessionId,
      };

      const options = {
        method: "GET",
        headers: headers,
      };

      // Fetch bills from Bill.com API
      const response = await fetch(url, options);
      const data = await response.json();

      // Log the response for debugging
      console.log("Bill.com API response status:", response.status);
      console.log("Bill.com API response structure:", JSON.stringify(data, null, 2).substring(0, 500) + "...");
      
      // Check if the response has the expected structure
      if (!data || !data.results) {
        console.error("Unexpected response format from Bill.com:", JSON.stringify(data, null, 2));
        return {
          success: false,
          error: "Unexpected response format from Bill.com. See server logs for details."
        };
      }
      
      // Log the successful response
      console.log(
        `Successfully fetched bills from Bill.com. Number of bills: ${
          data.results?.length ?? 0
        }`
      );

      // Return the response
      return { success: true, data };
    } catch (error) {
      // Handle errors gracefully
      console.error(`Error fetching bills from Bill.com:`, error);
      return {
        success: false,
        error: "Failed to fetch bills from Bill.com. Please try again later.",
      };
    }
  }
});

/**
 * Action to login to Bill.com and get a session ID
 * Makes a POST request to Bill.com API using credentials from integrations table
 */
export const billComLogin = action({
  args: {
    devKey: v.string(),
    username: v.string(),
    password: v.string(),
    organizationId: v.string(),
    rememberMeId: v.optional(v.string()),
    device: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<ApiResponse> => {
    try {
      // Get Bill.com configuration by running the query
      const billComConfig = await ctx.runQuery(
        api.integrations.billCom.getBillComConfig
      );

      // Handle case where config doesn't exist
      if (!billComConfig) {
        return {
          success: false,
          error:
            "Bill.com configuration not found. Please set up the Bill.com integration first.",
        };
      }

      const { environmentURL } = billComConfig;

      const url = `${environmentURL}login`;

      const options = {
        method: "POST",
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
        body: JSON.stringify({
          devKey: args.devKey,
          username: args.username,
          password: args.password,
          organizationId: args.organizationId,
          rememberMeId: args.rememberMeId,
          device: args.device,
        }),
      };

      const response = await fetch(url, options);
      const data = await response.json();
      console.log("billComLogin response", data);

      // If login was successful and we have a sessionId, save it
      if (data.sessionId) {
        await ctx.runMutation(api.integrations.billCom.updateBillComConfig, {
          sessionId: data.sessionId,
          rememberMeId: data.rememberMeId,
          device: args.device,
        });
      }

      return { success: true, data };
    } catch (error) {
      // Handle errors gracefully
      console.error(`Error logging in to Bill.com:`, error);
      return {
        success: false,
        error: "Failed to log in to Bill.com. Please try again later.",
      };
    }
  },
});
