import { z } from 'zod';
import { zQuery } from './functions';
import { v } from 'convex/values';
import { zid } from 'convex-helpers/server/zod'; // Import zid
import { Doc, Id } from './_generated/dataModel'; // Import Doc and Id

/**
 * Search for people by name with pagination support
 * 
 * This query uses the search_name index defined in schema.ts to efficiently
 * search for people by name. It's optimized for @mentions functionality.
 * 
 * @example
 * // Basic usage (returns array of people)
 * const people = await client.query.searchPeople({ search: "john" });
 * 
 * // With pagination (returns pagination object)
 * const result = await client.query.searchPeople({ 
 *   search: "john",
 *   paginate: true,
 *   cursor: "optional-cursor-from-previous-query",
 *   numItems: 50
 * });
 * 
 * // Access paginated results
 * const people = result.page;
 * const hasMoreResults = !result.isDone;
 * const nextCursor = result.continueCursor;
 */
export const searchPeople = zQuery({
  args: {
    search: z.string().optional(),
    paginate: z.boolean().optional().default(false),
    cursor: z.string().optional(),
    numItems: z.number().optional().default(50)
  },
  handler: async (ctx, args) => {
    const searchTerm = args.search || '';
    
    // Build the query
    let query = searchTerm.trim() !== ''
      ? ctx.db
          .query('people')
          .withSearchIndex('search_name', (q) => 
            q.search('name', searchTerm)
          )
      : ctx.db
          .query('people')
          .withIndex('by_updated')
          .order('desc');  // Order by the indexed field (updated_at)
    
    // If pagination is requested, return the pagination object
    if (args.paginate) {
      return await query.paginate({
        cursor: args.cursor || null,
        numItems: args.numItems || 50
      });
    }
    
    // Otherwise, just return the array of people (limited to 50)
    return await query.take(args.numItems || 50);
  },
});


/**
 * Retrieves a single person by their ID
 */
export const getPerson = zQuery({
  args: {
    id: zid('people')
  },
  output: z.union([z.custom<Doc<'people'>>(), z.null()]), // Allow null if not found
  handler: async (ctx, args): Promise<Doc<'people'> | null> => {
    return await ctx.db.get(args.id);
  },
});
