import { v } from 'convex/values';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod'; // Added zid import
import { zMutation, zQuery } from './functions';
import { mutation, query } from './_generated/server';
import {
  CreateEntityRelationshipInput,
  EntityRelationshipFilterSchema,
  PaginationSchema,
  UpdateEntityRelationshipInput,
  BulkRelationshipUpdateSchema,
} from '../zod/entity_relationships-schema';
import { Id } from './_generated/dataModel';

// ==================================================================
// Mutations
// ==================================================================

/**
 * Create a new entity relationship.
 */
export const create = zMutation({
  args: {
    data: CreateEntityRelationshipInput,
  },
  handler: async (ctx, args) => { // Use args directly, ctx type inferred
    const { data } = args; // Destructure data from args
    const relationshipId = await ctx.db.insert('entity_relationships', {
      entity_source_type: data.entity_source_type,
      entity_source_id: data.entity_source_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>, // Keep cast
      entity_target_type: data.entity_target_type,
      entity_target_id: data.entity_target_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>, // Keep cast
      entity_relationship_type: data.entity_relationship_type,
    });
    return relationshipId;
  },
});

/**
 * Delete an entity relationship by its ID.
 */
export const deleteById = mutation({
  args: { id: v.id('entity_relationships') },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});

/**
 * Update the relationship type of an existing entity relationship.
 */
export const update = zMutation({
  args: {
    id: zid('entity_relationships'), // Use zid for validation
    data: UpdateEntityRelationshipInput,
  },
  handler: async (ctx, args) => {
    const { id, data } = args;
    // Fetching the existing record first ensures it exists before patching
    const existing = await ctx.db.get(id);
    if (!existing) {
      throw new Error(`Relationship with ID ${id} not found`);
    }
    // Patch only the allowed fields from UpdateEntityRelationshipInput
    await ctx.db.patch(id, {
      entity_relationship_type: data.entity_relationship_type,
      // Add other updatable fields here if the Zod schema changes
    });
    return id; // Return the ID of the updated relationship
  },
});

/**
 * Delete a relationship based on source and target entities.
 */
export const deleteBySourceAndTarget = zMutation({
  args: {
    source_type: z.enum(['project', 'decision', 'task']),
    source_id: z.string(),
    target_type: z.enum(['project', 'decision', 'task']),
    target_id: z.string(),
  },
  handler: async (ctx, args) => {
    const { source_type, source_id, target_type, target_id } = args;

    // Cast IDs for type safety
    const typed_source_id = source_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>;
    const typed_target_id = target_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>;

    const existingRelationship = await ctx.db
      .query('entity_relationships')
      .withIndex('by_source', (q) => 
        q.eq('entity_source_type', source_type)
      )
      .filter((q) => 
        q.and(
          q.eq(q.field('entity_source_id'), typed_source_id),
          q.eq(q.field('entity_target_type'), target_type),
          q.eq(q.field('entity_target_id'), typed_target_id)
        )
      )
      .first();

    if (existingRelationship) {
      await ctx.db.delete(existingRelationship._id);
      return { deleted: true, id: existingRelationship._id };
    } else {
      return { deleted: false, id: null }; // Indicate relationship not found
    }
  },
});

// TODO: Add bulk delete/update mutations if needed (e.g., BulkRelationshipUpdateSchema)
// export const bulkDelete = mutation({ ... });


// ==================================================================
// Queries
// ==================================================================

/**
 * List relationships originating from a specific source entity.
 */
export const listBySource = zQuery({
  args: {
    source_type: z.enum(['project', 'decision', 'task']),
    source_id: z.union([z.string(), z.null()]),
    paginationOpts: PaginationSchema.optional(),
  },
  handler: async (ctx, args) => {
    const { source_type, source_id, paginationOpts } = args;
    if (!source_id) {
      return { page: [], isDone: true, continueCursor: null };
    }

    // Cast the source_id based on the source_type for type safety
    let typed_source_id: Id<'projects'> | Id<'decisions'> | Id<'tasks'>;
    switch (source_type) {
      case 'project':
        typed_source_id = source_id as Id<'projects'>;
        break;
      case 'decision':
        typed_source_id = source_id as Id<'decisions'>;
        break;
      case 'task':
        typed_source_id = source_id as Id<'tasks'>;
        break;
      default:
        throw new Error(`Invalid source_type: ${source_type}`);
    }

    // Build the query in a single chain
    const results = await ctx.db
      .query('entity_relationships')
      .withIndex('by_source', (q) => 
        q.eq('entity_source_type', source_type)
      )
      .filter((q) => 
        q.eq(q.field('entity_source_id'), typed_source_id)
      )
      .order(paginationOpts?.sortDirection ?? 'desc') // Use sortDirection if provided
      .paginate({
        cursor: paginationOpts?.cursor ?? null,
        numItems: paginationOpts?.numItems ?? 10
      });

    return results;
  },
});


/**
 * List relationships targeting a specific entity.
 */
export const listByTarget = zQuery({
  args: {
    target_type: z.enum(['project', 'decision', 'task']),
    target_id: z.union([z.string(), z.null()]),
    paginationOpts: PaginationSchema.optional(),
  },
  handler: async (ctx, args) => {
    const { target_type, target_id, paginationOpts } = args;
    if (!target_id) {
      return { page: [], isDone: true, continueCursor: null };
    }

    let typed_target_id: Id<'projects'> | Id<'decisions'> | Id<'tasks'>;
    switch (target_type) {
      case 'project':
        typed_target_id = target_id as Id<'projects'>;
        break;
      case 'decision':
        typed_target_id = target_id as Id<'decisions'>;
        break;
      case 'task':
        typed_target_id = target_id as Id<'tasks'>;
        break;
      default:
        throw new Error(`Invalid target_type: ${target_type}`);
    }

    // Build the query in a single chain
    const results = await ctx.db
      .query('entity_relationships')
      .withIndex('by_target', (q) => 
        q.eq('entity_target_type', target_type)
      )
      .filter((q) => 
        q.eq(q.field('entity_target_id'), typed_target_id)
      )
      .order(paginationOpts?.sortDirection ?? 'desc') // Use sortDirection if provided
      .paginate({
        cursor: paginationOpts?.cursor ?? null,
        numItems: paginationOpts?.numItems ?? 10
      });

    return results;
  },
});

/**
 * List relationships by their type.
 */
export const listByType = zQuery({
  args: {
    relationship_type: z.string().optional(),
    paginationOpts: PaginationSchema.optional(),
  },
  handler: async (ctx, args) => {
    const { relationship_type, paginationOpts } = args;

    let finalQuery;

    // Apply index and default sorting logic
    if (relationship_type) {
      // If filtering by type, use the index. Sorting might need to be handled client-side
      // or via a more complex query/composite index if sorting by a non-indexed field.
      // For now, we rely on the index's inherent order or default Convex order.
      // TODO: Add dynamic sorting if required and feasible with the index.
       finalQuery = ctx.db
        .query('entity_relationships')
        .withIndex('by_relationship_type', (q) => 
          q.eq('entity_relationship_type', relationship_type)
        );
    } else {
      // If not filtering by type, apply sorting based on paginationOpts
      // TODO: Implement dynamic sorting based on paginationOpts.sortBy (if schema is extended)
      const sortDirection = paginationOpts?.sortDirection ?? 'desc'; // Use sortDirection if provided
      finalQuery = ctx.db.query('entity_relationships').order(sortDirection); // Apply sort direction
    }

    // Apply pagination to the final query object
    const results = await finalQuery.paginate({
      cursor: paginationOpts?.cursor ?? null,
      numItems: paginationOpts?.numItems ?? 10,
    });

    return results;
  },
});

/**
 * Get a single entity relationship by its ID.
 */
export const getById = query({
  args: { id: v.id('entity_relationships') },
  handler: async (ctx, args) => {
    const relationship = await ctx.db.get(args.id);
    if (!relationship) {
      throw new Error(`Relationship with ID ${args.id} not found`);
    }
    return relationship; // Return raw doc, Zod validation happens on client if needed
  },
});

/**
 * Check if a relationship exists between a specific source and target.
 */
export const checkExistence = zQuery({
  args: {
    source_type: z.enum(['project', 'decision', 'task']),
    source_id: z.string(),
    target_type: z.enum(['project', 'decision', 'task']),
    target_id: z.string(),
  },
  output: z.boolean(),
  handler: async (ctx, args) => {
    const { source_type, source_id, target_type, target_id } = args;

    // Cast IDs for type safety
    const typed_source_id = source_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>;
    const typed_target_id = target_id as Id<'projects'> | Id<'decisions'> | Id<'tasks'>;

    // Build the query in a single chain
    const existingRelationship = await ctx.db
      .query('entity_relationships')
      .withIndex('by_source', (q) => 
        q.eq('entity_source_type', source_type)
      )
      .filter((q) => 
        q.and(
          q.eq(q.field('entity_source_id'), typed_source_id),
          q.eq(q.field('entity_target_type'), target_type),
          q.eq(q.field('entity_target_id'), typed_target_id)
        )
      )
      .first();

    return !!existingRelationship;
  },
});

/**
 * Fetches details (id, name, type) of all entities related to a given entity.
 * It fetches both relationships where the entity is the source and where it's the target.
 */
export const getRelatedEntityDetails = query({
  args: {
    entityId: v.string(), // Use string initially, cast later
    entityType: v.union(
      v.literal('project'),
      v.literal('decision'),
      v.literal('task')
      // Add other entity types here if they become relatable
    ),
  },
  handler: async (ctx, args) => {
    const { entityId, entityType } = args;

    // Cast the primary entity ID based on its type
    let typedEntityId: Id<'projects'> | Id<'decisions'> | Id<'tasks'>;
    switch (entityType) {
      case 'project': typedEntityId = entityId as Id<'projects'>; break;
      case 'decision': typedEntityId = entityId as Id<'decisions'>; break;
      case 'task': typedEntityId = entityId as Id<'tasks'>; break;
      default: throw new Error(`Invalid entityType: ${entityType}`);
    }

    // 1. Fetch relationships where the entity is the source
    const sourceRels = await ctx.db
      .query('entity_relationships')
      .withIndex('by_source', (q) =>
        q.eq('entity_source_type', entityType)
      )
      .filter(q => 
        q.eq(q.field('entity_source_id'), typedEntityId)
      )
      .collect();

    // 2. Fetch relationships where the entity is the target
    const targetRels = await ctx.db
      .query('entity_relationships')
      .withIndex('by_target', (q) =>
        q.eq('entity_target_type', entityType)
      )
      .filter(q => 
        q.eq(q.field('entity_target_id'), typedEntityId)
      )
      .collect();

    // 3. Collect unique IDs and types of related entities
    const relatedEntityRefs = new Map<string, { id: Id<any>, type: string }>();

    sourceRels.forEach(rel => {
      const key = `${rel.entity_target_type}-${rel.entity_target_id}`;
      if (!relatedEntityRefs.has(key)) {
        relatedEntityRefs.set(key, { id: rel.entity_target_id, type: rel.entity_target_type });
      }
    });

    targetRels.forEach(rel => {
      const key = `${rel.entity_source_type}-${rel.entity_source_id}`;
      if (!relatedEntityRefs.has(key)) {
        relatedEntityRefs.set(key, { id: rel.entity_source_id, type: rel.entity_source_type });
      }
    });

    // 4. Fetch details for each related entity
    const relatedEntityDetails = await Promise.all(
      Array.from(relatedEntityRefs.values()).map(async (ref) => {
        try {
          // Use ctx.db.get which is efficient for fetching by ID
          const doc = await ctx.db.get(ref.id);
          if (!doc) return null; // Handle case where related doc might be deleted

          // Extract name - adjust field name if necessary for different types
          const name = (doc as any).name ?? `Unnamed ${ref.type}`;

          return {
            _id: ref.id,
            name: name,
            type: ref.type,
          };
        } catch (error) {
          console.error(`Error fetching details for ${ref.type} ${ref.id}:`, error);
          return null; // Skip if fetching fails
        }
      })
    );

    // 5. Filter out nulls and ensure correct type
    return relatedEntityDetails.filter(details => details !== null).map(details => ({
      ...details!,
      type: details!.type as 'project' | 'decision' | 'task' // Cast to known relatable types for now
      // If organizations/people become relatable, update this cast and RelatedItemType
    })) as { _id: Id<any>, name: string, type: 'project' | 'decision' | 'task' }[];
     // Note: This return type is slightly more specific than RelatedItemType used in frontend,
     // but ensures type safety based on current implementation. Frontend might need adjustment
     // if it strictly expects 'organization' or 'person' types from this query.
  },
});
