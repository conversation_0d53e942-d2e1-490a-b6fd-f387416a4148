import { z } from "zod";
import { query } from "./_generated/server";
import { v } from "convex/values";
import { TeamOutputSchema } from "../zod/teams-schema"; // Import Zod schema
import { Id, Doc } from "./_generated/dataModel"; // Import Doc
import { zid } from 'convex-helpers/server/zod'; // Import zid
import Fuse from 'fuse.js';

/**
 * List all teams with pagination and filtering options
 */
export const listTeams = query({
  args: {
    pagination: v.optional(v.object({ 
        limit: v.optional(v.number()),
        cursor: v.optional(v.string()), 
        // Removed sortBy/sortDirection from args for simplicity for now
    })),
    filter: v.optional(v.object({
        searchText: v.optional(v.string()),
        slug: v.optional(v.string()),
    }))
  },
  handler: async (ctx, args) => {
    const { pagination = {}, filter = {} } = args;
    const {
      limit = 50, // Default limit
      cursor,
    } = pagination;
    const { searchText, slug } = filter;

    let teams: Doc<'teams'>[] = [];
    let continueCursor: string | null = null;

    // Fetch based on slug index if provided
    if (slug) {
        const results = await ctx.db.query('teams')
            .withIndex('by_slug', q => q.eq('slug', slug))
            .paginate({ cursor: cursor ?? null, numItems: limit });
        teams = results.page;
        continueCursor = results.continueCursor;
    } 
    // Otherwise, fetch all (up to the limit) for potential searching/default view
    else {
         const results = await ctx.db.query('teams')
            .order("desc") // Default order by creation time
            .paginate({ cursor: cursor ?? null, numItems: limit });
         teams = results.page;
         continueCursor = results.continueCursor;
    }

    // Apply text search in memory if needed (after fetching)
    if (searchText && searchText.trim() !== '') {
      const fuse = new Fuse(teams, {
        keys: ['name', 'description'], // Fields to search
        threshold: 0.3 // Adjust sensitivity
      });
      teams = fuse.search(searchText).map(result => result.item);
      // Note: In-memory search breaks pagination cursor logic if applied after fetch
      continueCursor = null; // Reset cursor if search is applied
    }

    // Validate results with Zod schema
    const validatedTeams = teams
      .map(team => {
        try {
          // Ensure system fields are included for validation if needed by schema
          return TeamOutputSchema.parse({
            ...team,
            _id: team._id.toString(), // Ensure ID is string for Zod
            _creationTime: team._creationTime,
          });
        } catch (error) {
          console.error(`Failed to validate team ${team._id}:`, error);
          return null;
        }
      })
      .filter((team): team is z.infer<typeof TeamOutputSchema> => team !== null);

    return {
      teams: validatedTeams,
      continueCursor: continueCursor // Return potentially nullified cursor
    };
  },
});

// Add a basic search query if needed, similar to listTeams but focused on search
export const searchTeams = query({
    args: { search: v.optional(v.string()) },
    handler: async (ctx, args) => {
        if (!args.search) {
            // Optionally return recent/all teams if search is empty
            return await ctx.db.query("teams").order("desc").take(10);
        }
        // Basic text search (consider Convex search index for performance)
        const teams = await ctx.db.query("teams").collect();
        const fuse = new Fuse(teams, { keys: ["name"], threshold: 0.3 });
        // Return full Doc object
        return fuse.search(args.search).map(result => result.item).slice(0, 10); 
    },
});


/**
 * Retrieves a single team by its ID
 */
export const getTeam = query({
  args: {
    // Use Convex validator v.id within v.object
    id: v.id('teams') 
  },
  // Output type is inferred, but handler must return correctly
  handler: async (ctx, args): Promise<Doc<'teams'> | null> => {
    // Fetch the document using the validated ID
    const team = await ctx.db.get(args.id);
    return team; // Return the document or null
  },
});

/**
 * Check if a team exists by ID without throwing an error
 */
export const teamExists = query({
  args: { id: v.id("teams") },
  handler: async (ctx, args) => {
    try {
      const team = await ctx.db.get(args.id);
      return !!team; // Return true if team exists, false otherwise
    } catch (error) {
      console.log(`Team ID check failed for ${args.id}:`, error);
      return false;
    }
  },
});
