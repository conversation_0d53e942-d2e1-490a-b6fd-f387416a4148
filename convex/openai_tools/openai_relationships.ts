import { mutation, MutationCtx } from '../_generated/server';
import { v } from 'convex/values';
import { Id } from '../_generated/dataModel';
import { api } from '../_generated/api';

/**
 * ==========================================
 * OpenAI Tool Mutations for Relationships
 * ==========================================
 * This file contains mutations specifically designed to be called by AI agents
 * using predefined OpenAI tool schemas for managing entity relationships.
 */

// Define the return type for the mutation
type LinkResult = {
  success: boolean;
  relationshipId: Id<"entity_relationships">;
};

/**
 * 🤖 Creates a link between a project and another item (task or decision) based on AI tool input.
 * Calls the existing 'create' function in convex/entity_relationships.ts.
 */
export const openai_linkItemToProject = mutation({
  args: {
    projectId: v.id("projects"),
    relatedItemId: v.string(), // Accept string initially, cast based on type
    relatedItemType: v.union(v.literal("task"), v.literal("decision")), // Only allow task/decision for now
    relationshipType: v.optional(v.string()) // Optional relationship description
  },
  handler: async (ctx: MutationCtx, args): Promise<LinkResult> => {
    const { projectId, relatedItemId, relatedItemType, relationshipType } = args;

    // Determine the target ID type based on relatedItemType
    let typedRelatedItemId: Id<"tasks"> | Id<"decisions">;
    if (relatedItemType === "task") {
      typedRelatedItemId = relatedItemId as Id<"tasks">;
    } else if (relatedItemType === "decision") {
      typedRelatedItemId = relatedItemId as Id<"decisions">;
    } else {
      // Should not happen due to validator, but good practice to handle
      throw new Error(`Unsupported relatedItemType: ${relatedItemType}`);
    }

    // Prepare data for the createRelationship function
    const relationshipData = {
      entity_source_type: "project" as const, // Source is always project for this tool
      entity_source_id: projectId,
      entity_target_type: relatedItemType, // Target type from args
      entity_target_id: typedRelatedItemId, // Casted target ID
      entity_relationship_type: relationshipType ?? "related" // Use provided type or default
    };

    try {
      // Call the existing create function using Convex's internal API pattern
      const relationshipId: Id<"entity_relationships"> = await ctx.runMutation(api.entity_relationships.create, { 
        data: relationshipData 
      });
      
      console.log(`Relationship created: ${relationshipId} between project ${projectId} and ${relatedItemType} ${relatedItemId}`);
      return { success: true, relationshipId: relationshipId };
    } catch (error) {
      console.error("Error creating relationship:", error);
      // Consider more specific error handling or re-throwing
      throw new Error(`Failed to create relationship: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
});
