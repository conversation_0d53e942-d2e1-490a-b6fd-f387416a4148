import { query } from '../_generated/server';
import { v } from 'convex/values';
import { api } from '../_generated/api';
import { DirectorySearchResultItem } from '../../zod/directory-schema';

/**
 * ==========================================
 * OpenAI Tool Queries for Directory
 * ==========================================
 * This file contains queries specifically designed to be called by AI agents
 * using predefined OpenAI tool schemas for directory operations.
 */

/**
 * 🤖 Adapter query to search for people and teams based on input from an AI tool call.
 * This function accepts the structure from the 'directory_search_people_and_teams'
 * OpenAI tool schema and calls the 'searchPeopleAndTeams' query from convex/directory/directory.ts.
 * 
 * Supports searching for:
 * - Users (people with user_id)
 * - Non-user people (people without user_id, for Requestor role)
 * - Teams
 */
export const searchPeopleAndTeamsFromAITool = query({
  args: {
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
    includeNonUsers: v.optional(v.boolean()),
    type: v.optional(v.union(v.literal('all'), v.literal('user'), v.literal('person'), v.literal('team')))
  },
  handler: async (ctx, args): Promise<DirectorySearchResultItem[]> => {
    const results = await ctx.runQuery(api.directory.directory.searchPeopleAndTeams, {
      search: args.search,
      limit: args.limit,
      includeNonUsers: args.includeNonUsers ?? true, // Default to including non-user people
      type: args.type ?? 'all' // Default to all types
    });
    
    // Ensure the results match the expected type
    return results as DirectorySearchResultItem[];
  }
});
