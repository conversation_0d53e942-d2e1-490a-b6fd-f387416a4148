import { mutation, MutationCtx } from '../_generated/server';
import { v } from 'convex/values';
import { api } from '../_generated/api';
import { Id, Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';

/**
 * ==========================================
 * OpenAI Tool Mutations for Projects
 * ==========================================
 * This file contains mutations specifically designed to be called by AI agents
 * using predefined OpenAI tool schemas. They often act as adapters,
 * translating the tool's input structure to call existing internal functions.
 */

/**
 * 🤖 Adapter mutation to update a project based on input from an AI tool call.
 * This function accepts the specific nested structure from the 'update_project'
 * OpenAI tool schema and calls the database directly.
 */
export const updateProjectFromAITool = mutation({
  args: {
    id: v.id("projects"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    short_description: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("completed"),
      v.literal("in_progress"),
      v.literal("perpetual"),
      v.literal("not_started"),
      v.literal("paused"),
      v.literal("cancelled")
    )),
    priority: v.optional(v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    )),
    driver: v.optional(v.id("users")), // Only accept user IDs
    contributors: v.optional(v.array(v.union(v.id("users"), v.id("teams")))),
    informed: v.optional(v.array(v.union(v.id("users"), v.id("teams"))))
  },
  handler: async (ctx, args) => {
    // Extract id from args
    const { id, ...updates } = args;

    // Validate that the project exists
    const project = await ctx.db.get(id);
    if (!project) {
      throw new ConvexError({ message: `Project ${id} not found` });
    }

    // Update the project with the processed updates
    return await ctx.db.patch(id, updates);
  }
});

/**
 * 🤖 Assigns DCI roles (Driver, Contributors, Informed) to a project based on AI tool input.
 * Accepts User IDs only for simplicity. Replaces existing values for provided roles.
 */
export const openai_assignProjectDCI = mutation({
  args: {
    projectId: v.id("projects"),
    driverId: v.optional(v.id("users")),
    // Accept arrays of user IDs. Team IDs could be added later if needed.
    contributorUserIds: v.optional(v.array(v.id("users"))),
    informedUserIds: v.optional(v.array(v.id("users")))
  },
  handler: async (ctx: MutationCtx, args) => {
    const { projectId, driverId, contributorUserIds, informedUserIds } = args;

    // Fetch the project to ensure it exists
    const project = await ctx.db.get(projectId);
    if (!project) {
      throw new Error(`Project with ID ${projectId} not found.`);
    }

    // Prepare the patch data, only including fields that were actually provided
    const patchData: Partial<Doc<"projects">> = {}; // Use Partial for selective updates

    if (driverId !== undefined) {
      patchData.driver = driverId; // Assign if provided
    }
    if (contributorUserIds !== undefined) {
      patchData.contributors = contributorUserIds;
    }
    if (informedUserIds !== undefined) {
      patchData.informed = informedUserIds;
    }

    // Only patch if there's something to update
    if (Object.keys(patchData).length > 0) {
      await ctx.db.patch(projectId, patchData);
      console.log(`DCI roles updated for project ${projectId}`);
      return { success: true, projectId: projectId };
    } else {
      console.log(`No DCI roles provided to update for project ${projectId}`);
      return { success: false, message: "No DCI roles provided to update.", projectId: projectId };
    }
  }
});
