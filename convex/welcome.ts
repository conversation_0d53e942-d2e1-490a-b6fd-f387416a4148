import { z } from "zod";
import { zMutation } from "./functions";
import { Id } from "./_generated/dataModel";

/**
 * Updates or creates a document checklist entry for a user
 */
export const updateUserDocumentChecklist = zMutation({
  args: {
    documentDefinitionId: z.string(),
    userId: z.string(),
    status: z.string(),
  },
  handler: async (ctx, args) => {
    const { documentDefinitionId, userId, status } = args;

    // Check if an entry already exists
    const existingEntry = await ctx.db
      .query("user_document_checklist")
      .filter(q => q.and(
        q.eq(q.field("document_definition_id"), documentDefinitionId as Id<"document_definitions">),
        q.eq(q.field("user_id"), userId as Id<"users">)
      ))
      .first();

    if (existingEntry) {
      // Update existing entry
      return await ctx.db.patch(existingEntry._id, {
        status: status as string,
        updated_at: Date.now()
      });
    } else {
      // Create new entry
      return await ctx.db.insert("user_document_checklist", {
        document_definition_id: documentDefinitionId as Id<"document_definitions">,
        user_id: userId as Id<"users">,
        status: status as string,
        updated_at: Date.now()
      });
    }
  }
}); 