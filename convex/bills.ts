import {
  query,
  mutation,
  internalMutation,
  MutationCtx,
  QueryCtx
} from './_generated/server';
import { Id } from './_generated/dataModel';
import { zCustomMutation, zCustomQuery, zid } from 'convex-helpers/server/zod';
import { NoOp } from 'convex-helpers/server/customFunctions';
import {
  BillInputSchema,
  BillQueryFiltersSchema,
  VendorBillsQuerySchema,
  VendorTotalQuerySchema,
  BulkBillUpdateSchema,
  StoreBillDocumentSchema,
  UnifiedBillQuerySchema,
  DeleteBillsSchema,
  BulkLineItemSchema,
  BillsListResponseSchema,
  BillUpdateSchema,
  BillSchema,
  BillStatus
} from '../zod/bills-schema';
import { z } from 'zod';

// Create custom query and mutation functions with Zod validation
const zQuery = zCustomQuery(query, NoOp);
const zMutation = zCustomMutation(mutation, NoOp);
const zInternalMutation = zCustomMutation(internalMutation, NoOp);

// Get a single bill by ID - using Zod validation
export const get = zQuery({
  args: { id: zid('bills') },
  output: BillSchema,
  handler: async (ctx, args) => {
    const bill = await ctx.db.get(args.id);
    if (!bill) {
      throw new Error('Bill not found');
    }
    // Validate the bill against the schema before returning
    return BillSchema.parse(bill);
  }
});

// Helper function to insert a bill with updated timestamp
// This consolidates the common bill creation logic
async function insertBill(
  ctx: MutationCtx,
  billData: z.infer<typeof BillInputSchema>
) {
  return await ctx.db.insert('bills', {
    ...billData,
    updated_at: billData.updated_at || Date.now()
  });
}

/**
 * Create a bill - can be used by both internal and external callers
 *
 * This single function replaces the previous separate internal and public mutations.
 * It uses BillInputSchema for validation which works for both internal and client use.
 */
export const createBill = zMutation({
  args: { bill: BillInputSchema },
  output: z.string(),
  handler: async (ctx, args) => {
    // Use the helper function for bill creation
    return await insertBill(ctx, args.bill);
  }
});

// Helper function to validate and process document storage
// This consolidates document handling logic
async function validateAndProcessDocument(
  ctx: MutationCtx,
  storageId: Id<'_storage'>
) {
  // Verify the storage ID exists
  const storage = await ctx.db.system.get(storageId);
  if (!storage) {
    throw new Error('Storage ID not found');
  }

  // Get the file URL - we can safely use storage.getUrl since we already verified the ID exists
  const url = await ctx.storage.getUrl(storageId);
  if (!url) {
    throw new Error('Failed to get file URL');
  }

  return url;
}
 

// Add a query to get document URL
export const getDocumentUrl = zQuery({
  args: { billId: zid('bills') },
  output: z.string().nullable(),
  handler: async (ctx, args) => {
    // Find the file record for this bill
    const file = await ctx.db
      .query('files')
      .withIndex('by_docType', q => q.eq('docType', 'BILL'))
      .filter(q => q.eq(q.field('parentEntityId'), args.billId))
      .first();

    if (!file?.fileStorageId) {
      return null;
    }
    
    // Verify the storage ID exists in the system table
    const storageExists = await ctx.db.system.get(file.fileStorageId);
    if (!storageExists) {
      return null; // Return null instead of throwing error for query
    }

    return await ctx.storage.getUrl(file.fileStorageId);
  }
});

// Helper function to update a single bill
// This consolidates the single bill update logic
async function updateSingleBill(
  ctx: MutationCtx,
  billId: Id<'bills'>,
  updateFields: z.infer<typeof BillUpdateSchema>
): Promise<Id<'bills'>> {
  const bill = await ctx.db.get(billId);
  if (!bill) {
    throw new Error(`Bill with ID ${billId} not found`);
  }

  await ctx.db.patch(billId, {
    ...updateFields,
    updated_at: Date.now()
  });

  return billId;
}

// Helper function to update multiple bills with the same fields
// This consolidates the bulk bill update logic
async function updateMultipleBills(
  ctx: MutationCtx,
  billIds: Id<'bills'>[],
  updateFields: z.infer<typeof BillUpdateSchema>
): Promise<Id<'bills'>[]> {
  // Use Promise.allSettled to update bills concurrently
  const results = await Promise.allSettled(
    billIds.map(id => updateSingleBill(ctx, id, updateFields))
  );
  
  // Filter successful updates and collect their IDs
  const updatedIds: Id<'bills'>[] = [];
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      updatedIds.push(billIds[index]);
    } else {
      console.error(`Failed to update bill ${billIds[index]}:`, result.reason);
    }
  });
  
  return updatedIds;
}

// Update bills - handles both single bill updates and bulk updates
export const update = zMutation({
  args: { updates: BulkBillUpdateSchema },
  output: z.array(z.string()),
  handler: async (ctx, args) => {
    const { id, ids, updates } = args.updates;

    // Handle bulk update
    if (ids && ids.length > 0) {
      return await updateMultipleBills(ctx, ids, updates);
    }
    // Handle single bill update
    else if (id) {
      const updatedId = await updateSingleBill(ctx, id, updates);
      return [updatedId];
    } else {
      throw new Error(
        "Invalid arguments: must provide either 'id' or 'ids' with 'updates'"
      );
    }
  }
});

// Helper function to apply common filters to bill queries
// This consolidates the filtering logic used in multiple query functions
function applyBillFilters<
  // Using 'any' for Convex query objects which have complex types
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  T extends { filter: (filterFn: (q: any) => boolean) => T }
>(
  query: T,
  filters: {
    startDate?: number;
    endDate?: number;
    billStatus?: string | undefined;
    // No need to include type or vendor_id as they're handled by indexes
  }
): T {
  // Create a single chained expression without reassigning variables
  return query
    .filter((q) =>
      filters.startDate !== undefined
        ? q.gte(q.field('billDate'), filters.startDate)
        : true
    )
    .filter((q) =>
      filters.endDate !== undefined
        ? q.lte(q.field('billDate'), filters.endDate)
        : true
    )
    .filter((q) =>
      filters.billStatus !== undefined
        ? q.eq(q.field('billStatus'), filters.billStatus)
        : true
    );
}

// Helper function to apply filters to an array of bills
// This is used for in-memory filtering when we already have the bills
function filterBillsArray(
  bills: Array<z.infer<typeof BillSchema>>,
  filters: {
    startDate?: number;
    endDate?: number;
    billStatus?: string;
  }
): Array<z.infer<typeof BillSchema>> {
  return bills.filter((bill) => {
    if (filters.startDate !== undefined && bill.billDate < filters.startDate) {
      return false;
    }
    if (filters.endDate !== undefined && bill.billDate > filters.endDate) {
      return false;
    }
    if (
      filters.billStatus !== undefined &&
      bill.billStatus !== filters.billStatus
    ) {
      return false;
    }
    return true;
  });
}

/**
 * Builds a query for bills with the appropriate indexes based on filters and sorting
 * This consolidates the query building logic used in multiple functions
 */
function buildBillsQuery(
  ctx: QueryCtx,
  filters: z.infer<typeof UnifiedBillQuerySchema>
) {
  // Determine which index to use based on filters and sort options
  // Priority: 1) Type filtering 2) Vendor filtering 3) Status filtering 4) Field-based sorting 5) Default index

  if (filters.type !== undefined) {
    // Type is already validated by the schema to be a valid BillType
    const billType = filters.type; // Type assertion to handle undefined
    return ctx.db
      .query('bills')
      .withIndex('by_type', (q) => q.eq('type', billType));
  }

  if (filters.vendor_id !== undefined) {
    // vendor_id is already validated by the schema to be a valid Id
    const vendorId = filters.vendor_id; // Type assertion to handle undefined
    return ctx.db
      .query('bills')
      .withIndex('by_vendor', (q) => q.eq('vendor_id', vendorId));
  }

  if (filters.billStatus !== undefined) {
    // Use the status index when filtering by status
    return ctx.db
      .query('bills')
      .withIndex('by_status', (q) => q.eq('billStatus', filters.billStatus));
  }

  if (filters.sortBy === 'billDate') {
    // Use date index when sorting by billDate
    return ctx.db.query('bills').withIndex('by_date');
  }

  if (filters.sortBy === 'dueDate') {
    // Use the new due date index when sorting by dueDate
    return ctx.db.query('bills').withIndex('by_due_date');
  }

  if (filters.sortBy === 'amount') {
    // Use the new amount index when sorting by amount
    return ctx.db.query('bills').withIndex('by_amount');
  }

  if (filters.sortBy === 'vendor_id') {
    // Use vendor index when sorting by vendor
    return ctx.db.query('bills').withIndex('by_vendor');
  }

  if (filters.sortBy === 'type') {
    // Use type index when sorting by type
    return ctx.db.query('bills').withIndex('by_type');
  }

  // Default to date index for most cases
  return ctx.db.query('bills').withIndex('by_date');
}

/**
 * Unified function to list bills with filtering, sorting, and pagination
 *
 * This function provides a flexible API for querying bills with:
 * - Filtering by vendor, type, date range, and status
 * - Sorting by any field in ascending or descending order
 * - Pagination support
 */
export const listBills = zQuery({
  args: { filters: UnifiedBillQuerySchema },
  output: BillsListResponseSchema,
  handler: async (ctx, args) => {
    const filters = args.filters;

    // Build the query using the helper function which now selects the appropriate
    // index and applies filters
    const baseQuery = buildBillsQuery(ctx, filters);

    // Apply common filters
    const filteredQuery = applyBillFilters(baseQuery, {
      startDate: filters.startDate,
      endDate: filters.endDate,
      billStatus: filters.billStatus
    });

    // Extract and validate sort order (default to 'desc' if not provided)
    const sortOrder = filters.query === 'asc' ? 'asc' : 'desc';

    // Apply the sort order to the query
    const finalQuery = filteredQuery.order(sortOrder);

    try {
      // Return paginated results if pagination options are provided
      if (filters.paginationOpts) {
        const paginatedResult = await finalQuery.paginate({
          numItems: filters.paginationOpts.numItems,
          cursor: filters.paginationOpts.cursor ?? null
        });

        // Validate each bill in the page
        const validatedPage = await Promise.all(
          paginatedResult.page.map(async (bill) => {
            try {
              return BillSchema.parse(bill);
            } catch (error) {
              console.error('Failed to validate bill:', error);
              throw error;
            }
          })
        );

        // Return the paginated response with validated bills
        return BillsListResponseSchema.parse({
          page: validatedPage,
          continuationToken: paginatedResult.continueCursor,
          isDone: paginatedResult.isDone
        });
      }

      // For non-paginated results
      const bills = await finalQuery.collect();
      const validatedBills = await Promise.all(
        bills.map(async (bill) => {
          try {
            return BillSchema.parse(bill);
          } catch (error) {
            console.error('Failed to validate bill:', error);
            throw error;
          }
        })
      );

      // Return the array of validated bills
      return BillsListResponseSchema.parse(validatedBills);
    } catch (error) {
      console.error('Error in listBills:', error);
      throw new Error('Failed to process bills query');
    }
  }
});

// Helper function to delete a bill and its associated line items
// This consolidates the deletion logic used in both remove and bulkRemove
async function deleteBillAndLineItems(
  ctx: MutationCtx,
  billId: Id<'bills'>
): Promise<boolean> {
  const bill = await ctx.db.get(billId);
  if (!bill) {
    return false;
  }

  // Delete all associated line items
  const lineItems = await ctx.db
    .query('lineItems')
    .withIndex('by_bill', (q) => q.eq('bill_id', billId))
    .collect();

  // Use Promise.all to delete line items concurrently
  if (lineItems.length > 0) {
    try {
      await Promise.all(
        lineItems.map(item => ctx.db.delete(item._id))
      );
    } catch (error) {
      console.error(`Error deleting line items for bill ${billId}:`, error);
      // Continue with bill deletion even if some line items failed to delete
    }
  }

  // Delete the bill itself
  await ctx.db.delete(billId);
  return true;
}

/**
 * Unified function to delete bills (single or bulk)
 *
 * This function provides a flexible API for deleting bills:
 * - Can delete a single bill by providing 'id'
 * - Can delete multiple bills by providing 'ids'
 */
export const deleteBills = zMutation({
  args: { delete: DeleteBillsSchema },
  output: z.array(z.string()),
  handler: async (ctx, args) => {
    const deleteArgs = args.delete;

    // Handle single bill deletion
    if (deleteArgs.id) {
      const deleted = await deleteBillAndLineItems(ctx, deleteArgs.id);
      return deleted ? [deleteArgs.id] : [];
    }
    // Handle bulk bill deletion
    else if (deleteArgs.ids && deleteArgs.ids.length > 0) {
      // Use Promise.all to delete bills concurrently
      const results = await Promise.allSettled(
        deleteArgs.ids.map(id => deleteBillAndLineItems(ctx, id))
      );
      
      // Filter successful deletions and collect their IDs
      const deletedIds = deleteArgs.ids.filter((id, index) => 
        results[index].status === 'fulfilled' && (results[index] as PromiseFulfilledResult<boolean>).value
      );
      
      return deletedIds;
    } else {
      throw new Error("Invalid arguments: must provide either 'id' or 'ids'");
    }
  }
});

/**
 * Get total amount by vendor with improved filtering
 * This function uses the same query logic as listBills for consistency
 */
export const getTotalByVendor = zQuery({
  args: { query: VendorTotalQuerySchema },
  output: z.object({
    total: z.number(),
    count: z.number()
  }),
  handler: async (ctx, args) => {
    const vendorQuery = args.query;

    // Build the query using the same helper function as listBills
    const billsQuery = buildBillsQuery(ctx, {
      vendor_id: vendorQuery.vendor_id,
      startDate: vendorQuery.startDate,
      endDate: vendorQuery.endDate,
      billStatus: vendorQuery.billStatus,
      query: vendorQuery.sortOrder // Map sortOrder to query
    });

    // Apply common filters
    const filteredQuery = applyBillFilters(billsQuery, {
      startDate: vendorQuery.startDate,
      endDate: vendorQuery.endDate,
      billStatus: vendorQuery.billStatus
    });

    // Apply a consistent sort order
    // Ensure sortOrder is always 'asc' or 'desc' by using a default value
    const sortOrder = vendorQuery.sortOrder === 'asc' ? 'asc' : 'desc';

    try {
      // Create a properly chained query with sort order
      const bills = await filteredQuery.order(sortOrder).collect();

      // Validate each bill and calculate total
      const validatedBills = await Promise.all(
        bills.map(bill => BillSchema.parse(bill))
      );

      // Calculate the total amount from validated bills
      const total = validatedBills.reduce((sum, bill) => sum + bill.amount, 0);

      return {
        total,
        count: validatedBills.length
      };
    } catch (error) {
      console.error('Error in getTotalByVendor:', error);
      throw new Error('Failed to calculate vendor total');
    }
  }
});

// Create line items for a bill
export const createLineItems = zMutation({
  args: { lineItems: BulkLineItemSchema },
  output: z.array(z.string()),
  handler: async (ctx, args) => {
    const items = args.lineItems.items;
    
    // Use Promise.all to create line items concurrently
    try {
      const results = await Promise.all(
        items.map(item => 
          ctx.db.insert('lineItems', {
            ...item,
            spending_category: item.spending_category
          })
        )
      );
      return results;
    } catch (error) {
      console.error('Error creating line items:', error);
      throw new Error('Failed to create line items');
    }
  }
});

/**
 * Create bills from Bill.com data
 * 
 * This mutation handles bulk creation of bills from Bill.com API data.
 * It transforms the Bill.com format into our application's bill format.
 */
export const createBillsFromBillCom = zMutation({
  args: {
    billComBills: z.array(
      z.object({
        id: z.string(),
        vendorId: z.string(),
        vendorName: z.string().optional(),
        invoice: z.object({
          invoiceNumber: z.string().optional(),
          invoiceDate: z.string().optional(),
        }).optional(),
        dueDate: z.string().optional(),
        amount: z.number(),
        description: z.string().optional(),
        paymentStatus: z.string().optional(),
        archived: z.boolean().optional(),
        createdTime: z.string().optional(),
        updatedTime: z.string().optional(),
        // Bill.com API returns camelCase field names
        creditAmount: z.number().optional(),
        dueAmount: z.number().optional(),
        scheduledAmount: z.number().optional(),
        // Allow any other fields that might come from Bill.com
        classifications: z.any().optional(),
        billLineItems: z.array(
          z.object({
            id: z.string(),
            amount: z.number(),
            description: z.string().optional(),
            classifications: z.any().optional()
          })
        ).optional(),
      }).passthrough() // Allow additional fields from Bill.com
    ),
    organizationMap: z.record(z.string(), zid('organizations')),
  },
  output: z.object({
    createdCount: z.number(),
    updatedCount: z.number(),
    skippedCount: z.number(),
  }),
  handler: async (ctx, args): Promise<{ createdCount: number; updatedCount: number; skippedCount: number }> => {
    const { billComBills, organizationMap = {} } = args;
    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;

    // First, get all existing vendors to build a mapping
    const allVendors = await ctx.db
      .query('organizations')
      .filter((q) => q.eq(q.field('is_vendor'), true))
      .collect();

    // Create a map of Bill.com vendor IDs to organization records
    const vendorMap = new Map<string, typeof allVendors[number]>();
    const nameToVendorMap = new Map<string, typeof allVendors[number]>();

    // Build the maps
    for (const vendor of allVendors) {
      if (vendor.billComVendorId) {
        vendorMap.set(vendor.billComVendorId, vendor);
      }
      if (vendor.name) {
        nameToVendorMap.set(vendor.name.toLowerCase(), vendor);
      }
    }

    // Process each bill
    for (const billComBill of billComBills) {
      try {
        // 1. Find or create vendor organization
        let vendorOrg = billComBill.vendorId ? vendorMap.get(billComBill.vendorId) : null;

        // Try to find by name if not found by ID
        if (!vendorOrg && billComBill.vendorName) {
          vendorOrg = nameToVendorMap.get(billComBill.vendorName.toLowerCase());
          
          // If found by name, update with Bill.com vendor ID if needed
          if (vendorOrg && billComBill.vendorId && !vendorOrg.billComVendorId) {
            await ctx.db.patch(vendorOrg._id, {
              billComVendorId: billComBill.vendorId,
              updated_at: Date.now()
            });
            vendorOrg = { ...vendorOrg, billComVendorId: billComBill.vendorId };
            vendorMap.set(billComBill.vendorId, vendorOrg);
          }
        }
        
        // If still no organization is found, create a new one
        if (!vendorOrg && billComBill.vendorName) {
          try {
            const vendorName = billComBill.vendorName.trim() || `Vendor-${billComBill.vendorId?.substring(0, 8) || 'new'}`;
            const vendorId = await ctx.db.insert('organizations', {
              name: vendorName,
              is_vendor: true,
              billComVendorId: billComBill.vendorId,
              updated_at: Date.now()
            });
            
            vendorOrg = await ctx.db.get(vendorId);
            if (vendorOrg) {
              console.log(`Created new vendor organization: ${vendorName}`);
              if (billComBill.vendorId) {
                vendorMap.set(billComBill.vendorId, vendorOrg);
              }
              nameToVendorMap.set(vendorName.toLowerCase(), vendorOrg);
            }
          } catch (error) {
            console.error('Error creating vendor organization:', error);
          }
        }
        
        // If still no organization is found, create a new vendor organization
        if (!vendorOrg) {
          try {
            // Use the vendorName from Bill.com or a default name if not available
            const vendorName = billComBill.vendorName?.trim() || `Vendor-${billComBill.vendorId.substring(0, 8)}`;
            
            const newVendorId = await ctx.db.insert('organizations', {
              name: vendorName,
              is_vendor: true,
              billComVendorId: billComBill.vendorId,
              updated_at: Date.now()
            }) as Id<"organizations">;
            
            vendorOrg = await ctx.db.get(newVendorId);
            console.log(`Created new vendor organization: ${vendorName} (ID: ${newVendorId})`);
            
            if (!vendorOrg) {
              console.error(`Failed to retrieve newly created vendor organization ${newVendorId}`);
              skippedCount++;
              continue;
            }
          } catch (error) {
            console.error('Error creating vendor organization:', error);
            skippedCount++;
            continue;
          }
        }
        
        // If we still don't have a vendor organization, skip this bill
        if (!vendorOrg) {
          console.warn(`No vendor organization found or created for Bill.com vendor ID: ${billComBill.vendorId}`);
          skippedCount++;
          continue;
        }
        
        // At this point, TypeScript knows vendorOrg is defined
        const vendorId = vendorOrg._id as Id<'organizations'>;

        // Get invoice details
        const invoiceNumber = billComBill.invoice?.invoiceNumber;
        const invoiceDate = billComBill.invoice?.invoiceDate;

        // Check if this bill already exists by invoice number
        const existingBill = invoiceNumber 
          ? await ctx.db
              .query('bills')
              .withIndex('by_vendor', q => q.eq('vendor_id', vendorId))
              .filter(q => q.eq(q.field('billNo'), invoiceNumber))
              .first()
          : null;

        // Parse dates
        const billDate = invoiceDate 
          ? new Date(invoiceDate).getTime() 
          : Date.now();
        
        const dueDate = billComBill.dueDate 
          ? new Date(billComBill.dueDate).getTime() 
          : undefined;

        // Use Bill.com payment status directly
        let billStatus: BillStatus | undefined = undefined;
        if (billComBill.paymentStatus) {
          // Get the payment status from Bill.com and use it directly
          const paymentStatus = billComBill.paymentStatus.toUpperCase();
          
          // Map Bill.com statuses to our canonical status enum
          switch (paymentStatus) {
            case 'PAID':
              billStatus = 'PAID';
              break;
            case 'UNPAID':
              billStatus = 'UNPAID';
              break;
            case 'PARTIALLY_PAID':
              billStatus = 'PARTIALLY_PAID';
              break;
            case 'SCHEDULED':
              billStatus = 'SCHEDULED';
              break;
            case 'IN_PROCESS':
              billStatus = 'IN_PROCESS';
              break;
              default:
                // For any other status, mark as UNDEFINED
                billStatus = 'UNDEFINED';
                break;
            }

            console.log(`Using Bill.com status "${paymentStatus}" as "${billStatus}"`);
          }

        // Parse createdTime and updatedTime
        const billComCreatedTime = billComBill.createdTime 
          ? new Date(billComBill.createdTime).getTime() 
          : undefined;
        
        const billComUpdatedTime = billComBill.updatedTime 
          ? new Date(billComBill.updatedTime).getTime() 
          : undefined;

        if (existingBill) {
          // Update existing bill
          await ctx.db.patch(existingBill._id, {
            amount: billComBill.amount,
            billDate,
            dueDate,
            memo: billComBill.description,
            billStatus,
            billComId: billComBill.id,
            billComArchived: billComBill.archived,
            billComCreatedTime,
            billComUpdatedTime,
            updated_at: Date.now(),
          });
          
          // Process line items if they exist
          if (billComBill.billLineItems && billComBill.billLineItems.length > 0) {
            // Delete existing line items for this bill
            const existingLineItems = await ctx.db
              .query('lineItems')
              .withIndex('by_bill', q => q.eq('bill_id', existingBill._id))
              .collect();
              
            // Use Promise.all to delete line items concurrently
            if (existingLineItems.length > 0) {
              try {
                await Promise.all(
                  existingLineItems.map(item => ctx.db.delete(item._id))
                );
              } catch (error) {
                console.error(`Error deleting existing line items for bill ${existingBill._id}:`, error);
                // Continue with creating new line items even if some deletions failed
              }
            }
            
            // Create new line items concurrently
            try {
              await Promise.all(
                billComBill.billLineItems.map(lineItem => 
                  ctx.db.insert('lineItems', {
                    bill_id: existingBill._id,
                    amount: lineItem.amount,
                    billComLineItemId: lineItem.id,
                    description: lineItem.description || 'No description',
                    post_date: billDate, // Use bill date as default
                    merchant_name: vendorOrg.name, // Always use the vendor organization name
                    vendor_id: vendorId,
                    updated_at: Date.now()
                  })
                )
              );
            } catch (error) {
              console.error(`Error creating line items for bill ${existingBill._id}:`, error);
              // Continue with the next bill even if line item creation failed
            }
          }
          
          updatedCount++;
        } else {
          // Create new bill
          const billId = await insertBill(ctx, {
            type: 'BILL',
            billNo: invoiceNumber || `BILL-${Date.now()}`,
            billDate,
            dueDate,
            vendor_id: vendorId,
            amount: billComBill.amount,
            memo: billComBill.description,
            billStatus,
            billComId: billComBill.id,
            billComArchived: billComBill.archived,
            billComCreatedTime,
            billComUpdatedTime,
            updated_at: Date.now(),
          });
          
          // Process line items if they exist
          if (billComBill.billLineItems && billComBill.billLineItems.length > 0) {
            // Create new line items concurrently
            try {
              await Promise.all(
                billComBill.billLineItems.map(lineItem => 
                  ctx.db.insert('lineItems', {
                    bill_id: billId,
                    amount: lineItem.amount,
                    billComLineItemId: lineItem.id,
                    description: lineItem.description || 'No description',
                    post_date: billDate, // Use bill date as default
                    merchant_name: vendorOrg.name, // Always use the vendor organization name
                    vendor_id: vendorId,
                    updated_at: Date.now()
                  })
                )
              );
            } catch (error) {
              console.error(`Error creating line items for new bill ${billId}:`, error);
              // Continue with the next bill even if line item creation failed
            }
          }
          
          createdCount++;
        }
      } catch (error) {
        console.error('Error processing Bill.com bill:', error, billComBill);
        skippedCount++;
      }
    }

    return {
      createdCount,
      updatedCount,
      skippedCount,
    };
  }
});
