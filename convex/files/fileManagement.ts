import { zMutation, zQuery } from '../functions';
import { z } from "zod";
import { 
  GenerateUploadUrlSchema,
  StoreDocumentSchema,
  GetFileUrlSchema,
  DeleteFileSchema,
  AttachmentSchema,
  ContractSchema,
  FileSchema,
  ALLOWED_TYPES,
  MAX_FILE_SIZE
} from '../../zod/file-management-schema';
import { Id } from '../_generated/dataModel';
import { zid } from 'convex-helpers/server/zod';
import { StorageReader } from 'convex/server';

/**
 * Validate that a storage ID exists in the system
 * 
 * This query checks if a given storage ID exists in the _storage system table.
 * It's used by other functions to validate storage IDs before accessing them.
 */
export const validateStorageExists = zQuery({
  args: {
    storageId: zid("_storage")
  },
  handler: async (ctx, args) => {
    const { storageId } = args;
    
    // Check if the storage exists in the system table
    const storageExists = await ctx.db.system.get(storageId);
    return storageExists !== null;
  }
});

/**
 * A mutation that generates a short-lived URL for uploading a file to Convex.
 * This URL is valid for 1 hour and can be used to upload a file directly from the client.
 */
export const generateUploadUrl = zMutation({
  args: { contentType: z.string().optional() },
  output: z.string(),
  handler: async (ctx, args) => {
    // Generate a one-time upload URL
    const uploadUrl = await ctx.storage.generateUploadUrl();
    return uploadUrl;
  }
});

/**
 * A mutation to store a document after it has been uploaded.
 * This creates a record in the files table and then extends it in the appropriate subtable.
 * 
 * This is the unified file upload function that handles all document types and creates
 * records in the appropriate subtables.
 */
export const storeDocument = zMutation({
  args: {
    storageId: z.string(),
    name: z.string(),
    type: z.enum(["DOCUMENT", "CONTRACT", "KNOWLEDGE_BASE", "MEETING_NOTES", "BILL"]),
    size: z.number(),
    category: z.string().optional(),
    parentEntityId: z.union([
      zid('tasks'),
      zid('projects'),
      zid('decisions'),
      zid('organizations'),
      zid('users'),
      zid('people'),
      zid('bills')
    ]).optional()
  },
  handler: async (ctx, args) => {
    const { storageId, name, type, size, category, parentEntityId } = args;

    // Get the file metadata from storage
    const fileMetadata = await ctx.db.system.get(storageId as Id<"_storage">);
    if (!fileMetadata) {
      throw new Error('File not found in storage');
    }

    // Check if the fileMetadata is from _storage (not _scheduled_functions)
    if (!('sha256' in fileMetadata)) {
      throw new Error('Invalid file metadata');
    }

    // Extract file extension from name
    const fileExtension = name.split('.').pop() || '';

    // First, create the base file record
    const fileId = await ctx.db.insert('files', {
      title: name,
      docType: type,
      fileFilename: name,
      fileStorageId: storageId as Id<"_storage">,
      fileSize: size,
      fileExtension,
      parentEntityId,
      uploadStatus: 'completed',
      updated_at: Date.now()
    });

    // Then extend it in the appropriate subtable
    switch (type) {
      case 'DOCUMENT':
        await ctx.db.insert('documents', {
          name,
          fileId: fileId,
          category,
          currentVersionNumber: 1,
          updated_at: Date.now()
        });
        break;

      case 'CONTRACT':
        await ctx.db.insert('contracts', {
          fileId: fileId,
          category,
          contentType: fileMetadata.contentType,
          currentVersionNumber: 1,
          updated_at: Date.now()
        });
        break;

      case 'KNOWLEDGE_BASE':
        await ctx.db.insert('knowledge_base', {
          fileId: fileId,
          category,
          updated_at: Date.now()
        });
        break;

      case 'MEETING_NOTES':
        await ctx.db.insert('meeting_notes', {
          fileId: fileId,
          category,
          updated_at: Date.now()
        });
        break;
        
      case 'BILL':
        // No additional record needed for BILL type as it's handled directly in the files table
        // The parentEntityId should be set to the billId if this is a bill document
        break;
    }

    return { fileId };
  }
});

/**
 * A mutation to get a temporary URL for a file.
 * This URL can be used to download or view the file.
 */
export const getFileUrl = zMutation({
  args: { storageId: z.string() },
  handler: async (ctx, args) => {
    // Cast the storageId to the correct type
    const storageId = args.storageId as Id<"_storage">;
    
    // Verify the storage ID exists in the system table
    const storageExists = await ctx.db.system.get(storageId);
    if (!storageExists) {
      throw new Error("Storage file not found in system");
    }
    
    // Generate a temporary URL for the file using the storageId
    const url = await ctx.storage.getUrl(storageId);
    return url;
  }
});

/**
 * A mutation to delete a file from storage and remove its references from the database.
 */
export const deleteFile = zMutation({
  args: {
    fileId: z.string()
  },
  output: z.boolean(),
  handler: async (ctx, args) => {
    try {
      // Get the file record to determine its type
      const fileRecord = await ctx.db.get(args.fileId as Id<"files">);
      if (!fileRecord) {
        throw new Error('File record not found');
      }

      // Delete from storage if storageId exists
      if (fileRecord.fileStorageId) {
        await ctx.storage.delete(fileRecord.fileStorageId);
      }

      // Delete from the appropriate subtable based on docType
      switch (fileRecord.docType) {
        case 'DOCUMENT': {
          const docRecords = await ctx.db
            .query('documents')
            .withIndex('by_file', q => q.eq('fileId', args.fileId as Id<"files">))
            .collect();
          for (const doc of docRecords) {
            await ctx.db.delete(doc._id);
          }
          break;
        }

        case 'CONTRACT': {
          const contractRecords = await ctx.db
            .query('contracts')
            .withIndex('by_file', q => q.eq('fileId', args.fileId as Id<"files">))
            .collect();
          for (const doc of contractRecords) {
            await ctx.db.delete(doc._id);
          }
          break;
        }

        case 'KNOWLEDGE_BASE': {
          const kbRecords = await ctx.db
            .query('knowledge_base')
            .withIndex('by_file', q => q.eq('fileId', args.fileId as Id<"files">))
            .collect();
          for (const doc of kbRecords) {
            await ctx.db.delete(doc._id);
          }
          break;
        }

        case 'MEETING_NOTES': {
          const mnRecords = await ctx.db
            .query('meeting_notes')
            .withIndex('by_file', q => q.eq('fileId', args.fileId as Id<"files">))
            .collect();
          for (const doc of mnRecords) {
            await ctx.db.delete(doc._id);
          }
          break;
        }
      }

      // Finally delete the main file record
      await ctx.db.delete(args.fileId as Id<"files">);

      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }
});
