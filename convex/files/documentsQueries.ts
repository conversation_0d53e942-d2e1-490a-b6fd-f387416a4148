import {
  internalQuery, type QueryCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from '../functions';

/**
 * Get files by parent entity
 *
 * @example
 * const files = await client.query.getByParentEntity({
 *   parentEntityId: "tasks:abc123",
 *   docType: "DOCUMENT"
 * });
 * console.log(`Found ${files.length} documents for this task`);
 */
export const getByParentEntity = zQuery({
  args: {
    parentEntityId: z.union([
      zid('tasks'),
      zid('projects'),
      zid('decisions'),
      zid('organizations'),
      zid('users'),
      zid('people'),
      zid('bills')
    ]),
    docType: z.enum(['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT', 'BILL']).optional()
  },
  handler: async (ctx, args) => {
    const { parentEntityId, docType } = args;

    try {
      // Query files directly using filter with field()
      let filesQuery = ctx.db
        .query('files')
        .filter(q => q.eq(q.field('parentEntityId'), parentEntityId));

      // If docType is specified, filter by it
      if (docType) {
        filesQuery = filesQuery.filter(q => q.eq(q.field('docType'), docType));
      }

      return await filesQuery.collect();
    } catch (error) {
      console.error(`Error getting files by parent entity: ${error}`);
      return [];
    }
  }
});

/**
 * Get all document definitions
 *
 * This query retrieves all document definitions from the document_definitions table.
 * These definitions are used to generate the document checklist for onboarding.
 *
 * @example
 * const definitions = await client.query.getDocumentDefinitions({ category: "Legal" });
 * console.log(`Found ${definitions.length} document definitions in the Legal category`);
 */
export const getDocumentDefinitions = zQuery({
  args: {
    category: z.string().optional()
  },
  handler: async (ctx, args) => {
    const { category } = args;

    try {
      // Build the query based on the category filter
      if (category) {
        // If category is specified, filter by it and use the by_category index
        return await ctx.db
          .query('document_definitions')
          .withIndex('by_category', q => q.eq('category', category))
          .collect();
      } else {
        // If no category filter, use the by_updated index for sorting
        return await ctx.db
          .query('document_definitions')
          .withIndex('by_updated')
          .order('desc')
          .collect();
      }
    } catch (error) {
      console.error(`Error getting document definitions: ${error}`);
      return [];
    }
  }
});

/**
 * Get a user's document checklist
 *
 * This query retrieves all entries from the user_document_checklist table
 * for a specific user, along with the corresponding document definitions.
 *
 * @example
 * const checklist = await client.query.getUserDocumentChecklist({ userId: "users:abc123" });
 * console.log(`User has ${checklist.length} document checklist items`);
 */
export const getUserDocumentChecklist = zQuery({
  args: {
    userId: zid('users')
  },
  handler: async (ctx, args) => {
    const { userId } = args;

    try {
      // Get all checklist entries for this user
      const checklistEntries = await ctx.db
        .query('user_document_checklist')
        .filter(q => q.eq(q.field('user_id'), userId))
        .collect();

      // Get all document definitions
      const documentDefinitions = await ctx.db
        .query('document_definitions')
        .collect();

      // Create a map of document definition IDs to document definitions
      const definitionsMap = new Map();
      for (const definition of documentDefinitions) {
        definitionsMap.set(definition._id, definition);
      }

      // Combine the checklist entries with their corresponding document definitions
      const result = checklistEntries.map(entry => {
        const definition = definitionsMap.get(entry.document_definition_id);
        return {
          ...entry,
          definition
        };
      });

      return result;
    } catch (error) {
      console.error(`Error getting user document checklist: ${error}`);
      return [];
    }
  }
});

/**
 * Get a document by its file ID
 *
 * This query retrieves a document record by its associated file ID.
 *
 * @example
 * const document = await client.query.getDocumentByFileId({ fileId: "files:abc123" });
 * if (document) {
 *   console.log(`Document analysis: ${document.ai_analysis.substring(0, 100)}...`);
 * }
 */
export const getDocumentByFileId = zQuery({
  args: {
    fileId: zid('files')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // Get the file record to get its storage ID
      const file = await ctx.db.get(fileId);
      if (!file) {
        return null;
      }

      // Query documents by the file's ID
      const document = await ctx.db
        .query('documents')
        .filter(q => q.eq(q.field('fileId'), file._id))
        .first();

      return document;
    } catch (error) {
      console.error(`Error getting document by file ID: ${error}`);
      return null;
    }
  }
});

/**
 * Get a document by its ID
 *
 * This query retrieves a document record by its ID.
 *
 * @example
 * const document = await client.query.getDocumentById({ documentId: "documents:abc123" });
 * if (document) {
 *   console.log(`Document name: ${document.name}`);
 * }
 */
export const getDocumentById = zQuery({
  args: {
    documentId: zid('documents')
  },
  handler: async (ctx, args) => {
    const { documentId } = args;

    try {
      // Get the document record
      const document = await ctx.db.get(documentId);
      return document;
    } catch (error) {
      console.error(`Error getting document by ID: ${error}`);
      return null;
    }
  }
});

/**
 * Get document counts by type
 *
 * This query returns the count of documents for each document type
 * (KNOWLEDGE_BASE, MEETING_NOTES, CONTRACT, DOCUMENT, BILL).
 *
 * @example
 * const counts = await client.query.getDocumentTypeCounts();
 * console.log(`Knowledge Base articles: ${counts.KNOWLEDGE_BASE}`);
 * console.log(`Meeting Notes: ${counts.MEETING_NOTES}`);
 */
export const getDocumentTypeCounts = zQuery({
  args: {},
  handler: async (ctx) => {
    try {
      // Get counts for each document type in parallel
      const [
        knowledgeBaseCount,
        meetingNotesCount,
        contractsCount,
        documentsCount,
        billsCount
      ] = await Promise.all([
        ctx.db.query('files').withIndex('by_docType', q => q.eq('docType', 'KNOWLEDGE_BASE')).collect().then(r => r.length),
        ctx.db.query('files').withIndex('by_docType', q => q.eq('docType', 'MEETING_NOTES')).collect().then(r => r.length),
        ctx.db.query('files').withIndex('by_docType', q => q.eq('docType', 'CONTRACT')).collect().then(r => r.length),
        ctx.db.query('files').withIndex('by_docType', q => q.eq('docType', 'DOCUMENT')).collect().then(r => r.length),
        ctx.db.query('files').withIndex('by_docType', q => q.eq('docType', 'BILL')).collect().then(r => r.length)
      ]);

      // Return counts as an object
      return {
        KNOWLEDGE_BASE: knowledgeBaseCount,
        MEETING_NOTES: meetingNotesCount,
        CONTRACT: contractsCount,
        DOCUMENT: documentsCount,
        BILL: billsCount
      };
    } catch (error) {
      console.error(`Error getting document type counts: ${error}`);
      return {
        KNOWLEDGE_BASE: 0,
        MEETING_NOTES: 0,
        CONTRACT: 0,
        DOCUMENT: 0,
        BILL: 0
      };
    }
  }
});

/**
 * Get the Box Sign Request ID for a document
 *
 * This query retrieves the box_sign_request_id field from a document.
 * This ID is used to track the signature request status in Box.
 *
 * @example
 * const signRequestId = await client.query.getSignRequestId({ documentId: "documents:abc123" });
 * if (signRequestId) {
 *   console.log(`Sign request ID: ${signRequestId}`);
 * }
 */
export const getSignRequestId = zQuery({
  args: {
    documentId: zid('documents')
  },
  handler: async (ctx, args) => {
    const { documentId } = args;

    try {
      // Get the document record
      const document = await ctx.db.get(documentId);
      return document?.box_sign_request_id ?? null;
    } catch (error) {
      console.error(`Error getting sign request ID: ${error}`);
      return null;
    }
  }
});
