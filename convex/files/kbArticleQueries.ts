import {
  internalQuery, type QueryCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from '../functions';
import { PaginationSchema } from '../../zod/pagination-schema';

/**
 * Get a single Knowledge Base article by its file ID
 *
 * @example
 * const article = await client.query.getKnowledgeBaseArticle({ fileId: "files:abc123" });
 * if (article) {
 *   console.log(`Article title: ${article.title}, Content: ${article.kbContent?.substring(0, 50)}...`);
 * }
 */
export const getKnowledgeBaseArticle = zQuery({
  args: {
    fileId: zid('files')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      const file = await ctx.db.get(fileId);
      if (!file || file.docType !== 'KNOWLEDGE_BASE') return null;

      const kbRecord = await ctx.db
        .query('knowledge_base')
        .withIndex('by_file', q => q.eq('fileId', fileId))
        .first();

      if (!kbRecord) {
        console.warn(`Knowledge base content missing for fileId ${fileId}.`);
        return {
          _id: file._id,
          _creationTime: file._creationTime,
          title: file.title,
          short_description: file.short_description,
          docType: file.docType,
          ownerId: file.ownerId,
          updated_at: file.updated_at,
          kbContent: null,
          kbCategory: null
        };
      }

      return {
        _id: file._id,
        _creationTime: file._creationTime,
        title: file.title,
        short_description: file.short_description,
        docType: file.docType,
        ownerId: file.ownerId,
        updated_at: file.updated_at,
        kbContent: kbRecord.content,
        kbCategory: kbRecord.category
      };
    } catch (error) {
      console.error(`Error getting knowledge base article: ${error}`);
      return null;
    }
  },
});

/**
 * List Knowledge Base articles with pagination
 */
export const listKnowledgeBaseArticles = zQuery({
  args: {
    paginationOpts: z.object({
      numItems: z.number(),  // Make numItems required with no default
      cursor: z.any().nullable().default(null)
    })
  },
  handler: async (ctx, args) => {
    const { paginationOpts } = args;

    try {
      const articles = await ctx.db
        .query('files')
        .withIndex('by_docType', q => q.eq('docType', 'KNOWLEDGE_BASE'))
        .order('desc')
        .paginate({
          numItems: paginationOpts.numItems,
          cursor: paginationOpts.cursor
        });

      return articles;
    } catch (error) {
      console.error(`Error listing knowledge base articles: ${error}`);
      return { page: [], continueCursor: null };
    }
  },
});

/**
 * Search for knowledge base articles by title with pagination
 *
 * This query follows the standardized pagination pattern from our guide.
 * It accepts a search query and pagination parameters, and returns a list
 * of knowledge base articles with a continuation token for pagination.
 */
export const searchKnowledgeBaseArticles = zQuery({
  args: {
    searchQuery: z.string(),
    pagination: PaginationSchema
  },
  output: z.object({
    articles: z.array(z.any()),
    continuation: z.string().nullable().optional()
  }),
  handler: async (ctx, args) => {
    const {
      searchQuery,
      pagination = {
        sortBy: '_creationTime' as const,
        sortDirection: 'desc' as const,
      }
    } = args;

    try {
      // Step 1: Initialize the query with the appropriate index
      let query;
      if (searchQuery) {
        query = ctx.db
          .query('files')
          .withSearchIndex('search_title', q => q.search('title', searchQuery))
          .filter(q => q.eq(q.field('docType'), 'KNOWLEDGE_BASE'));
      } else {
        query = ctx.db
          .query('files')
          .withIndex('by_docType', q => q.eq('docType', 'KNOWLEDGE_BASE'));
      }

      // Step 2: Execute the paginated query according to the pattern in tasks-decisions-pagination.md
      const sortDirection = pagination.sortDirection || 'desc';
      
      // Use numItems or limit, with a default of 12
      const itemsPerPage = pagination.numItems || pagination.limit || 12;
      
      const paginationResult = await query.paginate({
        cursor: pagination.cursor || null,
        numItems: itemsPerPage
      });

      // Step 3: Ensure all items have consistent fields expected by the frontend
      // Filter for KNOWLEDGE_BASE type articles and transform
      const articles = paginationResult.page
        .filter(article => article.docType === 'KNOWLEDGE_BASE')
        .map(article => ({
          ...article,
          title: article.title || "Untitled Article",
          short_description: article.short_description || null
        }));

      // Return standardized response with properly typed knowledge base articles
      return {
        articles,
        continuation: paginationResult.continueCursor
      };
    } catch (error) {
      console.error(`Error searching knowledge base articles: ${error}`);
      return {
        articles: [],
        continuation: null
      };
    }
  },
});
