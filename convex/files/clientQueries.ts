import { query } from '../_generated/server';
import { v } from 'convex/values';

export const getDocumentsForClient = query({
  args: { clientId: v.id("clients") },
  handler: async (ctx, args) => {
    // Get all file relationships for this client
    const relationships = await ctx.db
      .query("file_relationships")
      .filter((q) => q.and(
        q.eq(q.field("subject_type"), "client"),
        q.eq(q.field("subject_id"), args.clientId)
      ))
      .collect();

    // Get the file details and generate URLs, plus check for associated documents
    const documents = await Promise.all(
      relationships.map(async (rel) => {
        const file = await ctx.db.get(rel.file_id);
        if (!file || !file.fileStorageId) return null;

        const url = await ctx.storage.getUrl(file.fileStorageId);
        
        // Check if there's an associated document record for signature info
        const document = await ctx.db
          .query("documents")
          .filter(q => q.eq(q.field("fileId"), file._id))
          .first();

        return {
          _id: file._id,
          fileName: file.title || file.fileName || "Unknown File",
          url,
          _creationTime: file._creationTime,
          // Include document info for signature tracking
          documentId: document?._id || null,
          box_sign_request_id: document?.box_sign_request_id || null,
        };
      })
    );

    // Filter out null results and return
    return documents.filter((doc): doc is NonNullable<typeof doc> => doc !== null);
  },
});
