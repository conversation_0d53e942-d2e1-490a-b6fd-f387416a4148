import {
  internalQuery, type QueryCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from '../functions';
import { PaginationSchema } from '../../zod/pagination-schema';

/**
 * Search for meeting notes by title or content with pagination
 *
 * This query follows the standardized pagination pattern from our guide.
 * It accepts a search query and pagination parameters, and returns a list
 * of meeting notes with a continuation token for pagination.
 */
export const searchMeetingNotes = zQuery({
  args: {
    searchQuery: z.string(),
    pagination: PaginationSchema
  },
  output: z.object({
    meetingNotes: z.array(z.any()),
    continuation: z.string().nullable().optional()
  }),
  handler: async (ctx, args) => {
    const {
      searchQuery,
      pagination = {
        sortBy: '_creationTime' as const,
        sortDirection: 'desc' as const,
      }
    } = args;

    try {
      // Step 1: Initialize the query with the appropriate index
      let query;
      if (searchQuery) {
        query = ctx.db
          .query('files')
          .withSearchIndex('search_title', q => q.search('title', searchQuery))
          .filter(q => q.eq(q.field('docType'), 'MEETING_NOTES'));
      } else {
        query = ctx.db
          .query('files')
          .withIndex('by_docType', q => q.eq('docType', 'MEETING_NOTES'));
      }

      // Step 2: Execute the paginated query according to the pattern in tasks-decisions-pagination.md
      const sortDirection = pagination.sortDirection || 'desc';
      
      // Use numItems or limit, with a default of 12
      const itemsPerPage = pagination.numItems || pagination.limit || 12;
      
      const paginationResult = await query.paginate({
        cursor: pagination.cursor || null,
        numItems: itemsPerPage
      });

      // Step 3: Ensure all items have consistent fields expected by the frontend
      // Filter for MEETING_NOTES type and transform
      const meetingNotes = paginationResult.page
        .filter(note => note.docType === 'MEETING_NOTES')
        .map(note => ({
          ...note,
          title: note.title || "Untitled Meeting Note",
          short_description: note.short_description || null
        }));

      // Return standardized response with properly typed meeting notes
      return {
        meetingNotes,
        continuation: paginationResult.continueCursor
      };
    } catch (error) {
      console.error(`Error searching meeting notes: ${error}`);
      return {
        meetingNotes: [],
        continuation: null
      };
    }
  },
});

/**
 * Get a single Meeting Note by its file ID
 *
 * @example
 * const note = await client.query.getMeetingNote({ fileId: "files:abc123" });
 * if (note) {
 *   console.log(`Meeting note title: ${note.title}, Content: ${note.mnContent?.substring(0, 50)}...`);
 * }
 */
export const getMeetingNote = zQuery({
  args: {
    fileId: zid('files')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // 1. Get the main file record
      const file = await ctx.db.get(fileId);
      if (!file || file.docType !== 'MEETING_NOTES') {
        return null;
      }

      // 2. Get the corresponding meeting_notes record
      const mnRecord = await ctx.db
        .query('meeting_notes')
        .withIndex('by_file', q => q.eq('fileId', fileId))
        .first();

      // 3. Combine the data
      if (!mnRecord) {
        console.warn(`Meeting note content missing for fileId ${fileId}.`);
        // Ensure core file fields are returned even if mnRecord is missing
        return {
          _id: file._id,
          _creationTime: file._creationTime,
          title: file.title, // Explicitly include title
          short_description: file.short_description, // Add short_description
          docType: file.docType,
          ownerId: file.ownerId,
          updated_at: file.updated_at,
          mnContent: null,
          mnManualNotes: null, // Add manualNotes field
          mnMeetingDate: null,
          mnAttendees: null,
          mnCategory: null
        };
      }

      // Return combined data including file fields
      return {
        _id: file._id,
        _creationTime: file._creationTime,
        title: file.title, // Explicitly include title
        short_description: file.short_description, // Add short_description
        docType: file.docType,
        ownerId: file.ownerId,
        updated_at: file.updated_at,
        // Add fields from mnRecord
        mnContent: mnRecord.content,
        mnManualNotes: mnRecord.manualNotes, // Add manualNotes field
        mnTranscript: mnRecord.transcript,
        mnMeetingDate: mnRecord.meetingDate,
        mnAttendees: mnRecord.attendees,
        mnCategory: mnRecord.category,
      };
    } catch (error) {
      console.error(`Error fetching meeting note: ${error}`);
      return null;
    }
  },
});

/**
 * Internal query to get the meeting_notes record ID for a given file ID.
 * This is used by other functions that need to find and update the meeting_notes record.
 */
export const internalGetMeetingNoteRecordId = internalQuery({
  args: {
    fileId: v.id('files'),
  },
  handler: async (ctx, args: { fileId: Id<'files'> }) => {
    const { fileId } = args;

    // Get the meeting_notes record
    const mnRecord = await ctx.db
      .query('meeting_notes')
      .withIndex('by_file', (q: any) => q.eq('fileId', fileId))
      .first();

    if (!mnRecord) {
      return null;
    }

    return mnRecord._id;
  },
});
