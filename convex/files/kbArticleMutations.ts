import {
  mutation, internalMutation, type MutationCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';
import { api } from '../_generated/api';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zMutation } from '../functions';
import { scheduleDebouncedJob } from '../utils/schedulerUtils';
import { TRIEVE_OPERATION_DELAY } from '../utils/trieveUtils';
import {
  CreateKnowledgeBaseArticleArgsSchema,
  UpdateKnowledgeBaseArticleArgsSchema,
  DeleteKnowledgeBaseArticleArgsSchema,
  type RelationshipLink,
} from '../../zod/files-schema';

/**
 * Create a new Knowledge Base article
 *
 * This mutation:
 * 1. Creates a record in the 'files' table with docType 'KNOWLEDGE_BASE'.
 * 2. Creates a corresponding record in the 'knowledge_base' table.
 * 3. Optionally links initial related entities via 'file_relationships'.
 * 4. Triggers Trieve ingestion.
 * 5. Returns the ID of the new file record.
 */
export const createKnowledgeBaseArticle = zMutation({
  args: CreateKnowledgeBaseArticleArgsSchema.shape,
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to create an article.");
    }
    const userId = identity.subject as Id<"users">; // Assuming identity.subject is the user's Convex ID

    const now = Date.now();

    // 1. Create the record in the 'files' table
    const fileId = await ctx.db.insert('files', {
      title: args.title, // Use title from args
      docType: 'KNOWLEDGE_BASE',
      ownerId: args.ownerId, // Use ownerId from args (should match authenticated user?)
      updated_at: now,
      // Add other relevant fields from 'files' schema if needed, e.g., description
    });

    // 2. Create the corresponding record in the 'knowledge_base' table
    await ctx.db.insert('knowledge_base', {
      fileId: fileId,
      content: args.content, // Use content from args
      updated_at: now,
      // Add category if provided/needed
    });

    // 3. Handle initial relationships
    if (args.initialRelationships && args.initialRelationships.length > 0) {
      await Promise.all(args.initialRelationships.map(link =>
        ctx.db.insert('file_relationships', {
          file_id: fileId,
          subject_type: link.subject_type,
          subject_id: link.subject_id,
          linked_at: now,
        })
      ));
    }

    // --- Schedule Trieve Indexing using Debounce Utility ---
    // Only schedule if content or title was provided (assuming content is always provided on create)
    if (args.content || args.title) {
      try {
        await scheduleDebouncedJob(ctx, {
          tableName: "files",
          documentId: fileId,
          jobIdFieldName: "trieveIndexingJobId",
          jobToSchedule: api.actions.trieveActions.trieveUpsertDocumentChunks,
          jobArgs: { fileId: fileId }, // Pass fileId to the action
          delayMs: TRIEVE_OPERATION_DELAY,
        });
        console.log(`Successfully scheduled debounced Trieve indexing for KB article: ${fileId}`);
      } catch (error) {
        console.error(`Failed to schedule debounced Trieve indexing for KB article ${fileId}:`, error);
        // Decide if this error should block the mutation result or just be logged
      }
    }
    // --- End Trieve Indexing ---

    // 4. Return the ID of the new file record
    return fileId;
  },
});

/**
 * Update an existing Knowledge Base article
 *
 * This mutation:
 * 1. Updates the 'title' in the 'files' table record.
 * 2. Updates the 'content' in the corresponding 'knowledge_base' table record.
 * 3. Adds/Removes relationships.
 * 4. Triggers Trieve re-indexing (delete + ingest).
 */
export const updateKnowledgeBaseArticle = zMutation({
  args: {
    input: UpdateKnowledgeBaseArticleArgsSchema,
  },
  handler: async (ctx, args) => {
    const { input } = args;
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to update a knowledge base article.");
    }

    const now = Date.now();
    const typedFileId = input.fileId as Id<"files">;

    // 1. Fetch existing file record BEFORE updates to get original creation time
    const fileRecord = await ctx.db.get(typedFileId);
    if (!fileRecord) {
      throw new ConvexError(`File record ${typedFileId} not found.`);
    }

    // 2. Update the 'files' table record
    await ctx.db.patch(typedFileId, {
      updated_at: now,
      ...(input.title !== undefined && { title: input.title })
    });

    // 3. Update the 'knowledge_base' table record
    const kbRecord = await ctx.db
      .query('knowledge_base')
      .withIndex('by_file', (q) => q.eq('fileId', typedFileId))
      .first();

    if (!kbRecord) {
      throw new ConvexError(`Knowledge base entry not found for fileId: ${typedFileId}`);
    }

    await ctx.db.patch(kbRecord._id, {
      updated_at: now,
      ...(input.content !== undefined && { content: input.content })
    });

    // 4. Add new relationships
    if (input.relationshipsToAdd?.length) {
      await Promise.all(
        input.relationshipsToAdd.map(async (link: RelationshipLink) => {
          const existing = await ctx.db.query('file_relationships')
            .withIndex('by_subject_type', q => q.eq('subject_type', link.subject_type))
            .filter(q => q.and(q.eq(q.field('subject_id'), link.subject_id), q.eq(q.field('file_id'), typedFileId)))
            .first();
          if (!existing) {
            await ctx.db.insert('file_relationships', {
              file_id: typedFileId,
              subject_type: link.subject_type,
              subject_id: link.subject_id,
              linked_at: now,
              featuredFile: false
            });
          }
        })
      );
    }

    // 5. Remove relationships
    if (input.relationshipsToRemove?.length) {
      const relationshipRecords = await Promise.all(
        input.relationshipsToRemove.map(async (linkToRemove: RelationshipLink) => {
          return await ctx.db.query('file_relationships')
            .withIndex('by_subject_type', q => q.eq('subject_type', linkToRemove.subject_type))
            .filter(q => q.and(q.eq(q.field('subject_id'), linkToRemove.subject_id), q.eq(q.field('file_id'), typedFileId)))
            .first();
        })
      );
      const recordsToDelete = relationshipRecords.filter((r): r is Doc<'file_relationships'> => r !== null);
      await Promise.all(recordsToDelete.map((r) => ctx.db.delete(r._id)));
    }

    // --- Schedule Trieve Re-indexing using Debounce Utility ---
    // Only schedule if content or title was updated
    if (input.content !== undefined || input.title !== undefined) {
      try {
        await scheduleDebouncedJob(ctx, {
          tableName: "files",
          documentId: typedFileId,
          jobIdFieldName: "trieveIndexingJobId",
          jobToSchedule: api.actions.trieveActions.trieveUpsertDocumentChunks,
          jobArgs: { fileId: typedFileId }, // Pass fileId to the action
          delayMs: TRIEVE_OPERATION_DELAY,
        });
        console.log(`Successfully scheduled debounced Trieve re-indexing for KB article: ${typedFileId}`);
      } catch (error) {
        console.error(`Failed to schedule debounced Trieve re-indexing for KB article ${typedFileId}:`, error);
        // Decide if this error should block the mutation result or just be logged
      }
    }
    // --- End Trieve Re-indexing ---

    return { success: true };
  }
});

/**
 * Delete a Knowledge Base article and its associated data
 *
 * This mutation:
 * 1. Finds and deletes all relationships linked to the file ID from 'file_relationships'.
 * 2. Finds and deletes the corresponding record from 'knowledge_base'.
 * 3. Deletes the main record from the 'files' table.
 * 4. Triggers Trieve deletion.
 */
export const deleteKnowledgeBaseArticle = zMutation({
  args: DeleteKnowledgeBaseArticleArgsSchema.shape,
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("User must be authenticated to delete an article.");
    }

    const { fileId } = args;

    // 1. Delete associated relationships
    const relationshipRecords = await ctx.db
      .query('file_relationships')
      .withIndex('by_file', (q) => q.eq('file_id', fileId))
      .collect();
    await Promise.all(relationshipRecords.map(record => ctx.db.delete(record._id)));

    // 2. Delete the corresponding knowledge_base record
    const kbRecord = await ctx.db
      .query('knowledge_base')
      .withIndex('by_file', (q) => q.eq('fileId', fileId))
      .first();
    if (kbRecord) {
      await ctx.db.delete(kbRecord._id);
    } else {
      console.warn(`Knowledge base record not found for fileId ${fileId} during deletion.`);
    }

    // 3. Delete the main file record
    await ctx.db.delete(fileId);

    // --- Trigger Trieve Deletion ---
    try {
      // Call the new deleteDocument mutation
      await ctx.runMutation(api.trieve.deleteDocument.default, { docId: fileId });
      console.log(`Successfully triggered Trieve deletion for KB article: ${fileId}`);
    } catch (error) {
      console.error(`Failed to trigger Trieve deletion for KB article ${fileId}:`, error);
      // Decide if this error should block the mutation result or just be logged
    }
    // --- End Trieve Deletion ---

    return { success: true }; // Indicate successful deletion
  },
});
