import {
  internalQuery, type QueryCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { api } from '../_generated/api';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from '../functions';
import { getSubTableData } from '../utils/fileUtils';

/**
 * Get a basic file record by its ID.
 *
 * @example
 * const file = await client.query.getFileRecordBasic({ fileId: "files:abc123" });
 * if (file) {
 *   console.log(`File title: ${file.title}`);
 * }
 */
export const getFileRecordBasic = zQuery({
  args: {
    fileId: zid('files')
  },
  handler: async (ctx, args): Promise<Doc<'files'> | null> => {
    try {
      return await ctx.db.get(args.fileId);
    } catch (error) {
      console.error(`Error fetching file record: ${error}`);
      return null;
    }
  },
});

export const getFile = internalQuery({
  args: { id: v.id("files") },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  },
});

/**
 * Get file URL for download
 *
 * @example
 * const url = await client.query.getFileUrl({ fileId: "storage:abc123" });
 * console.log(`File download URL: ${url}`);
 */
export const getFileUrl = zQuery({
  args: {
    fileId: zid('_storage')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // First verify the storage ID exists in the system table
      const storageExists = await ctx.db.system.get(fileId);
      if (!storageExists) {
        throw new Error("Storage file not found in system");
      }

      return await ctx.storage.getUrl(fileId);
    } catch (error) {
      console.error(`Error getting file URL: ${error}`);
      throw new Error(`Failed to get URL for file: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
});

/**
 * Get a file by its storage ID
 *
 * @example
 * const file = await client.query.getFileByStorageId({ storageId: "storage:abc123" });
 * if (file) {
 *   console.log(`Found file: ${file.title}`);
 * }
 */
export const getFileByStorageId = zQuery({
  args: {
    storageId: zid('_storage')
  },
  handler: async (ctx, args) => {
    const { storageId } = args;

    try {
      // Query files by fileStorageId
      const file = await ctx.db
        .query('files')
        .filter(q => q.eq(q.field('fileStorageId'), storageId))
        .first();

      return file;
    } catch (error) {
      console.error(`Error getting file by storage ID: ${error}`);
      return null;
    }
  }
});

export const getFileByBoxId = internalQuery({
  args: {
    boxFileId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("files")
      .filter((q) => q.eq(q.field("box_file_id"), args.boxFileId))
      .first();
  },
});

/**
 * Get recently modified files
 *
 * This query retrieves the most recently modified files across all document types,
 * sorted by updated_at timestamp in descending order.
 *
 * @example
 * const recentFiles = await client.query.getRecentlyModifiedFiles({ limit: 10 });
 * console.log(`Found ${recentFiles.length} recently modified files`);
 */
export const getRecentlyModifiedFiles = zQuery({
  args: {
    limit: z.number().optional().default(5)
  },
  handler: async (ctx, args) => {
    const { limit } = args;

    try {
      // Query files and sort by updated_at in descending order
      const recentFiles = await ctx.db
        .query('files')
        .order('desc')
        .collect();

      // Sort manually by updated_at field and take the specified limit
      return recentFiles
        .sort((a, b) => (b.updated_at || 0) - (a.updated_at || 0))
        .slice(0, limit);
    } catch (error) {
      console.error(`Error getting recently modified files: ${error}`);
      return [];
    }
  }
});

/**
 * Get attendee details for meeting notes
 *
 * This query fetches the details of attendees (people or organizations) for a meeting note,
 * including organization memberships for people.
 *
 * @example
 * const attendees = await client.query.getAttendeeDetails({
 *   attendeeIds: ["people:abc123", "organizations:def456"]
 * });
 * console.log(`Found ${attendees.length} attendees`);
 */
export const getAttendeeDetails = zQuery({
  args: {
    attendeeIds: z.array(z.string())
  },
  handler: async (ctx, args) => {
    const { attendeeIds } = args;

    try {
      if (!attendeeIds || attendeeIds.length === 0) return [];

      const attendeeDetailsPromises = attendeeIds.map(async (idString) => {
        const id = idString as Id<"people"> | Id<"organizations">;

        // Try fetching as a person
        const person = await ctx.db.get(id as Id<'people'>);
        if (person) {
          // Fetch organization relationships for this person
          const relationships = await ctx.db
            .query('organization_people')
            .withIndex('by_person', q => q.eq('person_id', person._id))
            .collect();

          // Fetch the actual organization documents
          const orgDetailsPromises = relationships.map(rel => ctx.db.get(rel.organization_id));
          const orgDocs = (await Promise.all(orgDetailsPromises)).filter((org): org is Doc<'organizations'> => org !== null);

          const organizations = orgDocs.map(org => ({ id: org._id, name: org.name }));

          const nameParts = (person.name || '').split(' ');
          const initials = nameParts.length > 1
            ? `${nameParts[0][0] || ''}${nameParts[nameParts.length - 1][0] || ''}`.toUpperCase()
            : (person.name?.[0] || '?').toUpperCase();

          return {
            id: person._id,
            type: 'person' as const,
            name: person.name ?? 'Unknown Person',
            image: person.image,
            initials: initials,
            email: person.email,
            organizations: organizations // Include organizations
          };
        }

        // Try fetching as an organization
        const org = await ctx.db.get(id as Id<'organizations'>);
        if (org) {
          const nameParts = (org.name || '').split(' ');
          const initials = nameParts.length > 1
            ? `${nameParts[0][0] || ''}${nameParts[1]?.[0] || ''}`.toUpperCase()
            : (org.name?.[0] || '?').toUpperCase();

          return {
            id: org._id,
            type: 'organization' as const,
            name: org.name ?? 'Unknown Organization',
            image: undefined,
            initials: initials,
            email: org.email,
            organizations: [] // Organizations don't have orgs list
          };
        }

        console.warn(`Attendee ID ${idString} not found in people or organizations table.`);
        return null;
      });

      const results = await Promise.all(attendeeDetailsPromises);

      return results
        .filter((result): result is NonNullable<typeof result> => result !== null)
        .sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error(`Error getting attendee details: ${error}`);
      return [];
    }
  }
});

/**
 * Get all entities related to a specific file ID via file_relationships
 *
 * @example
 * const relatedEntities = await client.query.getRelatedEntities({ fileId: "files:abc123" });
 * console.log(`Found ${relatedEntities.length} related entities`);
 */
export const getRelatedEntities = zQuery({
  args: {
    fileId: zid('files')
  },
  handler: async (ctx, args) => {
    const { fileId } = args;

    try {
      // Get all relationships for this file
      const relationships = await ctx.db
        .query('file_relationships')
        .withIndex('by_file', q => q.eq('file_id', fileId))
        .collect();

      // Group relationships by subject type
      const entityIdsByType: Record<string, Id<any>[]> = {};
      for (const rel of relationships) {
        if (!entityIdsByType[rel.subject_type]) {
          entityIdsByType[rel.subject_type] = [];
        }
        entityIdsByType[rel.subject_type].push(rel.subject_id);
      }

      // Fetch entity details for each type
      const promises = Object.entries(entityIdsByType).map(async ([type, ids]) => {
        try {
          const documents = await Promise.all(ids.map(id => ctx.db.get(id as Id<any>)));
          return documents
            .filter((doc): doc is NonNullable<typeof doc> => doc !== null)
            .map(doc => ({
              _id: doc._id,
              type: type,
              name: doc.title ?? doc.name ?? 'Unknown Name'
            }));
        } catch (error) {
          console.error(`Error fetching details for type ${type}:`, error);
          return [];
        }
      });

      const resultsByType = await Promise.all(promises);
      return resultsByType.flat();
    } catch (error) {
      console.error(`Error getting related entities: ${error}`);
      return [];
    }
  },
});

export const getIntegrations = zQuery({
    args: {},
    handler: async (ctx) => {
        const integrations = await ctx.db.query("integrations").collect();
        return integrations.find(integration => 
            integration.immutable_slug === "box"
        );
    },
});

/**
 * Public query wrapper for getting sub-table data.
 *
 * @example
 * const data = await client.query.getSubTableDataPublic({
 *   fileId: "files:abc123",
 *   docType: "KNOWLEDGE_BASE"
 * });
 * console.log(`Category: ${data.category}, Content: ${data.content?.substring(0, 50)}...`);
 */
export const getSubTableDataPublic = zQuery({
  args: {
    fileId: zid('files'),
    docType: z.string(),
  },
  handler: async (ctx, args): Promise<{ category: string | null | undefined; content: string | null | undefined }> => {
    try {
      // Use the imported function directly
      return await getSubTableData(ctx, args.fileId, args.docType);
    } catch (error) {
      console.error(`Error getting sub-table data: ${error}`);
      return { category: null, content: null };
    }
  },
});
