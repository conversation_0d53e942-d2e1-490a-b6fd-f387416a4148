import {
  mutation, internalMutation, type MutationCtx
} from '../_generated/server';
import { v } from 'convex/values';
import { Id, Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';
import { api } from '../_generated/api';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zMutation } from '../functions';

/**
 * Create a new document
 *
 * This mutation creates a new document record associated with a file.
 */
export const createDocument = mutation({
  args: {
    storageId: v.id('_storage'),
    name: v.string(),
    aiAnalysis: v.string()
  },
  handler: async (ctx, args) => {
    const { storageId, name, aiAnalysis } = args;

    // Create the document record
    const file = await ctx.db
      .query("files")
      .filter(q => q.eq(q.field("fileStorageId"), storageId))
      .first();

    if (!file) {
      throw new ConvexError({
        message: "File not found",
        code: "FILE_NOT_FOUND",
      });
    }

    const documentId = await ctx.db.insert('documents', {
      fileId: file._id,
      name,
      ai_analysis: aiAnalysis,
      status: 'completed',  // Set status to completed
      updated_at: Date.now()
    });

    return documentId;
  }
});

export const internalCreateDocument = internalMutation({
  args: {
    clientId: v.id("clients"),
    storageId: v.id("_storage"),
    fileName: v.string(),
    fileExtension: v.optional(v.string()),
    fileSize: v.optional(v.number()),
    boxFileId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const fileId = await ctx.db.insert("files", {
      docType: "DOCUMENT",
      title: args.fileName,
      fileName: args.fileName,
      fileFilename: args.fileName, // Store the original filename
      fileStorageId: args.storageId,
      fileExtension: args.fileExtension,
      fileSize: args.fileSize,
      box_file_id: args.boxFileId, // Save the Box file ID
      updated_at: Date.now(),
    });

    await ctx.db.insert("documents", {
      fileId,
      name: args.fileName,
      updated_at: Date.now(),
    });

    await ctx.db.insert("file_relationships", {
      file_id: fileId,
      subject_type: "client",
      // @ts-ignore
      subject_id: args.clientId,
      linked_at: Date.now(),
    });
  },
});

/**
 * Update document and checklist based on AI analysis
 *
 * This mutation:
 * 1. Updates the documents.ai_analysis field with the analysis result
 * 2. Finds the matching document_definition_id based on the documentType
 * 3. Updates the user_document_checklist table
 */
export const updateDocumentAndChecklist = mutation({
  args: {
    fileId: v.id('files'),
    analysisResult: v.object({
      documentType: v.string(),
      summary: v.string(),
      analysis: v.string(),
      entities: v.optional(v.array(v.string())),
      maritalStatus: v.optional(v.string()),
      spouses: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        dateOfBirth: v.optional(v.string()),
      }))),
      children: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        dateOfBirth: v.optional(v.string()),
      }))),
      assets: v.optional(v.array(v.object({
        name: v.optional(v.string()),
        value: v.optional(v.number()),
        type: v.optional(v.string()),
        description: v.optional(v.string())
      })))
    }),
    userId: v.id('users')
  },
  handler: async (ctx, args) => {
    const { fileId, analysisResult, userId } = args;

    try {
      // 1. Get the file record
      const file = await ctx.db.get(fileId);
      if (!file) {
        throw new ConvexError({
          message: "File not found",
          code: "FILE_NOT_FOUND",
        });
      }

      // 2. Get the file's storage ID
      const storageId = file.fileStorageId;
      if (!storageId) {
        throw new ConvexError({
          message: "File storage ID not found",
          code: "FILE_STORAGE_ID_NOT_FOUND",
        });
      }

      // 3. Find the document record associated with this storage ID
      const document = await ctx.db
        .query("documents")
        .filter(q => q.eq(q.field("fileId"), file._id))
        .first();

      // 4. Update the document's ai_analysis field
      if (document) {
        await ctx.db.patch(document._id, {
          ai_analysis: analysisResult.analysis,
          status: 'completed',  // Set status to completed
          updated_at: Date.now()
        });
      } else {
      // Create a new document record if it doesn't exist
      await ctx.db.insert("documents", {
        fileId: file._id,
        name: file.title || "", // Use file.title here
        ai_analysis: analysisResult.analysis,
        status: 'completed',  // Set status to completed
        updated_at: Date.now()
        });
      }

      // 4. Find the document definition that matches the documentType
      const documentDefinition = await ctx.db
        .query("document_definitions")
        .filter(q => q.eq(q.field("name"), analysisResult.documentType))
        .first();

      // 5. If a matching document definition is found, update the checklist
      if (documentDefinition) {
        await ctx.db.insert("user_document_checklist", {
          document_definition_id: documentDefinition._id,
          user_id: userId,
          status: "completed",
          updated_at: Date.now()
        });
      }

      return { success: true };
    } catch (error) {
      // Handle errors
      if (error instanceof ConvexError) throw error;

      throw new ConvexError({
        message: error instanceof Error ? error.message : "Unknown error in updateDocumentAndChecklist",
        code: "UPDATE_DOCUMENT_ERROR",
      });
    }
  }
});

/**
 * Create or update a user document checklist entry
 *
 * This mutation creates a new entry in the user_document_checklist table
 * or updates an existing one if it already exists.
 */
export const updateUserDocumentChecklist = zMutation({
  args: {
    documentDefinitionId: z.string(),
    userId: z.string(),
    status: z.string(),
  },
  handler: async (ctx, args) => {
    const { documentDefinitionId, userId, status } = args;

    // Check if an entry already exists
    const existingEntry = await ctx.db
      .query("user_document_checklist")
      .filter(q => q.and(
        q.eq(q.field("document_definition_id"), documentDefinitionId as Id<"document_definitions">),
        q.eq(q.field("user_id"), userId as Id<"users">)
      ))
      .first();

    if (existingEntry) {
      // Update existing entry
      return await ctx.db.patch(existingEntry._id, {
        status: status as string,
        updated_at: Date.now()
      });
    } else {
      // Create new entry
      return await ctx.db.insert("user_document_checklist", {
        document_definition_id: documentDefinitionId as Id<"document_definitions">,
        user_id: userId as Id<"users">,
        status: status as string,
        updated_at: Date.now()
      });
    }
  }
});

/**
 * Update a document's status
 *
 * This mutation updates the status field of a document record.
 */
export const updateDocumentStatus = mutation({
  args: {
    documentId: v.id('documents'),
    status: v.string()
  },
  handler: async (ctx, args) => {
    const { documentId, status } = args;

    // Update the document record
    await ctx.db.patch(documentId, {
      status: status,
      updated_at: Date.now()
    });

    return documentId;
  }
});

/**
 * Update a document's AI analysis
 *
 * This mutation updates the ai_analysis field of a document record.
 */
export const updateDocumentAiAnalysis = mutation({
  args: {
    documentId: v.id('documents'),
    aiAnalysis: v.string()
  },
  handler: async (ctx, args) => {
    const { documentId, aiAnalysis } = args;

    // Update the document record
    await ctx.db.patch(documentId, {
      ai_analysis: aiAnalysis,
      status: 'completed',  // Set status to completed
      updated_at: Date.now()
    });

    return documentId;
  }
});

// Schema for analysis results
export const DocumentAnalysisSchema = z.object({
  insights: z.object({
    topics: z.array(z.string()),
    summary: z.string(),
    sentiment: z.enum(["positive", "neutral", "negative"]).optional(),
  }),
  metadata: z.object({
    pageCount: z.number().optional(),
    wordCount: z.number().optional(),
    processingTime: z.number().optional(),
  }).optional(),
});

/**
 * Save document analysis results to the database
 */
export const saveAnalysisResults = zMutation({
  args: {
    documentId: zid("documents"),
    analysisResults: DocumentAnalysisSchema,
  },
  handler: async (ctx, args) => {
    const { documentId, analysisResults } = args;
    
    // Check if document exists
    const document = await ctx.db.get(documentId);
    if (!document) {
      throw new ConvexError(`Document with ID ${documentId} not found`);
    }
    
    // Update document with analysis results
    await ctx.db.patch(documentId, {
      insights: {
        ...analysisResults.insights,
        sentiment: analysisResults.insights.sentiment ?? "neutral"
      },
      metadata: analysisResults.metadata,
      status: "analyzed",
      updated_at: Date.now(),
    });
    
    return documentId;
  },
});

/**
 * Save markdown content to a document
 */
export const saveMarkdown = internalMutation({
  args: {
    documentId: v.id("documents"),
    markdown: v.string(),
  },
  handler: async (ctx, { documentId, markdown }) => {
    await ctx.db.patch(documentId, { markdown });
  },
});
