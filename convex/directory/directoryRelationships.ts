import { 
    query, 
    mutation, 
    internalQuery, 
    action 
  } from '../_generated/server';
  
  import { 
    GenericId, 
    ConvexError, 
    v 
  } from 'convex/values';
  
  import { Id } from '../_generated/dataModel';
  import { api } from '../_generated/api';
  import { zQuery, zMutation } from '../functions';

  import { NoOp } from "convex-helpers/server/customFunctions";
  import { 
    PersonSchema, 
    Person, 
    UpdatePersonSchema, 
    CreatePersonSchema,
    DirectoryNoteSchema,
    OrganizationSchema,
    Organization,
    OrgPeopleRelationshipSchema,
    OrganizationWithPeopleCountSchema,
    UpdateOrganizationSchema
  } from '../../zod/directory-schema';
  import { zid } from "convex-helpers/server/zod";
  import Fuse from 'fuse.js';
  import { z } from 'zod';


/******************************************
 * ORGANIZATION-PEOPLE RELATIONSHIPS
 ******************************************/

/**
 * List organizations associated with a specific person with pagination and filtering options
 * 
 * @example
 * // List organizations for a person with default pagination
 * const result = await client.query.listOrganizationssByPerson({
 *   personId: "123abc"
 * });
 * 
 * // List organizations with custom pagination and filtering
 * const result = await client.query.listOrganizationssByPerson({
 *   personId: "123abc",
 *   pagination: {
 *     sortDirection: "asc",
 *     limit: 10
 *   },
 *   filter: {
 *     searchText: "Acme"
 *   }
 * });
 */
export const listOrganizationssByPerson = zQuery({
    args: {
      personId: zid('people'),
      pagination: z.object({
        sortDirection: z.enum(['asc', 'desc']).optional().default('desc'),
        cursor: z.any().optional(),
        limit: z.number().optional().default(50)
      }).optional(),
      filter: z.object({
        searchText: z.string().optional(),
        email: z.string().optional()
      }).optional()
    },
    output: z.object({
      organizations: z.array(
        OrganizationSchema.extend({
          relationship: z.object({
            updated_at: z.number(),
            role: z.string().optional()
          })
        })
      ),
      continueCursor: z.any().optional()
    }),
    handler: async (ctx, args) => {
      const { personId, pagination = { sortDirection: 'desc', limit: 50 }, filter = {} } = args;
      const { sortDirection = 'desc', limit = 50, cursor } = pagination;
  
      let relationshipsQuery = ctx.db
        .query('organization_people')
        .filter(q => q.eq(q.field('person_id'), personId))
        .order(sortDirection);
  
      const paginationResults = await relationshipsQuery.paginate({ cursor, numItems: limit + 1 });
      const relationships = paginationResults.page;
  
      let continueCursor;
      if (relationships.length > limit) {
        relationships.pop(); // Remove the extra item
        continueCursor = paginationResults.continueCursor;
      }
  
      const orgsWithDetails = await Promise.all(
        relationships.map(async (rel) => {
          const org = await ctx.db.get(rel.organization_id);
          if (!org) return null;
  
          const validatedOrg = OrganizationSchema.parse(org);
          return {
            ...validatedOrg,
            relationship: {
              updated_at: rel.updated_at,
              ...(rel.role !== undefined ? { role: rel.role } : {})
            }
          };
        })
      );
  
      return {
        organizations: orgsWithDetails.filter((org): org is NonNullable<typeof org> => org !== null),
        continueCursor
      };
    }
  });
  
/**
 * List people associated with a specific organization with pagination and filtering options
 * 
 * @example
 * // List people for an organization with default pagination
 * const result = await client.query.listPeopleByOrganization({
 *   organizationId: "123abc"
 * });
 * 
 * // List people with custom pagination and filtering
 * const result = await client.query.listPeopleByOrganization({
 *   organizationId: "123abc",
 *   pagination: {
 *     sortDirection: "asc",
 *     limit: 10
 *   },
 *   filter: {
 *     searchText: "John"
 *   }
 * });
 */
export const listPeopleByOrganization = zQuery({
    args: {
      organizationId: zid('organizations'),
      pagination: z.object({
        sortDirection: z.enum(['asc', 'desc']).optional().default('desc'),
        cursor: z.any().optional(),
        limit: z.number().optional().default(50)
      }).optional(),
      filter: z.object({
        searchText: z.string().optional(),
        email: z.string().optional()
      }).optional()
    },
    output: z.object({
      people: z.array(
        PersonSchema.extend({
          relationship: z.object({
            updated_at: z.number(),
            role: z.string().optional()
          })
        })
      ),
      continueCursor: z.any().optional()
    }),
    handler: async (ctx, args) => {
      const { organizationId, pagination = { sortDirection: 'desc', limit: 50 }, filter = {} } = args;
      const { sortDirection = 'desc', limit = 50, cursor } = pagination;
  
      let relationshipsQuery = ctx.db
        .query('organization_people')
        .filter(q => q.eq(q.field('organization_id'), organizationId))
        .order(sortDirection);
  
      const paginationResults = await relationshipsQuery.paginate({ cursor, numItems: limit + 1 });
      const relationships = paginationResults.page;
  
      let continueCursor;
      if (relationships.length > limit) {
        relationships.pop(); // Remove the extra item
        continueCursor = paginationResults.continueCursor;
      }
  
      const peopleWithDetails = await Promise.all(
        relationships.map(async (rel) => {
          const person = await ctx.db.get(rel.person_id);
          if (!person) return null;
  
          const validatedPerson = PersonSchema.parse(person);
          return {
            ...validatedPerson,
            relationship: {
              updated_at: rel.updated_at,
              ...(rel.role !== undefined ? { role: rel.role } : {})
            }
          };
        })
      );
  
      return {
        people: peopleWithDetails.filter((p): p is NonNullable<typeof p> => p !== null),
        continueCursor
      };
    }
  });
  
/**
 * Create one or more organization-people relationships
 * 
 * @example
 * // Create a single relationship
 * const result = await client.mutation.createOrganizationPeopleRelationship({
 *   items: [{
 *     organization_id: "org123",
 *     person_id: "person456",
 *     role: "Employee"
 *   }]
 * });
 * 
 * // Create multiple relationships
 * const result = await client.mutation.createOrganizationPeopleRelationship({
 *   items: [
 *     {
 *       organization_id: "org123",
 *       person_id: "person456",
 *       role: "Employee"
 *     },
 *     {
 *       organization_id: "org789",
 *       person_id: "person456",
 *       role: "Contractor"
 *     }
 *   ]
 * });
 */
export const createOrganizationPeopleRelationship = zMutation({
  args: {
    items: z.array(
      z.object({
        organization_id: zid('organizations'),
        person_id: zid('people'),
        role: z.string().optional(),
        reason: z.string().optional()
      })
    )
  },
  output: z.object({
    ids: z.array(zid('organization_people')),
    failed: z.array(
      z.object({
        item: z.object({
          organization_id: zid('organizations'),
          person_id: zid('people'),
          role: z.string().optional(),
          reason: z.string().optional()
        }),
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    try {
      const now = Date.now();
      const createdIds: GenericId<"organization_people">[] = [];
      const failedCreations: any[] = [];
  
      await Promise.all(
        args.items.map(async (item) => {
          try {
            // Check if relationship already exists
            const existing = await ctx.db
              .query('organization_people')
              .filter(q =>
                q.and(
                  q.eq(q.field('person_id'), item.person_id),
                  q.eq(q.field('organization_id'), item.organization_id)
                )
              )
              .unique();
  
            if (existing) {
              // Update the existing relationship if it exists
              await ctx.db.patch(existing._id, {
                role: item.role,
                updated_at: now
              });
              createdIds.push(existing._id as Id<"organization_people">);
            } else {
              // Create a new relationship
              const id = await ctx.db.insert('organization_people', {
                person_id: item.person_id,
                organization_id: item.organization_id,
                role: item.role,
                updated_at: now
              });
              createdIds.push(id);
            }
          } catch (error: unknown) {
            // Capture individual item failures
            failedCreations.push({
              item,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          }
        })
      );
  
      // If all items failed, throw a ConvexError
      if (failedCreations.length === args.items.length) {
        throw new ConvexError("Failed to create all organization-people relationships");
      }
  
      // Return both successes and failures
      return {
        ids: createdIds,
        failed: failedCreations.length > 0 ? failedCreations : undefined
      };
    } catch (error: unknown) {
      // Handle unexpected errors
      throw new ConvexError(error instanceof Error ? error.message : "Failed to create organization-people relationships");
    }
  }
});
  
/**
 * Remove one or more organization-people relationships
 * 
 * @example
 * // Remove a single relationship
 * const result = await client.mutation.removeOrganizationPeopleRelationship({
 *   items: [{
 *     organization_id: "org123",
 *     person_id: "person456"
 *   }]
 * });
 * 
 * // Remove multiple relationships
 * const result = await client.mutation.removeOrganizationPeopleRelationship({
 *   items: [
 *     {
 *       organization_id: "org123",
 *       person_id: "person456"
 *     },
 *     {
 *       organization_id: "org789",
 *       person_id: "person456"
 *     }
 *   ]
 * });
 */
export const removeOrganizationPeopleRelationship = zMutation({
  args: {
    items: z.array(
      z.object({
        organization_id: zid('organizations'),
        person_id: zid('people')
      })
    )
  },
  output: z.object({
    ids: z.array(zid('organization_people')),
    failed: z.array(
      z.object({
        organization_id: zid('organizations'),
        person_id: zid('people'),
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    const deletedIds: GenericId<"organization_people">[] = [];
    const failedDeletes: any[] = [];

    await Promise.all(
      args.items.map(async (item) => {
        try {
          const existing = await ctx.db
            .query('organization_people')
            .filter(q =>
              q.and(
                q.eq(q.field('person_id'), item.person_id),
                q.eq(q.field('organization_id'), item.organization_id)
              )
            )
            .unique();

          if (existing) {
            await ctx.db.delete(existing._id as Id<"organization_people">);
            deletedIds.push(existing._id as Id<"organization_people">);
          } else {
            failedDeletes.push({
              organization_id: item.organization_id,
              person_id: item.person_id,
              error: "Relationship not found"
            });
          }
        } catch (error: unknown) {
          failedDeletes.push({
            organization_id: item.organization_id,
            person_id: item.person_id,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      })
    );

    return {
      ids: deletedIds,
      failed: failedDeletes.length > 0 ? failedDeletes : undefined
    };
  }
});

/**
 * Get organization-people relationships by their IDs
 * 
 * @example
 * // Get a single relationship
 * const relationships = await client.query.getOrganizationPeopleRelationship({
 *   ids: ["rel123"]
 * });
 * 
 * // Get multiple relationships
 * const relationships = await client.query.getOrganizationPeopleRelationship({
 *   ids: ["rel123", "rel456"]
 * });
 */
export const getOrganizationPeopleRelationship = zQuery({
  args: {
    ids: z.array(zid('organization_people'))
  },
  output: z.array(OrgPeopleRelationshipSchema),
  handler: async (ctx, args) => {
    // Get all relationships in parallel
    const relationships = await Promise.all(
      args.ids.map(async id => ctx.db.get(id))
    );

    // Filter out any null results and validate
    return relationships
      .filter((rel): rel is NonNullable<typeof rel> => rel !== null)
      .map(rel => {
        try {
          return OrgPeopleRelationshipSchema.parse(rel);
        } catch (error: unknown) {
          console.error('Failed to validate relationship:', error);
          return null;
        }
      })
      .filter((rel): rel is NonNullable<typeof rel> => rel !== null);
  }
});

/**
 * Update one or more organization-people relationships
 * 
 * @example
 * // Update a single relationship
 * const result = await client.mutation.updateOrganizationPeopleRelationship({
 *   items: [{
 *     id: "rel123",
 *     role: "Manager"
 *   }]
 * });
 * 
 * // Update multiple relationships
 * const result = await client.mutation.updateOrganizationPeopleRelationship({
 *   items: [
 *     {
 *       id: "rel123",
 *       role: "Manager"
 *     },
 *     {
 *       id: "rel456",
 *       role: "Contractor"
 *     }
 *   ]
 * });
 */
export const updateOrganizationPeopleRelationship = zMutation({
  args: {
    items: z.array(
      z.object({
        id: zid('organization_people'),
        role: z.string().optional()
      })
    )
  },
  output: z.object({
    ids: z.array(zid('organization_people')),
    failed: z.array(
      z.object({
        id: zid('organization_people'),
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    try {
      const now = Date.now();
      const updatedIds: GenericId<"organization_people">[] = [];
      const failedUpdates: any[] = [];
  
      await Promise.all(
        args.items.map(async (item) => {
          try {
            const { id, ...fields } = item;
            
            // Check if the relationship exists
            const existing = await ctx.db.get(id);
            if (!existing) {
              throw new Error(`Relationship with ID ${id} not found`);
            }
  
            // Update the relationship
            await ctx.db.patch(id, {
              ...fields,
              updated_at: now
            });
  
            updatedIds.push(id);
          } catch (error: unknown) {
            // Capture individual item failures
            failedUpdates.push({
              id: item.id,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          }
        })
      );
  
      // If all items failed, throw a ConvexError
      if (failedUpdates.length === args.items.length) {
        throw new ConvexError("Failed to update all organization-people relationships");
      }
  
      // Return both successes and failures
      return {
        ids: updatedIds,
        failed: failedUpdates.length > 0 ? failedUpdates : undefined
      };
    } catch (error: unknown) {
      // Handle unexpected errors
      throw new ConvexError(error instanceof Error ? error.message : "Failed to update organization-people relationships");
    }
  }
});
  