// directory/organizations.ts
import { z } from 'zod';
import { PersonSchema, CreatePersonSchema, UpdatePersonSchema, OrganizationSchema, OrganizationWithPeopleCountSchema, DuplicateGroupSchema } from '../../zod/directory-schema';
import { zid } from 'convex-helpers/server/zod';
import { zQuery, zMutation } from '../functions';
import Fuse from 'fuse.js';
import { Id } from "../_generated/dataModel";
import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { ConvexError } from "convex/values";

/**
 * Calculate string similarity between two strings using multiple techniques
 * Returns a value between 0 and 1, where 1 is an exact match
 */
function calculateSimilarity(a: string, b: string): number {
  if (a === b) return 1;
  if (a.length === 0 || b.length === 0) return 0;
  
  // Normalize strings for comparison
  const strA = a.toLowerCase().trim();
  const strB = b.toLowerCase().trim();
  
  // Early exact match check
  if (strA === strB) return 1;
  
  // Check if one string contains the other
  if (strA.includes(strB)) return 0.9 * (strB.length / strA.length);
  if (strB.includes(strA)) return 0.9 * (strA.length / strB.length);
  
  // Check for acronym matches (e.g., "IBM" matches "International Business Machines")
  const isAcronym = (short: string, long: string) => {
    const words = long.split(/\s+/);
    if (short.length === words.length) {
      const acronym = words.map(word => word[0]).join('');
      return short === acronym;
    }
    return false;
  };
  
  if (strA.length < strB.length && isAcronym(strA, strB)) return 0.95;
  if (strB.length < strA.length && isAcronym(strB, strA)) return 0.95;
  
  // Count matching characters
  const longer = strA.length > strB.length ? strA : strB;
  const shorter = strA.length > strB.length ? strB : strA;
  
  // Count matching characters with position bonus
  let matches = 0;
  let position_matches = 0;
  
  for (let i = 0; i < shorter.length; i++) {
    if (longer.includes(shorter[i])) {
      matches++;
      // Give bonus for characters in the same position
      if (i < longer.length && shorter[i] === longer[i]) {
        position_matches++;
      }
    }
  }
  
  // Calculate basic similarity
  const charSimilarity = matches / longer.length;
  
  // Add position bonus (up to 20% additional similarity)
  const positionBonus = (position_matches / shorter.length) * 0.2;
  
  // Calculate word overlap for multi-word names
  let wordSimilarity = 0;
  if (strA.includes(' ') && strB.includes(' ')) {
    const wordsA = new Set(strA.split(/\s+/));
    const wordsB = new Set(strB.split(/\s+/));
    
    // Count common words
    let commonWords = 0;
    for (const word of wordsA) {
      if (wordsB.has(word)) commonWords++;
    }
    
    wordSimilarity = commonWords / Math.max(wordsA.size, wordsB.size);
  }
  
  // Combine the different similarity measures
  return Math.max(charSimilarity + positionBonus, wordSimilarity);
}

/******************************************
 * ORGANIZATIONS
 ******************************************/

/**
 * Get organizations by their IDs
 *
 * @example
 * // Get a single organization
 * const organizations = await client.query.getOrganization({ ids: ["123abc"] });
 *
 * // Get multiple organizations
 * const organizations = await client.query.getOrganization({ ids: ["123abc", "456def"] });
 */
export const getOrganization = zQuery({
    args: {
      ids: z.array(zid('organizations'))
    },
    output: z.array(OrganizationSchema),
    handler: async (ctx, args) => {
      const organizations = await Promise.all(
        args.ids.map(async id => ctx.db.get(id))
      );
  
      // Filter out nulls and parse
      return organizations
        .filter((org): org is NonNullable<typeof org> => org !== null)
        .map((org) => {
          try {
            return OrganizationSchema.parse({
              ...org,
              is_vendor: org.is_vendor ?? false,
              billComVendorId: org.billComVendorId,
              updated_at: org.updated_at ?? org._creationTime
            });
          } catch (error: unknown) {
            console.error(`Failed to validate organization ${org._id}:`, error);
            return null;
          }
        })
        .filter((org): org is NonNullable<typeof org> => org !== null);
    }
  });
  
  /**
   * Create one or more organizations
   *
   * @example
   * // Create a single organization
   * const result = await client.mutation.createOrganizations({
   *   items: [{
   *     name: "Acme Inc.",
   *     description: "A company that makes everything"
   *   }]
   * });
   *
   * // Create multiple organizations
   * const result = await client.mutation.createOrganizations({
   *   items: [
   *     {
   *       name: "Acme Inc.",
   *       description: "A company that makes everything"
   *     },
   *     {
   *       name: "Globex Corporation",
   *       is_vendor: true
   *     }
   *   ]
   * });
   */
  export const createOrganizations = zMutation({
    args: {
      items: z.array(
        z.object({
          name: z.string(),
          description: z.string().optional(),
          short_description: z.string().optional(),
          research: z.string().optional(),
          parent_org_id: zid('organizations').optional(),
          is_vendor: z.boolean().optional(),
          website: z.string().optional(),
          logo_url: z.string().optional(),
          email: z.string().optional(),
          phone: z.string().optional(),
          address: z.string().optional(),
          billComVendorId: z.string().optional(),
          vendor_category: z.string().optional()
        })
      )
    },
    output: z.object({
      ids: z.array(zid('organizations')),
      failed: z.array(z.any())
    }),
    handler: async (ctx, args) => {
      const now = Date.now();
      const createdIds: Id<"organizations">[] = [];
      const failedCreations: any[] = [];
  
      await Promise.all(
        args.items.map(async (item) => {
          try {
            // Check if organization already exists with the same name
            const existing = await ctx.db
              .query('organizations')
              .withIndex('by_name')
              .filter(q => q.eq(q.field('name'), item.name))
              .unique();
  
            if (existing) {
              createdIds.push(existing._id as Id<"organizations">);
              return;
            }
  
            // Create new organization
            const id = await ctx.db.insert('organizations', {
              ...item,
              is_vendor: item.is_vendor ?? false,
              billComVendorId: item.billComVendorId,
              updated_at: now,
            });
  
            createdIds.push(id);
          } catch (error) {
            failedCreations.push({
              item,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          }
        })
      );
  
      return {
        ids: createdIds,
        failed: failedCreations
      };
    }
  });
  
  /**
   * Update one or more organizations
   *
   * @example
   * // Update a single organization
   * const result = await client.mutation.updateOrganizations({
   *   items: [{
   *     id: "123abc",
   *     updates: {
   *       name: "Acme Inc.",
   *       description: "A company that makes everything"
   *     }
   *   }]
   * });
   *
   * // Update multiple organizations
   * const result = await client.mutation.updateOrganizations({
   *   items: [
   *     { id: "123abc", updates: { name: "Acme Inc." } },
   *     { id: "456def", updates: { description: "Another company" } }
   *   ]
   * });
   */
  export const updateOrganizations = zMutation({
    args: {
      items: z.array(
        z.object({
          id: zid('organizations'),
          updates: z.object({
            name: z.string().optional(),
            description: z.string().optional(),
            short_description: z.string().optional(),
            research: z.string().optional(),
            parent_org_id: zid('organizations').optional(),
            is_vendor: z.boolean().optional(),
            website: z.string().optional(),
            logo_url: z.string().optional(),
            email: z.string().optional(),
            phone: z.string().optional(),
            address: z.string().optional(),
            billComVendorId: z.string().optional(),
            vendor_category: z.string().optional()
          })
        })
      )
    },
    output: z.object({
      ids: z.array(zid('organizations')),
      failed: z.array(z.any())
    }),
    handler: async (ctx, args) => {
      const now = Date.now();
      const updatedIds: Id<"organizations">[] = [];
      const failedUpdates: any[] = [];
  
      await Promise.all(
        args.items.map(async (item) => {
          try {
            const { id, updates } = item;
  
            // Check if the organization exists
            const existing = await ctx.db.get(id);
            if (!existing) {
              failedUpdates.push({
                _id: id,
                error: "Organization not found"
              });
              return;
            }
  
            await ctx.db.patch(id, {
              ...updates,
              updated_at: now
            });
  
            updatedIds.push(id);
          } catch (error) {
            failedUpdates.push({
              _id: item.id,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          }
        })
      );
  
      return {
        ids: updatedIds,
        failed: failedUpdates
      };
    }
  });
  
  /**
   * List all organizations with pagination and filtering options
   *
   * @example
   * // Basic usage
   * const result = await client.query.listOrganizations();
   *
   * // With pagination
   * const result = await client.query.listOrganizations({
   *   pagination: { limit: 20, sortDirection: "asc" }
   * });
   *
   * // With filtering
   * const result = await client.query.listOrganizations({
   *   filter: { isVendor: true, searchText: "Acme" }
   * });
   *
   * // Pagination with cursor for next page
   * const nextPage = await client.query.listOrganizations({
   *   pagination: { cursor: result.continueCursor }
   * });
   */
  export const listOrganizations = zQuery({
    args: {
      pagination: z.object({
        limit: z.number().optional().default(50),
        cursor: z.any().optional(),
        sortBy: z.enum(['name', 'updated_at', '_creationTime']).optional().default('_creationTime'),
        sortDirection: z.enum(['asc', 'desc']).optional().default('desc')
      }),
      filter: z.object({
        searchText: z.string().optional(),
        isVendor: z.boolean().optional(),
        parentOrgId: zid('organizations').optional()
      })
    },
    output: z.object({
      organizations: z.array(OrganizationSchema),
      continueCursor: z.any().optional()
    }),
    handler: async (ctx, args) => {
      const { pagination, filter } = args;
      const {
        limit = 50,
        cursor,
        sortBy = '_creationTime',
        sortDirection = 'desc'
      } = pagination as {
        limit?: number;
        cursor?: string;
        sortBy?: string;
        sortDirection?: 'asc' | 'desc';
      };
      const { searchText, isVendor, parentOrgId } = filter;
  
      // Build and execute query based on filters and sorting
      let results;
      
      // Different query paths based on filters and sorting
      if (parentOrgId) {
        // Query with parent organization filter
        if (sortBy === 'name') {
          results = await ctx.db.query('organizations')
            .withIndex('by_parent', q => q.eq('parent_org_id', parentOrgId))
            .order(sortDirection)
            .paginate({ cursor: cursor || null, numItems: limit });
        } else if (sortBy === 'updated_at') {
          results = await ctx.db.query('organizations')
            .withIndex('by_updated')
            .order(sortDirection)
            .filter(q => q.eq(q.field('parent_org_id'), parentOrgId))
            .paginate({ cursor: cursor || null, numItems: limit });
        } else {
          // Default to _creationTime
          results = await ctx.db.query('organizations')
            .order(sortDirection)
            .filter(q => q.eq(q.field('parent_org_id'), parentOrgId))
            .paginate({ cursor: cursor || null, numItems: limit });
        }
      } else if (sortBy === 'name') {
        // Sort by name without parent filter
        results = await ctx.db.query('organizations')
          .withIndex('by_name')
          .order(sortDirection)
          .filter(q => isVendor !== undefined ? q.eq(q.field('is_vendor'), isVendor) : true)
          .paginate({ cursor: cursor || null, numItems: limit });
      } else if (sortBy === 'updated_at') {
        // Sort by updated_at without parent filter
        results = await ctx.db.query('organizations')
          .withIndex('by_updated')
          .order(sortDirection)
          .filter(q => isVendor !== undefined ? q.eq(q.field('is_vendor'), isVendor) : true)
          .paginate({ cursor: cursor || null, numItems: limit });
      } else {
        // Default sort by _creationTime without parent filter
        results = await ctx.db.query('organizations')
          .order(sortDirection)
          .filter(q => isVendor !== undefined ? q.eq(q.field('is_vendor'), isVendor) : true)
          .paginate({ cursor: cursor || null, numItems: limit });
      }

      // Apply text search in memory if needed
      let organizations = results.page;
      if (searchText && searchText.trim() !== '') {
        const fuse = new Fuse(organizations, {
          keys: ['name', 'description', 'email'],
          threshold: 0.3
        });
        
        organizations = fuse.search(searchText).map(result => result.item);
      }

      return {
        organizations: organizations.map(org => OrganizationSchema.parse({
          ...org,
          is_vendor: org.is_vendor ?? false,
          billComVendorId: org.billComVendorId,
          updated_at: org.updated_at ?? org._creationTime
        })),
        continueCursor: results.continueCursor
      };
    }
  });
  
  /**
   * Remove one or more organizations
   *
   * @example
   * // Remove a single organization
   * const result = await client.mutation.removeOrganizations({
   *   ids: ["org123"]
   * });
   *
   * // Remove multiple organizations
   * const result = await client.mutation.removeOrganizations({
   *   ids: ["org123", "org456"]
   * });
   */
  export const removeOrganizations = zMutation({
    args: {
      ids: z.array(zid('organizations'))
    },
    output: z.object({
      ids: z.array(zid('organizations')),
      failed: z.array(z.any())
    }),
    handler: async (ctx, args) => {
      const deletedIds: Id<"organizations">[] = [];
      const failedDeletes: any[] = [];
  
      await Promise.all(
        args.ids.map(async (id) => {
          try {
            // Check if the organization exists
            const existing = await ctx.db.get(id);
            if (!existing) {
              failedDeletes.push({
                id,
                error: "Organization not found"
              });
              return;
            }
  
            // Delete the organization
            await ctx.db.delete(id);
            deletedIds.push(id);
          } catch (error) {
            failedDeletes.push({
              id,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          }
        })
      );
  
      return {
        ids: deletedIds,
        failed: failedDeletes
      };
    }
  });
  
  /**
   * Search organizations by name
   */
  export const searchOrganizations = zQuery({
    args: {
      query: z.string(),
      isVendor: z.boolean().optional()
    },
    output: z.array(OrganizationSchema),
    handler: async (ctx, args) => {
      const queryStr = args.query.toLowerCase().trim();
      if (!queryStr) return [];
  
      let dbQuery = ctx.db.query('organizations').withIndex('by_name');
  
      // Add vendor filter if specified
      if (args.isVendor !== undefined) {
        dbQuery = dbQuery.filter(q =>
          q.eq(q.field('is_vendor'), args.isVendor)
        );
      }
  
      const organizations = await dbQuery.collect();
  
      return organizations
        .filter((org) => org.name.toLowerCase().includes(queryStr))
        .slice(0, 10)
        .map(org => OrganizationSchema.parse(org));
    }
  });
  
  /**
   * Get an organization by name
   *
   * @example
   * // Find an organization by name
   * const organization = await client.query.getOrganizationByName({ name: "Acme Inc." });
   */
  export const getOrganizationByName = zQuery({
    args: {
      name: z.string()
    },
    output: z.object({
      organization: OrganizationSchema.nullable(),
      exactMatch: z.boolean()
    }),
    handler: async (ctx, args) => {
      const { name } = args;
  
      // Try to find an exact match first
      const exactMatches = await ctx.db
        .query('organizations')
        .withIndex('by_name')
        .filter(q => q.eq(q.field('name'), name))
        .collect();
  
      if (exactMatches.length > 0) {
        try {
          const org = exactMatches[0];
          const organization = OrganizationSchema.parse({
            ...org,
            is_vendor: org.is_vendor ?? false,
            billComVendorId: org.billComVendorId,
            updated_at: org.updated_at ?? org._creationTime
          });
          return { organization, exactMatch: true };
        } catch (error: unknown) {
          console.error('Failed to validate organization:', error);
        }
      }
  
      // If no exact match, try fuzzy
      const allOrgs = await ctx.db.query('organizations').collect();
      let bestMatch = null;
      let highestSimilarity = 0;
  
      for (const org of allOrgs) {
        const similarity = calculateSimilarity(org.name.toLowerCase(), name.toLowerCase());
        if (similarity > highestSimilarity && similarity > 0.8) {
          highestSimilarity = similarity;
          bestMatch = org;
        }
      }
  
      if (bestMatch) {
        try {
          const organization = OrganizationSchema.parse({
            ...bestMatch,
            is_vendor: bestMatch.is_vendor ?? false,
            billComVendorId: bestMatch.billComVendorId,
            updated_at: bestMatch.updated_at ?? bestMatch._creationTime
          });
          return { organization, exactMatch: false };
        } catch (error: unknown) {
          console.error('Failed to validate organization:', error);
        }
      }
  
      return { organization: null, exactMatch: false };
    }
  });
  
  /**
   * Get or create an organization by name
   */
  export const getOrCreateOrganization = zMutation({
    args: {
      name: z.string(),
      description: z.string().optional(),
      short_description: z.string().optional(),
      research: z.string().optional(),
      website: z.string().optional(),
      logo_url: z.string().optional(),
      email: z.string().optional(),
      phone: z.string().optional(),
      address: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      postal_code: z.string().optional()
    },
    output: z.object({
      organization: OrganizationSchema,
      created: z.boolean()
    }),
    handler: async (ctx, args) => {
      const { name, ...otherFields } = args;
      const normalizedName = name.trim().toLowerCase();
  
      // Search for existing
      const existingOrgs = await ctx.db.query("organizations").collect();
      let bestMatch = existingOrgs.find(
        org => org.name.trim().toLowerCase() === normalizedName
      );
  
      if (!bestMatch) {
        // Fuzzy match
        const matches = existingOrgs
          .map(org => ({
            org,
            similarity: calculateSimilarity(normalizedName, org.name.trim().toLowerCase())
          }))
          .filter(match => match.similarity > 0.8)
          .sort((a, b) => b.similarity - a.similarity);
  
        if (matches.length > 0) {
          bestMatch = matches[0].org;
        }
      }
  
      // If found, return existing
      if (bestMatch) {
        return {
          organization: OrganizationSchema.parse(bestMatch),
          created: false
        };
      }
  
      // Otherwise, create new
      const now = Date.now();
      const newOrg = await ctx.db.insert('organizations', {
        name,
        ...otherFields,
        updated_at: now,
        is_vendor: false
      });
  
      const organization = await ctx.db.get(newOrg);
      return {
        organization: OrganizationSchema.parse(organization!),
        created: true
      };
    }
  });
  
  // List all vendors
  export const listVendors = zQuery({
    args: {
      pagination: z.object({
        limit: z.number().optional().default(50),
        sortBy: z.enum(['name', 'updated_at']).optional().default('name'),
        sortDirection: z.enum(['asc', 'desc']).optional().default('asc'),
        cursor: z.any().optional()
      }),
      filter: z.object({
        category: z.string().optional(),
        searchText: z.string().optional()
      })
    },
    output: z.object({
      page: z.array(OrganizationSchema),
      continuationToken: z.any().optional()
    }),
    handler: async (ctx, args) => {
      const { pagination = {}, filter = {} } = args;
      const {
        limit = 50,
        sortBy = 'name',
        sortDirection = 'asc',
        cursor
      } = pagination as {
        limit?: number;
        sortBy?: string;
        sortDirection?: 'asc' | 'desc';
        cursor?: string;
      };
  
      let queryOrg = ctx.db
        .query('organizations')
        .withIndex('by_name')
        .filter(q => q.eq(q.field('is_vendor'), true));
  
      const result = await queryOrg
        .order(sortDirection)
        .paginate({
          cursor: cursor || null,
          numItems: limit
        });
  
      // In-memory filter by searchText
      let filteredPage = result.page;
      if (filter.searchText) {
        const st = filter.searchText.toLowerCase();
        filteredPage = filteredPage.filter(
          v =>
            v.name.toLowerCase().includes(st) ||
            (v.description && v.description.toLowerCase().includes(st))
        );
      }
  
      // Validate each vendor
      const validatedVendors = filteredPage
        .map(vendor => {
          try {
            return OrganizationSchema.parse({
              ...vendor,
              is_vendor: vendor.is_vendor ?? true,
              updated_at: vendor.updated_at ?? vendor._creationTime
            });
          } catch (error: unknown) {
            console.error('Failed to validate vendor:', error);
            return null;
          }
        })
        .filter((v): v is NonNullable<typeof v> => v !== null);
  
      return {
        page: validatedVendors,
        continuationToken: result.continueCursor
      };
    }
  });
  
  // List organizations with people count
  export const listOrganizationsWithPeopleCount = zQuery({
    args: {},
    output: z.array(OrganizationWithPeopleCountSchema),
    handler: async (ctx) => {
      const organizations = await ctx.db.query('organizations').collect();
      const relationships = await ctx.db.query('organization_people').collect();
  
      // Count people for each organization
      const orgCounts = new Map<Id<"organizations">, number>();
      for (const rel of relationships) {
        const orgId = rel.organization_id as Id<"organizations">;
        if (!orgCounts.has(orgId)) {
          orgCounts.set(orgId, 0);
        }
        orgCounts.set(orgId, orgCounts.get(orgId)! + 1);
      }
  
      // Create result with people count and all required fields
      const result = organizations.map(org => {
        try {
          return OrganizationWithPeopleCountSchema.parse({
            ...org,
            peopleCount: orgCounts.get(org._id as Id<"organizations">) || 0,
            updated_at: org.updated_at ?? org._creationTime,
            _creationTime: org._creationTime,
            name: org.name,
            _id: org._id as string & { __tableName: "organizations" }
          });
        } catch (error) {
          console.error(`Failed to validate organization ${org._id}:`, error);
          return null;
        }
      }).filter((org): org is NonNullable<typeof org> => org !== null);
  
      return result;
    }
  });
  
  // Get all members of an organization
  export const getOrganizationMembers = zQuery({
    args: {
      organizationId: zid('organizations')
    },
    output: z.array(PersonSchema),
    handler: async (ctx, args) => {
      const { organizationId } = args;
  
      const relationships = await ctx.db
        .query('organization_people')
        .withIndex('by_organization')
        .filter(q => q.eq(q.field('organization_id'), organizationId))
        .collect();
  
      const peopleIds = relationships.map(rel => rel.person_id);
      if (peopleIds.length === 0) {
        return [];
      }
  
      const people = await Promise.all(
        peopleIds.map(async id => ctx.db.get(id))
      );
  
      return people
        .filter((person): person is NonNullable<typeof person> => person !== null)
        .map(person => {
          try {
            return PersonSchema.parse({
              ...person,
              updated_at: person.updated_at ?? person._creationTime
            });
          } catch (error: unknown) {
            console.error(`Failed to validate person ${person._id}:`, error);
            return null;
          }
        })
        .filter((p): p is NonNullable<typeof p> => p !== null);
    }
  });
  
  /**
   * Find the best matching vendor organization by name
   * Uses multiple matching techniques to find the most likely match
   */
  export const findBestVendorMatch = action({
    args: { vendorName: v.string() },
    returns: v.object({
      id: v.union(v.id('organizations'), v.null()),
      name: v.string(),
      similarity: v.number(),
      possibleMatches: v.array(v.object({
        id: v.id('organizations'),
        name: v.string(),
        similarity: v.number()
      }))
    }),
    handler: async (ctx, args) => {
      const { vendorName } = args;
      const name = vendorName.trim();
      
      // Define vendor type for proper type checking
      interface VendorData {
        _id: Id<"organizations">,
        name: string,
        _creationTime: number,
        is_vendor: boolean,
        billComVendorId: string,
        updated_at: number
      }
      
      // Fetch all vendors from the database
      const vendors = await ctx.runQuery(api.directory.directoryOrganizations.listVendors, {
        filter: {}, // No filter to get all vendors
        pagination: { limit: 100 } // Reasonable limit to avoid performance issues
      });
      
      // Extract just the vendor data we need for matching
      const vendorData: VendorData[] = vendors.page.map((vendor: any) => ({
        _id: vendor._id,
        name: vendor.name,
        _creationTime: vendor._creationTime,
        is_vendor: vendor.is_vendor,
        billComVendorId: vendor.billComVendorId,
        updated_at: vendor.updated_at || Date.now()
      }));
      
      // Check for exact match (case-insensitive)
      const exactMatch: VendorData | undefined = vendorData.find(
        (vendor: VendorData) => vendor.name.toLowerCase() === name.toLowerCase()
      );
      
      if (exactMatch) {
        return {
          id: exactMatch._id,
          name: exactMatch.name,
          similarity: 1.0,
          possibleMatches: []
        };
      }
      
      // Find best match
      interface VendorMatch {
        id: Id<"organizations">;
        name: string;
        similarity: number;
      }
      
      const matches: VendorMatch[] = vendorData.map((org: VendorData) => ({
        id: org._id,
        name: org.name,
        similarity: calculateSimilarity(org.name.toLowerCase(), name.toLowerCase())
      }));
      
      // Sort by similarity (highest first)
      matches.sort((a, b) => b.similarity - a.similarity);
      
      // Get the best match (if any meets the threshold)
      const MATCH_THRESHOLD = 0.8;
      const bestMatch = matches.length > 0 && matches[0].similarity >= MATCH_THRESHOLD 
        ? matches[0] 
        : null;
      
      // Get other possible matches (close but not the best)
      const possibleMatches = matches
        .filter(match => match.similarity >= MATCH_THRESHOLD)
        .slice(0, 5); // Return top 5 matches
      
      return {
        id: bestMatch ? bestMatch.id : null,
        name: bestMatch ? bestMatch.name : name,
        similarity: bestMatch ? bestMatch.similarity : 0,
        possibleMatches: bestMatch ? possibleMatches.filter(m => m.id !== bestMatch.id) : possibleMatches
      };
    }
  });
  
  // Create vendor records
  export const createVendorRecords = zMutation({
    args: {
      items: z.array(
        z.object({
          name: z.string(),
          description: z.string().optional(),
          website: z.string().optional(),
          logo_url: z.string().optional(),
          email: z.string().optional(),
          phone: z.string().optional(),
          address: z.string().optional(),
          city: z.string().optional(),
          state: z.string().optional(),
          country: z.string().optional(),
          postal_code: z.string().optional()
        })
      )
    },
    output: z.array(zid('organizations')),
    handler: async (ctx, args) => {
      const now = Date.now();
      return await Promise.all(
        args.items.map(async (item) => {
          return await ctx.db.insert('organizations', {
            ...item,
            is_vendor: true,
            billComVendorId: undefined,
            updated_at: now
          });
        })
      );
    }
  });


// Query to find duplicate organizations
export const findDuplicateOrganizations = zQuery({
  args: {
    limit: z.number().optional().default(100),
    threshold: z.number().optional().default(0.7)
  },
  output: z.array(DuplicateGroupSchema),
  handler: async (ctx, args) => {
    const { limit, threshold } = args;
    // Get all organizations
    const organizations = await ctx.db.query('organizations').collect();

    // Group organizations by normalized name
    const nameGroups = new Map();
    for (const org of organizations) {
      const normalizedName = org.name.toLowerCase().trim();
      if (!nameGroups.has(normalizedName)) {
        nameGroups.set(normalizedName, []);
      }
      nameGroups.get(normalizedName).push(org);
    }

    // Filter to only groups with more than one organization
    const exactDuplicates = Array.from(nameGroups.entries())
      .filter(([_, group]) => group.length > 1)
      .map(([name, group]) => ({
        ids: group.map((org: { _id: any }) => org._id),
        name: name,
        similarity: 1
      }));

    // Find similar names using fuzzy matching
    const fuzzyGroups = new Map();
    const processedNames = new Set();

    for (let i = 0; i < organizations.length; i++) {
      const name1 = organizations[i].name.toLowerCase().trim();
      if (processedNames.has(name1)) continue;

      const similarOrgs = [organizations[i]];

      for (let j = i + 1; j < organizations.length; j++) {
        const name2 = organizations[j].name.toLowerCase().trim();
        if (processedNames.has(name2)) continue;

        const similarity = calculateSimilarity(name1, name2);
        if (similarity > threshold && similarity < 1.0) {
          similarOrgs.push(organizations[j]);
          processedNames.add(name2);
        }
      }

      if (similarOrgs.length > 1) {
        fuzzyGroups.set(name1, {
          ids: similarOrgs.map((org: { _id: any }) => org._id),
          name: name1,
          similarity: threshold
        });
        processedNames.add(name1);
      }
    }

    const fuzzyDuplicates = Array.from(fuzzyGroups.values());
    
    // Combine and limit results
    return [...exactDuplicates, ...fuzzyDuplicates].slice(0, limit);
  }
});


// Mutation to merge organizations
export const mergeOrganizations = zMutation({
  args: {
    targetId: zid('organizations'),
    sourceIds: z.array(zid('organizations'))
  },
  output: z.object({
    success: z.boolean(),
    message: z.string()
  }),
  handler: async (ctx, args) => {
    const { targetId, sourceIds } = args as { 
      targetId: Id<"organizations">, 
      sourceIds: Id<"organizations">[] 
    };

    // Get the target organization
    const targetOrg = await ctx.db.get(targetId);
    if (!targetOrg) {
      throw new Error(`Target organization with ID ${targetId} not found`);
    }

    // Get all source organizations
    const sourceOrgs = await Promise.all(
      sourceIds.map(async (id: Id<"organizations">) => ctx.db.get(id))
    );

    // Filter out any null values
    const validSourceOrgs = sourceOrgs.filter(
      (org): org is NonNullable<typeof org> => org !== undefined
    );

    // Update relationships for each source organization
    for (const sourceOrg of validSourceOrgs) {
      // Find all people relationships where the source organization is involved
      const relationships = await ctx.db
        .query('organization_people')
        .filter(q => q.eq(q.field('organization_id'), sourceOrg._id))
        .collect();

      // For each relationship, create a new one with the target organization if none exists
      for (const rel of relationships) {
        const existingRel = await ctx.db
          .query('organization_people')
          .filter(q =>
            q.and(
              q.eq(q.field('person_id'), rel.person_id),
              q.eq(q.field('organization_id'), targetId as any)
            )
          )
          .first();

        if (!existingRel) {
          await ctx.db.insert('organization_people', {
            person_id: rel.person_id,
            organization_id: targetId,
            updated_at: Date.now(),
            ...(rel.role !== undefined ? { role: rel.role } : {})
          });
        }
      }

      // Merge fields from source organization that don't exist in target
      const updateFields: Partial<typeof targetOrg> = {};
      
      if (!targetOrg.description && sourceOrg.description) {
        updateFields.description = sourceOrg.description;
      }
      
      if (!targetOrg.research && sourceOrg.research) {
        updateFields.research = sourceOrg.research;
      }
      
      if (!targetOrg.short_description && sourceOrg.short_description) {
        updateFields.short_description = sourceOrg.short_description;
      }
      
      if (!targetOrg.website && sourceOrg.website) {
        updateFields.website = sourceOrg.website;
      }
      
      if (!targetOrg.email && sourceOrg.email) {
        updateFields.email = sourceOrg.email;
      }
      
      if (!targetOrg.phone && sourceOrg.phone) {
        updateFields.phone = sourceOrg.phone;
      }
      
      if (!targetOrg.address && sourceOrg.address) {
        updateFields.address = sourceOrg.address;
      }
      
      if (Object.keys(updateFields).length > 0) {
        await ctx.db.patch(targetId, {
          ...updateFields,
          updated_at: Date.now()
        });
      }

      // Delete the source organization
      await ctx.db.delete(sourceOrg._id);
    }

    return {
      success: true,
      message: `Organizations merged successfully`
    };
  }
});
