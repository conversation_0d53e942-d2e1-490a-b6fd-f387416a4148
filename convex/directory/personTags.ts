import { z } from "zod";
import { zQuery, zMutation } from '../functions';
import { Id } from '../_generated/dataModel';
import { zid } from "convex-helpers/server/zod";
import { QueryCtx, MutationCtx } from '../_generated/server';
import { TaggableTypeSchema } from "../../zod/tags-schema";
import { api } from "../_generated/api";
// Removed paginationOptsValidator for now to fix type errors
// import { paginationOptsValidator } from 'convex/server'; 

// Define Zod schemas for pagination
const PaginationSchema = z.object({
  cursor: z.string().optional(),
  numItems: z.number().optional().default(50)
});

// Import the TagSchema for output types
import { TagSchema } from "../../zod/tags-schema";

// --- Query to list tags for a specific person ---
export const listTagsByPerson = zQuery({
  args: {
    personId: zid('people'),
    paginationOpts: PaginationSchema.optional()
  },
  output: z.array(TagSchema),
  handler: async (ctx: QueryCtx, args): Promise<z.infer<typeof TagSchema>[]> => {
    // Use the consolidated getTagsForTaggable function
    return ctx.runQuery(api.tags.getTagsForTaggable, {
      taggable_type: "person",
      taggable_id: args.personId
    });
  },
});

// --- Mutation to add a tag to a person ---
export const addTagToPerson = zMutation({
  args: {
    personId: zid('people'),
    tagId: zid('tags')
  },
  output: z.object({
    id: zid('taggings')
  }),
  handler: async (ctx: MutationCtx, args): Promise<{ id: Id<"taggings"> }> => {
    // Use the consolidated addTagToTaggable function
    const newTagId = await ctx.runMutation(api.tags.addTagToTaggable, {
      taggable_type: "person",
      taggable_id: args.personId,
      tagId: args.tagId
    });

    return { id: newTagId as Id<"taggings"> };
  },
});

// --- Mutation to remove a tag from a person ---
export const removeTagFromPerson = zMutation({
  args: {
    personId: zid('people'),
    tagId: zid('tags')
  },
  output: z.object({
    success: z.boolean(),
    message: z.string().optional()
  }),
  handler: async (ctx: MutationCtx, args): Promise<{ success: boolean; message?: string }> => {
    // Use the consolidated removeTagFromTaggable function
    return ctx.runMutation(api.tags.removeTagFromTaggable, {
      taggable_type: "person",
      taggable_id: args.personId,
      tagId: args.tagId
    });
  },
});
