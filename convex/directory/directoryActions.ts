import { action } from "../_generated/server";
import { api } from "../_generated/api";
import { v } from "convex/values";
import { ActionCtx } from "../_generated/server";
import { DirectorySearchResultItem } from "../../zod/directory-schema";

/**
 * Action to perform a directory search for people and teams imperatively.
 * Wraps the searchPeopleAndTeams query.
 * 
 * Supports searching for:
 * - Users (people with user_id)
 * - Non-user people (people without user_id, for Requestor role)
 * - Teams
 */
export const searchDirectoryAction = action({
  args: {
    search: v.optional(v.string()),
    includeNonUsers: v.optional(v.boolean()),
    limit: v.optional(v.number()),
    type: v.optional(v.union(v.literal('all'), v.literal('user'), v.literal('person'), v.literal('team'))),
  },
  handler: async (
    ctx: ActionCtx,
    args: { search?: string; includeNonUsers?: boolean; limit?: number; type?: 'all' | 'user' | 'person' | 'team' }
  ): Promise<DirectorySearchResultItem[]> => {
    // Determine if we should include non-user people based on args or default to true
    const includeNonUsers = args.includeNonUsers ?? true;
    
    // Call the internal query using the provided arguments
    const results = await ctx.runQuery(api.directory.directory.searchPeopleAndTeams, {
      search: args.search,
      includeNonUsers: includeNonUsers,
      limit: args.limit ?? 25, // Use 25 if limit is not provided
      type: args.type ?? 'all', // Use 'all' if type is not provided
    });

    // Check if the initial search returned no results and if a fallback is feasible
    if (results.length === 0 && args.search && args.search.length > 1) {
      console.log(`Initial search for "${args.search}" yielded no results. Falling back to first letter search.`);
      // Perform fallback search using only the first letter and a larger limit
      const fallbackResults = await ctx.runQuery(api.directory.directory.searchPeopleAndTeams, {
        search: args.search.substring(0, 1), // Use only the first letter
        includeNonUsers: includeNonUsers,
        limit: 100, // Increase limit for fallback
        type: args.type ?? 'all',
      });
      console.log(`Fallback search for "${args.search.substring(0, 1)}" returned ${fallbackResults.length} results.`);
      
      // Ensure the results match the expected type
      return fallbackResults as DirectorySearchResultItem[];
    }

    // Return initial results if they exist or if fallback wasn't triggered
    return results;
  },
});
