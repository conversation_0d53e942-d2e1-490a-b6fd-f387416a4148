// directory/people.ts
import { z } from 'zod';
import { PersonSchema, CreatePersonSchema, UpdatePersonSchema } from '../../zod/directory-schema';
import { zid } from 'convex-helpers/server/zod';
import { zQuery, zMutation } from '../functions';
import { ConvexError } from 'convex/values';
import { Id } from '../_generated/dataModel';

/**
 * Get a person by their exact name
 * 
 * @example
 * // Get a person by name
 * const person = await client.query.getPersonByName({ name: "<PERSON>" });
 */
export const getPersonByName = zQuery({
  args: {
    name: z.string(),
  },
  output: PersonSchema.optional(),
  handler: async (ctx, { name }) => {
    // Get the person by name using the by_name index
    const person = await ctx.db
      .query('people')
      .withIndex('by_name', (q) => q.eq('name', name))
      .unique();

    if (!person) {
      return undefined;
    }

    return PersonSchema.parse(person);
  },
});

/**
 * Get a single person by ID
 *
 * @example
 * // Get a single person
 * const person = await client.query.getPerson({ id: "123abc" });
 */
export const getPerson = zQuery({
  args: {
    id: zid('people'),
  },
  output: PersonSchema,
  handler: async (ctx, { id }) => {
    // Get the person
    const person = await ctx.db.get(id);

    // Here, we'll throw an error if not found.
    if (!person) {
      throw new Error(`Person with ID ${id} not found`);
    }

    // Ensure updated_at is present and validate the person
    return PersonSchema.parse({
      ...person
    });
  },
});

/**
 * Get a single person by User ID
 */
export const getPersonByUserId = zQuery({
  args: {
    userId: zid('users'),
  },
  output: PersonSchema.optional(),
  handler: async (ctx, { userId }) => {
    const person = await ctx.db
      .query('people')
      .withIndex('by_userId', (q) => q.eq('user_id', userId))
      .unique();

    if (!person) {
      return undefined;
    }

    return PersonSchema.parse(person);
  },
});

/**
 * Create one or more people
 *
 * @example
 * // Create a single person
 * const result = await client.mutation.createPeople({
 *   items: [{
 *     name: "John",
 *     email: "<EMAIL>",
 *     description: "Description of John",
 *     research: "Research information about John"
 *   }]
 * });
 *
 * // Create multiple people
 * const result = await client.mutation.createPeople({
 *   items: [
 *     {
 *       name: "John",
 *       email: "<EMAIL>"
 *     },
 *     {
 *       name: "Jane",
 *       email: "<EMAIL>",
 *       description: "Description of Jane",
 *       research: "Research information about Jane"
 *     }
 *   ]
 * });
 */
export const createPeople = zMutation({
  args: {
    items: z.array(CreatePersonSchema)
  },
  output: z.object({
    ids: z.array(zid('people')),
    failed: z.array(
      z.object({
        item: CreatePersonSchema,
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    const now = Date.now();
    const createdIds: Id<"people">[] = [];
    const failedCreations: { item: z.infer<typeof CreatePersonSchema>, error: string }[] = [];

    await Promise.all(
      args.items.map(async (item) => {
        try {
          // Check if person already exists with the same email
          if (item.email) {
            const existing = await ctx.db
              .query('people')
              .withIndex('by_email')
              .filter(q => q.eq(q.field('email'), item.email))
              .first();

            if (existing) {
              createdIds.push(existing._id as Id<"people">);
              return;
            }
          }

          // Validate the item before insertion
          const validatedItem = CreatePersonSchema.parse(item);

          // Create new person - updated_at is optional
          const id = await ctx.db.insert('people', {
            ...validatedItem
          });

          // Validate the created person
          const created = await ctx.db.get(id);
          if (!created) throw new Error('Failed to create person');
          
          PersonSchema.parse(created);
          createdIds.push(id);
        } catch (error: unknown) {
          failedCreations.push({
            item,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      })
    );

    return {
      ids: createdIds,
      ...(failedCreations.length > 0 ? { failed: failedCreations } : {})
    };
  },
});

/**
 * Update one or more people
 *
 * @example
 * // Update a single person
 * const result = await client.mutation.updatePeople({
 *   items: [{
 *     id: "123abc",
 *     name: "John Updated",
 *     description: "Updated description",
 *     research: "Updated research information"
 *   }]
 * });
 *
 * // Update multiple people
 * const result = await client.mutation.updatePeople({
 *   items: [
 *     {
 *       id: "123abc",
 *       name: "John Updated"
 *     },
 *     {
 *       id: "456def",
 *       name: "Smith Updated",
 *       description: "Updated description for Smith",
 *       research: "Updated research information for Smith"
 *     }
 *   ]
 * });
 */
export const updatePeople = zMutation({
  args: {
    items: z.array(UpdatePersonSchema)
  },
  output: z.object({
    ids: z.array(zid('people')),
    failed: z.array(
      z.object({
        item: UpdatePersonSchema,
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    const updatedIds: Id<"people">[] = [];
    const failedUpdates: { item: z.infer<typeof UpdatePersonSchema>, error: string }[] = [];

    await Promise.all(
      args.items.map(async (item) => {
        try {
          const { id, ...fields } = item;
          
          // Check if person exists and validate current state
          const existing = await ctx.db.get(id);
          if (!existing) {
            throw new Error(`Person with ID ${id} not found`);
          }
          PersonSchema.parse(existing);

          // Validate the update fields
          const validatedFields = UpdatePersonSchema.omit({ id: true }).parse(fields);

          // Update person - updated_at is optional
          await ctx.db.patch(id, {
            ...validatedFields
          });

          // Validate the updated person
          const updated = await ctx.db.get(id);
          if (!updated) throw new Error('Failed to update person');
          
          PersonSchema.parse(updated);
          updatedIds.push(id);
        } catch (error: unknown) {
          failedUpdates.push({
            item,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      })
    );

    return {
      ids: updatedIds,
      ...(failedUpdates.length > 0 ? { failed: failedUpdates } : {})
    };
  },
});

/**
 * List all people with pagination and filtering options
 *
 * @example
 * // List all people
 * const people = await client.query.listPeople({});
 *
 * // List people with sorting
 * const people = await client.query.listPeople({
 *   sortBy: "name",
 *   sortDirection: "asc"
 * });
 *
 * // List people with filtering
 * const people = await client.query.listPeople({
 *   filter: "John"
 * });
 */
export const listPeople = zQuery({
  args: {
    sortBy: z.enum(['name', 'email', 'updated_at', '_creationTime']).optional(),
    sortDirection: z.enum(['asc', 'desc']).optional().default('desc'),
    filter: z.string().optional(),
    limit: z.number().optional().default(50),
    cursor: z.any().optional()
  },
  output: z.object({
    people: z.array(PersonSchema),
    continueCursor: z.string().optional()
  }),
  handler: async (ctx, args) => {
    const { sortBy, sortDirection, filter, limit, cursor } = args;
    let results;

    // If there's a search filter, use search index
    if (filter?.trim()) {
      results = await ctx.db
        .query('people')
        .withSearchIndex('search_name', q => q.search('name', filter.trim()))
        .paginate({ cursor, numItems: limit });
    } 
    // Otherwise use appropriate index for sorting
    else if (sortBy === 'name') {
      results = await ctx.db
        .query('people')
        .withIndex('by_name')
        .order(sortDirection)
        .paginate({ cursor, numItems: limit });
    } else if (sortBy === 'email') {
      results = await ctx.db
        .query('people')
        .withIndex('by_email')
        .order(sortDirection)
        .paginate({ cursor, numItems: limit });
    } else if (sortBy === 'updated_at') {
      // For updated_at sorting, fall back to _creationTime if updated_at is not present
      results = await ctx.db
        .query('people')
        .withIndex('by_updated')
        .order(sortDirection)
        .paginate({ cursor, numItems: limit });
    } else {
      // Default to _creationTime
      results = await ctx.db
        .query('people')
        .order(sortDirection)
        .paginate({ cursor, numItems: limit });
    }

    // Validate results
    const validatedPeople = results.page
      .filter((person): person is NonNullable<typeof person> => person !== null)
      .map(person => {
        try {
          return PersonSchema.parse({
            ...person
          });
        } catch (error) {
          console.error("Failed to parse person:", error);
          return null;
        }
      })
      .filter((person): person is NonNullable<typeof person> => person !== null);

    return {
      people: validatedPeople,
      continueCursor: results.continueCursor
    };
  },
});

/**
 * Delete one or more people
 *
 * @example
 * // Delete a single person
 * const result = await client.mutation.deletePeople({
 *   ids: ["123abc"]
 * });
 *
 * // Delete multiple people
 * const result = await client.mutation.deletePeople({
 *   ids: ["123abc", "456def"]
 * });
 */
export const deletePeople = zMutation({
  args: {
    ids: z.array(zid('people'))
  },
  output: z.object({
    ids: z.array(zid('people')),
    failed: z.array(
      z.object({
        id: zid('people'),
        error: z.string()
      })
    ).optional()
  }),
  handler: async (ctx, args) => {
    const removedIds: Id<"people">[] = [];
    const failedRemovals: { id: Id<"people">, error: string }[] = [];

    await Promise.all(
      args.ids.map(async (id) => {
        try {
          // Check if person exists
          const existing = await ctx.db.get(id);
          if (!existing) {
            throw new Error(`Person with ID ${id} not found`);
          }

          // Delete person
          await ctx.db.delete(id);
          removedIds.push(id);
        } catch (error: unknown) {
          failedRemovals.push({
            id,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      })
    );

    // Return both successes and failures
    return {
      ids: removedIds,
      ...(failedRemovals.length > 0 ? { failed: failedRemovals } : {})
    };
  },
});


/**
 * Find duplicate people based on name and email similarity
 *
 * @example
 * // Find all duplicate people
 * const duplicates = await client.query.findDuplicatePeople({});
 */
export const findDuplicatePeople = zQuery({
  args: {
    limit: z.number().optional().default(100),
    threshold: z.number().optional().default(0.7),
    cursor: z.any().optional()
  },
  output: z.object({
    duplicates: z.array(
      z.object({
        ids: z.array(zid('people')),
        name: z.string(),
        similarity: z.number()
      })
    ),
    continueCursor: z.any().optional()
  }),
  handler: async (ctx, args) => {
    const { limit, threshold, cursor } = args;
    
    // Get people with pagination to avoid loading too many records
    const results = await ctx.db
      .query('people')
      .withIndex('by_name')
      .order('asc')
      .paginate({ cursor: cursor || null, numItems: limit });
    
    // Ensure we have valid people records with proper typing
    const people = results.page
      .filter((p): p is NonNullable<typeof p> => p !== null)
      .map(person => PersonSchema.parse(person)); // Parse each person through the schema
    
    // Group potential duplicates
    const duplicateGroups: { 
      ids: Id<"people">[],
      name: string,
      similarity: number
    }[] = [];
    
    // Compare each person with every other person
    for (let i = 0; i < people.length; i++) {
      const person1 = people[i];
      
      for (let j = i + 1; j < people.length; j++) {
        const person2 = people[j];
        
        let similarity = 0;
        
        // Check exact email match first
        if (person1.email && person2.email && person1.email.toLowerCase() === person2.email.toLowerCase()) {
          similarity = 1.0;
        } else {
          // Calculate name similarity using a more robust method
          const name1Words = new Set(person1.name.toLowerCase().split(/\s+/).filter(Boolean));
          const name2Words = new Set(person2.name.toLowerCase().split(/\s+/).filter(Boolean));
          
          const intersection = new Set([...name1Words].filter(x => name2Words.has(x)));
          const union = new Set([...name1Words, ...name2Words]);
          
          similarity = intersection.size / union.size;
        }
        
        // If similarity is high enough, consider as potential duplicate
        if (similarity > threshold) {
          duplicateGroups.push({
            ids: [person1._id, person2._id] as Id<"people">[],
            name: person1.name,
            similarity
          });
        }
      }
    }
    
    return {
      duplicates: duplicateGroups,
      continueCursor: results.continueCursor
    };
  },
});

/**
 * Merge duplicate people
 *
 * @example
 * // Merge two people, keeping the first one
 * const result = await client.mutation.mergePeople({
 *   primaryId: "123abc",
 *   secondaryIds: ["456def"]
 * });
 */
export const mergePeople = zMutation({
  args: {
    primaryId: zid('people'),
    secondaryIds: z.array(zid('people'))
  },
  output: z.object({
    success: z.boolean(),
    primaryId: zid('people'),
    mergedIds: z.array(zid('people')),
    failed: z.array(z.object({
      id: zid('people'),
      error: z.string()
    })).optional()
  }),
  handler: async (ctx, args) => {
    const { primaryId, secondaryIds } = args;
    const now = Date.now();
    const mergedIds: Id<"people">[] = [];
    const failedMerges: { id: Id<"people">, error: string }[] = [];

    // Get and validate primary person
    const primaryPerson = await ctx.db.get(primaryId);
    if (!primaryPerson) {
      throw new ConvexError({ message: `Primary person with ID ${primaryId} not found` });
    }
    PersonSchema.parse(primaryPerson);

    // Process each secondary person
    await Promise.all(
      secondaryIds.map(async (secondaryId) => {
        try {
          // Skip if trying to merge with self
          if (secondaryId === primaryId) {
            throw new ConvexError({ message: "Cannot merge a person with themselves" });
          }
          
          // Get and validate secondary person
          const secondaryPerson = await ctx.db.get(secondaryId);
          if (!secondaryPerson) {
            throw new ConvexError({ message: `Secondary person with ID ${secondaryId} not found` });
          }
          PersonSchema.parse(secondaryPerson);
          
          // Update relationships to point to primary person
          const relationships = await ctx.db
            .query('organization_people')
            .withIndex('by_person')
            .filter(q => q.eq(q.field('person_id'), secondaryId))
            .collect();
          
          await Promise.all(relationships.map(async (relationship) => {
            const existingRelationship = await ctx.db
              .query('organization_people')
              .withIndex('by_person_org')
              .filter(q => 
                q.and(
                  q.eq(q.field('person_id'), primaryId),
                  q.eq(q.field('organization_id'), relationship.organization_id)
                )
              )
              .unique();
            
            if (!existingRelationship) {
              // Create new relationship for primary person
              await ctx.db.insert('organization_people', {
                organization_id: relationship.organization_id,
                person_id: primaryId,
                role: relationship.role,
                updated_at: now
              });
            }
            
            await ctx.db.delete(relationship._id);
          }));
          
          // Update notes to point to primary person
          const notes = await ctx.db
            .query('directory_notes')
            .withIndex('by_subject')
            .filter(q => 
              q.and(
                q.eq(q.field('subject_type'), 'PERSON'),
                q.eq(q.field('subject_id'), secondaryId)
              )
            )
            .collect();
          
          await Promise.all(notes.map(async (note) => {
            // Create new note for primary person
            await ctx.db.insert('directory_notes', {
              subject_type: 'PERSON',
              subject_id: primaryId,
              content: note.content,
              updated_at: now
            });
            
            await ctx.db.delete(note._id);
          }));
          
          // Merge fields from secondary person that don't exist in primary
          const updateFields: Partial<typeof primaryPerson> = {};
          if (!primaryPerson.email && secondaryPerson.email) {
            updateFields.email = secondaryPerson.email;
          }
          if (!primaryPerson.phone && secondaryPerson.phone) {
            updateFields.phone = secondaryPerson.phone;
          }
          if (!primaryPerson.title && secondaryPerson.title) {
            updateFields.title = secondaryPerson.title;
          }
          if (!primaryPerson.description && secondaryPerson.description) {
            updateFields.description = secondaryPerson.description;
          }
          if (!primaryPerson.research && secondaryPerson.research) {
            updateFields.research = secondaryPerson.research;
          }
          if (!primaryPerson.short_description && secondaryPerson.short_description) {
            updateFields.short_description = secondaryPerson.short_description;
          }
          
          if (Object.keys(updateFields).length > 0) {
            await ctx.db.patch(primaryId, updateFields);
          }
          
          await ctx.db.delete(secondaryId);
          
          mergedIds.push(secondaryId);
        } catch (error: unknown) {
          failedMerges.push({
            id: secondaryId,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      })
    );
    
    return {
      success: mergedIds.length > 0,
      primaryId,
      mergedIds,
      ...(failedMerges.length > 0 ? { failed: failedMerges } : {})
    };
  },
});
