import { z } from "zod";
import { zQuery, zMutation } from "../functions";
import { TeamFilterSchema, CreateTeamSchema, UpdateTeamsSchema, TeamOutputSchema, TeamOutput, TeamWithMembersOutputSchema, TeamWithMembersOutput } from "../../zod/teams-schema";
import { ConvexError } from "convex/values";
import { Id } from "../_generated/dataModel";

/**
 * Lists teams with optional filtering
 *
 * @example
 * // List all teams
 * const teams = await client.query.listTeams({});
 *
 * // List teams with filters
 * const teams = await client.query.listTeams({
 *   filter: { slug: "engineering" }
 * });
 */
export const listTeams = zQuery({
  args: {
    filter: TeamFilterSchema.optional(),
  },
  output: z.array(TeamOutputSchema),
  handler: async (ctx, args) => {
    // Since we have a small number of teams, we can fetch all and filter in memory
    const teams = await ctx.db.query("teams").collect();
    
    // Transform and validate each team
    const validatedTeams = teams.map(team => TeamOutputSchema.parse({
      _id: team._id.toString(),
      _creationTime: team._creationTime,
      name: team.name,
      slug: team.slug,
      description: team.description
    }));

    // Apply any filters in memory
    if (args.filter) {
      return validatedTeams.filter(team => {
        if (args.filter?.slug && team.slug !== args.filter.slug) {
          return false;
        }
        if (args.filter?.searchText && team.name !== args.filter.searchText) {
          return false;
        }
        return true;
      });
    }

    return validatedTeams;
  }
});

/**
 * Lists teams with their members
 * Returns teams with a nested array of user information
 */
export const listTeamsWithMembers = zQuery({
  args: {
    filter: TeamFilterSchema.optional(),
  },
  output: z.array(TeamWithMembersOutputSchema),
  handler: async (ctx, args) => {
    // Get all teams since we know it's a small dataset
    const teams = await ctx.db.query("teams").collect();

    const teamsWithMembers = await Promise.all(
      teams.map(async (team) => {
        const teamMembers = await ctx.db
          .query("team_members")
          .withIndex("by_team", q => q.eq("team_id", team._id))
          .collect();

        const userIds = teamMembers.map(member => member.user_id);
        
        const [users, people] = await Promise.all([
          Promise.all(userIds.map(userId => ctx.db.get(userId))),
          Promise.all(userIds.map(userId => 
            ctx.db.query("people")
              .filter(q => q.eq(q.field("user_id"), userId))
              .unique()
          ))
        ]);

        const members = users
          .map((user, index) => {
            if (!user) return null;
            const person = people[index];
            return { 
              _id: user._id,
              email: user.email,
              name: person?.name ?? user.name,
              image: person?.image ?? user.image,
              role: user.roles?.[0]
            };
          })
          .filter((m): m is NonNullable<typeof m> => m !== null);

        return TeamWithMembersOutputSchema.parse({
          _id: team._id.toString(),
          _creationTime: team._creationTime,
          name: team.name,
          slug: team.slug,
          description: team.description,
          members
        });
      })
    );

    // Apply any filters in memory
    if (args.filter) {
      return teamsWithMembers.filter(team => {
        if (args.filter?.slug && team.slug !== args.filter.slug) {
          return false;
        }
        if (args.filter?.searchText && team.name !== args.filter.searchText) {
          return false;
        }
        return true;
      });
    }

    return teamsWithMembers;
  }
});

/**
 * Creates a single team
 * @example
 * const { id } = await client.mutation.createTeam({
 *   name: "Engineering",
 *   description: "Product development team",
 *   slug: "engineering"
 * });
 */
export const createTeam = zMutation({
  args: {
    name: CreateTeamSchema.shape.name,
    description: CreateTeamSchema.shape.description,
    slug: CreateTeamSchema.shape.slug,
  },
  handler: async (ctx, args) => {
    const { name, description, slug } = args;

    // If slug is provided, check for uniqueness
    if (slug) {
      const existingTeam = await ctx.db
        .query("teams")
        .withIndex("by_slug", q => q.eq("slug", slug))
        .unique();

      if (existingTeam) {
        throw new ConvexError({ message: `Team with slug "${slug}" already exists` });
      }
    }

    // Check for duplicate team names
    const existingTeamWithName = await ctx.db
      .query("teams")
      .withIndex("by_name", q => q.eq("name", name))
      .unique();

    if (existingTeamWithName) {
      throw new ConvexError({ message: `Team with name "${name}" already exists` });
    }

    // Create the team
    const teamId = await ctx.db.insert("teams", {
      name,
      description,
      ...(slug && { slug }),
    });

    return { id: teamId };
  },
});

/**
 * Updates one or multiple teams
 * 
 * @description
 * This mutation allows updating one or more teams in a single operation.
 * For each team, you can update any combination of name, slug, and description.
 * The operation is atomic - if any update fails, none of the updates will be applied.
 * 
 * @example
 * // Update a single team
 * await client.mutation.updateTeams({
 *   updates: [{
 *     teamId: "team_id",
 *     data: { name: "New Name" }
 *   }]
 * });
 * 
 * // Update multiple teams
 * await client.mutation.updateTeams({
 *   updates: [
 *     { teamId: "team1_id", data: { name: "Team 1 New Name" } },
 *     { teamId: "team2_id", data: { description: "New description" } }
 *   ]
 * });
 */
export const updateTeams = zMutation({
  args: {
    updates: UpdateTeamsSchema.shape.updates,
  },
  handler: async (ctx, args) => {
    const results: { teamId: Id<"teams">; success: boolean }[] = [];

    // Validate all updates before applying any changes
    await Promise.all(args.updates.map(async (update) => {
      const { teamId, data } = update;

      // Check if team exists
      const existingTeam = await ctx.db.get(teamId);
      if (!existingTeam) {
        throw new ConvexError({ 
          message: `Team with ID "${teamId}" not found`,
          code: "NOT_FOUND",
          teamId 
        });
      }

      // If updating name, check for duplicates
      if (data.name !== undefined) {
        const teamWithName = await ctx.db
          .query("teams")
          .withIndex("by_name", q => q.eq("name", data.name ?? ""))
          .filter(q => q.neq(q.field("_id"), teamId)) // Exclude current team
          .unique();

        if (teamWithName) {
          throw new ConvexError({ 
            message: `Team with name "${data.name}" already exists`,
            code: "DUPLICATE_NAME",
            teamId,
            name: data.name 
          });
        }
      }

      // If updating slug, check for duplicates
      if (data.slug !== undefined) {
        const teamWithSlug = await ctx.db
          .query("teams")
          .withIndex("by_slug", q => q.eq("slug", data.slug))
          .filter(q => q.neq(q.field("_id"), teamId)) // Exclude current team
          .unique();

        if (teamWithSlug) {
          throw new ConvexError({ 
            message: `Team with slug "${data.slug}" already exists`,
            code: "DUPLICATE_SLUG",
            teamId,
            slug: data.slug 
          });
        }
      }
    }));

    // Apply all updates
    await Promise.all(args.updates.map(async (update) => {
      const { teamId, data } = update;
      
      // Create update object with only defined values
      const updateData: Record<string, string> = {};
      if (typeof data.name === 'string') updateData.name = data.name;
      if (typeof data.slug === 'string') updateData.slug = data.slug;
      if (typeof data.description === 'string') updateData.description = data.description;
      
      // Update the team
      await ctx.db.patch(teamId, updateData);
      
      results.push({
        teamId,
        success: true
      });
    }));

    return { results };
  }
});

/**
 * Deletes a single team by ID.
 *
 * @param id - The unique ID of the team to delete.
 * @returns void
 * @throws {ConvexError} If the team with the given ID is not found.
 */
export const deleteTeam = zMutation({
  args: {
    id: z.string(),
  },
  handler: async (ctx, args) => {
    const teamId = args.id as Id<"teams">;

    // Check if the team exists
    const team = await ctx.db.get(teamId);
    if (!team) {
      throw new ConvexError({ message: `Team with ID "${args.id}" not found` });
    }

    // Optional: Handle related data (e.g., remove team members)
    const teamMembers = await ctx.db
      .query("team_members")
      .withIndex("by_team", (q) => q.eq("team_id", teamId))
      .collect();

    await Promise.all(teamMembers.map((member) => ctx.db.delete(member._id)));

    // Delete the team
    await ctx.db.delete(teamId);
  },
});
