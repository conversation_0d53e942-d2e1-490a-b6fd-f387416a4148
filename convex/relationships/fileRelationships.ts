// convex/relationships/fileRelationships.ts
import { v } from 'convex/values';
import { query } from '../_generated/server';
import { Id } from '../_generated/dataModel';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { FileSchema } from '../../zod/files-schema'; // Assuming this schema exists
import { paginationOptsValidator } from "convex/server";

// Define the allowed subject types based on the schema definition
const SubjectTypeSchema = v.union(
  v.literal("task"),
  v.literal("project"),
  v.literal("decision"),
  v.literal("organization"),
  v.literal("bills"),
  v.literal("users"),
  v.literal("people"),
  v.literal("files"),
  v.literal("tags")
);

// Define the subject ID type based on the subject types
const SubjectIdSchema = v.union(
  v.id("tasks"),
  v.id("projects"),
  v.id("decisions"),
  v.id("organizations"),
  v.id("bills"),
  v.id("users"),
  v.id("people"),
  v.id("files"),
  v.id("tags")
);

export const listFilesBySubject = query({
  args: {
    subject_type: SubjectTypeSchema,
    subject_id: SubjectIdSchema,
    paginationOpts: paginationOptsValidator,
  },
  handler: async (ctx, args) => {
    // 1. Find all relationship entries matching the subject with pagination
    const relationships = await ctx.db
      .query('file_relationships')
      .withIndex('by_subject', (q) =>
        q.eq('subject_type', args.subject_type)
      )
      .filter((q) => q.eq(q.field('subject_id'), args.subject_id))
      .paginate(args.paginationOpts);

    // 2. Extract the file IDs from the current page
    const fileIds = relationships.page.map((rel) => rel.file_id);

    if (fileIds.length === 0) {
      return {
        page: [],
        isDone: relationships.isDone,
        continueCursor: relationships.continueCursor
      }; 
    }

    // 3. Fetch the corresponding file documents
    const files = await Promise.all(
      fileIds.map((fileId) => ctx.db.get(fileId))
    );

    // 4. Filter out any null results and validate
    const validFiles = files
      .filter((file): file is NonNullable<typeof file> => file !== null)
      .map(file => {
        try {
          // Assuming FileSchema exists and validates the structure
          // Add necessary fields if FileSchema expects them (like _id, _creationTime)
          return FileSchema.parse({
            ...file,
            _id: file._id,
            _creationTime: file._creationTime,
            // Ensure all fields required by FileSchema are present
          });
        } catch (error) {
          console.error(`Failed to validate file ${file._id}:`, error);
          return null; // Skip invalid files
        }
      })
      .filter((file): file is NonNullable<typeof file> => file !== null);

    // TODO: Add sorting if needed

    return {
      page: validFiles,
      isDone: relationships.isDone,
      continueCursor: relationships.continueCursor
    };
  },
});

// Add other relationship-related functions here if needed in the future
// e.g., addFileRelationship, removeFileRelationship
