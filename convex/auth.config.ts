/**
 * Convex Auth Configuration
 * This file configures authentication providers for Convex
 */

export default {
  providers: [
    {
      domain: process.env.NEXT_PUBLIC_CONVEX_SITE_URL,
      applicationID: 'convex'
    },
    {
      // Google OAuth provider configuration
      domain: 'https://accounts.google.com',
      applicationID: 'google'
    }
  ],
  // Set the cookie options for auth sessions
  cookieOptions: {
    // <PERSON><PERSON> will expire after 30 days
    maxAge: 60 * 60 * 24 * 30, // 30 days in seconds
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const
  }
};
