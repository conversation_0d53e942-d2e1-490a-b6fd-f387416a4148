import { v } from 'convex/values';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zQuery } from './functions';
import { getAuthUserId } from '@convex-dev/auth/server';
import { Id } from './_generated/dataModel';
import { query } from './_generated/server';
import { Doc } from './_generated/dataModel';

// Common interface for fields shared across entities
interface BaseEntity {
  _id: Id<any>; // Using any as a generic type parameter for Id
  updated_at?: number;
}

// Entity type definitions
type ProjectEntity = BaseEntity & { entityType: 'project' };
type TaskEntity = BaseEntity & { entityType: 'task'; status?: string; due_date?: number; driver?: Id<'users'>; contributors?: (Id<'users'> | Id<'teams'>)[]; };
type DecisionEntity = BaseEntity & { entityType: 'decision'; status?: string; due_date?: number; driver?: Id<'users'>; contributors?: (Id<'users'> | Id<'teams'>)[]; };
type FileEntity = BaseEntity & { entityType: 'file' };
type PersonEntity = BaseEntity & { entityType: 'person' };
type OrganizationEntity = BaseEntity & { entityType: 'organization' };
type BillEntity = BaseEntity & { entityType: 'bill' };

// Combined entity type
type Entity = ProjectEntity | TaskEntity | DecisionEntity | FileEntity | PersonEntity | OrganizationEntity | BillEntity;

// Type for informed items
type InformedItem = BaseEntity & ({ 
  itemType: 'task'; 
  informed?: (Id<'users'> | Id<'teams'>)[]; 
  updated_at?: number;
} | { 
  itemType: 'decision'; 
  informed?: (Id<'users'> | Id<'teams'>)[]; 
  updated_at?: number;
});

/**
 * Get recently updated entities across all tables
 * 
 * This query retrieves the most recently updated entities from various tables
 * (projects, tasks, decisions, files, people, organizations, bills)
 * and returns them sorted by updated_at timestamp.
 */
export const getRecentlyUpdatedEntities = query({
  args: {
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const { limit = 10 } = args;
    
    // Get current user ID for access control
    const userId = await getAuthUserId(ctx);
    if (!userId) return { entities: [] };
    
    // Fetch entities from different tables
    const [projects, tasks, decisions, files, people, organizations, bills] = await Promise.all([
      ctx.db.query('projects').collect(),
      ctx.db.query('tasks').collect(),
      ctx.db.query('decisions').collect(),
      ctx.db.query('files').collect(),
      ctx.db.query('people').collect(),
      ctx.db.query('organizations').collect(),
      ctx.db.query('bills').collect()
    ]);
    
    // Combine and transform entities
    const allEntities: Entity[] = [
      ...projects.map(p => ({ ...p, entityType: 'project' as const })),
      ...tasks.map(t => ({ ...t, entityType: 'task' as const })),
      ...decisions.map(d => ({ ...d, entityType: 'decision' as const })),
      ...files.map(f => ({ ...f, entityType: 'file' as const })),
      ...people.map(p => ({ ...p, entityType: 'person' as const })),
      ...organizations.map(o => ({ ...o, entityType: 'organization' as const })),
      ...bills.map(b => ({ ...b, entityType: 'bill' as const }))
    ];
    
    // Sort by updated_at (descending) and take the specified limit
    const sortedEntities = allEntities
      .filter(entity => entity.updated_at !== undefined)
      .sort((a, b) => (b.updated_at || 0) - (a.updated_at || 0))
      .slice(0, limit);
    
    return { entities: sortedEntities };
  }
});

/**
 * Get tasks where the current user is driver or contributor
 * 
 * This query retrieves tasks where the current user is either the driver
 * or a contributor (directly or via team membership), and the task is not completed.
 */
export const getUserTasks = query({
  args: {
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const { limit = 10 } = args;
    
    // Get current user ID
    const userId = await getAuthUserId(ctx);
    if (!userId) return { tasks: [] };
    
    // Get user's teams
    const user = await ctx.db.get(userId);
    if (!user) return { tasks: [] };
    
    const userTeamIds = user.teams || [];
    
    // Fetch all tasks
    const allTasks = await ctx.db.query('tasks').collect();
    
    // Filter tasks where user is driver or contributor (directly or via team)
    // and task is not completed
    const userTasks = allTasks.filter(task => {
      // Check if user is driver
      const isDriver = task.driver === userId;
      
      // Check if user is contributor (directly or via team)
      const isContributor = task.contributors?.some((contributorId: Id<'users'> | Id<'teams'>) => 
        contributorId === userId || userTeamIds.includes(contributorId as Id<'teams'>)
      );
      
      // Check if task is not completed
      const isNotCompleted = task.status !== 'completed' && task.status !== 'cancelled';
      
      return (isDriver || isContributor) && isNotCompleted;
    });
    
    // Sort by due_date (ascending) or updated_at (descending)
    const sortedTasks = userTasks.sort((a, b) => {
      // Prioritize tasks with due dates
      if (a.due_date && !b.due_date) return -1;
      if (!a.due_date && b.due_date) return 1;
      
      // Sort by due_date if both have it
      if (a.due_date && b.due_date) return a.due_date - b.due_date;
      
      // Fall back to updated_at
      return (b.updated_at || 0) - (a.updated_at || 0);
    });
    
    return { tasks: sortedTasks.slice(0, limit) };
  }
});

/**
 * Get decisions where the current user is driver or contributor
 * 
 * This query retrieves decisions where the current user is either the driver
 * or a contributor (directly or via team membership), and the decision is not completed.
 */
export const getUserDecisions = query({
  args: {
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const { limit = 10 } = args;
    
    // Get current user ID
    const userId = await getAuthUserId(ctx);
    if (!userId) return { decisions: [] };
    
    // Get user's teams
    const user = await ctx.db.get(userId);
    if (!user) return { decisions: [] };
    
    const userTeamIds = user.teams || [];
    
    // Fetch all decisions
    const allDecisions = await ctx.db.query('decisions').collect();
    
    // Filter decisions where user is driver or contributor (directly or via team)
    // and decision is not completed
    const userDecisions = allDecisions.filter(decision => {
      // Check if user is driver
      const isDriver = decision.driver === userId;
      
      // Check if user is contributor (directly or via team)
      const isContributor = decision.contributors?.some((contributorId: Id<'users'> | Id<'teams'>) => 
        contributorId === userId || userTeamIds.includes(contributorId as Id<'teams'>)
      );
      
      // Check if decision is not completed
      const isNotCompleted = decision.status !== 'approved' && 
                            decision.status !== 'rejected' && 
                            decision.status !== 'cancelled';
      
      return (isDriver || isContributor) && isNotCompleted;
    });
    
    // Sort by due_date (ascending) or updated_at (descending)
    const sortedDecisions = userDecisions.sort((a, b) => {
      // Prioritize decisions with due dates
      if (a.due_date && !b.due_date) return -1;
      if (!a.due_date && b.due_date) return 1;
      
      // Sort by due_date if both have it
      if (a.due_date && b.due_date) return a.due_date - b.due_date;
      
      // Fall back to updated_at
      return (b.updated_at || 0) - (a.updated_at || 0);
    });
    
    return { decisions: sortedDecisions.slice(0, limit) };
  }
});

/**
 * Get tasks and decisions where the current user is informed
 * 
 * This query retrieves tasks and decisions where the current user is informed
 * (directly or via team membership).
 */
export const getUserInformedItems = query({
  args: {
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const { limit = 10 } = args;
    
    // Get current user ID
    const userId = await getAuthUserId(ctx);
    if (!userId) return { items: [] };
    
    // Get user's teams
    const user = await ctx.db.get(userId);
    if (!user) return { items: [] };
    
    const userTeamIds = user.teams || [];
    
    // Fetch all tasks and decisions
    const [allTasks, allDecisions] = await Promise.all([
      ctx.db.query('tasks').collect(),
      ctx.db.query('decisions').collect()
    ]);
    
    // Filter tasks where user is informed (directly or via team)
    const informedTasks = allTasks.filter(task => 
      task.informed?.some((informedId: Id<'users'> | Id<'teams'>) => 
        informedId === userId || userTeamIds.includes(informedId as Id<'teams'>)
      )
    ).map(task => ({ 
      ...task, 
      itemType: 'task' as const,
      updated_at: task.updated_at
    }));
    
    // Filter decisions where user is informed (directly or via team)
    const informedDecisions = allDecisions.filter(decision => 
      decision.informed?.some((informedId: Id<'users'> | Id<'teams'>) => 
        informedId === userId || userTeamIds.includes(informedId as Id<'teams'>)
      )
    ).map(decision => ({ 
      ...decision, 
      itemType: 'decision' as const,
      updated_at: decision.updated_at
    }));
    
    // Combine and sort by updated_at (descending)
    const allInformedItems: InformedItem[] = [...informedTasks, ...informedDecisions]
      .sort((a, b) => (b.updated_at || 0) - (a.updated_at || 0))
      .slice(0, limit);
    
    return { items: allInformedItems };
  }
});
