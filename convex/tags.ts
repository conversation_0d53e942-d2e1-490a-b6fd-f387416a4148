/**
 * tags.ts
 *
 * Single Convex module for managing Tags, Tag Types, and Taggings.
 * Combines logic from the original `convex/tags/`, `tagTypes.ts`, and `taggingMutations.ts`.
 *
 * Exports:
 *   - Tag Management
 *       saveTag
 *       removeTag
 *       fetchTags
 *       getHierarchy
 *       getStats
 *   - Tag Type Management
 *       listTagTypes
 *       getTagType
 *       getTagTypeBySlug
 *       createTagType
 *       updateTagType
 *       deleteTagType
 *       seedDefaultTagTypes
 *   - Tagging Management
 *       getTagsForTaggable
 *       addTagToTaggable
 *       removeTagFromTaggable
 *       setTagsForTaggable
 */

import { z } from "zod";
import { ConvexError, v } from "convex/values";
import { zMutation, zQuery } from "./functions";
import { zid } from "convex-helpers/server/zod";
import {
  DatabaseReader,
  QueryCtx,
  MutationCtx,
} from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { PaginationSchema } from "../zod/pagination-schema";

import {
  SaveTagArgsSchema,
  TagFetchFilterSchema,
  RemoveTagArgsSchema,
  TagHierarchyResponseSchema,
  TagSchema,
  GetHierarchyArgsSchema,
  TagWithChildren,
  Tag,
  TaggableTypeSchema,
  TaggableIdSchema,
  AllHierarchiesResponseSchema,
} from "../zod/tags-schema";

import {
  TagTypeSchema,
  SaveTagTypeSchema,
  DeleteTagTypeArgsSchema,
} from "../zod/tagTypes-schema";

/**
 * Tag Kanban Report Schemas and Query
 */
export const TagKanbanReportInputSchema = z.object({
  tag: z.union([z.string(), zid("tags")]),
  kanbanColumnField: z.string().optional(),
});

export const TagKanbanEntitySchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  amount: z.number().optional(),
});

export const TagKanbanReportOutputSchema = z.record(z.string(), z.array(TagKanbanEntitySchema));

/**
 * tagKanbanReport
 * Given a tag and kanban column context, returns all entities with that tag grouped by kanban column.
 */
export const tagKanbanReport = zQuery({
  args: TagKanbanReportInputSchema.shape,
  output: TagKanbanReportOutputSchema,
  handler: async (ctx, { tag, kanbanColumnField }) => {
    // Debug: Log received tag and type
    console.log("[tagKanbanReport] Received tag:", tag, "Type:", typeof tag);

    let tagId: Id<"tags"> | undefined = undefined;
    let tagDoc = null;

    // Try to resolve as Convex Id
    if (typeof tag === "string") {
      try {
        // Use type assertion to treat the string as an Id<"tags">
        const possibleId = tag as Id<"tags">;
        tagDoc = await ctx.db.get(possibleId);
        if (tagDoc) {
          tagId = possibleId;
        }
      } catch (e) {
        console.warn("[tagKanbanReport] ID conversion failed for:", tag, e);
      }
    }

    // If not found by id, try to look up by name
    if (!tagId && typeof tag === "string") {
      tagDoc = await ctx.db
        .query("tags")
        .withIndex("by_name", (q) => q.eq("name", tag))
        .first();
      console.log("[tagKanbanReport] Lookup by name:", tag, "Result:", tagDoc);
      if (tagDoc) {
        tagId = tagDoc._id as Id<"tags">;
      }
    }

    // If still not found, log all tags for debugging
    if (!tagId) {
      console.error("[tagKanbanReport] Tag not found for:", tag);
      const allTags = await ctx.db.query("tags").collect();
      console.error("[tagKanbanReport] All tags in DB:", allTags);
      throw new Error("Tag not found");
    }

    console.log("[tagKanbanReport] Resolved tagId:", tagId);

    // 2. Get all taggings for this tag
    const taggings = await ctx.db
      .query("taggings")
      .withIndex("by_tag", (q) => q.eq("tag_id", tagId as Id<"tags">))
      .collect();

    // 3. Group taggings by type
    const taggingsByType: Record<string, Array<{ id: string }>> = {};
    for (const t of taggings) {
      if (!taggingsByType[t.taggable_type]) taggingsByType[t.taggable_type] = [];
      taggingsByType[t.taggable_type].push({ id: t.taggable_id as string });
    }

    // 4. Fetch all entities by type
    const entityFetchers: Record<string, (ids: string[]) => Promise<any[]>> = {
      person: async (ids) => {
        if (!ids.length) return [];
        const people = await Promise.all(ids.map(id => ctx.db.get(id as Id<"people">)));
        return people.filter((p): p is NonNullable<typeof p> => !!p).map(p => ({
          ...p,
          type: "person",
          name: p.name,
          id: p._id,
        }));
      },
      organization: async (ids) => {
        if (!ids.length) return [];
        const orgs = await Promise.all(ids.map(id => ctx.db.get(id as Id<"organizations">)));
        return orgs.filter((o): o is NonNullable<typeof o> => !!o).map(o => ({
          ...o,
          type: "organization",
          name: o.name,
          id: o._id,
        }));
      },
      task: async (ids) => {
        if (!ids.length) return [];
        const tasks = await Promise.all(ids.map(id => ctx.db.get(id as Id<"tasks">)));
        return tasks.filter((t): t is NonNullable<typeof t> => !!t).map(t => ({
          ...t,
          type: "task",
          name: t.name,
          id: t._id,
          status: t.status,
        }));
      },
      decision: async (ids) => {
        if (!ids.length) return [];
        const decisions = await Promise.all(ids.map(id => ctx.db.get(id as Id<"decisions">)));
        return decisions.filter((d): d is NonNullable<typeof d> => !!d).map(d => ({
          ...d,
          type: "decision",
          name: d.title ?? "(untitled)",
          id: d._id,
          status: d.status,
          amount: d.amount, // Include amount field for frontend
        }));
      },
      project: async (ids) => {
        if (!ids.length) return [];
        const projects = await Promise.all(ids.map(id => ctx.db.get(id as Id<"projects">)));
        return projects.filter((p): p is NonNullable<typeof p> => !!p).map(p => ({
          ...p,
          type: "project",
          name: p.name ?? "(untitled)",
          id: p._id,
          status: p.status,
        }));
      },
      file: async (ids) => {
        if (!ids.length) return [];
        const files = await Promise.all(ids.map(id => ctx.db.get(id as Id<"files">)));
        return files.filter((f): f is NonNullable<typeof f> => !!f).map(f => ({
          ...f,
          type: "file",
          name: f.title ?? f.fileName ?? "(untitled)",
          id: f._id,
        }));
      },
    };

    // 5. Fetch all entities
    let allEntities: Array<{ id: string, name: string, type: string, status?: string, amount?: number }> = [];
    for (const [type, arr] of Object.entries(taggingsByType)) {
      const fetcher = entityFetchers[type];
      if (fetcher) {
        const entities = await fetcher(arr.map(x => x.id));
        // Ensure all entities have an amount property (undefined for non-decisions)
        allEntities = allEntities.concat(
          entities.map(e => ({
            ...e,
            amount: e.type === "decision" ? e.amount : undefined,
          }))
        );
      }
    }

    // 6. Group by kanban column (status for tasks, decisions, projects; 'none' for others)
    const groupField = kanbanColumnField || "status";
    const grouped: Record<string, Array<any>> = {};
    for (const entity of allEntities as any[]) {
      let col = "none";
      if (
        (entity.type === "task" || entity.type === "decision" || entity.type === "project") &&
        typeof (entity as Record<string, unknown>)[groupField] === "string"
      ) {
        col = (entity as Record<string, unknown>)[groupField] as string || "none";
      }
      if (!grouped[col]) grouped[col] = [];
      // Always include amount (may be undefined for non-decisions)
      grouped[col].push({
        id: entity.id,
        name: entity.name,
        type: entity.type,
        amount: entity.amount,
      });
    }
    return grouped;
  },
});

/*
|--------------------------------------------------------------------------
| Helper Functions (General)
|--------------------------------------------------------------------------
*/

/** Check for circular references in a parent-child chain of tags. */
async function hasCircularReference(
  ctx: MutationCtx | QueryCtx,
  parentId: Id<"tags">,
  skipId?: Id<"tags">
): Promise<boolean> {
  const visited = new Set<string>();
  let current: Id<"tags"> | undefined = parentId;

  while (current) {
    if (skipId && current === skipId) return true;
    if (visited.has(current)) return true;
    visited.add(current);

    const tagDoc = await ctx.db.get(current);
    if (!tagDoc) break;

    const next = tagDoc.parent_id as Id<"tags"> | undefined;
    if (!next) break;
    current = next;
  }
  return false;
}

/** Ensure no sibling tag at the same parent with the same tag type has the same name (case-insensitive). */
async function checkDuplicateNameAndTypeAtParent(
  ctx: MutationCtx | QueryCtx,
  currentTagId: Id<"tags"> | undefined,
  requestedName: string,
  parentId: Id<"tags"> | undefined,
  tagTypeId: Id<"tag_types">
) {
  const nameLower = requestedName.trim().toLowerCase();
  const siblings = await ctx.db
    .query("tags")
    .withIndex("by_parent", (q) =>
      parentId ? q.eq("parent_id", parentId) : q.eq("parent_id", undefined)
    )
    .collect();

  const conflict = siblings.find(
    (sibling) =>
      sibling._id !== currentTagId &&
      sibling.name.trim().toLowerCase() === nameLower &&
      (sibling as Tag).tag_type === tagTypeId
  );
  if (conflict) {
    throw new Error(
      `A tag named "${conflict.name}" with the same tag type already exists at this level.`
    );
  }
}

/** Recursively delete a tag (and children) along with associated taggings. */
async function deleteRecursively(ctx: MutationCtx, tagId: Id<"tags">) {
  // Descendants
  const children = await ctx.db
    .query("tags")
    .withIndex("by_parent", (q) => q.eq("parent_id", tagId))
    .collect();

  // Delete all children recursively
  await Promise.all(children.map((child) =>
    deleteRecursively(ctx, child._id as Id<"tags">)
  ));

  // Taggings for this tag
  const taggings = await ctx.db
    .query("taggings")
    .withIndex("by_tag", (q) => q.eq("tag_id", tagId))
    .collect();

  // Delete all taggings
  await Promise.all(taggings.map((t) => ctx.db.delete(t._id)));

  // Delete the tag itself
  await ctx.db.delete(tagId);
}

/**
 * mergeTags
 * (Merge multiple tags into a canonical tag, reassigning all taggings and deleting old tags)
 */
export const mergeTags = zMutation({
  args: {
    canonicalTagId: zid("tags"),
    tagIdsToMerge: z.array(zid("tags")),
  },
  output: z.object({
    success: z.boolean(),
    reassigned: z.number(),
    deletedTags: z.number(),
  }),
  handler: async (ctx, { canonicalTagId, tagIdsToMerge }) => {
    if (!canonicalTagId || !tagIdsToMerge.length) {
      throw new Error("Must provide canonicalTagId and at least one tag to merge.");
    }
    // Prevent merging canonical into itself
    const mergeSet = new Set(tagIdsToMerge.map(id => id.toString()));
    mergeSet.delete(canonicalTagId.toString());
    const tagsToMerge = Array.from(mergeSet);

    let reassigned = 0;
    let deletedTags = 0;

    for (const tagId of tagsToMerge) {
      // 1. Find all taggings for this tag
      const taggings = await ctx.db
        .query("taggings")
        .withIndex("by_tag", (q) => q.eq("tag_id", tagId as Id<"tags">))
        .collect();

      for (const tagging of taggings) {
        // Check if this entity is already tagged with the canonical tag
        const alreadyTagged = await ctx.db
          .query("taggings")
          .withIndex("by_taggable", (q: any) =>
            q.eq("taggable_type", tagging.taggable_type)
             .eq("taggable_id", tagging.taggable_id)
          )
          .filter((q: any) => q.eq(q.field("tag_id"), canonicalTagId))
          .unique();

        if (!alreadyTagged) {
          // Reassign tagging to canonical tag
          await ctx.db.patch(tagging._id, { tag_id: canonicalTagId });
          reassigned++;
        } else {
          // Entity already tagged, just delete this tagging
          await ctx.db.delete(tagging._id);
        }
      }

      // 2. Delete the old tag (and its children/taggings)
      await deleteRecursively(ctx, tagId as Id<"tags">);
      deletedTags++;
    }

    return {
      success: true,
      reassigned,
      deletedTags,
    };
  },
});

/** Recursively update a tag's children to match a newly changed parent tag type. */
async function updateChildrenTypes(
  ctx: MutationCtx,
  parentId: Id<"tags">,
  parentTagType: Id<"tag_types">
) {
  const kids = await ctx.db
    .query("tags")
    .withIndex("by_parent", (q) => q.eq("parent_id", parentId))
    .collect();

  // Update children that need tag type changes
  const updatePromises = kids.map(async (child) => {
    if ((child as Tag).tag_type !== parentTagType) {
      await ctx.db.patch(child._id, {
        tag_type: parentTagType,
        updated_at: Date.now(),
      });
    }
    return updateChildrenTypes(ctx, child._id as Id<"tags">, parentTagType);
  });

  await Promise.all(updatePromises);
}

/**
 * Fetch the tag type doc for each tag to retrieve `immutable_slug`,
 * then merge that slug back into the tag object.
 */
async function enrichTagsWithSlug(ctx: QueryCtx, tags: Tag[]): Promise<Tag[]> {
  const uniqueTypes = Array.from(new Set(tags.map((t) => t.tag_type)));
  const typeMap = new Map<Id<"tag_types">, string>();

  // Fetch all type documents concurrently
  await Promise.all(
    uniqueTypes.map(async (typeId) => {
      if (!typeId) return;
      const typeDoc = await ctx.db.get(typeId as Id<"tag_types">);
      if (typeDoc?.immutable_slug) {
        typeMap.set(typeId as Id<"tag_types">, typeDoc.immutable_slug);
      }
    })
  );

  return tags.map((t) => ({
    ...t,
    immutable_slug: typeMap.get(t.tag_type as Id<"tag_types">) || undefined,
  }));
}

/*
|--------------------------------------------------------------------------
| Tag Management
|--------------------------------------------------------------------------
*/

/**
 * saveTag
 * (Create or Update a Tag)
 */
export const saveTag = zMutation({
  args: { tag: SaveTagArgsSchema },
  output: TagSchema,
  handler: async (ctx, { tag }) => {
    const parsedTag = SaveTagArgsSchema.parse(tag);
    const now = Date.now();
    const {
      id,
      name,
      parent_id,
      description,
      color,
      immutable_slug,
    } = parsedTag;

    if (!name || !name.trim()) throw new Error("Tag name cannot be empty.");

    // Validate parent if any and protect against circular references
    let finalParentId: Id<"tags"> | undefined;
    if (parent_id !== null && parent_id !== undefined) {
      const parentTag = await ctx.db.get(parent_id as Id<"tags">);
      if (!parentTag) throw new Error("Parent tag not found.");
      if (
        id &&
        (await hasCircularReference(ctx, parent_id as Id<"tags">, id as Id<"tags">))
      ) {
        throw new Error("Circular reference detected.");
      }

      // If parent's tag_type differs, force the child's tag_type to match
      if ((parentTag as Tag).tag_type !== parsedTag.tag_type) {
        parsedTag.tag_type = (parentTag as Tag).tag_type as Id<"tag_types">;
      }
      finalParentId = parent_id as Id<"tags">;
    } else if (parent_id === null) {
      // If explicitly null, store as undefined
      finalParentId = undefined;
    }

    // Ensure no duplicate name AND type at this parent level
    await checkDuplicateNameAndTypeAtParent(
      ctx,
      id ? (id as Id<"tags">) : undefined,
      name,
      finalParentId,
      parsedTag.tag_type
    );

    // Update existing
    if (id) {
      const tagId = id as Id<"tags">;
      const existing = await ctx.db.get(tagId);
      if (!existing) throw new Error("Tag not found.");
      if (finalParentId && finalParentId === tagId) {
        throw new Error("Tag cannot be its own parent.");
      }

      const updateData: Partial<Tag> = {
        name: name.trim(),
        description,
        tag_type: parsedTag.tag_type,
        parent_id: finalParentId,
        updated_at: now,
        color,
        immutable_slug,
      };
      Object.keys(updateData).forEach((k) => {
        if (updateData[k as keyof Tag] === undefined) {
          delete updateData[k as keyof Tag];
        }
      });

      await ctx.db.patch(tagId, updateData);

      // If tag_type changed, propagate to children
      if (
        parsedTag.tag_type !== undefined &&
        (existing as Tag).tag_type !== parsedTag.tag_type
      ) {
        await updateChildrenTypes(ctx, tagId, parsedTag.tag_type);
      }
      const updated = await ctx.db.get(tagId);
      if (!updated) throw new Error("Failed to retrieve updated tag.");
      return updated as Tag;
    }

    // Create new
    const newTagData = {
      name: name.trim(),
      tag_type: parsedTag.tag_type,
      updated_at: now,
      color,
      immutable_slug,
      description,
      parent_id: finalParentId,
    };
    const newId = await ctx.db.insert("tags", newTagData);
    const newTag = await ctx.db.get(newId);
    if (!newTag) throw new Error("Failed to create new tag.");
    return newTag as Tag;
  },
});

/**
 * removeTag
 * (Delete a tag + all its descendants + their taggings)
 */
export const removeTag = zMutation({
  args: { id: RemoveTagArgsSchema },
  output: z.object({
    success: z.boolean(),
    lineItemsAffected: z.number(),
  }),
  handler: async (ctx, { id }) => {
    const tagDoc = await ctx.db.get(id.id);
    if (!tagDoc) throw new Error("Tag not found.");

    // Count how many taggings reference this tag (for the response)
    const taggings = await ctx.db
      .query("taggings")
      .withIndex("by_tag", (q) => q.eq("tag_id", id.id))
      .collect();

    // Recursively delete
    await deleteRecursively(ctx, id.id);

    return {
      success: true,
      lineItemsAffected: taggings.length,
    };
  },
});

/**
 * fetchTags
 * (Query tags by optional ID, parent, type, or text search)
 */
export const fetchTags = zQuery({
  args: { filter: TagFetchFilterSchema },
  output: z.array(TagSchema),
  handler: async (ctx, { filter }) => {
    const { id, tag_type, parentId, searchText } = TagFetchFilterSchema.parse(
      filter
    );

    // Debug: Log incoming filter
    console.log("[convex/tags.ts] fetchTags called with filter:", filter);

    // If single ID provided, return that one or throw.
    if (id) {
      const doc = await ctx.db.get(id as Id<"tags">);
      if (!doc) throw new Error("Tag not found.");
      const [enriched] = await enrichTagsWithSlug(ctx, [doc as Tag]);
      // Debug: Log single tag returned
      console.log("[convex/tags.ts] fetchTags returning single tag");
      return [enriched];
    }

    // Otherwise, gather a list
    let results: Tag[] = [];

    // Optimized: Use composite index if both parentId and tag_type are provided
    if (parentId !== undefined && tag_type) {
      const parent = parentId === null ? undefined : (parentId as Id<"tags">);
      if (parent !== undefined) {
        // Use composite index when parent is defined
        results = (await ctx.db
          .query("tags")
          .withIndex("by_parent_tag_type", (q) =>
            q.eq("parent_id", parent as Id<"tags">)
          )
          .filter((q) => q.eq(q.field("tag_type"), tag_type as Id<"tag_types">))
          .collect()) as Tag[];
        // Debug: Log query path
        console.log("[convex/tags.ts] fetchTags used by_parent_tag_type index");
      } else {
        // If parent is undefined, use by_tag_type index and filter for parent_id === undefined
        results = (await ctx.db
          .query("tags")
          .withIndex("by_tag_type", (q) => q.eq("tag_type", tag_type as Id<"tag_types">))
          .filter((q) => q.eq(q.field("parent_id"), undefined))
          .collect()) as Tag[];
        // Debug: Log query path
        console.log("[convex/tags.ts] fetchTags used by_tag_type index with parent_id undefined");
      }
    }
    // Filter by parent only
    else if (parentId !== undefined) {
      const parent = parentId === null ? undefined : (parentId as Id<"tags">);
      results = (await ctx.db
        .query("tags")
        .withIndex("by_parent", (q) => q.eq("parent_id", parent))
        .collect()) as Tag[];
      // Debug: Log query path
      console.log("[convex/tags.ts] fetchTags used by_parent index");
    }
    // Filter by tag_type only
    else if (tag_type) {
      // Use index for efficient filtering
      results = (await ctx.db
        .query("tags")
        .withIndex("by_tag_type", (q) => q.eq("tag_type", tag_type as Id<"tag_types">))
        .collect()) as Tag[];
      // Debug: Log query path
      console.log("[convex/tags.ts] fetchTags used by_tag_type index");
    } else {
      // No filters => fetch all (with some index if available)
      results = (await ctx.db.query("tags").withIndex("by_name").collect()) as Tag[];
      // Debug: Log query path
      console.log("[convex/tags.ts] fetchTags used by_name index (no filters)");
    }

    let enriched = await enrichTagsWithSlug(ctx, results);

    // Debug: Log number of tags returned
    console.log(`[convex/tags.ts] fetchTags returning ${enriched.length} tags`);

    // Search text filter (in memory)
    if (searchText && searchText.trim()) {
      const term = searchText.trim().toLowerCase();
      enriched = enriched.filter((t) => {
        const nm = t.name.toLowerCase();
        const desc = t.description?.toLowerCase() || "";
        return nm.includes(term) || desc.includes(term);
      });
    }

    // Sort results by name
    return enriched.sort((a, b) => a.name.localeCompare(b.name));
  },
});

/**
 * getHierarchy
 * (Return a tag and its entire descendant tree, or all top-level tags if none specified)
 */
export const getHierarchy = zQuery({
  args: { params: GetHierarchyArgsSchema },
  output: TagHierarchyResponseSchema,
  handler: async (ctx, { params }) => {
    const { id, tag_type } = GetHierarchyArgsSchema.parse(params);
    return (await buildHierarchy(
      ctx,
      id ? (id as Id<"tags">) : undefined,
      tag_type as Id<"tag_types"> | undefined
    )) as z.infer<typeof TagHierarchyResponseSchema>;
  },
});

/** Recursively assemble a tag hierarchy. */
async function buildHierarchy(
  ctx: QueryCtx,
  rootId?: Id<"tags">,
  tagTypeFilter?: Id<"tag_types">
): Promise<TagWithChildren | TagWithChildren[]> {
  // If a specific root ID
  if (rootId) {
    const rootDoc = await ctx.db.get(rootId);
    if (!rootDoc) throw new Error("Tag not found.");
    if (tagTypeFilter && (rootDoc as Tag).tag_type !== tagTypeFilter) {
      // Mismatch => return empty children
      return { ...(rootDoc as Tag), children: [] };
    }
    const [withSlug] = await enrichTagsWithSlug(ctx, [rootDoc as Tag]);
    const kids = await getChildren(ctx, withSlug._id as Id<"tags">, tagTypeFilter);

    return {
      ...withSlug,
      children: kids,
    };
  }

  // Otherwise, get all top-level tags
  let query = ctx.db.query("tags").withIndex("by_parent");
  query = query.filter((q) => q.eq(q.field("parent_id"), undefined));
  if (tagTypeFilter) {
    query = query.filter((q) => q.eq(q.field("tag_type"), tagTypeFilter));
  }

  const roots = (await query.collect()) as Tag[];
  const enriched = await enrichTagsWithSlug(ctx, roots);

  return Promise.all(
    enriched.map(async (r) => ({
      ...r,
      children: await getChildren(ctx, r._id as Id<"tags">, tagTypeFilter),
    }))
  );
}

/** Get direct children of a tag, then recursively get their children. */
async function getChildren(
  ctx: QueryCtx,
  parent: Id<"tags">,
  tagTypeFilter?: Id<"tag_types">
): Promise<TagWithChildren[]> {
  let query = ctx.db.query("tags").withIndex("by_parent");
  query = query.filter((q) => q.eq(q.field("parent_id"), parent));
  if (tagTypeFilter) {
    query = query.filter((q) => q.eq(q.field("tag_type"), tagTypeFilter));
  }

  const childDocs = (await query.collect()) as Tag[];
  const enriched = await enrichTagsWithSlug(ctx, childDocs);

  return Promise.all(
    enriched.map(async (c) => ({
      ...c,
      children: await getChildren(ctx, c._id as Id<"tags">, tagTypeFilter),
    }))
  );
}

/**
 * getStats
 * (Aggregate stats about all tags)
 */
export const getStats = zQuery({
  args: {},
  output: z.object({
    total: z.number(),
    rootTags: z.number(),
    subTags: z.number(),
    byType: z.record(z.string(), z.number()),
    averageSubTagsPerTag: z.number(),
  }),
  handler: async (ctx) => {
    const all = (await ctx.db.query("tags").collect()) as Tag[];
    const total = all.length;
    const rootCount = all.filter((t) => !t.parent_id).length;
    const subCount = total - rootCount;

    // Tally counts by tag_type
    const byType: Record<string, number> = {};
    for (const t of all) {
      const key = t.tag_type?.toString() || "";
      byType[key] = (byType[key] || 0) + 1;
    }

    const avg = rootCount ? Number((subCount / rootCount).toFixed(1)) : 0;

    return {
      total,
      rootTags: rootCount,
      subTags: subCount,
      byType,
      averageSubTagsPerTag: avg,
    };
  },
});

/*
|--------------------------------------------------------------------------
| Tag Type Management
|--------------------------------------------------------------------------
*/

/** Helper: check for an existing Tag Type with the same name (case-insensitive). */
async function findDuplicateTagTypeName(
  db: DatabaseReader,
  name: string,
  excludeId?: Id<"tag_types">
) {
  const lowerName = name.trim().toLowerCase();
  const maybeDupe = await db
    .query("tag_types")
    .withIndex("by_name", (q) => q.eq("name", lowerName))
    .first();
  if (maybeDupe && (!excludeId || maybeDupe._id !== excludeId)) {
    return maybeDupe;
  }
  return undefined;
}

// Pagination & listing schemas
const ListTagTypesOutputSchema = z.object({
  page: z.array(TagTypeSchema),
  continuationToken: z.any().optional(),
});

/**
 * listTagTypes
 * (List all Tag Types with optional ID lookup, search, and pagination)
 */
export const listTagTypes = zQuery({
  args: {
    id: zid("tag_types").optional(),
    searchText: z.string().optional(),
    paginationOpts: PaginationSchema.optional(), // Use standard pagination schema
  },
  output: ListTagTypesOutputSchema,
  handler: async (ctx, args) => {
    const { id, searchText, paginationOpts } = args;
    // Destructure with defaults from the updated PaginationSchema
    const numItems = paginationOpts?.numItems ?? 50;
    const cursor = paginationOpts?.cursor ?? null;
    const sortDirection = paginationOpts?.sortDirection ?? "asc"; // Use provided or default
    const sortBy = paginationOpts?.sortBy ?? "name"; // Use provided or default

    // If specific ID
    if (id) {
      const doc = await ctx.db.get(id as Id<"tag_types">);
      return {
        page: doc ? [doc as z.infer<typeof TagTypeSchema>] : [],
        continuationToken: undefined,
      };
    }

    // Otherwise list/paginate
    let queryBuilder;

    // TODO: Add proper index support for different sorting fields if needed
    // Currently defaults to sorting by name index
    if (sortBy === "name") {
      queryBuilder = ctx.db
        .query("tag_types")
        .withIndex("by_name") // Assuming 'by_name' index exists
        .order(sortDirection); // Apply sort direction
    } else if (sortBy === "updated_at") {
       queryBuilder = ctx.db
        .query("tag_types")
        .withIndex("by_updated") // Assuming 'by_updated' index exists
        .order(sortDirection);
    }
     else {
      // Default to sorting by _creationTime if sortBy is not 'name' or 'updated_at'
      queryBuilder = ctx.db
        .query("tag_types")
        .order(sortDirection); // Sorts by _creationTime by default
    }

    const results = await queryBuilder.paginate({ cursor, numItems });

    // In-memory filter by searchText (apply after pagination for simplicity, might be inefficient for large datasets)
    let page = results.page as z.infer<typeof TagTypeSchema>[];
    if (searchText && searchText.trim()) {
      const term = searchText.trim().toLowerCase();
      page = page.filter((t) => {
        const nm = t.name.toLowerCase();
        const desc = t.description?.toLowerCase() || "";
        return nm.includes(term) || desc.includes(term);
      });
    }
    return { page, continuationToken: results.continueCursor };
  },
});

/**
 * getTagType
 * (Retrieve a single Tag Type by ID)
 */
export const getTagType = zQuery({
  args: { id: zid("tag_types") },
  output: TagTypeSchema,
  handler: async (ctx, { id }) => {
    const doc = await ctx.db.get(id as Id<"tag_types">);
    if (!doc) {
      throw new Error(`Tag type not found with ID ${id}`);
    }
    return doc as z.infer<typeof TagTypeSchema>;
  },
});

/**
 * getTagTypeBySlug
 * (Retrieve a single Tag Type by its immutable_slug)
 */
export const getTagTypeBySlug = zQuery({
  args: { slug: z.string() },
  output: TagTypeSchema,
  handler: async (ctx, { slug }) => {
    // Use ctx.db.query for database access
    const doc = await ctx.db
      .query("tag_types")
      .filter((q: any) => q.eq(q.field("immutable_slug"), slug))
      .first();
    if (!doc) {
      throw new Error(`Tag type not found with slug ${slug}`);
    }
    return doc as z.infer<typeof TagTypeSchema>;
  },
});

/**
 * getTagTypesByIds
 * (Retrieve multiple Tag Types by an array of IDs)
 */
export const getTagTypesByIds = zQuery({
  args: { ids: z.array(zid("tag_types")) },
  output: z.array(TagTypeSchema),
  handler: async (ctx, { ids }) => {
    // Fetch all tag_types by ID, skipping any not found
    const results = await Promise.all(
      ids.map(async (id) => {
        const doc = await ctx.db.get(id as Id<"tag_types">);
        return doc ? (doc as z.infer<typeof TagTypeSchema>) : undefined;
      })
    );
    // Filter out any undefined (not found)
    return results.filter(Boolean) as z.infer<typeof TagTypeSchema>[];
  },
});

/**
 * createTagType
 * (Create a new Tag Type; ensures unique name)
 */
export const createTagType = zMutation({
  args: {
    name: z.string().min(1),
    description: z.string().optional(),
    color: z.string().optional(),
    immutable_slug: z.string().optional(),
  },
  output: TagTypeSchema,
  handler: async (ctx, args) => {
    const now = Date.now();
    const { name, immutable_slug, description, color } = args;

    // Check duplicates
    if (await findDuplicateTagTypeName(ctx.db, name)) {
      throw new Error(`Tag type with name "${name}" already exists.`);
    }

    const newId = await ctx.db.insert("tag_types", {
      name: name.trim(),
      immutable_slug,
      description,
      color,
      updated_at: now,
    });
    const doc = await ctx.db.get(newId);
    if (!doc) throw new Error("Failed to create tag type.");
    return doc as z.infer<typeof TagTypeSchema>;
  },
});

/**
 * updateTagType
 * (Update an existing Tag Type; ensures unique name if changed)
 */
export const updateTagType = zMutation({
  args: {
    id: zid("tag_types"),
    updates: SaveTagTypeSchema.omit({ id: true }).partial(),
  },
  output: TagTypeSchema,
  handler: async (ctx, { id, updates }) => {
    const doc = await ctx.db.get(id as Id<"tag_types">);
    if (!doc) {
      throw new Error(`Tag type not found with ID ${id}`);
    }
    if (updates.name) {
      if (await findDuplicateTagTypeName(ctx.db, updates.name, id as Id<"tag_types">)) {
        throw new Error(`Tag type "${updates.name}" already exists.`);
      }
    }

    await ctx.db.patch(id as Id<"tag_types">, {
      ...updates,
      name: updates.name?.trim(),
      updated_at: Date.now(),
    });

    const updated = await ctx.db.get(id as Id<"tag_types">);
    if (!updated) throw new Error("Failed to update tag type.");
    return updated as z.infer<typeof TagTypeSchema>;
  },
});

/**
 * deleteTagType
 * (Delete a Tag Type if not in use by any existing Tag)
 */
export const deleteTagType = zMutation({
  args: { id: zid("tag_types") },
  output: z.object({ success: z.boolean(), message: z.string() }),
  handler: async (ctx, { id }) => {
    const doc = await ctx.db.get(id as Id<"tag_types">);
    if (!doc) throw new Error("Tag type not found.");
    const used = await ctx.db
      .query("tags")
      .filter((q) => q.eq(q.field("tag_type"), doc._id))
      .take(1);

    if (used.length > 0) {
      return {
        success: false,
        message: `Cannot delete: Tag type "${doc.name}" is in use.`,
      };
    }
    await ctx.db.delete(id as Id<"tag_types">);
    return {
      success: true,
      message: `Tag type "${doc.name}" was deleted.`,
    };
  },
});

/**
 * seedDefaultTagTypes
 * (Create or update default Tag Types if they don't already exist)
 */
export const seedDefaultTagTypes = zMutation({
  args: {},
  output: z.object({ success: z.boolean(), message: z.string() }),
  handler: async (ctx) => {
    const defaults = [
      {
        name: "Expense Category",
        immutable_slug: "expense_category",
        description: "For categorizing expenses",
        color: "#FF5733",
      },
      {
        name: "Project",
        immutable_slug: "project",
        description: "For project tagging",
        color: "#33FF57",
      },
      {
        name: "Department",
        immutable_slug: "department",
        description: "For department tagging",
        color: "#3357FF",
      },
      {
        name: "Campaign",
        immutable_slug: "campaign",
        description: "For campaign tracking",
        color: "#F3FF33",
      },
      {
        name: "General Tags",
        immutable_slug: "general-tags",
        description: "General purpose tags",
        color: "#AAAAAA",
      },
    ];

    let created = 0;
    let updated = 0;

    await Promise.all(defaults.map(async (def) => {
      const existing = await ctx.db
        .query("tag_types")
        .filter((q) => q.eq(q.field("immutable_slug"), def.immutable_slug))
        .take(1);

      if (existing.length === 0) {
        await ctx.db.insert("tag_types", {
          ...def,
          updated_at: Date.now(),
        });
        created++;
      } else {
        await ctx.db.patch(existing[0]._id, {
          name: def.name,
          description: def.description,
          color: def.color,
          updated_at: Date.now(),
        });
        updated++;
      }
    }));

    return {
      success: true,
      message: `${created} default tag types created, ${updated} updated.`,
    };
  },
});

/*
|--------------------------------------------------------------------------
| Tagging Management
|--------------------------------------------------------------------------
*/

/**
 * getTagsForMultipleDecisions
 * (Get all Tags assigned to multiple decisions in a single batch query)
 */
export const getTagsForMultipleDecisions = zQuery({
  args: {
    decisionIds: z.array(zid("decisions")),
  },
  output: z.record(z.string(), z.array(TagSchema)),
  handler: async (ctx, args) => {
    const { decisionIds } = args;

    if (!decisionIds || decisionIds.length === 0) {
      return {};
    }

    // Query all taggings for taggable_type === "decision" using the new index
    const allDecisionTaggings = await ctx.db
      .query("taggings")
      .withIndex("by_taggable_type", (q) =>
        q.eq("taggable_type", "decision")
      )
      .collect();

    // Filter in-memory for taggings whose taggable_id is in the provided decisionIds
    const decisionIdSet = new Set(decisionIds.map(id => id.toString()));
    const relevantTaggings = allDecisionTaggings.filter(
      tagging => decisionIdSet.has(tagging.taggable_id.toString())
    );

    // Group taggings by decision ID
    const taggingsByDecision: Record<string, { tag_id: string }[]> = {};
    for (const tagging of relevantTaggings) {
      const decisionId = tagging.taggable_id.toString();
      if (!taggingsByDecision[decisionId]) {
        taggingsByDecision[decisionId] = [];
      }
      taggingsByDecision[decisionId].push({ tag_id: tagging.tag_id });
    }

    // Gather all unique tag IDs to fetch in one batch
    const allTagIds = Array.from(
      new Set(relevantTaggings.map(t => t.tag_id.toString()))
    );

    // Fetch all tags in a single batch
    const tagDocs = await Promise.all(
      allTagIds.map(async (tagId) => {
        const tag = await ctx.db.get(tagId as Id<"tags">);
        return tag ? { tagId, tag } : null;
      })
    );
    const tagMap = new Map<string, any>();
    tagDocs.forEach(entry => {
      if (entry && entry.tag) {
        tagMap.set(entry.tagId, TagSchema.parse(entry.tag));
      }
    });

    // Build the result: decisionId -> array of tag objects
    const result: Record<string, any[]> = {};
    decisionIds.forEach(id => {
      const idStr = id.toString();
      const taggings = taggingsByDecision[idStr] || [];
      result[idStr] = taggings
        .map(t => tagMap.get(t.tag_id.toString()))
        .filter(Boolean);
    });

    return result;
  },
});

/**
 * getTagsForTaggable
 * (Get all Tags assigned to a specific record)
 */
export const getTagsForTaggable = zQuery({
  args: {
    taggable_type: TaggableTypeSchema,
    taggable_id: TaggableIdSchema,
  },
  output: z.array(TagSchema),
  handler: async (ctx, args) => {
    const links = await ctx.db
      .query("taggings")
      .withIndex("by_taggable", (q: any) =>
        q.eq("taggable_type", args.taggable_type)
      )
      .filter((q) => q.eq(q.field("taggable_id"), args.taggable_id))
      .collect();

    const tagIds = links.map((l) => l.tag_id);
    if (tagIds.length === 0) return [];

    const fetched = await Promise.allSettled(
      tagIds.map((tagId) => ctx.db.get(tagId))
    );

    // Validate tags against the schema (exclude any missing/broken ones)
    return fetched
      .map((r) => (r.status === "fulfilled" ? r.value : null))
      .filter(Boolean)
      .map((t) => TagSchema.parse(t));
  },
});

/**
 * addTagToTaggable
 * (Assign a Tag to a given record, avoiding duplicates)
 */
export const addTagToTaggable = zMutation({
  args: {
    taggable_type: TaggableTypeSchema,
    taggable_id: TaggableIdSchema,
    tagId: zid("tags"),
  },
  output: z.union([zid("taggings"), z.null()]),
  handler: async (ctx, args) => {
    // First, check if the tag exists
    const tag = await ctx.db.get(args.tagId);
    if (!tag) {
      throw new Error("Tag not found");
    }

    // Check if this specific tagging already exists
    const exists = await ctx.db
      .query("taggings")
      .withIndex("by_taggable", (q: any) =>
        q.eq("taggable_type", args.taggable_type)
         .eq("taggable_id", args.taggable_id)
      )
      .filter((q: any) => q.eq(q.field("tag_id"), args.tagId))
      .unique();

    // If it exists, return the existing tagging ID
    if (exists) {
      console.log(`Tagging already exists: ${JSON.stringify(exists)}`);
      return exists._id as Id<"taggings">;
    }

    // Otherwise, create a new tagging
    console.log(`Creating new tagging for ${args.taggable_type} ${args.taggable_id} with tag ${args.tagId}`);
    return (await ctx.db.insert("taggings", {
      taggable_type: args.taggable_type,
      taggable_id: args.taggable_id,
      tag_id: args.tagId,
      assigned_at: Date.now(),
    })) as Id<"taggings">;
  },
});

/**
 * removeTagFromTaggable
 * (Unassign a Tag from a specific record)
 */
export const removeTagFromTaggable = zMutation({
  args: {
    taggable_type: TaggableTypeSchema,
    taggable_id: TaggableIdSchema,
    tagId: zid("tags"),
  },
  output: z.object({ success: z.boolean(), message: z.string().optional() }),
  handler: async (ctx, args) => {
    // Check if this specific tagging exists
    const link = await ctx.db
      .query("taggings")
      .withIndex("by_taggable", (q: any) =>
        q.eq("taggable_type", args.taggable_type)
         .eq("taggable_id", args.taggable_id)
      )
      .filter((q: any) => q.eq(q.field("tag_id"), args.tagId))
      .unique();

    if (!link) {
      console.log(`Tagging not found for ${args.taggable_type} ${args.taggable_id} with tag ${args.tagId}`);
      return {
        success: false,
        message: "Tag assignment not found.",
      };
    }

    console.log(`Removing tagging ${link._id} for ${args.taggable_type} ${args.taggable_id} with tag ${args.tagId}`);
    if (link) {
      await ctx.db.delete(link._id);
    }
    return { success: true };
  },
});

/**
 * setTagsForTaggable
 * (Replace all Tags on a record with a new set of Tag IDs)
 */
export const setTagsForTaggable = zMutation({
  args: {
    taggable_type: TaggableTypeSchema,
    taggable_id: TaggableIdSchema,
    tagIds: z.array(zid("tags")),
  },
  output: z.object({ added: z.number(), removed: z.number() }),
  handler: async (ctx, args) => {
    const now = Date.now();

    // Get all current taggings for this entity
    const current = await ctx.db
      .query("taggings")
      .withIndex("by_taggable", (q: any) =>
        q.eq("taggable_type", args.taggable_type)
         .eq("taggable_id", args.taggable_id)
      )
      .collect();

    // Create sets for comparison
    const currentSet = new Set(current.map((t) => t.tag_id.toString()));
    const desiredSet = new Set(args.tagIds.map(id => id.toString()));

    // Find tags to add and taggings to remove
    const toAdd = args.tagIds.filter((id) => !currentSet.has(id.toString()));
    const toRemove = current.filter((c) => !desiredSet.has(c.tag_id.toString()));

    console.log(`Setting tags for ${args.taggable_type} ${args.taggable_id}:`);
    console.log(`- Current tags: ${Array.from(currentSet).join(', ')}`);
    console.log(`- Desired tags: ${Array.from(desiredSet).join(', ')}`);
    console.log(`- Adding: ${toAdd.map(id => id.toString()).join(', ')}`);
    console.log(`- Removing: ${toRemove.map(t => t.tag_id.toString()).join(', ')}`);


    let added = 0;
    const addPromises = toAdd.map((tagId) => {
      added++;
      return ctx.db.insert("taggings", {
        taggable_type: args.taggable_type,
        taggable_id: args.taggable_id,
        tag_id: tagId,
        assigned_at: now,
      });
    });
    await Promise.all(addPromises);

    let removed = 0;
    const removePromises = toRemove.map((r) => {
      removed++;
      return ctx.db.delete(r._id);
    });
    await Promise.all(removePromises);

    return { added, removed };
  },
});

/**
 * getAllHierarchies
 * (Return the full tag hierarchy for all tag types in a single call)
 */
export const getAllHierarchies = zQuery({
  args: {},
  output: AllHierarchiesResponseSchema,
  handler: async (ctx, args) => {
    // Use ctx.db.query to access the database query builder
    const tagTypes = await ctx.db.query("tag_types").collect();
    // 2. For each tag type, build its hierarchy (reuse buildHierarchy)
    const result: Record<string, any[]> = {};
    for (const tagType of tagTypes) {
      // Cast tagType._id as Id<"tag_types"> for type safety
      const hierarchy = await buildHierarchy(ctx, undefined, tagType._id as Id<"tag_types">);
      // Ensure the result is always an array for each tag type
      result[tagType._id] = Array.isArray(hierarchy) ? hierarchy : [hierarchy];
    }
    return result;
  }
});
