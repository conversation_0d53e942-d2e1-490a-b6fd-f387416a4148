import Google from '@auth/core/providers/google';
import { convexAuth } from '@convex-dev/auth/server';

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Google({
      profile(profile) {
        // Log the profile data we're receiving from Google
        console.log('[Convex Auth] Google profile received:', {
          sub: profile.sub,
          name: profile.name,
          email: profile.email,
          picture: <PERSON><PERSON><PERSON>(profile.picture),
          timestamp: new Date().toISOString()
        });

        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture
        };
      }
    })
  ]
});
