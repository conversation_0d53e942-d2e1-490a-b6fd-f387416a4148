const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        search: ''
      },
      {
        protocol: 'https',
        hostname: '*.public.blob.vercel-storage.com',
        search: ''
      },
      {
        protocol: 'https',
        hostname: '*.cressetcapital.com',
        search: ''
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
        search: ''
      }
    ]
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  async rewrites() {
    return [
      {
        // Proxy requests from /api/* to the Convex backend URL
        source: "/api/:path*",
        destination: `${process.env.NEXT_PUBLIC_CONVEX_SITE_URL}/api/:path*`,
      },
    ];
  },
};

export default nextConfig;
