# FOJO Digital Chief of Staff App - Project Brief

## Overview

FOJO is a comprehensive digital chief of staff application designed to streamline organizational workflows, project management, and decision-making processes. The platform serves as a centralized hub for teams to manage projects, track tasks, document decisions, store important files, manage bills, maintain a company directory, and generate insightful reports. With its modern, clean interface featuring rounded corners and an intuitive navigation system, FOJO empowers teams to work more efficiently and maintain clear visibility across all aspects of their operations.

## Navigation Menu Items

### Homepage

The Homepage serves as the central dashboard and entry point for users. It provides a personalized overview of the user's most important information, including:

- Recent activity and notifications
- Quick access to priority tasks and projects
- Key performance indicators and metrics
- Upcoming deadlines and milestones
- Personalized widgets configurable by the user

This hub is designed to give users immediate visibility into what matters most to them, with contextual information tailored to their role and responsibilities.

### Projects

The Projects section enables comprehensive project management with features including:

- Multiple view options: Kanban, card grid, and table views
- Project status tracking (Not Started, In Progress, Paused, Completed, Cancelled, Perpetual)
- Priority management (Low, Medium, High, Urgent) with visual indicators
- Progress tracking with percentage-based completion bars
- Team member assignment and collaboration tools
- Milestone tracking and task management integration
- Project descriptions and documentation

Each project card displays critical information including completion status, milestone information, team members (as avatars), last updated timestamp, task completion status, decision completion status, and recent updates, all presented in a clean, modern interface with rounded corners.

### Tasks

The Tasks section provides a dedicated task management system with robust features:

- Three view modes: Kanban board (default), card grid, and table view
- Task filtering by status (To Do, In Progress, Blocked, Completed, Cancelled)
- Priority filtering (Low, Medium, High, Critical)
- Search functionality for task titles and descriptions
- Task assignment capabilities
- Due date tracking and timeline visualization
- Drag-and-drop workflow management in Kanban view
- Task details including description, status, importance, assignee, and due date

The Tasks module integrates with Projects, allowing tasks to be associated with specific projects while maintaining a centralized view of all tasks across the organization.

### Decisions

The Decisions section serves as a repository for tracking, documenting, and referencing important organizational decisions:

- Decision documentation with context and rationale
- Status tracking for decision implementation
- Decision categorization and tagging
- Responsible parties and stakeholders
- Decision history and audit trail
- Integration with projects and tasks for implementation tracking
- Search and filter capabilities for quick reference

This module helps organizations maintain institutional knowledge, ensure accountability, and track the implementation of key decisions over time.

### Documents

The Documents section provides a centralized document management system:

- Document storage and organization by category
- Version control and document history
- Access control and permission management
- Preview capabilities for common file types
- Search functionality across document content
- Tagging and metadata management
- Integration with projects and decisions for contextual relevance
- New document creation tools

The document repository ensures that important files are easily accessible, properly organized, and securely stored with appropriate access controls.

### Bills

The Bills section helps organizations track financial transactions and manage expenses:

- Invoice and bill tracking
- Payment status monitoring (Paid, Pending, Overdue)
- Expense categorization and reporting
- Budget allocation and monitoring
- Vendor management and payment history
- Financial document storage (receipts, invoices)
- Approval workflows for expense management
- Integration with financial reporting

This module streamlines financial processes and provides visibility into organizational spending patterns and vendor relationships.

### Directory

The Directory section serves as a comprehensive organizational directory with two primary components:

- People: Profiles of team members with contact information, roles, departments, skills, and reporting relationships
- Organizations: Information about partner companies, clients, vendors, and other external organizations

The directory features include:

- Search and filter capabilities
- Detailed profile pages for both people and organizations
- Organizational hierarchy visualization
- Contact information and communication tools
- Integration with tasks and projects for assignment purposes
- Relationship mapping between entities

This module facilitates easy access to contact information and organizational relationships, supporting effective communication and collaboration.

### Reports

The Reports section provides data visualization and analytics capabilities:

- Overview dashboards with key metrics and KPIs
- Custom report generation with filtering options
- Resource allocation analysis and tracking
- Performance metrics across projects and tasks
- Time-based trend analysis
- Export capabilities for presentations and sharing
- Interactive data visualization tools
- Scheduled report generation and distribution

The reporting tools help leadership make data-driven decisions and maintain visibility across all organizational activities.

### Admin

The Admin section (accessible via the bottom navigation area) provides system configuration and management tools:

- User management and permissions
- Team creation and management
- Tag and category management
- System integrations configuration
- Global settings and preferences
- Customization options for the organization
- Audit logs and system monitoring
- Security settings and controls

This section is typically restricted to administrators and allows for the configuration and customization of the FOJO platform to meet specific organizational needs.

## Quick Create Functionality

The navigation bar also includes a Quick Create button that allows users to rapidly create new items (tasks, projects, decisions, documents) without navigating to the respective sections. This feature enhances productivity by reducing the steps required to add new content to the system.

## Technical Implementation

FOJO is built using a modern tech stack including:
- Next.js 15 and React 19 for the frontend
- Tailwind CSS with shadcn UI components for styling
- Convex database for data storage and retrieval
- Vercel AI SDK with OpenAI integration for AI capabilities

The application features a responsive design with a collapsible sidebar navigation system that adapts to different screen sizes while maintaining functionality and usability.
