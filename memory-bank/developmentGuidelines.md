# Development Guidelines

This document outlines the specific coding standards, patterns, and best practices to follow when developing the FOJO application.

## Comments

- Never delete old comments unless they're obviously wrong or obsolete
- Include lots of helpful and explanatory comments in your code
- Always write well-documented code
- Document all changes and their reasoning in the comments that you write
- When writing comments, use clear and easy-to-understand language
- Write comments in short sentences

## Assertions

- Introduce runtime assertions for business logic that <PERSON><PERSON> can't express
- Add business logic assertions to mutations/queries
- Add UI component assertions to React components

## Typing and Zod Validation

- Don't use the 'any' type
- Put all types and zod schemas in the zod/<domainname>-schema.ts files
- Each domain has its own zod/<domainname>-schema.ts file
- Read @zod-schema-guide.md for more information
- Use undefined for all empty or optional fields (do not use null because it's treated as an actual value that must satisfy the validator)

### Convex Query/Mutation Zod Integration

- **Use Zod Wrappers:** Always use the `zQuery` and `zMutation` helpers (defined in `convex/functions.ts`) for defining Convex queries and mutations that require argument or output validation. Do *not* use the standard Convex `query` or `mutation` functions with `v.*` validators for these cases.
- **External Schemas:** Define all Zod schemas in dedicated files within the `/zod/` directory (e.g., `zod/users-schema.ts`). Import these schemas into your `convex/` files. Do *not* define Zod schemas inline within `convex` query/mutation files. (Note: Actions in `convex/actions/` might follow different rules, refer to `zguide/guide-actions.md`).
- **`args` Object:** Pass Zod schemas to `zQuery`/`zMutation` within an `args` object. Each key in this object represents an argument and its value must be a Zod schema. The structure `args: YourSchema.shape` is often used when `YourSchema` is a `z.object`.
  ```ts
  // Example: convex/users.ts
  import { zQuery } from "./functions";
  import { GetUserInputSchema, UserOutputSchema } from "@/zod/users-schema";

  export const getUser = zQuery({
    args: GetUserInputSchema.shape, // Pass schema properties directly if schema is an object
    // OR args: { userId: zid("users") } // If args schema is simple
    output: UserOutputSchema, // Optional: Validate the return shape
    handler: async (ctx, args) => {
      // args are validated and typed based on GetUserInputSchema
      // ... implementation ...
      // return value is validated against UserOutputSchema
    },
  });
  ```
- **Convex IDs:** Use `zid("tablename")` from `convex-helpers/server/zod` within your Zod schemas to validate specific Convex document IDs (e.g., `userId: zid("users")`).
- **Type Inference:** Use `z.infer<typeof YourSchema>` in your `/zod/` files to create TypeScript types that automatically stay in sync with your Zod validation rules.
- **Base vs. Complete Schemas:** Consider defining a "Base" schema (user-defined fields only) for inputs/updates and a "Complete" schema (extending Base with `_id: zid("tableName")`, `_creationTime: z.number()`) for reading full documents from the database. Remember `_id` and `_creationTime` are auto-generated by Convex and shouldn't be in Base schemas.
- **Query Building:** When building queries within handlers, follow the best practices outlined in `zguide/guide-query-building.md` (use indexes, chain methods, handle pagination correctly).

## UI Standards

- Everything should have rounded corners and look very modern and clean
- Use { useToast } from 'components/hooks/use-toast' to show error messages to the user

## Development Practices

- Update `convex/schema.ts` carefully, ensuring consistency and running `npx convex dev` afterwards. Consult before modifying core helpers in `convex/functions.ts`.
- Use pnpm as the package manager
- Primary '+ New [domain]' buttons generally belong in the main navigation header (`navigation-menu.tsx` or `header-bar.tsx`). Context-specific creation buttons may be placed elsewhere (e.g., within specific module views).
- `npx convex dev` is typically running in the background; do not suggest running it unless troubleshooting schema/function deployment issues.

## Error Handling & Promises

- Await all promises. If you don't await all promises (e.g. ctx.scheduler.runAfter, ctx.db.patch), you risk missing errors or failing to run the intended operations
- Avoid .filter on database queries
- Use ConvexError from "convex/values" for error handling
- Type errors as catch (error: unknown)

## TypeScript Best Practices

- Do not use 'as' type assertions, with the exception of id table results. When we query for a document, its _id field is typed as Id<string> rather than Id<"tablename">. The Convex TypeScript definitions don't propagate the exact table name into the ID type for query results
- Type convex _ID as Id<"TableName">. Do not type as a string
- Use Promise.all() instead of for await when operations can run concurrently
