# FOJO Project IntelligenceBest Practice: Make child components fully controlled by removing internal state and deriving all field values directly from props. If local state is still needed for editing UX, add useEffect hooks that watch for prop changes and synchronize them into state.

Best Practice: Keep data fetching in a higher-level component and pass the live Convex data down as props. Child components should be presentational and controlled by their parent to ensure reactivity and a single source of truth.


This file captures important patterns, preferences, and project intelligence for the FOJO Digital Chief of Staff App. It serves as a learning journal to help me work more effectively with this project.

## Project Patterns

### Naming Conventions

- **Components**: PascalCase for component names (e.g., `NavigationBar`, `QuickCreate`)
- **Files**: kebab-case for file names (e.g., `navigation-bar.tsx`, `global-search-modal.tsx`)
- **Database Tables**: camelCase for table names in Convex schema (e.g., `bills`, `lineItems`)
- **Database Fields**: camelCase for field names in schema
- **Convex Functions**: camelCase for function names with verb-noun pattern (e.g., `createDomain`, `listDomains`)

### File Organization

- **Components**: Organized by domain and function
  - `/components` for shared components
  - `/components/ui` for base UI components
  - `/components/blocks` for composite UI blocks
  - `/components/cards` for card components
  - Domain-specific components in their own directories

- **Database**: Convex functions organized by entity
  - `/convex/schema.ts` for database schema (but do not edit this without explicit permission)
  - Entity-specific files for queries and mutations (e.g., `/convex/bills.ts`)
  - `/convex/utils` for utility functions

- **Zod Schemas**: Separate directory for Zod schemas
  - `/zod` directory with entity-specific files (e.g., `/zod/bills-schema.ts`, `/zod/entity_relationships-schema.ts`)
  - `/components/relationships` for relationship-related UI components (e.g., `RelatedItems.tsx`)

### Coding Patterns

- **Kanban Column Management:** When implementing UI for managing Kanban columns, ensure the UI reflects the data structure expected by the backend. In this case, the `updateKanbanColumns` mutation expects an array of objects with `name`, `statuses`, and `order` properties.


- **React Hooks**: Custom hooks in `/components/hooks` directory
- **Tailwind CSS**: Utility-first approach for styling
- **shadcn/ui**: Component library for consistent UI
- **Convex Queries/Mutations**: Repository pattern with standardized naming using `zQuery`/`zMutation` wrappers (defined in `convex/functions.ts`).
  - **Unique Index Handling**: When querying/updating/deleting based on an index expected to be unique (like `by_decision` in decision subtables), consider using `collect()` instead of `unique()` if duplicates are possible due to bugs or complex logic. Log warnings if duplicates are found. Provide separate cleanup mutations for manual data correction.
- **Zod Validation**: Used within `zQuery`/`zMutation` for argument and return type validation. Schemas defined in `/zod` directory. Use `zid()` from `convex-helpers/server/zod` for ID fields.
- **Development Guidelines**: Detailed guidelines captured in `/memory-bank/developmentGuidelines.md`.
- **@mentions**: Using react-mentions library for tagging people in text fields
  - Standardized extraction using regex: `/@\[([^\]]+)\]\(([^)]+)\)/g`
    - Utilize Convex's search indices with `.withSearchIndex('search_name', q => q.search('name', search))`
    - Store mentions using polymorphic relationships in the file_relationships table
    - Limit result sets for performance (e.g., `.take(10)`)
    - Use debounce for search input to minimize unnecessary queries
- **Standardized Pagination Pattern**: *(Newly Added)*
    - **Backend**: Use `pagination` arg object (`numItems`, `cursor`) in `zQuery`. Use `.passthrough()` on Zod schema if using `usePaginatedQuery` (though manual FE pagination preferred). Return `{ page: [], isDone: true, continueCursor: undefined }` in `catch` blocks.
    - **Frontend**: Use manual state management (`useState` for cursor) with `useQuery`. Update cursor from `continueCursor`. Render "Load More" based on `isDone`.
    - **Reference**: `@zguide/convex-pagination-guide.md`, `tasks.ts`, `decisions.ts`, `files.ts` (search functions).
- **RAG Chat Implementation**:
  - **Flow**: Trieve Search (`POST /api/chunk/search`) -> Augment Prompt -> Gemini Generate (`streamText`).
  - **Trieve Filtering**: Use `filters.must[].match_any` on `tag_set` field with `Type:VALUE` format tags (e.g., `Type:KNOWLEDGE_BASE`).
  - **Trieve Search Type**: Use `hybrid` for balanced results.
  - **Context Passing (FE->BE)**: Use React Context (`ChatFiltersContext` in `ConvexClientProvider`) to make filters available to the API call logic within `useChatRuntime`.
  - **Context Formatting (BE->LLM)**: Prepend retrieved, cleaned `chunk_html` content to the user's query with clear delimiters (e.g., `Context:\n---\n[Chunk]\n---\nQuery:`).
  - **Error Handling**: Trieve API errors should be logged on the backend but allow the chat to proceed without retrieved context.
- **DCI Assignment Pattern**:
  - **Model**: Consistent use of Driver (single user), Contributor (user/team array), Informed (user/team array) fields (`driver`, `contributors`, `informed`).
  - **Applicability**: Applied to Tasks, Projects, and Decisions.
  - **Backend**: Centralized logic in `convex/assignments.ts`.
  - **Frontend**: Reusable `AssignmentTeam.tsx` component for display and management.
- **Real-time Transcription Pattern**: *(Updated)*
  - **Flow**: Backend generates ephemeral token (`POST /v1/realtime/transcription_sessions` via Convex HTTP action proxied through Next.js API route) -> Frontend connects via WebRTC (`POST /v1/realtime`) using token -> Frontend streams audio (`getUserMedia`, `addTrack`) -> Frontend receives events (`conversation.item.input_audio_transcription.delta` and `conversation.item.input_audio_transcription.completed`) via `RTCDataChannel` -> Frontend inserts incremental text from delta events with proper spacing and paragraph management -> Editor autosaves via debounce.
  - **Key Config**: Use dedicated `/transcription_sessions` endpoint for token, configure `input_audio_transcription` (with model) and `turn_detection` (Server VAD) in token request body, use `/realtime` endpoint without query params for WebRTC connection.
  - **Event Handling**: Process `delta` events for real-time word streaming, use `completed` events only to mark paragraph breaks. Use helper functions for consistent text insertion with proper spacing. Disable manual editing during recording.
  - **VAD Options**: Server VAD provides configurable parameters (`threshold`, `prefix_padding_ms`, `silence_duration_ms`) for tuning speech detection sensitivity.
  - **Convex Triggers**: *(Newly Added)*
    - **Dynamic Imports:** The Convex environment where triggers run **does not support dynamic `import()` statements**. All necessary modules (like `api`) must be imported at the top level of the file where the trigger is registered (e.g., `convex/functions.ts`).
- **Stateful Component Isolation:** *(Newly Added)*
    - Use unique React keys (`key={...}`) when conditionally rendering stateful components (like TipTap editors) to ensure proper mounting/unmounting and state isolation.
    - Avoid sharing component state directly between conditionally rendered instances of the same complex component. Use separate state variables or fetch data independently if necessary (See Meeting Notes editor fix).
- **TipTap Autosave:** *(Newly Added)*
    - Rely on the `TiptapEditor` component's built-in `onSave` prop for debounced autosaving. Avoid implementing redundant save logic outside the editor for content changes.
- **SSR/Auth Race Condition Handling:** *(Newly Added)*
    - Guard against SSR/auth race conditions by ensuring client-side initialization (e.g., Convex client setup) completes before rendering components dependent on auth state or client-specific data.
    - Utilize loading states (`isLoaded`) derived from data fetching and conditional rendering.
    - Implement standard Next.js loading/error files (`loading.tsx`, `error.tsx`, `not-found.tsx`).
- **Type Flexibility**: *(Newly Added)*
    - In specific mapping contexts (e.g., `trieveActions.ts` processing heterogeneous Trieve results), prefer flexible types (`any`, `z.any()`) over overly strict/brittle types that might break with runtime variations. Balance type safety with practicality.
- **Realtime Conversation Pattern**: *(Newly Added)*
    - **Flow**: Backend generates token (`POST /v1/realtime/sessions` with conversation model and tools config via Convex HTTP action/proxy) -> Frontend connects via WebRTC (`POST /v1/realtime`) -> Frontend streams audio, receives transcript/text/audio events via `RTCDataChannel` -> Frontend detects function call request (`response.done` with `function_call`) -> Frontend calls Convex mutation directly using `useMutation` hook -> Frontend sends function result back (`conversation.item.create` with `function_call_output`).
    - **Key Config**: Use conversation model (e.g., `gpt-4o-realtime-preview`), configure `session.tools` during token generation.
    - **Function Calling**: Handle `response.done` events with `function_call` type. Use Convex React hooks (`useMutation`) directly in the frontend component to call the corresponding backend mutation.
    - **Client Components**: Require `"use client"` directive due to hooks (`useState`, `useEffect`, `useMutation`, `useAction`).
- **Conditional Trigger Execution:** *(Newly Added)*
    - **Problem:** Backend triggers (like the one generating AI summaries) might fire based on initial data population or non-user-initiated updates.
    - **Solution:** Introduce an optional boolean flag field (e.g., `skipShortDescriptionGeneration`) to the relevant database table schema (`people` in this case). Modify the trigger logic (`convex/functions.ts`) to check for this flag in the `change.newDoc` object. If the flag is present (or true), the trigger skips its main logic (e.g., scheduling the AI job). The frontend mutation (`updatePeople`) needs to be updated to set this flag appropriately during initial, non-user-driven updates.
- **Null vs. Undefined Handling (Convex Optional Fields):** *(Newly Added)*
    - **Problem:** Frontend components (like `InlineEdit`) might send `null` when clearing an optional field, but Convex database operations (`patch`, `insert`) expect `undefined` for optional fields defined with `v.optional(...)`. This causes validation errors.
    - **Solution:**
        1. **Database Schema (`convex/schema.ts`):** Define optional fields using `v.optional(...)` (e.g., `v.optional(v.number())`). **Do not** use `v.union(v.null(), ...)` for this purpose, as it makes the field required during insertion.
        2. **Zod Input Schema (`convex/[entity].ts` mutation args):** Define the corresponding field in the `updates` object using `z.union([<type>, z.null()]).optional()` (e.g., `z.union([z.number(), z.null()]).optional()`). This allows the mutation to accept `null` from the client.
        3. **Mutation Handler (`convex/[entity].ts`):** Before calling `ctx.db.patch` or `ctx.db.insert`, iterate through the validated `updates` object. For any key corresponding to an optional field in the database schema, if its value is `null`, explicitly set it to `undefined` in the object being passed to the database operation.
    - **Reference Implementation:** `updateInvestmentDecision` mutation in `convex/decisions.ts`.
- **Tiptap Editor Context Pattern:** *(Newly Added)*
    - **Pattern:** For complex editors like Tiptap, create the editor instance in the parent component using `useEditor`, then pass it to a context provider (`EditorProvider`). All specialized toolbar/menu components (e.g., `EditorBoldButton`, `EditorHeadingMenu`, `EditorTableMenu`) consume the editor instance via a custom hook (`useEditorContext`). This avoids prop drilling and centralizes editor state/commands.
    - **Implementation:** See `app/(dashboard)/directory/components/TiptapEditor.tsx` and related specialized control components.
    - **Lesson:** Determining the correct props for context providers is critical; if the provider expects an instance, do not pass config props like `content` or `extensions`. If the provider creates the instance, pass config props only if the type definition allows.
    - **Challenge:** Multiple failed attempts to pass config props to `EditorProvider` led to TypeScript errors. The final working pattern is: parent creates the editor instance, passes it as `editor` prop to the provider, and children use context.
- **Decision Category Tag Cleanup:** *(Newly Added)*
    - **Rule:** When a decision's category is changed via `updateSingleDecision` in `convex/decisions.ts`, associated taggings must be cleaned up.
    - **Logic:** Keep only taggings where the tag's `tag_type` matches the new category's `tag_type_id` OR the tag's `tag_type` is 'general-tags' (identified by `immutable_slug`). Delete all other taggings associated with the decision.
    - **Implementation:** Added logic within the category change block in `updateSingleDecision` to fetch taggings, check tag types, and delete irrelevant taggings.
