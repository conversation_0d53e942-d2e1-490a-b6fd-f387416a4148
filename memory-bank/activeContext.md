# Active Context

## Current Work Focus

As of April 14, 2025, the FOJO Digital Chief of Staff App development is focused on implementing core activity tracking features, refining the real-time conversation agent, and completing document management enhancements.

1.  **Fix Decision Category Switching:** Investigate and fix the root cause of duplicate subtable record creation during category switching in `updateSingleDecision`.
2.  **Implement Decision Category UI:** Build out the UI for creating and managing decision categories, including custom fields, statuses, and kanban columns.
3.  **Implement Decision Creation Flow:** Update the decision creation flow to select a category and show the appropriate fields based on the category's `subtable_type`.
4.  **Implement Decision Detail View:** Update the decision detail view to fetch and display data from the appropriate subtables based on the category's `subtable_type`.
5.  **Enhance Investment Details Editing:** Implement a proper date picker for `target_close_date` and potentially a multi-line editor for `notes` in the Investment Details card.
6.  **Implement Activity Feeds:** Build out the actual activity log functionality for Projects, Decisions, and Tasks (currently placeholders).
7.  **Implement Project Timeline:**
    *   **Core Goal:** Clearly display recent project activity (manual and automatic updates).
    *   **Data Model:** Utilize `project_updates` table, handle dates, link sources, include `short_description`.
    *   **UI (`ProjectTimeline.tsx`):** Display chronologically, show content/author/date, icons/badges, links, attachments, inline manual update, empty state button, filters.
    *   **Backend:** Ensure `createProjectUpdate` handles manual updates. Implement triggers/actions for automatic updates.
8.  **Realtime Conversation Agent Integration & Refinement:**
    *   Integrate into actual UI flows (e.g., project creation/editing).
    *   Implement LLM logic in Convex action for field extraction.
    *   Implement Convex mutations in action to save data.
    *   Refine UI/UX (transcript display, status indicators).
    *   Test directory search fallback and prompt context usage.
9.  **Complete Document Relationship UI:** Fully implement the search result display and selection logic within the `RelationshipManager` component modal/popover.
10. **Implement Placeholder Document Pages:** Build out list/view functionality for Collections, Contracts, and General Documents pages under `/documents`.
11. **Enhance Meeting Notes:**
    *   Add UI for viewing/editing meeting date and attendees.
    *   Test AI notes generation timing (5s delay).
    *   Test fix for saving deleted AI notes content.
    *   Verify tag management functionality.
12. **Fix AI Summary Trigger:** Implement setting the `skipShortDescriptionGeneration` flag in the `updatePeople` mutation (`convex/directory/directoryPeople.ts`).
13. **Implement Content Search:** Implement search utilizing the `search_content` index for KB articles (and potentially Meeting Notes).
14. **Refactor Remaining Mutations:** Convert remaining standard `mutation` functions in `convex/files.ts` to use `zMutation`.
15. **Refine Documents Dashboard:** Update main `/documents` page content.
16. **Testing:** Add tests for recent features and refactors.
17. **Address Remaining TODOs:** Review code for outstanding TODO comments.
18. **Address Remaining Type Issues:** Investigate/refactor remaining `any` types or `field()` access issues.
19. **Refine Relationship Types:** Consider if more specific relationship types are needed.
20. **Continue Core Features:** Resume work on other modules (Bills, Reports, Admin, etc.).

## Recent Changes

*(Previously completed work includes implementing RAG Chat, Knowledge Base features, Meeting Notes v1, Relationship Management foundations, DCI assignments, and initial Detail Page layouts.)*

1.  **Client Details Page Implementation (Full Stack):** *(Completed)*
    *   Created a new `ClientDetailsPageDataSchema` in `zod/clients-schema.ts` to serve as the single source of truth for the client details page.
    *   Implemented a unified `getClientDetailsPageData` query in `convex/clients/clientQueries.ts` to fetch all necessary data for the page.
    *   Created a `ClientDetailsContext` to provide the fetched data to all components on the page.
    *   Built the main layout for the client details page at `app/(dashboard)/clients/[id]/layout.tsx`, including the header, action bar, and submenu.
    *   Created placeholder pages for the overview, initiatives, workspace, and documents tabs.
    *   Built and integrated all components for the overview tab, including `PrincipalCard`, `ClientDetailsCard`, `ClientTeamSection`, `ActiveItemsComponent`, and `ContactInformationComponent`.
    *   Resolved several TypeScript errors in the `getClientDetailsPageData` query related to incorrect index usage and field assumptions.
    *   Updated the `ClientCard` component to link to the new client details page.

1.  **Client Card Refactor (Full Stack):** *(Completed)*
    *   Refactored the `ClientCard` component with a new, more detailed UI design.
    *   Updated the `convex/clients/clientQueries.ts` `listClients` query to be a full-stack data source, joining `clients` with `client_assignments`, `users`, and `badges` to provide all necessary data.
    *   Added the `client_badges` field to the `clients` table in `convex/schema.ts` and the corresponding `zod/clients-schema.ts`.
    *   Created a new frontend view model `ClientData` in `components/clients/types.ts` to provide a clean data contract for the new component.
    *   The "Connections" metric is currently a placeholder and needs to be implemented with real data.

2.  **AI Kanban Reporting (Reports Module):** *(Completed)*
    *   Implemented two new backend actions in `convex/actions/aiActions.ts`: `tagReporterColumnSummaryAction` (summarizes each Kanban column) and `tagReporterColumnComparisonAction` (compares/contrasts the summaries) using Google Gemini Flash.
    *   Updated the frontend in `components/cards/TagKanbanBoard.tsx` to call these actions when the "Generate Report" button is clicked, displaying the AI-generated summaries and comparison in a modern, card-based UI section below the Kanban board.
    *   Resolved issues with Convex codegen and deployment to ensure the frontend could access and trigger the new actions. All changes were strictly additive; no existing Kanban or drag-and-drop logic was replaced or removed.

3.  **Decision Categorization System:** *(Completed)*
    *   Implemented a flexible decision management system with core table and specialized subtables.
    *   Added `decision_categories` table with critical `subtable_type` field to determine data storage strategy.
    *   Updated `decisions` table with `decision_category_id` field and added `by_decision_category` index.
    *   Created specialized subtables (`general_decisions`, `investment_decisions`) for type-specific fields.
    *   Added `decision_custom_data` table for user-defined fields using Entity-Attribute-Value pattern.
    *   Updated triggers in `convex/functions.ts` to handle description field moved to subtables.
    *   Updated Zod schemas in `zod/decisions-schema.ts` to match the new structure.
    *   Documented the pattern in `plan.md` and `systemPatterns.md`.
    *   Implemented decision category switching logic in `updateSingleDecision` to transfer description between subtables.
    *   Addressed `unique()` error by making `getDecisionDetails`, `updateDecisionDescription`, and `deleteDecisions` resilient to duplicate subtable records (using `collect()` instead of `unique()` and logging warnings).
    *   Refactored `deleteDecisions` to robustly delete records from all potential subtables.
    *   Added manual utility mutation `cleanupDuplicateSubtableRecords` to address existing duplicates.
    *   Confirmed that both `QuickCreate` and `HeaderBar` default new decisions to the 'General' category.
    *   Implemented tag cleanup logic in `updateSingleDecision` to remove irrelevant tags when a decision's category changes, keeping only 'general-tags' and tags associated with the new category's `tag_type_id`.

2.  **Decision Type Component Implementation & Autosave:** *(Completed)*
    *   Created `components/decisions/DecisionType.tsx` to handle 'approval' and 'selection' types.
    *   Added fields for financial implications (amount, currency, time period, years) and option implications (yes/no means) to the 'approval' form.
    *   Implemented autosave functionality using `onBlur` and `onCheckedChange` handlers, calling the `updateDecisionTypeDetails` mutation.
    *   Integrated the component into the Decision Overview page (`app/(dashboard)/decisions/[id]/overview/page.tsx`), passing initial data and the save handler.
    *   Added `updateDecisionTypeDetails` mutation in `convex/decisions.ts`.
    *   Updated `convex/schema.ts` and `zod/decisions-schema.ts` with the new decision type fields.
    *   Implemented currency formatting for normalized cost display and the main amount input field using `toLocaleString` and dynamic input type switching.

3.  **Convex Files Refactoring (Zod/zQuery/zMutation):** *(Completed)*
    *   Refactored `convex/files.ts` to consistently use `zQuery` and `zMutation` wrappers with Zod schemas.
    *   Standardized argument/return type validation and ID validation (`zid()`).
    *   Added JSDoc comments and basic error handling.
    *   Covered most functions related to files, KB articles, meeting notes, relationships, and documents.
4.  **Pagination Validation Fix (usePaginatedQuery):** *(Completed)*
    *   Resolved server-side validation error by adding `.passthrough()` to pagination Zod schemas in `convex/files.ts` search functions.
    *   Removed client-side workarounds.
5.  **Pagination Schema Standardization:** *(Completed)*
    *   Updated shared `PaginationSchema` (`zod/pagination-schema.ts`) with optional `sortBy`/`sortDirection`.
    *   Refactored pagination logic in `convex/entity_relationships.ts` and `convex/tags.ts` to use the standard schema.
6.  **TypeScript Error Resolution & Pagination Standardization:** *(Completed)*
    *   Resolved various TypeScript errors related to pagination and types.
    *   Standardized pagination in `convex/files.ts` search functions (`pagination` arg with `numItems`/`cursor`).
    *   Updated frontend components to use manual pagination state.
    *   Corrected type handling in `convex/actions/trieveActions.ts`.
    *   Fixed interface issues in `ProjectTimeline.tsx`.
    *   Documented approach in `@zguide/convex-pagination-guide.md` and `@zguide/tasks-decisions-pagination.md`.
    *   Confirmed resolution via `tsc --noEmit`.
7.  **Realtime Conversation Agent (Initial Implementation):** *(Completed)*
    *   Created `RealtimeConversationAgent.tsx` component.
    *   Added Convex HTTP action (`handleRealtimeConversationSessionRequest`) and Next.js proxy route for OpenAI conversation mode session setup.
    *   Implemented WebRTC connection, microphone access, audio handling, and data channel processing for server events (transcripts, text, function calls).
    *   Implemented client-driven function calling and handling of server function call requests.
    *   Created placeholder Convex action (`aiUpdateEntityFieldsAction`).
    *   Integrated into a test page.
8.  **Realtime Conversation Function Calling Fix:** *(Completed)*
    *   Diagnosed and fixed "No handler found" error by refactoring `RealtimeConversationAgent.tsx` to use Convex React hooks (`useMutation`) directly instead of `window.api`.
9.  **Realtime Conversation Directory Search Enhancement:** *(Completed)*
    *   Added optional `type` filter to directory search schema and query.
    *   Created wrapper action `searchDirectoryAction` with fallback logic.
    *   Updated agent component to use the action.
10. **Realtime Conversation Prompt Context Enhancement:** *(Completed)*
    *   Added `getDirectoryEntityNames` query.
    *   Updated Project overview page to fetch DCI names and include them in the agent's instructions.
11. **Directory Detail Pages (`short_description` & `research`):** *(Completed)*
    *   Added fields and UI for AI summary and research notes to People/Organization detail pages.
    *   Updated schemas, mutations, and added Convex trigger for AI summary generation.
12. **Tiptap Editor Autosave Fix:** *(Completed)*
    *   Fixed `onSave` triggering during initial load by adding an `isInitialized` flag to `TiptapEditor`.
13. **Initial Load Toast Notification Fix:** *(Completed)*
    *   Fixed spurious "updated successfully" toasts on directory page load by adding `isInitialLoad` flag and conditional toast display logic.
14. **AI Summary Trigger Fix (Partial):** *(Partially Completed)*
    *   Added `skipShortDescriptionGeneration` flag to `people` schema and updated trigger logic. *(Still requires setting the flag in the `updatePeople` mutation)*.
15. **Directory Detail Page Refinement (People/Org Linking):** *(Completed)*
    *   Removed the left-sidebar list of associated People/Organizations from both Person and Organization detail pages (`app/(dashboard)/directory/people/[id]/page.tsx`, `app/(dashboard)/directory/organizations/[id]/page.tsx`).
    *   Created `AssignPersonPopover.tsx` component using a command/search interface.
    *   Implemented immediate linking of existing people to an organization upon selection in the popover.
    *   Added an inline form within the popover to create a new person.
    *   Implemented logic to create and immediately link the new person to the organization upon saving the inline form.
    *   Replaced the old "Add Existing Person" modal trigger on the Organization detail page's "People" tab with the new popover.
    *   Deleted the unused `AddPersonToOrganizationDialog.tsx` component.
16. **Person Detail Page Refinement (Org Linking):** *(Completed)*
    *   Created `AssignOrganizationPopover.tsx` component for linking organizations to a person.
    *   Implemented immediate linking of existing organizations upon selection.
    *   Added an inline form to create and immediately link a new organization.
    *   Replaced the old "Add Organization" dialog trigger on the Person detail page's "Relations" tab with the new popover and updated button text to "+ Add Relationship".
    *   Deleted the unused `AddOrganizationDialog.tsx` component.
17. **Entity Deletion Pattern Refinement:** *(Completed)*
    *   Moved delete actions for Projects, Decisions, and Tasks into a 3-dot dropdown menu in their respective detail page layouts (`app/(dashboard)/[entity]/[id]/layout.tsx`).
    *   Implemented optimistic navigation (`router.push`) immediately upon delete confirmation.
    *   Added a 500ms delay after navigation before calling the delete mutation to mitigate race conditions with Convex reactivity.
    *   Updated confirmation modals (`AlertDialog` or `CollectionCardModal`) to handle loading states.
    *   Documented the pattern in `.clinerules`.
18. **Theme Tag Popover Configuration:** *(Completed)*
    *   Added `allowCreation` and `allowEditing` props to `AddTaggableTagPopover.tsx` to conditionally disable tag creation and editing functionalities.
    *   Updated `DecisionActionBar.tsx` to pass `allowCreation={false}` and `allowEditing={false}` to the popover when used for "Theme Tags" associated with "Investment Decisions", preventing creation/editing while still allowing adding/removing existing theme tags.
19. **Investment Decision Details & Editing:** *(Completed)*
    *   Created a general `updateInvestmentDecision` mutation (`convex/decisions.ts`) to handle updates for most fields in the `investment_decisions` subtable, replacing specialized mutations.
    *   Implemented `InlineEdit` component (`components/ui/inline-edit.tsx`) for direct, autosaving edits.
    *   Integrated `InlineEdit` into the Investment Details card (`app/(dashboard)/decisions/[id]/overview/page.tsx`) for most fields, including `target_close_date` (as text) and `gdrive_folder` (with link rendering).
    *   Refined the `updateInvestmentDecision` mutation to correctly handle `null` values sent from the frontend for optional fields (like cleared number inputs) by converting them to `undefined` before database operations, aligning with Convex schema expectations (`v.optional(...)`). Updated the Zod schema to accept `null` input for these fields.
    *   Adjusted vertical spacing in the Investment Details card for better density.
20. **Tiptap Editor Refactoring:** *(Completed)*
    *   Refactored `app/(dashboard)/directory/components/TiptapEditor.tsx` to use a new architecture with specialized, reusable control components (`EditorControls.tsx`, `EditorHeadingMenu.tsx`, `EditorTableMenu.tsx`, `EditorFormatButtons.tsx`) and an `EditorContext.tsx` provider.
    *   The final pattern involves the parent component (`TiptapEditor`) creating the editor instance via `useEditor` and passing it as a prop to `EditorProvider`.
    *   Child components (`TiptapEditorCore` and specialized controls) consume the editor instance via the `useEditorContext` hook.
    *   This resolved previous focus management issues (e.g., two-click dropdowns, focus loss).
    *   Encountered significant challenges determining the correct props for `EditorProvider` due to lack of visibility into its definition, requiring multiple iterations and attempts to pass configuration vs. instance.
21. **TypeScript Error Resolution (ActionBar):** *(Completed)* Fixed type errors in `components/decisions/ActionBar.tsx` related to the `importance` field by allowing 'urgent' and updating helper functions (`getImportanceStyle`, `getImportanceIcon`) and the handler (`handleImportanceChange`).
22. **DCI Assignment Refactoring & Fixes:** *(Completed)*
    *   Refactored `UserAssignmentPopover.tsx` to remove tabs, show a combined list (users/teams/people based on role), pre-fetch initial results, implement 50ms debounced search, and enable direct click-to-assign.
    *   Implemented keyboard navigation (Arrow keys, Home/End, Enter/Space) and focus management (initial focus on input, seamless transfer to list via ArrowDown).
    *   Adjusted popover width (`w-80`).
    *   Fixed TypeScript errors in `convex/directory/directoryActions.ts` and `convex/openai_tools/openai_directory.ts` by adding the `type` parameter.
    *   Updated `convex/directory/directory.ts` (`searchPeopleAndTeams`) to accept and filter by the `type` parameter.
    *   Fixed `removeFromArray` helper in `convex/assignments.ts` to use string comparison (`.toString()`) for reliable ID removal across different types (`Id<'users'>`, `Id<'teams'>`, `Id<'people'>`).
    *   Updated `AssignmentTeam.tsx` to pass all existing member IDs to the popover and correctly track dragged item type for validation.

## Next Steps

*(Consolidated and prioritized from previous 'Next Steps' and 'Immediate Action Items')*

1.  **Implement Client Card Connections Data:** Replace the placeholder value for "Connections" on the `ClientCard` with real data, likely derived from the `relationships` table.
2.  **Fix Decision Category Switching:** Investigate and fix the root cause of duplicate subtable record creation during category switching in `updateSingleDecision`.
2.  **Implement Decision Category UI:** Build out the UI for creating and managing decision categories, including custom fields, statuses, and kanban columns.
3.  **Implement Client Card Connections Data:** Replace the placeholder value for "Connections" on the `ClientCard` with real data, likely derived from the `relationships` table.
3.  **Implement Decision Creation Flow:** Update the decision creation flow to select a category and show the appropriate fields based on the category's `subtable_type`.
4.  **Implement Decision Detail View:** Update the decision detail view to fetch and display data from the appropriate subtables based on the category's `subtable_type`.
5.  **Enhance Investment Details Editing:** Implement a proper date picker for `target_close_date` and potentially a multi-line editor for `notes` in the Investment Details card.
6.  **Implement Activity Feeds:** Build out the actual activity log functionality for Projects, Decisions, and Tasks (currently placeholders).
7.  **Implement Project Timeline:**
    *   **Core Goal:** Clearly display recent project activity (manual and automatic updates).
    *   **Data Model:** Utilize `project_updates` table, handle dates, link sources, include `short_description`.
    *   **UI (`ProjectTimeline.tsx`):** Display chronologically, show content/author/date, icons/badges, links, attachments, inline manual update, empty state button, filters.
    *   **Backend:** Ensure `createProjectUpdate` handles manual updates. Implement triggers/actions for automatic updates.
8.  **Realtime Conversation Agent Integration & Refinement:**
    *   Integrate into actual UI flows (e.g., project creation/editing).
    *   Implement LLM logic in Convex action for field extraction.
    *   Implement Convex mutations in action to save data.
    *   Refine UI/UX (transcript display, status indicators).
    *   Test directory search fallback and prompt context usage.
9.  **Complete Document Relationship UI:** Fully implement the search result display and selection logic within the `RelationshipManager` component modal/popover.
10. **Implement Placeholder Document Pages:** Build out list/view functionality for Collections, Contracts, and General Documents pages under `/documents`.
11. **Enhance Meeting Notes:**
    *   Add UI for viewing/editing meeting date and attendees.
    *   Test AI notes generation timing (5s delay).
    *   Test fix for saving deleted AI notes content.
    *   Verify tag management functionality.
12. **Fix AI Summary Trigger:** Implement setting the `skipShortDescriptionGeneration` flag in the `updatePeople` mutation (`convex/directory/directoryPeople.ts`).
13. **Implement Content Search:** Implement search utilizing the `search_content` index for KB articles (and potentially Meeting Notes).
14. **Refactor Remaining Mutations:** Convert remaining standard `mutation` functions in `convex/files.ts` to use `zMutation`.
15. **Refine Documents Dashboard:** Update main `/documents` page content.
16. **Testing:** Add tests for recent features and refactors.
17. **Address Remaining TODOs:** Review code for outstanding TODO comments.
18. **Address Remaining Type Issues:** Investigate/refactor remaining `any` types or `field()` access issues.
19. **Refine Relationship Types:** Consider if more specific relationship types are needed.
20. **Continue Core Features:** Resume work on other modules (Bills, Reports, Admin, etc.).

## Active Decisions and Considerations

*(Kept most decisions, focusing on recent ones)*

1.  **Decision Categorization System**: Use a flexible system with a core table (`decisions`), a configuration table (`decision_categories`), specialized subtables (`general_decisions`, `investment_decisions`), and a custom data table (`decision_custom_data`). The critical `subtable_type` field in the category determines which subtable to use.
2.  **New Decision Default Category**: New decisions default to 'General' category via hardcoded ID in `QuickCreate` and `HeaderBar`. Consider making this configurable (e.g., dropdown, default setting).
3.  **Duplicate Subtable Records Handling**: `cleanupDuplicateSubtableRecords` is a manual utility; automatic cleanup is not implemented. Read/update/delete functions now handle duplicates gracefully using `collect()` instead of `unique()`.
4.  **RAG Implementation**: Use Trieve `POST /api/chunk/search` + manual prompt augmentation. Hybrid search, `Type:VALUE` tag filtering.
5.  **Data Access Patterns**: Standardize on `zQuery`/`zMutation` with Zod schemas. Consolidated tag functions.
6.  **UI Component Architecture**: Ongoing refinement (`RelationshipManager`, Action Bars, `RealtimeTranscriptEditor`).
7.  **Development Guidelines**: Adhere to `developmentGuidelines.md`, `guide-zod-schema.md`. Emphasize `zQuery`/`zMutation`.
8.  **Real-time Updates**: Leverage Convex; WebRTC for OpenAI Realtime API.
9.  **Performance Optimization**: Monitor queries; added search indexes.
10. **Documents Section Structure:** Shared layout `documents/layout.tsx`.
11. **Document Sub-types:** `files` table + specific sub-tables pattern.
12. **Relationship Storage:** Polymorphic `file_relationships` table.
13. **DCI Assignment Model:** Dedicated fields (`driver`, `contributors`, `informed`, `requestor`) for Tasks, Projects, Decisions. *(Updated)*
14. **Entity Relationship Type:** Currently generic `'related'`. Refinement may be needed.
15. **Detail Page Structure:** Standardized using Next.js App Router layouts/sub-routes.
16. **Tag Display:** Standardized within Action Bars.
17. **Real-time Transcription Strategy:** Dedicated component (`RealtimeTranscriptEditor`), Server VAD, real-time delta processing, WebRTC via `/realtime` endpoint, backend token generation via `/transcription_sessions`.
18. **Meeting Note Summary Generation:** Triggered indirectly after AI notes update via `internalUpdateMeetingNoteContent` calling `scheduleFieldGeneration`.
19. **Meeting Note AI Notes Generation:** Convex trigger on `transcript` schedules `updateAiNotesFromTranscript` action (5s delay).
20. **Transcript Editing Flow:** Conditional editability with warning modal (`CollectionCardModal`).
21. **Stateful Component Isolation:** Use unique React keys for conditionally rendered stateful components.
22. **SSR/Auth Race Condition Handling:** Ensure client waits for initialization; use Next.js loading/error files.
23. **TipTap Autosave:** Rely on editor's `onSave` prop.
24. **Hover State with Interactive Children Pattern:** Use `keepExpanded` state, callbacks, event stopping, `handleClickOutside`.
25. **Standardized Pagination:** `pagination` arg (`numItems`, `cursor`), manual frontend state.
26. **Type Handling Flexibility:** Use `any`/`z.any()` judiciously for heterogeneous external data.
27. **Realtime Conversation Strategy:** OpenAI conversation mode via WebRTC. Frontend uses Convex hooks directly for function calls. Wrapper action for directory search.
28. **Realtime Conversation Context:** Provide richer initial context (DCI names) in prompt.
29. **Conditional Trigger Execution:** Use optional schema flag (e.g., `skipShortDescriptionGeneration`) checked by trigger.
30. **Popover Interaction Model (Directory Linking):** Use immediate action on selection (linking existing person) or inline form submission (creating + linking new person) within popovers for adding relationships, similar to `AddTaggableTagPopover`.
31. **Optimistic Deletion Pattern:** Use optimistic navigation (`router.push`) followed by a 500ms delay before calling the delete mutation to handle potential race conditions between navigation and Convex reactivity on detail pages.
32. **Configurable Component Behavior:** Components like `AddTaggableTagPopover` are made configurable via props (`allowCreation`, `allowEditing`) to adapt behavior based on context (e.g., disabling creation/editing for Theme Tags).
33. **Inline Editing Pattern:** Use a dedicated `InlineEdit` component for fields requiring direct, autosaving edits on detail pages.
34. **Null vs. Undefined Handling:** Zod schemas for mutations accept `null` for optional fields cleared by the frontend. Mutation handlers explicitly convert `null` to `undefined` before database operations (`patch`/`insert`) to satisfy Convex's `v.optional()` schema requirement.
35. **Tiptap Editor Architecture:** *(Newly Added)* Use specialized control components (`EditorBoldButton`, `EditorHeadingMenu`, etc.). Editor instance created in parent (`TiptapEditor`), passed via prop to `EditorProvider`, consumed by children via `useEditorContext`.
36. **DCI Assignment Popover Interaction:** *(Newly Added)* Use a single combined list, pre-fetch initial results, 50ms debounce search, click-to-assign, keyboard navigation (initial focus on input, ArrowDown transfers to list).
37. **Client Card View Model:** *(Newly Added)* Use a dedicated frontend view model (`ClientData`) to decouple the `ClientCard` component from the raw database schema. The `listClients` query is responsible for shaping the data to fit this model.

## Recent Challenges

*(Pruned resolved/less relevant items)*

1.  **Decision Subtable Duplicates:** *(Partially Resolved)* `unique()` error in decision subtables: Read/update/delete functions now handle duplicates gracefully, but the root cause (duplicate creation during category switch) still needs fixing.
2.  **Type Safety & Inference:** *(Ongoing)* Persistent issues require investigation/manual fixes despite refactoring. Using `any` sometimes necessary.
3.  **Tooling Errors:** *(Persistent)* Repeated failures with `replace_in_file`.
4.  **RAG Error Handling:** Trieve API errors logged but don't halt chat (proceeds without context). *(No Change)*
5.  **Convex Trigger/Action Flow (Meeting Notes):** *(Improved)* Complex flow debugged and refined (5s delay). Still monitoring AI notes generation timing.
6.  **Initial Load Side Effects (AI Summary Trigger):** *(Partially Resolved)* Frontend fixes implemented. Backend trigger still fires; requires setting `skipShortDescriptionGeneration` flag in mutation.
7.  **Tiptap EditorProvider Props:** *(Resolved)* Significant difficulty determining the correct props for `EditorProvider` during refactoring, requiring multiple attempts and ultimately settling on passing the pre-created editor instance as a prop.
8.  **Assignment Popover Focus Management:** *(Resolved)* Initial attempts to auto-focus the list broke input focus. Final solution uses `autoFocus` on input + `onKeyDown` handler on input to transfer focus to list on ArrowDown.
