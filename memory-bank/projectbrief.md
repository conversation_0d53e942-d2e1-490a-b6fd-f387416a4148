# <PERSON>OJO Digital Chief of Staff App - Project Brief

## Overview

FOJO is a comprehensive digital chief of staff application designed to streamline organizational workflows, project management, and decision-making processes. The platform serves as a centralized hub for teams to manage projects, track tasks, document decisions, store important files, manage bills, maintain a company directory, and generate insightful reports. With its modern, clean interface featuring rounded corners and an intuitive navigation system, FOJO empowers teams to work more efficiently and maintain clear visibility across all aspects of their operations.

## Core Features

### Homepage
- Personalized dashboard with recent activity and notifications
- Quick access to priority tasks and projects
- Key performance indicators and metrics
- Upcoming deadlines and milestones
- Configurable widgets

### Projects
- Multiple view options: Kanban, card grid, and table views
- Project status tracking (Not Started, In Progress, Paused, Completed, Cancelled, Perpetual)
- Priority management (Low, Medium, High, Urgent)
- Progress tracking with percentage-based completion
- Team member assignment and collaboration tools
- Milestone tracking and task management integration

### Tasks
- Three view modes: Kanban board (default), card grid, and table view
- Task filtering by status (To Do, In Progress, Blocked, Completed, Cancelled)
- Priority filtering (Low, Medium, High, Critical)
- Task assignment capabilities
- Due date tracking and timeline visualization
- Integration with Projects

### Decisions
- Decision documentation with context and rationale
- Status tracking for decision implementation
- Decision categorization and tagging
- Responsible parties and stakeholders
- Decision history and audit trail
- Integration with projects and tasks

### Documents
- Document storage and organization by category
- Version control and document history
- Access control and permission management
- Preview capabilities for common file types
- Search functionality across document content
- Integration with projects and decisions

### Bills
- Invoice and bill tracking
- Payment status monitoring (Paid, Pending, Overdue)
- Expense categorization and reporting
- Budget allocation and monitoring
- Vendor management and payment history
- Financial document storage

### Directory
- People: Profiles of team members with contact information, roles, departments
- Organizations: Information about partner companies, clients, vendors
- Search and filter capabilities
- Detailed profile pages
- Organizational hierarchy visualization
- Integration with tasks and projects

### Reports
- Overview dashboards with key metrics and KPIs
- Custom report generation with filtering options
- Resource allocation analysis and tracking
- Performance metrics across projects and tasks
- Time-based trend analysis
- Export capabilities for presentations and sharing

### Admin
- User management and permissions
- Team creation and management
- Tag and category management
- System integrations configuration
- Global settings and preferences
- Audit logs and system monitoring

## Technical Implementation

FOJO is built using a modern tech stack including:
- Next.js 15 and React 19 for the frontend
- Tailwind CSS with shadcn UI components for styling
- Convex database for data storage and retrieval
- Vercel AI SDK with OpenAI integration for AI capabilities

The application features a responsive design with a collapsible sidebar navigation system that adapts to different screen sizes while maintaining functionality and usability.

## Project Goals

1. Create a unified platform that centralizes organizational workflows
2. Improve team collaboration and communication
3. Enhance decision-making processes with better documentation
4. Provide clear visibility into project status and progress
5. Streamline financial management and reporting
6. Build an intuitive, modern interface that requires minimal training
7. Integrate AI capabilities to enhance productivity and insights
8. Onboarding Flow to onboard new clients. 
9. Request for Information flow to various third parties that relate back to clients. 
