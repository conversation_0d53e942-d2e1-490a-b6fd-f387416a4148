## Semantic Search

**Queries:**
- `semanticSearch:searchDocuments` - Searches documents using semantic search
- `semanticSearch:searchKnowledgeBase` - Searches knowledge base articles semantically
- `semanticSearch:searchMeetingNotes` - Searches meeting notes semantically
- `trieve:search` - Searches using the Trieve semantic search engine

**Mutations:**
- `trieve:indexDocument` - Indexes a document for semantic search
- `trieve:removeDocument` - Removes a document from the semantic search index## Bills (Additional Functions)

**Queries:**
- `bills:getBill` - Gets a bill by ID
- `bills:listBills` - Lists bills with filtering and pagination
- `bills:searchBills` - Searches bills by criteria
- `bills:getDueBills` - Gets bills that are due soon
- `bills:getBillsStats` - Gets statistics about bills

**Mutations:**
- `bills:createBill` - Creates a new bill
- `bills:updateBill` - Updates a bill
- `bills:deleteBill` - Deletes a bill
- `bills:markBillAsPaid` - Marks a bill as paid
- `bills:bulkUpdateBills` - Updates multiple bills at once## Dashboard & Reports

**Queries:**
- `dashboard:getSummaryStats` - Gets summary statistics for dashboard
- `dashboard:getRecentActivity` - Gets recent activity for dashboard
- `dashboard:getUpcomingTasks` - Gets upcoming tasks for dashboard
- `tasks:getDashboardTasks` - Gets tasks for dashboard display
- `decisions:getDashboardDecisions` - Gets decisions for dashboard display
- `reports:getFinancialReport` - Gets financial reports
- `reports:getActivityReport` - Gets activity reports
- `reports:getProjectStatusReport` - Gets project status reports

**Mutations:**
- `dashboard:updateDashboardPreferences` - Updates user's dashboard preferences## AI Actions & Prompts

**Actions:**
- `aiCategorizeAction:aiCategorize` - Uses AI to categorize line items
- `parseDocumentAction:parseDocument` - Uses AI to parse document contents
- `meetingNoteActions:generateMeetingNotes` - Generates AI meeting notes from transcript
- `writeTaskDescription:generateTaskDescription` - Generates AI description for tasks
- `analyzeUpload:analyzeDocument` - Analyzes uploaded documents using AI

**Queries:**
- `prompts:getPrompt` - Gets a prompt by ID or slug
- `prompts:listPrompts` - Lists available prompts

**Mutations:**
- `prompts:createPrompt` - Creates a new prompt
- `prompts:updatePrompt` - Updates a prompt
- `prompts:deletePrompt` - Deletes a prompt# Fojo System - Queries and Mutations

## Authentication

**Queries:**
- `auth:isAuthenticated` - Checks if the current user is authenticated
- `users:currentUser` - Gets the current authenticated user information
- `users:currentUserExists` - Checks if a specific user exists by ID

**Mutations:**
- `auth:signIn` - Signs in a user with provider credentials
- `auth:signOut` - Signs out the current user

## Users & People

**Queries:**
- `users:listUsers` - Lists all users with pagination
- `users:listUsersWithPeopleAndTeams` - Lists users with their associated people records and team memberships
- `users:get` - Gets a specific user by ID
- `users:getSummary` - Gets a summary of a user's information
- `users:currentUser` - Gets the currently authenticated user
- `directoryPeople:getPerson` - Gets a person record by ID
- `directoryPeople:getPersonByName` - Finds a person by name
- `directoryPeople:getPersonByUserId` - Gets a person record associated with a user ID
- `directoryPeople:listPeople` - Lists people records with filtering and pagination
- `directoryPeople:findDuplicatePeople` - Finds potential duplicate people records

**Mutations:**
- `users:checkNewUser` - Verifies if a user is new to the system
- `users:createMissingPeopleRecords` - Creates people records for users that don't have them
- `users:createUsers` - Creates new user records (single or bulk)
- `users:update` - Updates a user's information
- `users:updateUsers` - Updates multiple users' information
- `directoryPeople:createPeople` - Creates new people records
- `directoryPeople:updatePeople` - Updates people records
- `directoryPeople:deletePeople` - Deletes people records
- `directoryPeople:mergePeople` - Merges duplicate people records

## Teams

**Queries:**
- `teams:listTeams` - Lists all teams with filtering options
- `teams:getTeam` - Gets a team by ID
- `teams:getTeamMembers` - Gets members of a team
- `teams:searchTeams` - Searches teams by name
- `directory:searchPeopleAndTeams` - Searches people and teams by name
- `users:listUsersWithPeopleAndTeams` - Lists users with people and teams info

**Mutations:**
- `teams:createTeam` - Creates a new team
- `teams:updateTeam` - Updates a team
- `teams:deleteTeam` - Deletes a team
- `teams:addMember` - Adds a member to a team
- `teams:removeMember` - Removes a member from a team
- `teams:updateMemberRole` - Updates a team member's role

## Organizations

**Queries:**
- `directoryOrganizations:getOrganization` - Gets organization details by ID
- `directoryOrganizations:getOrganizationByName` - Finds an organization by name
- `directoryOrganizations:getOrganizationMembers` - Gets members of an organization
- `directoryOrganizations:listOrganizations` - Lists organizations with filtering and pagination
- `directoryOrganizations:listOrganizationsWithPeopleCount` - Lists organizations with count of associated people
- `directoryOrganizations:listVendors` - Lists vendor organizations
- `directoryOrganizations:searchOrganizations` - Searches for organizations by name
- `directoryOrganizations:findDuplicateOrganizations` - Finds potential duplicate organizations

**Mutations:**
- `directoryOrganizations:createOrganizations` - Creates new organization records
- `directoryOrganizations:createVendorRecords` - Creates vendor organization records
- `directoryOrganizations:getOrCreateOrganization` - Gets an org if it exists or creates it if not
- `directoryOrganizations:updateOrganizations` - Updates organization records
- `directoryOrganizations:removeOrganizations` - Deletes organization records
- `directoryOrganizations:mergeOrganizations` - Merges duplicate organization records

## Organization-People Relationships

**Queries:**
- `directoryRelationships:getOrganizationPeopleRelationship` - Gets relationship details
- `directoryRelationships:listOrganizationssByPerson` - Lists organizations related to a person
- `directoryRelationships:listPeopleByOrganization` - Lists people related to an organization

**Mutations:**
- `directoryRelationships:createOrganizationPeopleRelationship` - Creates relationships between orgs and people
- `directoryRelationships:updateOrganizationPeopleRelationship` - Updates relationships
- `directoryRelationships:removeOrganizationPeopleRelationship` - Removes relationships

## Projects

**Queries:**
- `projects:get` - Gets a project by ID
- `projects:listProjects` - Lists projects with filtering and pagination
- `projects:searchProjectsByName` - Searches projects by name
- `projects:countProjects` - Counts projects with optional status filter

**Mutations:**
- `projects:createProjects` - Creates new projects (single or bulk)
- `projects:update` - Updates project information (single or bulk)
- `projects:updateProjects` - Alternative method to update projects (bulk)
- `projects:deleteProjects` - Deletes projects

## Tasks

**Queries:**
- `tasks:getTask` - Gets a task by ID
- `tasks:listTasks` - Lists tasks with filtering and pagination
- `tasks:searchTasksByName` - Searches tasks by name
- `tasks:countTasks` - Counts tasks matching filters
- `tasks:getDashboardTasks` - Gets tasks for dashboard display

**Mutations:**
- `tasks:createTasks` - Creates new tasks (single or bulk)
- `tasks:updateTasks` - Updates task information (single or bulk)
- `tasks:deleteTasks` - Deletes tasks
- `tasks:completeTask` - Marks a task as completed with notes

## Decisions

**Queries:**
- `decisions:getDecision` - Gets a decision by ID
- `decisions:listDecisions` - Lists decisions with filtering and pagination
- `decisions:searchDecisionsByName` - Searches decisions by name
- `decisions:countDecisions` - Counts decisions matching filters
- `decisions:getDashboardDecisions` - Gets decisions for dashboard display

**Mutations:**
- `decisions:createDecisions` - Creates new decisions (single or bulk)
- `decisions:updateDecisions` - Updates decision information (handles category changes, including subtable data transfer and tag cleanup)
- `decisions:deleteDecisions` - Deletes decisions
- `decisions:approveDecision` - Approves or rejects a decision

## Files & Documents

**Queries:**
- `files:getDocumentByFileId` - Gets document by file ID
- `files:getDocumentById` - Gets document by document ID
- `files:getFileRecordBasic` - Gets basic file record information
- `files:getFileUrl` - Gets a URL for a file
- `files:getKnowledgeBaseArticle` - Gets a knowledge base article
- `files:getMeetingNote` - Gets meeting notes
- `files:getRecentlyModifiedFiles` - Gets recently modified files
- `files:getRelatedEntities` - Gets entities related to a file
- `files:listKnowledgeBaseArticles` - Lists knowledge base articles
- `files:searchKnowledgeBaseArticles` - Searches knowledge base articles
- `files:searchMeetingNotes` - Searches meeting notes
- `files:getByParentEntity` - Gets files by parent entity ID
- `files:getDocumentTypeCounts` - Gets counts of document types
- `files:getDocumentDefinitions` - Gets document definitions
- `files:getUserDocumentChecklist` - Gets document checklist for a user
- `files:getAttendeeDetails` - Gets details of meeting attendees

**Mutations:**
- `files:createKnowledgeBaseArticle` - Creates a knowledge base article
- `files:createMeetingNote` - Creates meeting notes
- `files:deleteKnowledgeBaseArticle` - Deletes a knowledge base article
- `files:deleteMeetingNote` - Deletes meeting notes
- `files:generateUploadUrl` - Generates a URL for file upload
- `files:saveFileMetadata` - Saves metadata for a file
- `files:updateKnowledgeBaseArticle` - Updates a knowledge base article
- `files:updateMeetingNote` - Updates meeting notes
- `files:deleteFile` - Deletes a file
- `files:processFile` - Processes a file after upload
- `files:updateFileParsedData` - Updates parsed data for a file
- `files:updateUserDocumentChecklist` - Updates document checklist for a user
- `files:updateDocumentAndChecklist` - Updates document and checklist together
- `files:scheduleDocumentAnalysis` - Schedules document analysis
- `files:updateDocumentStatus` - Updates document status
- `files:updateDocumentAiAnalysis` - Updates AI analysis for a document
- `files:createDocument` - Creates a document
- `files:updateFileField` - Updates a specific field in a file record

## Bills & Financial

**Queries:**
- `lineItems:getLineItemsByBill` - Gets line items for a bill
- `lineItems:listLineItems` - Lists line items with filtering and pagination
- `lineItems:getSpendingByCategory` - Gets spending data grouped by category
- `lineItems:getAiInsights` - Gets AI-generated insights about spending

**Mutations:**
- `lineItems:createLineItem` - Creates a new line item
- `lineItems:updateLineItem` - Updates a line item
- `lineItems:updateLineItemsBulk` - Updates multiple line items at once

**Actions:**
- `aiCategorizeAction:aiCategorize` - Uses AI to categorize line items
- `parseDocumentAction:parseDocument` - Uses AI to parse bill/financial document contents

## Tags & Categories

**Queries:**
- `tags:listTags` - Lists tags with filtering and pagination
- `tags:getTag` - Gets a tag by ID
- `tags:getTagsByIds` - Gets multiple tags by an array of IDs
- `tags:searchTags` - Searches tags by name
- `personTags:getPersonTags` - Gets tags associated with a person
- `tagTypes:listTagTypes` - Lists tag types
- `tagTypes:getTagType` - Gets a tag type by ID

**Mutations:**
- `tags:createTag` - Creates a new tag
- `tags:updateTag` - Updates a tag
- `tags:deleteTag` - Deletes a tag
- `personTags:addTagToPerson` - Adds a tag to a person
- `personTags:removeTagFromPerson` - Removes a tag from a person
- `tagTypes:createTagType` - Creates a new tag type
- `tagTypes:updateTagType` - Updates a tag type
- `tagTypes:deleteTagType` - Deletes a tag type

## Relationships

**Queries:**
- `relationships:getLinkedDocuments` - Gets documents linked to an entity

**Mutations:**
- `relationships:unlinkDocument` - Unlinks a document from an entity

## Project Updates

**Queries:**
- `projectUpdates:listProjectUpdates` - Lists updates for a project
- `projectUpdates:getProjectUpdate` - Gets a project update by ID
- `projectUpdates:getRecentProjectUpdates` - Gets recent project updates
- `projectUpdates:filterProjectUpdates` - Filters project updates by various criteria

**Mutations:**
- `projectUpdates:createProjectUpdate` - Creates a new project update
- `projectUpdates:updateProjectUpdate` - Updates a project update
- `projectUpdates:deleteProjectUpdate` - Deletes a project update
- `projectUpdates:generateAIUpdate` - Generates an AI-powered project update

## Directory Notes

**Queries:**
- `directory:getDirectoryNote` - Gets a directory note by ID
- `directory:listDirectoryNote` - Lists directory notes with filtering
- `directory:searchPeopleAndOrgs` - Searches both people and organizations
- `directory:searchPeopleAndTeams` - Searches both people and teams

**Mutations:**
- `directory:createDirectoryNote` - Creates a directory note
- `directory:updateDirectoryNote` - Updates a directory note
- `directory:removeDirectoryNote` - Removes a directory note

## Badges

**Queries:**
- `badges:listBadges` - Lists badges with filtering options
- `badges:getBadge` - Gets a badge by ID
- `badges:searchBadges` - Searches badges by name
- `badges:getBadgesByDomain` - Gets badges by domain (task, project, decision, etc.)

**Mutations:**
- `badges:createBadge` - Creates a new badge
- `badges:updateBadge` - Updates a badge
- `badges:deleteBadge` - Deletes a badge
- Various entity mutations (decisions, projectUpdates) for assigning badges

## Integrations

**Queries:**
- `integrations:listIntegrations` - Lists all integrations
- `integrations:getIntegration` - Gets an integration by ID
- `integrations:getIntegrationBySlug` - Gets an integration by its immutable slug

**Mutations:**
- `integrations:createIntegration` - Creates a new integration
- `integrations:updateIntegration` - Updates an integration
- `integrations:deleteIntegration` - Deletes an integration
- `integrations:updateIntegrationStatus` - Updates an integration's status

**Actions:**
- `billComActions:syncBillComVendors` - Syncs vendors from Bill.com
- `billComActions:syncBillComBills` - Syncs bills from Bill.com

## Entity Relationships

**Queries:**
- `entity_relationships:getRelationships` - Gets relationships for an entity
- `entity_relationships:getRelatedEntities` - Gets entities related to another entity
- `relationships:getLinkedDocuments` - Gets documents linked to a task
- `fileRelationships:getFileRelationships` - Gets relationships for a file
- `files:getRelatedEntities` - Gets entities related to a file

**Mutations:**
- `entity_relationships:createRelationship` - Creates a relationship between entities
- `entity_relationships:deleteRelationship` - Deletes a relationship between entities
- `fileRelationships:linkFileToEntity` - Links a file to an entity
- `fileRelationships:unlinkFileFromEntity` - Unlinks a file from an entity
- `relationships:unlinkDocument` - Unlinks a document from an entity
- File relationship management via file APIs (updateKnowledgeBaseArticle, updateMeetingNote, etc.)
