'use client';

import React from 'react';
import { Id } from '@/convex/_generated/dataModel';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

/**
 * Special constants for category selection.
 */
const UNCATEGORIZED_VALUE = '__uncategorized__';
const ALL_CATEGORIES_VALUE = '__all_categories__';

/**
 * Represents the type of selection made in the category dropdown.
 * 'category' indicates a specific category was selected.
 * 'uncategorized' indicates "Uncategorized" option was selected.
 * 'all_categories' indicates "All Categories" selection for filtering.
 */
export type SelectionType = 'category' | 'uncategorized' | 'all_categories';

/**
 * Parent/child category interface definitions.
 */
export interface ParentCategory {
  _id: Id<'tags'>;
  name: string;
  tag_type: Id<'tag_types'>;
  children?: SubCategory[];
}
export interface SubCategory {
  _id: Id<'tags'>;
  name: string;
  tag_type: Id<'tag_types'>;
}

/**
 * Props for the CategoryDropdown component.
 * This is a controlled component that avoids internal state to reduce unnecessary re-renders.
 */
interface CategoryDropdownProps {
  lineItem: {
    _id: Id<'lineItems'> | string;
    spending_category: Id<'tags'> | undefined;
  };
  onCategoryChangeAction: (
    categoryId: Id<'tags'> | null | undefined,
    selectionType: SelectionType
  ) => void;
  categories: ParentCategory[];
  showAllCategories?: boolean;
  triggerClassName?: string;
  contentClassName?: string;
  forceUncategorized?: boolean;
}

/**
 * CategoryDropdown Component
 * A controlled dropdown for selecting a category.
 *
 * Optimizations:
 * - Removed internal state to avoid redundant re-renders.
 * - Derives current value solely from props; optimistic updates from Convex will trigger updates only for this component.
 * - Memoizes rendered options and callback functions.
 * - Wrapped in React.memo with a custom comparator to prevent unnecessary updates.
 */
const CategoryDropdownComponent: React.FC<CategoryDropdownProps> = ({
  lineItem,
  onCategoryChangeAction,
  categories,
  showAllCategories = false,
  triggerClassName,
  contentClassName,
  forceUncategorized = false
}) => {
  // Compute the current value based solely on props.
  const currentValue = forceUncategorized
    ? UNCATEGORIZED_VALUE
    : showAllCategories && !lineItem.spending_category
    ? ALL_CATEGORIES_VALUE
    : lineItem.spending_category?.toString() || UNCATEGORIZED_VALUE;

  // Handler for value change.
  const handleCategoryChange = React.useCallback((value: string) => {
    let categoryId: Id<'tags'> | undefined | null;
    let selectionType: SelectionType = 'category';

    if (value === UNCATEGORIZED_VALUE || value === '') {
      categoryId = null;
      selectionType = 'uncategorized';
    } else if (value === ALL_CATEGORIES_VALUE) {
      categoryId = undefined;
      selectionType = 'all_categories';
    } else {
      categoryId = value as Id<'tags'>;
    }
    // Notify parent about the change.
    onCategoryChangeAction(categoryId, selectionType);
  }, [onCategoryChangeAction]);

  // Memoize the rendered category options to avoid unnecessary recalculations.
  const renderedOptions = React.useMemo(() => {
    return (
      <>
        {showAllCategories && (
          <SelectItem value={ALL_CATEGORIES_VALUE}>All Categories</SelectItem>
        )}
        <SelectItem value={UNCATEGORIZED_VALUE} className="italic text-gray-500">
          Uncategorized
        </SelectItem>
        {categories.map((parentCategory) => (
          <SelectGroup key={parentCategory._id.toString()}>
            <SelectLabel className="font-medium">
              {parentCategory.name}
            </SelectLabel>
            {parentCategory.children?.map((subCategory) => (
              <SelectItem
                key={subCategory._id.toString()}
                value={subCategory._id.toString()}
                className="pl-6"
              >
                {subCategory.name}
              </SelectItem>
            ))}
          </SelectGroup>
        ))}
      </>
    );
  }, [categories, showAllCategories]);

  // Callback to get the display name for the current value.
  const getCategoryDisplayName = React.useCallback(
    (value: string): string => {
      if (showAllCategories && value === ALL_CATEGORIES_VALUE) {
        return 'All Categories';
      }
      if (value === UNCATEGORIZED_VALUE || (!value && showAllCategories)) {
        return 'Uncategorized';
      }
      const tagId = value as Id<'tags'>;
      const parent = categories.find((cat) =>
        cat.children?.some((sub) => sub._id === tagId)
      );
      const subcategory = parent?.children?.find((sub) => sub._id === tagId);
      return subcategory ? subcategory.name : 'Select Category';
    },
    [categories, showAllCategories]
  );

  return (
    <Select value={currentValue} onValueChange={handleCategoryChange}>
      <SelectTrigger
        className={cn(
          'w-[170px] h-7 text-xs truncate',
          (currentValue === UNCATEGORIZED_VALUE || !currentValue) && 'text-gray-500 italic',
          triggerClassName
        )}
        aria-label="Select category"
      >
        <SelectValue placeholder="Select category" className="truncate">
          {getCategoryDisplayName(currentValue)}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className={cn('max-h-[clamp(200px,50vh,650px)]', contentClassName)}>
        {renderedOptions}
      </SelectContent>
    </Select>
  );
};

// Custom comparator for React.memo to prevent unnecessary re-renders.
// It compares only the relevant props: spending_category, options, and callbacks.
function areEqual(prevProps: CategoryDropdownProps, nextProps: CategoryDropdownProps) {
  return (
    prevProps.lineItem.spending_category === nextProps.lineItem.spending_category &&
    prevProps.showAllCategories === nextProps.showAllCategories &&
    prevProps.forceUncategorized === nextProps.forceUncategorized &&
    prevProps.triggerClassName === nextProps.triggerClassName &&
    prevProps.contentClassName === nextProps.contentClassName &&
    prevProps.categories === nextProps.categories &&
    prevProps.onCategoryChangeAction === nextProps.onCategoryChangeAction
  );
}

export const CategoryDropdown = React.memo(CategoryDropdownComponent, areEqual);
export default CategoryDropdown;