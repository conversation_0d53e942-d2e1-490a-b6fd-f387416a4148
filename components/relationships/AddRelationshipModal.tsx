"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { AddRelationshipModalProps, RelationshipFormState, ModalStep, TargetTypeOption } from "@/types/relationships";

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Spinner } from "@/components/ui/spinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import { Pop<PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { EntitySearchInput } from "./EntitySearchInput";

export function AddRelationshipModal({ clientId, isOpen, onClose, onSuccess }: AddRelationshipModalProps) {
  const [currentStep, setCurrentStep] = useState<ModalStep>("selectType");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formState, setFormState] = useState<RelationshipFormState>({
    selectedTypeId: null,
    selectedTargetType: null,
    selectedTargetId: null,
    isActive: true,
    startDate: undefined,
    notes: "",
    customFields: {},
  });

  // --- Data Fetching ---
  const relationshipTypes = useQuery(api.clients.relationshipQueries.listRelationshipTypesForClient);
  const createRelationship = useMutation(api.clients.relationshipMutations.createRelationship);

  // Get the selected relationship type details
  const selectedRelationshipType = relationshipTypes?.find((type: any) => type._id === formState.selectedTypeId);

  // Reset form when modal closes
  const handleClose = () => {
    setCurrentStep("selectType");
    setFormState({
      selectedTypeId: null,
      selectedTargetType: null,
      selectedTargetId: null,
      isActive: true,
      startDate: undefined,
      notes: "",
      customFields: {},
    });
    onClose();
  };

  const handleSubmit = async () => {
    if (!formState.selectedTypeId || !formState.selectedTargetType || !formState.selectedTargetId) {
      return;
    }

    setIsSubmitting(true);
    try {
      await createRelationship({
        sourceId: clientId,
        relationshipTypeId: formState.selectedTypeId,
        targetType: formState.selectedTargetType,
        targetId: formState.selectedTargetId,
        isActive: formState.isActive,
        startDate: formState.startDate,
        notes: formState.notes || undefined,
        customFields: Object.keys(formState.customFields).length > 0 ? formState.customFields : undefined,
      });
      
      onSuccess?.();
      handleClose();
    } catch (error) {
      console.error("Failed to create relationship", error);
      // TODO: Show error toast or message
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get available target types for selected relationship type
  const getAvailableTargetTypes = (): TargetTypeOption[] => {
    if (!selectedRelationshipType) return [];
    
    const targetTypes = selectedRelationshipType.valid_combinations
      .filter((combo: any) => combo.source_type === "client")
      .map((combo: any) => combo.target_type);

    return targetTypes.map((type: any) => ({
      value: type,
      label: type === "person" ? "Person" : type === "organization" ? "Organization" : "Client",
      description: type === "person" ? "Individual person" : type === "organization" ? "Company or organization" : "Another client",
    }));
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case "selectType":
        return formState.selectedTypeId !== null;
      case "selectTarget":
        return formState.selectedTargetType !== null && formState.selectedTargetId !== null;
      case "addDetails":
        return true; // Details step is optional
      default:
        return false;
    }
  };

  const handleNextStep = () => {
    if (currentStep === "selectType" && canProceedToNextStep()) {
      setCurrentStep("selectTarget");
    } else if (currentStep === "selectTarget" && canProceedToNextStep()) {
      setCurrentStep("addDetails");
    }
  };

  const handlePreviousStep = () => {
    if (currentStep === "selectTarget") {
      setCurrentStep("selectType");
    } else if (currentStep === "addDetails") {
      setCurrentStep("selectTarget");
    }
  };

  // --- Render Step Content ---
  const renderStepContent = () => {
    switch (currentStep) {
      case "selectType":
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="relationshipType">Relationship Type</Label>
              {relationshipTypes === undefined && <Spinner />}
              {relationshipTypes && relationshipTypes.length === 0 && (
                <p className="text-sm text-muted-foreground">No valid relationship types found for a client.</p>
              )}
              {relationshipTypes && relationshipTypes.length > 0 && (
                <Select 
                  value={formState.selectedTypeId || ""} 
                  onValueChange={(value) => setFormState(prev => ({ ...prev, selectedTypeId: value as any }))}
                >
                  <SelectTrigger id="relationshipType">
                    <SelectValue placeholder="Select a relationship type..." />
                  </SelectTrigger>
                  <SelectContent>
                    {relationshipTypes.map((type: any) => (
                      <SelectItem key={type._id} value={type._id}>
                        <div>
                          <div className="font-medium">{type.relationship_name}</div>
                          {type.relationship_description && (
                            <div className="text-sm text-muted-foreground">{type.relationship_description}</div>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        );

      case "selectTarget":
        const targetTypes = getAvailableTargetTypes();
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="targetType">Target Type</Label>
              <Select 
                value={formState.selectedTargetType || ""} 
                onValueChange={(value) => setFormState(prev => ({ 
                  ...prev, 
                  selectedTargetType: value as any,
                  selectedTargetId: null // Reset target ID when type changes
                }))}
              >
                <SelectTrigger id="targetType">
                  <SelectValue placeholder="Select target type..." />
                </SelectTrigger>
                <SelectContent>
                  {targetTypes.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formState.selectedTargetType && (
              <div className="space-y-2">
                <Label htmlFor="targetEntity">Select {formState.selectedTargetType}</Label>
                <EntitySearchInput
                  entityType={formState.selectedTargetType}
                  selectedEntityId={formState.selectedTargetId}
                  onEntitySelect={(entity) => setFormState(prev => ({ 
                    ...prev, 
                    selectedTargetId: entity._id 
                  }))}
                  placeholder={`Search for ${formState.selectedTargetType}...`}
                />
              </div>
            )}
          </div>
        );

      case "addDetails":
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formState.isActive}
                onCheckedChange={(checked) => setFormState(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="isActive">Active Relationship</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formState.startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formState.startDate ? format(new Date(formState.startDate), "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formState.startDate ? new Date(formState.startDate) : undefined}
                    onSelect={(date) => setFormState(prev => ({ ...prev, startDate: date?.getTime() }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any additional notes about this relationship..."
                value={formState.notes}
                onChange={(e) => setFormState(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>

            {/* TODO: Implement dynamic custom fields based on relationship type */}
            {selectedRelationshipType?.custom_fields_schema && (
              <div className="space-y-2">
                <Label>Custom Fields</Label>
                <div className="p-4 border-2 border-dashed border-muted rounded-lg text-center text-muted-foreground">
                  Dynamic custom fields will be implemented here
                  <br />
                  <small>Based on relationship type schema</small>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "selectType":
        return "Select Relationship Type";
      case "selectTarget":
        return "Select Target Entity";
      case "addDetails":
        return "Add Relationship Details";
      default:
        return "Add New Relationship";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getStepTitle()}</DialogTitle>
        </DialogHeader>
        
        {/* Handle top-level error state, e.g., if the query fails entirely */}
        {relationshipTypes === null && (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>Could not load relationship types. Please try again later.</AlertDescription>
          </Alert>
        )}

        <div className="py-4">
          {renderStepContent()}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            {currentStep !== "selectType" && (
              <Button variant="outline" onClick={handlePreviousStep}>
                <ChevronLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            
            {currentStep === "addDetails" ? (
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting || !canProceedToNextStep()}
              >
                {isSubmitting && <Spinner className="mr-2 h-4 w-4" />}
                Save Relationship
              </Button>
            ) : (
              <Button 
                onClick={handleNextStep} 
                disabled={!canProceedToNextStep()}
              >
                Next
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
