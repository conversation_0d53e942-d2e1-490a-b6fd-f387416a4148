"use client";

import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Spinner } from "@/components/ui/spinner";
import { Search, Check } from "lucide-react";
import { EntitySearchResult } from "@/types/relationships";
import { Id } from "@/convex/_generated/dataModel";

interface EntitySearchInputProps {
  entityType: "person" | "organization" | "client";
  selectedEntityId: Id<"people"> | Id<"organizations"> | Id<"clients"> | null;
  onEntitySelect: (entity: EntitySearchResult) => void;
  placeholder?: string;
}

export function EntitySearchInput({ 
  entityType, 
  selectedEntityId, 
  onEntitySelect, 
  placeholder 
}: EntitySearchInputProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const searchResults = useQuery(
    api.clients.relationshipQueries.searchEntitiesForRelationships,
    debouncedSearchTerm.trim() 
      ? { searchTerm: debouncedSearchTerm, entityType, limit: 10 }
      : "skip"
  );

  const handleInputChange = (value: string) => {
    setSearchTerm(value);
    setIsOpen(true);
  };

  const handleEntitySelect = (entity: EntitySearchResult) => {
    setSearchTerm(entity.name);
    setIsOpen(false);
    onEntitySelect(entity);
  };

  const handleInputFocus = () => {
    if (searchTerm.trim()) {
      setIsOpen(true);
    }
  };

  const handleInputBlur = () => {
    // Delay closing to allow for clicks on results
    setTimeout(() => setIsOpen(false), 200);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(word => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder={placeholder || `Search for ${entityType}...`}
          value={searchTerm}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          className="pl-10"
        />
      </div>

      {isOpen && debouncedSearchTerm.trim() && (
        <div className="absolute z-50 w-full mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
          {searchResults === undefined && (
            <div className="flex items-center justify-center p-4">
              <Spinner className="h-4 w-4" />
              <span className="ml-2 text-sm text-muted-foreground">Searching...</span>
            </div>
          )}

          {searchResults && searchResults.length === 0 && (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No {entityType}s found matching "{debouncedSearchTerm}"
            </div>
          )}

          {searchResults && searchResults.length > 0 && (
            <div className="py-2">
              {searchResults.map((entity) => (
                <Button
                  key={entity._id}
                  variant="ghost"
                  className="w-full justify-start p-3 h-auto"
                  onClick={() => handleEntitySelect(entity)}
                >
                  <div className="flex items-center gap-3 w-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={entity.imageUrl} alt={entity.name} />
                      <AvatarFallback className="text-xs">
                        {getInitials(entity.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 text-left">
                      <div className="font-medium">{entity.name}</div>
                      {entity.description && (
                        <div className="text-sm text-muted-foreground truncate">
                          {entity.description}
                        </div>
                      )}
                    </div>

                    {selectedEntityId === entity._id && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                </Button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
