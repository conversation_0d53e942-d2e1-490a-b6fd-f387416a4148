import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useConvex } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import Link from 'next/link';
import { useToast } from "@/components/hooks/use-toast";
import {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
  CardTitle
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  FileText,
  LinkIcon,
  File,
  FileCheck,
  ClipboardList,
  Lightbulb,
  Building,
  UserCircle,
  Search,
  X,
  Loader2,
  Plus
} from "lucide-react";

// Type for each entity that can be related
type RelatedItemType = 'project' | 'decision' | 'task' | 'organization' | 'person';

// Entity object structure
interface RelatedEntity {
  _id: Id<any>;
  name: string;
  type: RelatedItemType;
  short_description?: string;
}

// Props for the RelatedItems component
interface RelatedItemsProps {
  entityId: Id<any>;
  entityType: RelatedItemType;
}

// Helper function to get entity icon based on type
const getEntityIcon = (type: RelatedItemType) => {
  switch (type) {
    case 'project':
      return <ClipboardList className="h-4 w-4 text-blue-500" />;
    case 'decision':
      return <Lightbulb className="h-4 w-4 text-amber-500" />;
    case 'task':
      return <FileCheck className="h-4 w-4 text-green-500" />;
    case 'organization':
      return <Building className="h-4 w-4 text-purple-500" />;
    case 'person':
      return <UserCircle className="h-4 w-4 text-sky-500" />;
    default:
      return <FileText className="h-4 w-4 text-gray-400" />;
  }
};

// Helper function to convert entity types between display and schema formats
const mapEntityType = (type: RelatedItemType): string => {
  const typeMap: Record<RelatedItemType, string> = {
    'project': 'project',
    'decision': 'decision',
    'task': 'task',
    'organization': 'organization',
    'person': 'person'
  };
  return typeMap[type];
};

// Format display name for entity type
const formatEntityType = (type: RelatedItemType): string => {
  return type.charAt(0).toUpperCase() + type.slice(1);
};

// Get entity URL based on type and ID
const getEntityUrl = (type: RelatedItemType, id: Id<any>): string => {
  switch (type) {
    case 'project':
      return `/projects/${id}`;
    case 'decision':
      return `/decisions/${id}`;
    case 'task':
      return `/tasks/${id}`;
    case 'organization':
      return `/directory/organization/${id}`;
    case 'person':
      return `/directory/person/${id}`;
    default:
      return '#';
  }
};

const RelatedItems = ({ entityId, entityType }: RelatedItemsProps) => {
  const { toast } = useToast();
  const convex = useConvex();
  const [isAddingRelation, setIsAddingRelation] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<RelatedEntity[]>([]);
  const [recentItems, setRecentItems] = useState<RelatedEntity[]>([]); // State to hold recent items

  // Fetch related entity details using the new query
  const relatedEntitiesData = useQuery(
    api.entity_relationships.getRelatedEntityDetails,
    {
      entityId: entityId,
      entityType: mapEntityType(entityType) as 'project' | 'decision' | 'task',
    }
  );

  // Mutations for managing relationships
  const createRelationship = useMutation(api.entity_relationships.create);
  const deleteRelationship = useMutation(api.entity_relationships.deleteBySourceAndTarget);

  // Fetch search results OR recent items, and handle 1-char filter
  useEffect(() => {
    const handler = setTimeout(async () => {
      const limit = 20; // Max items to show

      // 1. Handle 1-character filtering (client-side)
      if (searchTerm.length === 1) {
        setIsSearching(false); // No backend search needed
        const filteredRecent = recentItems.filter(item =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setSearchResults(filteredRecent);
        return; // Stop further execution
      }

      // 2. Handle backend search (>= 2 chars)
      if (searchTerm.length >= 2) {
        setIsSearching(true);
        try {
          const searchLimit = 10;
          const [taskResults, projectResults, decisionResults] = await Promise.all([
            convex.query(api.tasks.searchTasksByName, { searchQuery: searchTerm, limit: searchLimit }),
            convex.query(api.projects.searchProjectsByName, { searchQuery: searchTerm, limit: searchLimit }),
            convex.query(api.decisions.searchDecisionsByTitle, { searchQuery: searchTerm, limit: searchLimit })
          ]);

          // Process each text individually
          const combinedResults: RelatedEntity[] = [
            ...taskResults.map(task => ({
              _id: task._id as Id<any>,
              name: task.name,
              type: 'task' as RelatedItemType,
              short_description: task.short_description || ""
            })),
            ...projectResults.filter(proj => proj.name).map(proj => ({
              _id: proj._id as Id<any>,
              name: proj.name!,
              type: 'project' as RelatedItemType,
              short_description: proj.description || "" // Use description until short_description is available
            })),
            ...decisionResults.filter(dec => dec.title).map(dec => ({
              _id: dec._id as Id<any>,
              name: dec.title!,
              type: 'decision' as RelatedItemType,
              short_description: dec.short_description || ""
            }))
          ];

          const filteredResults = combinedResults.filter(result =>
            !(result.type === entityType && result._id === entityId)
          );
          filteredResults.sort((a, b) => a.name.localeCompare(b.name));
          setSearchResults(filteredResults.slice(0, limit)); // Use overall limit

        } catch (error) {
          console.error(`Error searching related items:`, error);
          toast({ title: 'Search Error', description: `Failed to search for related items.`, variant: 'destructive' });
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      } else { // searchTerm.length === 0
        // 3. Fetch recent items if search is empty and recentItems is empty
        // Avoid refetching if recentItems already populated
        if (recentItems.length === 0) {
           setIsSearching(true);
           try {
             const recentLimit = 7;
             const paginationOpts = { numItems: recentLimit };
           const [recentTasks, recentProjects, recentDecisions] = await Promise.all([
             convex.query(api.tasks.listTasks, { pagination: { sortBy: 'updated_at', sortDirection: 'desc', limit: recentLimit } }),
             convex.query(api.projects.listProjects, { sortBy: 'updated_at', sortDirection: 'desc', paginationOpts }),
             convex.query(api.decisions.listDecisions, { pagination: { sortBy: 'updated_at', sortDirection: 'desc', limit: recentLimit } })
           ]);

           const combinedRecent: RelatedEntity[] = [
             ...recentTasks.tasks.map(task => ({ 
               _id: task._id as Id<any>, 
               name: task.name, 
               type: 'task' as RelatedItemType,
               short_description: task.short_description || ""
             })),
             ...recentProjects.page.filter(proj => proj.name).map(proj => ({ 
               _id: proj._id as Id<any>, 
               name: proj.name!, 
               type: 'project' as RelatedItemType,
               short_description: proj.description || ""  // Use description until short_description is available
             })),
             ...recentDecisions.decisions.map(dec => ({ 
               _id: dec._id as Id<any>, 
               name: dec.title ?? 'Unnamed Decision', 
               type: 'decision' as RelatedItemType,
               short_description: dec.short_description || ""
             }))
           ];

           const filteredRecent = combinedRecent.filter(result =>
               !(result.type === entityType && result._id === entityId)
             );

             filteredRecent.sort((a, b) => a.name.localeCompare(b.name));
             const finalRecentItems = filteredRecent.slice(0, limit);
             setSearchResults(finalRecentItems); // Display recent items initially
             setRecentItems(finalRecentItems); // Store for 1-char filtering

          } catch (error) {
             console.error(`Error fetching recent items:`, error);
             toast({ title: 'Error Fetching Recent', description: `Failed to load recent items.`, variant: 'destructive' });
             setSearchResults([]);
             setRecentItems([]); // Clear recent items on error
          } finally {
             setIsSearching(false);
          }
        } else {
           // If recent items are already loaded and search is empty, just display them
           setSearchResults(recentItems);
           setIsSearching(false);
        }
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(handler);
  }, [searchTerm, entityId, entityType, convex, toast, recentItems]); // Add recentItems to dependency array

  // Use the data directly from the new query
  const relatedEntities = relatedEntitiesData ?? [];

  // Handle adding a new relationship
  const handleAddRelationship = async (item: RelatedEntity) => {
    try {
      await createRelationship({
        data: {
          entity_source_type: mapEntityType(entityType) as 'project' | 'decision' | 'task',
          entity_source_id: entityId,
          entity_target_type: mapEntityType(item.type) as 'project' | 'decision' | 'task',
          entity_target_id: item._id,
          entity_relationship_type: 'related' // Default relationship type
        }
      });

      toast({
        title: 'Relationship added',
        description: `Successfully linked to ${item.name}`,
      });

      setIsAddingRelation(false);
      setSearchTerm('');
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create relationship',
        variant: 'destructive',
      });
    }
  };

  // Handle removing a relationship (try both directions)
  const handleRemoveRelationship = async (item: RelatedEntity) => {
    try {
      // Try deleting assuming current entity is the source
      let result = await deleteRelationship({
        source_type: mapEntityType(entityType) as 'project' | 'decision' | 'task',
        source_id: entityId,
        target_type: mapEntityType(item.type) as 'project' | 'decision' | 'task',
        target_id: item._id
      });

      // If not deleted, try deleting assuming current entity is the target
      if (!result.deleted) {
         result = await deleteRelationship({
          source_type: mapEntityType(item.type) as 'project' | 'decision' | 'task',
          source_id: item._id,
          target_type: mapEntityType(entityType) as 'project' | 'decision' | 'task',
          target_id: entityId
        });
      }

      if (result.deleted) {
         toast({
          title: 'Relationship removed',
          description: `Successfully unlinked from ${item.name}`,
        });
      } else {
         // This case should ideally not happen if the item was displayed,
         // but handle it just in case.
         toast({
          title: 'Removal Failed',
          description: `Could not find relationship with ${item.name} to remove.`,
          variant: 'default', // Changed variant to default
        });
      }

    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove relationship',
        variant: 'destructive',
      });
    }
  };

  // Group related entities by type for rendering
  const groupedRelatedEntities = React.useMemo(() => {
    // Initialize with all possible types
    const initialGroups: Record<RelatedItemType, Array<{ _id: Id<any>, name: string, type: string }>> = {
      project: [],
      decision: [],
      task: [],
      organization: [],
      person: []
    };

    // Ensure relatedEntitiesData is an array before reducing
    if (!Array.isArray(relatedEntitiesData)) return initialGroups;

    return relatedEntitiesData.reduce((acc, item) => {
      // Ensure item and item.type are valid before proceeding
      if (item && item.type) {
        const type = item.type as RelatedItemType;
        // Item will be added to the pre-initialized array
        if (item._id && item.name) {
          // Ensure the type exists as a key before pushing
          if (acc[type]) {
             acc[type].push(item as { _id: Id<any>, name: string, type: string });
          } else {
             // Handle unexpected types if necessary, though unlikely with current logic
             console.warn(`Unexpected entity type encountered: ${type}`);
          }
        }
      }
      return acc;
    }, initialGroups);
  }, [relatedEntitiesData]);

  // Define the order of types for display
  const displayOrder: RelatedItemType[] = ['project', 'decision', 'task']; // Add others if needed

  return (
    <Card>
      {/* Move Add Button Popover to Header */}
      <CardHeader className="px-3 py-1.5 flex flex-row items-center justify-between">
         <div className="flex items-center gap-1.5">
            <LinkIcon className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">Related Items</CardTitle>
         </div>
         {/* Popover for Adding Items */}
         <Popover open={isAddingRelation} onOpenChange={setIsAddingRelation}>
           <PopoverTrigger asChild>
             {/* Use a smaller icon button */}
             <Button variant="ghost" size="icon" className="h-6 w-6">
               <Plus className="h-4 w-4" />
               <span className="sr-only">Add Related Item</span>
             </Button>
           </PopoverTrigger>
           {/* Add align="end" and sideOffset */}
           <PopoverContent className="w-[450px] p-0 overflow-hidden" align="end" sideOffset={4}>
             <div className="px-3 pt-2 pb-1.5">
               <h4 className="font-medium">Link to another item</h4>
               <p className="text-xs text-muted-foreground mt-0.5">
                 Link to an existing Task, Project, or Decision
               </p>
             </div>
             {/* Search Input */}
             <div className="px-3 py-1.5">
               <div className="relative">
                 <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                 <Input
                   placeholder="Search Tasks, Projects, Decisions..."
                   className="pl-8"
                   value={searchTerm}
                   onChange={(e) => setSearchTerm(e.target.value)}
                 />
               </div>
             </div>
             {/* Search Results - Add max-width to constrain content */}
             <ScrollArea className="h-[200px]">
               <div className="p-2">
                 {isSearching ? (
                   <div className="flex justify-center py-4">
                     <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                   </div>
                 ) : searchResults.length === 0 ? (
                   <div className="text-center py-4 text-sm text-muted-foreground">
                     {searchTerm.length === 0 ? 'Showing recent items' :
                      searchTerm.length === 1 ? 'Showing filtered recent items' :
                      'No results found'}
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {searchResults.map((result) => (
                        <TooltipProvider key={`${result.type}-${result._id}`}>
                          <Tooltip delayDuration={300}>
                            <TooltipTrigger asChild>
                              <button
                                className="w-full text-left rounded hover:bg-gray-100 p-2 grid grid-cols-[20px_1fr_70px] gap-2 items-center"
                                onClick={() => handleAddRelationship(result)}
                              >
                                {/* Icon Column - Fixed Width */}
                                {getEntityIcon(result.type)}
                                
                                {/* Name Column - Truncated */}
                                <span className="text-sm truncate overflow-hidden">{result.name}</span>
                                
                                {/* Badge Column - Fixed Width */}
                                <Badge
                                  className={`justify-self-end w-full text-center text-xs font-medium ${
                                    result.type === 'task' ? 'bg-green-100 text-green-800 border border-green-200' :
                                    result.type === 'project' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                    result.type === 'decision' ? 'bg-amber-100 text-amber-800 border border-amber-200' :
                                    'bg-gray-100 text-gray-800 border border-gray-200'
                                  }`}
                                >
                                  {formatEntityType(result.type)}
                                </Badge>
                              </button>
                            </TooltipTrigger>
                            <TooltipContent side="top" align="center" className="max-w-[300px]">
                              <p className="text-sm">{result.short_description}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ))}
                    </div>
                  )}
               </div>
             </ScrollArea>
           </PopoverContent>
         </Popover>
      </CardHeader>
      {/* Remove ScrollArea and fixed height */}
      <CardContent className="pt-1 pb-2 px-3">
          {relatedEntitiesData === undefined ? (
            <div className="flex justify-center py-6">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : Object.values(groupedRelatedEntities).every(arr => arr.length === 0) ? ( // Check if all groups are empty
            <div className="py-6 text-center text-sm text-muted-foreground">
              No related items linked yet.
            </div>
          ) : (
             <div className="space-y-2">
               {displayOrder.map(type => {
                 const items = groupedRelatedEntities[type];
                 if (!items || items.length === 0) return null; // Skip empty groups

                 return (
                   <div key={type}>
                     <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-1 px-0.5 flex items-center gap-1">
                       {getEntityIcon(type)} {/* Use icon in heading */}
                      {formatEntityType(type)}s {/* Pluralize */}
                      <span className="text-xs font-normal ml-0.5">({items.length})</span>
                    </h4>
                    {/* Explicitly type 'item' here */}
                    {items.map((item: { _id: Id<any>, name: string, type: string, short_description?: string }) => (
                      // Apply AssignmentTeam item styling
                      <TooltipProvider key={`${item.type}-${item._id}`}>
                        <Tooltip delayDuration={300}>
                          <TooltipTrigger asChild>
                            <div className="flex items-center justify-between rounded-lg border p-1.5 mb-1 bg-card group relative">
                              <div className="flex items-center gap-1.5 overflow-hidden pl-1.5">
                                {/* Icon already in heading, maybe remove here or keep for consistency? Let's keep for now. */}
                                {/* {getEntityIcon(item.type as RelatedItemType)} */}
                                <Link href={getEntityUrl(item.type as RelatedItemType, item._id)} className="text-sm font-medium hover:underline truncate">
                                  {item.name}
                                </Link>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive flex-shrink-0" // Added flex-shrink-0
                                onClick={() => handleRemoveRelationship(item as RelatedEntity)}
                              >
                                <X className="h-3 w-3" />
                                <span className="sr-only">Remove relation to {item.name}</span>
                              </Button>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-[300px]">
                            <p className="text-sm">{item.short_description || ""}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                   </div>
                 );
               })}
             </div>
          )}
      </CardContent>
      {/* Remove CardFooter */}
    </Card>
  );
};

export default RelatedItems;
