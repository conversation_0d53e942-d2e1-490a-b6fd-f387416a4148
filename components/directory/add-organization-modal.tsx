'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { CreateOrganizationSchema } from '@/zod/directory-schema';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox'; // Added for is_vendor
import { Loader2 } from 'lucide-react';

// Define the props for the modal
interface AddOrganizationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (organizationId: Id<'organizations'>) => void; // Optional callback on success
}

// Infer the type from the Zod schema
type OrganizationFormData = z.infer<typeof CreateOrganizationSchema>;

export function AddOrganizationModal({
  open,
  onOpenChange,
  onSuccess
}: AddOrganizationModalProps) {
  const [isSaving, setIsSaving] = useState(false);
  // Use the existing mutation
  const createOrganizationMutation = useMutation(
    api.directory.directoryOrganizations.createOrganizations
  );

  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(CreateOrganizationSchema),
    defaultValues: {
      name: '',
      description: '',
      email: '',
      phone: '',
      address: '',
      website: '',
      is_vendor: false // Default is_vendor to false for general directory addition
      // parent_org_id and billComVendorId are omitted as they might be less common for direct entry
    }
  });

  const {
    register,
    handleSubmit,
    reset,
    control, // Needed for Checkbox
    formState: { errors }
  } = form;

  // Reset form when modal opens or closes
  useEffect(() => {
    if (!open) {
      const timer = setTimeout(() => {
        reset();
        setIsSaving(false);
      }, 150);
      return () => clearTimeout(timer);
    } else {
      reset();
    }
  }, [open, reset]);

  const onSubmit = async (data: OrganizationFormData) => {
    setIsSaving(true);
    toast.loading('Adding organization...');

    try {
      // Ensure is_vendor is explicitly false if not provided, matching schema expectation
      const orgData = {
        ...data,
        is_vendor: data.is_vendor ?? false
      };

      const result = await createOrganizationMutation({
        items: [orgData] // Pass data as an array item
      });

      if (result.failed && result.failed.length > 0) {
        throw new Error(result.failed[0].error || 'Failed to add organization.');
      }

      const newOrgId = result.ids[0];
      toast.success('Organization added successfully!');
      onSuccess?.(newOrgId);
      onOpenChange(false);
    } catch (error) {
      console.error('Error adding organization:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add organization.');
    } finally {
      setIsSaving(false);
      toast.dismiss();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Add New Organization</DialogTitle>
            <DialogDescription>
              Enter the details for the new organization.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {/* Name */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org-name" className="text-right">
                Name*
              </Label>
              <Input
                id="org-name"
                {...register('name')}
                className={`col-span-3 ${errors.name ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.name && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org-email" className="text-right">
                Email
              </Label>
              <Input
                id="org-email"
                type="email"
                {...register('email')}
                className={`col-span-3 ${errors.email ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.email && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Phone */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org-phone" className="text-right">
                Phone
              </Label>
              <Input
                id="org-phone"
                {...register('phone')}
                className={`col-span-3 ${errors.phone ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.phone && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.phone.message}
                </p>
              )}
            </div>

            {/* Website */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org-website" className="text-right">
                Website
              </Label>
              <Input
                id="org-website"
                {...register('website')}
                className={`col-span-3 ${errors.website ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.website && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.website.message}
                </p>
              )}
            </div>

            {/* Address */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="org-address" className="text-right pt-2">
                Address
              </Label>
              <Textarea
                id="org-address"
                {...register('address')}
                className={`col-span-3 ${errors.address ? 'border-red-500' : ''}`}
                rows={2}
                disabled={isSaving}
              />
              {errors.address && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.address.message}
                </p>
              )}
            </div>

            {/* Description */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="org-description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="org-description"
                {...register('description')}
                className={`col-span-3 ${errors.description ? 'border-red-500' : ''}`}
                rows={3}
                disabled={isSaving}
              />
              {errors.description && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Is Vendor Checkbox - Optional, defaults to false */}
            {/*
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="is_vendor" className="text-right">
                Is Vendor?
              </Label>
              <div className="col-span-3 flex items-center">
                <Checkbox
                  id="is_vendor"
                  {...register('is_vendor')}
                  disabled={isSaving}
                />
              </div>
            </div>
            */}

          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isSaving}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Add Organization'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
