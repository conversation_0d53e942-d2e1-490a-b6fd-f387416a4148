"use client";

// Popover for assigning existing organizations or creating/assigning new organizations to a person.

import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/hooks/use-toast";
import { useDebounce } from 'use-debounce';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Plus, Building2, X, Check, Loader2 } from 'lucide-react'; // Use Building2 icon
import { cn } from '@/lib/utils';

interface AssignOrganizationPopoverProps { // Renamed interface
  personId: Id<'people'>; // Changed prop name
  trigger?: React.ReactNode; // Optional custom trigger
  onSuccess?: (organizationId: Id<'organizations'>) => void; // Optional callback, returns org ID
}

// Changed type name and properties
type SearchResultOrg = {
  _id: Id<'organizations'>;
  name: string;
  // Add other relevant org fields if needed for display (e.g., website)
};

export const AssignOrganizationPopover: React.FC<AssignOrganizationPopoverProps> = ({ // Renamed component
  personId, // Changed prop name
  trigger,
  onSuccess,
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  const [isAssigning, setIsAssigning] = useState<Id<'organizations'> | null>(null); // Track assigning state by org ID
  const [isCreating, setIsCreating] = useState(false); // Track if inline create form is shown
  const [isSavingNew, setIsSavingNew] = useState(false); // Track saving state for new org
  const [newOrgName, setNewOrgName] = useState(''); // Renamed state
  const [newOrgWebsite, setNewOrgWebsite] = useState(''); // Added state for website
  const nameInputRef = useRef<HTMLInputElement>(null);

  // 1. Get IDs of organizations already linked to this person
  const linkedOrgsData = useQuery(
    api.directory.directoryRelationships.listOrganizationssByPerson, // Use the correct query
    { personId }
  );
  const linkedOrgIds = new Set(linkedOrgsData?.organizations?.map(o => o._id) ?? []);

  // 2. Fetch initial list or search results based on search term
  const isSearching = debouncedSearchTerm.trim().length > 0;

  // Fetch initial list (most recent 12) if not searching
  const initialOrgListData = useQuery(
    api.directory.directoryOrganizations.listOrganizations,
    !isSearching && isOpen ? {
      // Add the required empty filter object
      filter: {},
      pagination: { limit: 20, sortBy: 'updated_at', sortDirection: 'desc' } // Fetch a bit more to filter
    } : 'skip'
  );

  // Fetch search results if searching
  const searchResultsData = useQuery(
    api.directory.directory.searchPeopleAndOrgs,
    isSearching && isOpen ? { search: debouncedSearchTerm, limit: 15 } : 'skip'
  );

  // Determine which data source to use and filter
  const searchResults: SearchResultOrg[] = (() => {
    if (isSearching) {
      // Use search results
      return (searchResultsData ?? [])
        .filter(entity => entity.type === 'organization' && !linkedOrgIds.has(entity.id as Id<'organizations'>))
        .map(entity => ({
          _id: entity.id as Id<'organizations'>,
          name: entity.name,
        }));
    } else if (initialOrgListData) {
      // Use initial list results, filter out linked, and limit to 12
      return initialOrgListData.organizations
        .filter(org => !linkedOrgIds.has(org._id))
        .slice(0, 12) // Apply the limit of 12 here
        .map(org => ({
          _id: org._id,
          name: org.name,
        }));
    }
    return []; // Default empty array
  })();

  // 3. Mutations
  const linkPersonToOrg = useMutation(api.directory.directoryRelationships.createOrganizationPeopleRelationship);
  const createOrganizations = useMutation(api.directory.directoryOrganizations.createOrganizations); // Correct mutation name

  // Handle selecting an existing organization
  const handleAssignOrganization = async (organizationId: Id<'organizations'>) => { // Renamed function
    if (isAssigning) return;
    setIsAssigning(organizationId);
    try {
      await linkPersonToOrg({
        items: [{
          organization_id: organizationId,
          person_id: personId, // Use personId from props
        }]
      });
      toast({
        title: 'Success',
        description: 'Organization linked to person.', // Updated message
      });
      setIsOpen(false);
      if (onSuccess) onSuccess(organizationId);
    } catch (error) {
      console.error("Failed to link organization:", error);
      toast({
        title: 'Error Linking Organization', // Updated title
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(null);
    }
  };

  // Handle creating and assigning a new organization
  const handleCreateAndAssignOrganization = async () => { // Renamed function
    if (!newOrgName.trim()) {
      toast({ title: "Organization Name is required", variant: "destructive" });
      nameInputRef.current?.focus();
      return;
    }
    if (isSavingNew) return;
    setIsSavingNew(true);

    try {
      // Step 1: Create the organization
      const createResult = await createOrganizations({ // Use correct mutation
        items: [{
          name: newOrgName.trim(),
          website: newOrgWebsite.trim() || undefined, // Add website
          // Add other default fields from CreateOrganizationSchema if necessary
        }]
      });

      // Check for creation failure or if no ID was returned
      if (createResult.failed?.length || !createResult.ids?.length) {
         const errorMsg = createResult.failed?.[0]?.error || "Failed to get new organization ID after creation";
         throw new Error(errorMsg);
      }
      const newOrganizationId = createResult.ids[0]; // Get the ID from the result array

      // Step 2: Link the new organization to the person
      await linkPersonToOrg({
        items: [{
          organization_id: newOrganizationId,
          person_id: personId, // Use personId from props
        }]
      });

      toast({
        title: 'Success',
        description: `Organization "${newOrgName.trim()}" created and linked.`, // Updated message
      });
      setIsOpen(false);
      if (onSuccess) onSuccess(newOrganizationId);

    } catch (error) {
      console.error("Failed to create and link organization:", error);
      toast({
        title: 'Error Creating Organization', // Updated title
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSavingNew(false);
    }
  };

  // Reset state when popover opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setIsCreating(false);
      setNewOrgName(''); // Reset org name
      setNewOrgWebsite(''); // Reset org website
      setIsAssigning(null);
      setIsSavingNew(false);
    }
  }, [isOpen]);

  // Focus name input when create form appears
  useEffect(() => {
    if (isCreating) {
      nameInputRef.current?.focus();
      // Pre-fill name from search term if creating directly
      if (searchTerm && !newOrgName) {
        setNewOrgName(searchTerm);
      }
    }
  }, [isCreating, searchTerm, newOrgName]);


  const showCreateOption = debouncedSearchTerm.trim() && !searchResults.some(o => o.name.toLowerCase() === debouncedSearchTerm.trim().toLowerCase());

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger || (
          // Default trigger if none provided
          <Button variant="ghost" size="sm" className="h-8 px-2">
            <Plus className="w-4 h-4 mr-1" /> Add Organization
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" side="bottom" align="start">
        {!isCreating ? (
          <Command shouldFilter={false} /* Handle filtering manually */ >
            <CommandInput
              autoFocus
              value={searchTerm}
              onValueChange={setSearchTerm}
              placeholder="Search or create organization..." // Updated placeholder
              className="h-9"
            />
            <CommandList>
              <ScrollArea className="max-h-[300px]">
                <CommandEmpty>
                  {isSearching
                    ? "No matching organizations found."
                    : "No organizations available or all linked."}
                </CommandEmpty>
                {searchResults.length > 0 && (
                  <CommandGroup heading={isSearching ? "Search Results" : "Recent Organizations"}>
                    {searchResults.map((org) => ( // Use org variable
                      <CommandItem
                        key={org._id}
                        value={org.name} // Value for potential filtering if enabled
                        onSelect={() => handleAssignOrganization(org._id)} // Use correct handler
                        className="flex items-center gap-2 cursor-pointer"
                        disabled={isAssigning === org._id}
                      >
                        {isAssigning === org._id ? (
                          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                        ) : (
                          // Use Building2 icon for organizations
                          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                        )}
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm font-medium truncate">{org.name}</p>
                          {/* Optionally display other org info like website */}
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
                {showCreateOption && (
                  <CommandGroup>
                     <CommandItem
                        onSelect={() => {
                          setIsCreating(true);
                          setNewOrgName(debouncedSearchTerm.trim()); // Pre-fill name
                        }}
                        className="text-sm cursor-pointer"
                      >
                        <Building2 className="mr-2 h-4 w-4" /> {/* Use Building2 icon */}
                        Create new organization: "{debouncedSearchTerm.trim()}"
                      </CommandItem>
                  </CommandGroup>
                )}
              </ScrollArea>
            </CommandList>
          </Command>
        ) : (
          // Inline Create Form for Organization
          <div className="p-4 space-y-3">
            <p className="text-sm font-medium">Create New Organization</p>
            <Input
              ref={nameInputRef}
              placeholder="Organization Name (Required)" // Updated placeholder
              value={newOrgName}
              onChange={(e) => setNewOrgName(e.target.value)}
              disabled={isSavingNew}
              className="h-9"
            />
            <Input
              placeholder="Website (Optional)" // Added website field
              type="text"
              value={newOrgWebsite}
              onChange={(e) => setNewOrgWebsite(e.target.value)}
              disabled={isSavingNew}
              className="h-9"
            />
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCreating(false)}
                disabled={isSavingNew}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCreateAndAssignOrganization} // Use correct handler
                disabled={isSavingNew || !newOrgName.trim()}
              >
                {isSavingNew ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Check className="mr-2 h-4 w-4" />
                )}
                Create & Link
              </Button>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};
