"use client";

// Popover for assigning existing people or creating/assigning new people to an organization.

import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from "@/components/hooks/use-toast";
import { useDebounce } from 'use-debounce';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Plus, UserPlus, X, Check, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AssignPersonPopoverProps {
  organizationId: Id<'organizations'>;
  trigger?: React.ReactNode; // Optional custom trigger
  onSuccess?: (personId: Id<'people'>) => void; // Optional callback
}

type SearchResultPerson = {
  _id: Id<'people'>;
  name: string;
  email?: string;
  image?: string;
  initials: string;
};

export const AssignPersonPopover: React.FC<AssignPersonPopoverProps> = ({
  organizationId,
  trigger,
  onSuccess,
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  const [isAssigning, setIsAssigning] = useState<Id<'people'> | null>(null); // Track assigning state by person ID
  const [isCreating, setIsCreating] = useState(false); // Track if inline create form is shown
  const [isSavingNew, setIsSavingNew] = useState(false); // Track saving state for new person
  const [newPersonName, setNewPersonName] = useState('');
  const [newPersonEmail, setNewPersonEmail] = useState('');
  const nameInputRef = useRef<HTMLInputElement>(null);

  // 1. Get IDs of people already linked to this organization
  const linkedPeopleData = useQuery(
    api.directory.directoryRelationships.listPeopleByOrganization,
    { organizationId }
  );
  const linkedPeopleIds = new Set(linkedPeopleData?.people?.map(p => p._id) ?? []);

  // 2. Search for people, excluding already linked ones
  const searchResultsData = useQuery(
    api.directory.directory.searchPeopleAndOrgs, // Using the combined search, will filter client-side for now
    isOpen ? { search: debouncedSearchTerm, limit: 15 } : 'skip'
  );

  // Filter results to only people and exclude linked ones
  const searchResults: SearchResultPerson[] = (searchResultsData ?? [])
    .filter(entity => entity.type === 'person' && !linkedPeopleIds.has(entity.id as Id<'people'>))
    .map(entity => ({
      _id: entity.id as Id<'people'>,
      name: entity.name,
      email: entity.email,
      image: entity.image,
      initials: entity.initials,
    }));

  // 3. Mutations
  const linkPersonToOrg = useMutation(api.directory.directoryRelationships.createOrganizationPeopleRelationship);
  const createPeople = useMutation(api.directory.directoryPeople.createPeople); // Correct mutation name

  // Handle selecting an existing person
  const handleAssignPerson = async (personId: Id<'people'>) => {
    if (isAssigning) return; // Prevent double clicks
    setIsAssigning(personId);
    try {
      await linkPersonToOrg({
        items: [{
          organization_id: organizationId,
          person_id: personId,
        }]
      });
      toast({
        title: 'Success',
        description: 'Person linked to organization.',
      });
      setIsOpen(false);
      if (onSuccess) onSuccess(personId);
    } catch (error) {
      console.error("Failed to link person:", error);
      toast({
        title: 'Error Linking Person',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(null);
    }
  };

  // Handle creating and assigning a new person
  const handleCreateAndAssignPerson = async () => {
    if (!newPersonName.trim()) {
      toast({ title: "Name is required", variant: "destructive" });
      nameInputRef.current?.focus();
      return;
    }
    if (isSavingNew) return;
    setIsSavingNew(true);

    try {
      // Step 1: Create the person (using createPeople with a single item)
      const createResult = await createPeople({
        items: [{
          name: newPersonName.trim(),
          email: newPersonEmail.trim() || undefined, // Send undefined if empty
          // Add other default fields from CreatePersonSchema if necessary
        }]
      });

      // Check for creation failure or if no ID was returned
      if (createResult.failed?.length || !createResult.ids?.length) {
         const errorMsg = createResult.failed?.[0]?.error || "Failed to get new person ID after creation";
         throw new Error(errorMsg);
      }
      const newPersonId = createResult.ids[0]; // Get the ID from the result array

      // Step 2: Link the new person
      await linkPersonToOrg({
        items: [{
          organization_id: organizationId,
          person_id: newPersonId,
        }]
      });

      toast({
        title: 'Success',
        description: `Person "${newPersonName.trim()}" created and linked.`,
      });
      setIsOpen(false);
      if (onSuccess) onSuccess(newPersonId);

    } catch (error) {
      console.error("Failed to create and link person:", error);
      toast({
        title: 'Error Creating Person',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSavingNew(false);
    }
  };

  // Reset state when popover opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setIsCreating(false);
      setNewPersonName('');
      setNewPersonEmail('');
      setIsAssigning(null);
      setIsSavingNew(false);
    } else {
      // Optionally focus search input on open
    }
  }, [isOpen]);

  // Focus name input when create form appears
  useEffect(() => {
    if (isCreating) {
      nameInputRef.current?.focus();
      // Pre-fill name from search term if creating directly
      if (searchTerm && !newPersonName) {
        setNewPersonName(searchTerm);
      }
    }
  }, [isCreating, searchTerm, newPersonName]);


  const showCreateOption = debouncedSearchTerm.trim() && !searchResults.some(p => p.name.toLowerCase() === debouncedSearchTerm.trim().toLowerCase());

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-1" /> Add Person
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" side="bottom" align="start">
        {!isCreating ? (
          <Command shouldFilter={false} /* Handle filtering manually */ >
            <CommandInput
              autoFocus
              value={searchTerm}
              onValueChange={setSearchTerm}
              placeholder="Search or create person..."
              className="h-9"
            />
            <CommandList>
              <ScrollArea className="max-h-[300px]">
                <CommandEmpty>
                  {!debouncedSearchTerm.trim()
                    ? "Type to search for people."
                    : "No matching people found."}
                </CommandEmpty>
                {searchResults.length > 0 && (
                  <CommandGroup heading="Existing People">
                    {searchResults.map((person) => (
                      <CommandItem
                        key={person._id}
                        value={person.name} // Value for potential filtering if enabled
                        onSelect={() => handleAssignPerson(person._id)}
                        className="flex items-center gap-2 cursor-pointer"
                        disabled={isAssigning === person._id}
                      >
                        {isAssigning === person._id ? (
                          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                        ) : (
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={person.image ?? undefined} alt={person.name} />
                            <AvatarFallback className="text-[10px]">{person.initials}</AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm font-medium truncate">{person.name}</p>
                          {person.email && <p className="text-xs text-muted-foreground truncate">{person.email}</p>}
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
                {showCreateOption && (
                  <CommandGroup>
                     <CommandItem
                        onSelect={() => {
                          setIsCreating(true);
                          setNewPersonName(debouncedSearchTerm.trim()); // Pre-fill name
                        }}
                        className="text-sm cursor-pointer"
                      >
                        <UserPlus className="mr-2 h-4 w-4" />
                        Create new person: "{debouncedSearchTerm.trim()}"
                      </CommandItem>
                  </CommandGroup>
                )}
              </ScrollArea>
            </CommandList>
          </Command>
        ) : (
          // Inline Create Form
          <div className="p-4 space-y-3">
            <p className="text-sm font-medium">Create New Person</p>
            <Input
              ref={nameInputRef}
              placeholder="Full Name (Required)"
              value={newPersonName}
              onChange={(e) => setNewPersonName(e.target.value)}
              disabled={isSavingNew}
              className="h-9"
            />
            <Input
              placeholder="Email (Optional)"
              type="email"
              value={newPersonEmail}
              onChange={(e) => setNewPersonEmail(e.target.value)}
              disabled={isSavingNew}
              className="h-9"
            />
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCreating(false)}
                disabled={isSavingNew}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCreateAndAssignPerson}
                disabled={isSavingNew || !newPersonName.trim()}
              >
                {isSavingNew ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Check className="mr-2 h-4 w-4" />
                )}
                Create & Link
              </Button>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};
