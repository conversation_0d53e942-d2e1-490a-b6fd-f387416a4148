'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { CreatePersonSchema } from '@/zod/directory-schema';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';

// Define the props for the modal
interface AddPersonModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (personId: Id<'people'>) => void; // Optional callback on success
}

// Infer the type from the Zod schema
type PersonFormData = z.infer<typeof CreatePersonSchema>;

export function AddPersonModal({
  open,
  onOpenChange,
  onSuccess
}: AddPersonModalProps) {
  const [isSaving, setIsSaving] = useState(false);
  const createPersonMutation = useMutation(api.directory.directoryPeople.createPeople);

  const form = useForm<PersonFormData>({
    resolver: zodResolver(CreatePersonSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      title: '',
      description: ''
    }
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = form;

  // Reset form when modal opens or closes
  useEffect(() => {
    if (!open) {
      // Delay reset slightly to avoid flicker during close animation
      const timer = setTimeout(() => {
        reset();
        setIsSaving(false);
      }, 150);
      return () => clearTimeout(timer);
    } else {
      reset(); // Reset immediately when opening
    }
  }, [open, reset]);

  const onSubmit = async (data: PersonFormData) => {
    setIsSaving(true);
    toast.loading('Adding person...');

    try {
      const result = await createPersonMutation({
        items: [data] // Pass data as an array item
      });

      if (result.failed && result.failed.length > 0) {
        throw new Error(result.failed[0].error || 'Failed to add person.');
      }

      const newPersonId = result.ids[0];
      toast.success('Person added successfully!');
      onSuccess?.(newPersonId); // Call success callback if provided
      onOpenChange(false); // Close modal on success
    } catch (error) {
      console.error('Error adding person:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add person.');
    } finally {
      setIsSaving(false);
      toast.dismiss(); // Dismiss loading toast
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Add New Person</DialogTitle>
            <DialogDescription>
              Enter the details for the new person you want to add to the directory.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name*
              </Label>
              <Input
                id="name"
                {...register('name')}
                className={`col-span-3 ${errors.name ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.name && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.name.message}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                className={`col-span-3 ${errors.email ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.email && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                Phone
              </Label>
              <Input
                id="phone"
                {...register('phone')}
                className={`col-span-3 ${errors.phone ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.phone && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.phone.message}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title
              </Label>
              <Input
                id="title"
                {...register('title')}
                className={`col-span-3 ${errors.title ? 'border-red-500' : ''}`}
                disabled={isSaving}
              />
              {errors.title && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.title.message}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                {...register('description')}
                className={`col-span-3 ${errors.description ? 'border-red-500' : ''}`}
                rows={3}
                disabled={isSaving}
              />
              {errors.description && (
                <p className="col-span-4 text-xs text-red-600 text-right -mt-2">
                  {errors.description.message}
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isSaving}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Add Person'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
