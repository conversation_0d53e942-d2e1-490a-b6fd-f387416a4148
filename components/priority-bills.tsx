'use client';
/**
 * PriorityBills.tsx
 *
 * This component displays and prioritizes bills based on their status, due dates, and other factors.
 * It groups bills into "overdue", "watch", "upcoming", and "handled" categories.
 *
 * Implementation Notes / Key Points:
 * - We create memoized transformations for bills to avoid unnecessary re-renders.
 * - Unused imports, variables, and components have been removed.
 * - Local state is minimized; we only track the active tab for filtering.
 * - Where possible, we rely on pure functions to compute priorities and group bills.
 * - Database (Convex) updates are handled externally via the provided callback props:
 *   onProcessBill, onViewDetails, onUpdateStatus.
 * - The UI is kept responsive and immediate; data changes are reflected via real-time updates.
 */

import { differenceInDays, format, isPast } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PriorityBillsCard } from './cards/priorityBillsCard';
import { useMemo, useState } from 'react';

// -------------------------------
// Interfaces & Types
// -------------------------------

import { Id } from '@/convex/_generated/dataModel';
import { BillStatus, BillType } from '@/zod/bills-schema';

interface Bill {
  _id: Id<'bills'>;
  dueDate: Date | number;
  billDate?: Date | number;
  amount: number;
  vendor: string;
  vendorId?: Id<'organizations'>;
  billStatus?: BillStatus;
  category?: string;
  type: BillType;
  isHighValue?: boolean;
  isUnusual?: boolean;
  percentageChange?: number;
  previousAmount?: number;
  billNo?: string;
  memo?: string;
}

interface PriorityBillsProps {
  bills: Bill[];
  onProcessBill: (billId: Id<'bills'>) => void;
  onViewDetails?: (billId: Id<'bills'>) => void;
  onUpdateStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<void>;
}

type PriorityLevel = 'overdue' | 'watch' | 'upcoming' | 'handled';

// -------------------------------
// Helper Functions
// -------------------------------

/**
 * Check if a BillStatus is considered "terminal" or "fully completed".
 */
function isTerminalStatus(status?: BillStatus): boolean {
  return status === 'PAID';
}

/**
 * Check if a BillStatus requires user action
 */
function statusNeedsAction(status?: BillStatus): boolean {
  return status === 'UNPAID';
}

/**
 * Check if a BillStatus is currently in progress
 */
function isInProgress(status?: BillStatus): boolean {
  return (
    status === 'PARTIALLY_PAID' ||
    status === 'SCHEDULED' ||
    status === 'IN_PROCESS'
  );
}

/**
 * Determine the priority level, reasons, and a suggested action for a bill.
 * Overdue is highest priority, then "watch" (close to due date or needs action),
 * then "upcoming", and "handled" for bills that are terminal.
 */
function calculatePriority(bill: Bill) {
  const now = new Date();
  const reasons: string[] = [];
  let suggestedAction: string | undefined;
  let score = 0 as number;

  // Terminal status => "handled".
  if (isTerminalStatus(bill.billStatus)) {
    return {
      level: 'handled' as PriorityLevel,
      reasons: ['Bill has been processed and completed'],
      score: 0,
    };
  }

  // Days until due date (negative if overdue).
  const dueDateObj = new Date(bill.dueDate);
  const daysUntilDue = differenceInDays(dueDateObj, now);
  const isOverdue = isPast(dueDateObj);
  const isApproachingDue = daysUntilDue <= 5 && daysUntilDue >= 0;

  // Overdue: Past its due date.
  if (isOverdue) {
    score = 100 + Math.abs(daysUntilDue);

    if (isInProgress(bill.billStatus)) {
      reasons.push(`In processing stage "${bill.billStatus}"`);
      suggestedAction = 'Expedite current processing stage';
    } else {
      suggestedAction = 'Process immediately';
    }

    return {
      level: 'overdue' as PriorityLevel,
      reasons,
      score,
      suggestedAction,
    };
  }

  // "Watch": Bill is approaching due date or needs action.
  if (isApproachingDue || statusNeedsAction(bill.billStatus)) {
    score = 50 + (5 - daysUntilDue);
    if (isApproachingDue) reasons.push(`Due in ${daysUntilDue} days`);

    if (!isInProgress(bill.billStatus)) {
      reasons.push('Not yet in appropriate processing stage');
      suggestedAction = 'Schedule for payment soon';
    } else {
      reasons.push(`In processing stage "${bill.billStatus}"`);
      suggestedAction = 'Continue processing as scheduled';
    }

    return {
      level: 'watch' as PriorityLevel,
      reasons,
      score,
      suggestedAction,
    };
  }

  // "Upcoming": Not overdue and not close to due date.
  score = 10;
  if (isInProgress(bill.billStatus)) {
    reasons.push(`In processing stage "${bill.billStatus}"`);
    reasons.push(`Due on ${format(dueDateObj, 'MMM d')}`);
    suggestedAction = 'Continue normal processing';
  } else {
    reasons.push(`Due on ${format(dueDateObj, 'MMM d')}`);
    suggestedAction = 'Review and schedule when appropriate';
  }

  return {
    level: 'upcoming' as PriorityLevel,
    reasons,
    score,
    suggestedAction,
  };
}

/**
 * Group bills by their priority (overdue, watch, upcoming, handled).
 */
function groupBillsByPriority(bills: Bill[]) {
  const groups = new Map<
    PriorityLevel,
    { bill: Bill; priority: ReturnType<typeof calculatePriority> }[]
  >();

  // Initialize empty arrays for each priority level.
  groups.set('overdue', []);
  groups.set('watch', []);
  groups.set('upcoming', []);
  groups.set('handled', []);

  // Compute priority for each bill, then group accordingly.
  bills.forEach(bill => {
    const priority = calculatePriority(bill);
    groups.get(priority.level)?.push({ bill, priority });
  });

  // Sort each group by descending amount.
  for (const [level, items] of groups.entries()) {
    groups.set(
      level,
      items.sort((a, b) => b.bill.amount - a.bill.amount)
    );
  }

  return groups;
}

function getStatusPriority(status?: BillStatus): number {
  if (!status) return 0;

  // Highest priority: Unpaid bills
  if (status === 'UNPAID') return 4;

  // Medium-high priority: Partially paid
  if (status === 'PARTIALLY_PAID') return 3;

  // Medium priority: Scheduled or in process
  if (status === 'SCHEDULED' || status === 'IN_PROCESS') return 2;

  // Low priority: Paid
  if (status === 'PAID') return 1;

  // Default priority for unknown statuses
  return 0;
}

// -------------------------------
// Main Component
// -------------------------------

export function PriorityBills({
  bills,
  onProcessBill,
  onViewDetails,
  onUpdateStatus,
}: PriorityBillsProps) {
  /**
   * Convert date fields to proper Date objects. We store the result in a memo
   * so we don't do this repeatedly if "bills" hasn't changed.
   */
  const processedBills = useMemo(() => {
    return bills.map(bill => ({
      ...bill,
      dueDate: bill.dueDate instanceof Date ? bill.dueDate : new Date(bill.dueDate),
      billDate:
        bill.billDate instanceof Date
          ? bill.billDate
          : bill.billDate
          ? new Date(bill.billDate)
          : undefined,
    }));
  }, [bills]);

  /**
   * Group processed bills by priority. Memoize so we only re-group when processedBills changes.
   */
  const groupedBills = useMemo(() => {
    return groupBillsByPriority(processedBills);
  }, [processedBills]);

  // Tally relevant counts.
  const overdueCount = groupedBills.get('overdue')?.length || 0;
  const watchCount = groupedBills.get('watch')?.length || 0;
  const upcomingCount = groupedBills.get('upcoming')?.length || 0;
  const needAttention = overdueCount + watchCount + upcomingCount;

  /**
   * Tabs for filtering: "all", "overdue", "watch", "upcoming".
   * Default to "all" so users see a quick snapshot.
   */
  const [activeTab, setActiveTab] = useState<PriorityLevel | 'all'>('all');

  // If there are no bills that need attention, skip rendering.
  if (needAttention === 0) {
    return null;
  }

  /**
   * Gather filtered bills (overdue first, watch second, upcoming third) when "all" is active,
   * or just one subset if a specific tab is selected.
   */
  const getFilteredBills = () => {
    if (activeTab === 'all') {
      return [
        ...(groupedBills.get('overdue') || []),
        ...(groupedBills.get('watch') || []),
        ...(groupedBills.get('upcoming') || []),
      ];
    }
    return groupedBills.get(activeTab) || [];
  };

  // Get the filtered bills to determine count for responsive layout
  const filteredBills = getFilteredBills();
  const billCount = filteredBills.length;

  // Determine appropriate grid columns based on number of items
  const getGridColsClass = () => {
    if (billCount <= 2) {
      return "grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2";
    } else if (billCount <= 3) {
      return "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3";
    } else {
      return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3";
    }
  };

  // Determine appropriate gap based on number of items
  const getGapClass = () => {
    if (billCount <= 2) {
      return "gap-6 sm:gap-8 md:gap-10 lg:gap-12"; // Larger gaps for fewer items
    } else {
      return "gap-2 sm:gap-4 lg:gap-6 xl:gap-10"; // Standard responsive gaps
    }
  };

  return (
    <div className="w-full relative mt-4 overflow-visible pr-4 md:pr-6 lg:pr-8">
      {/* Title & Tabs */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Priority Bills</h2>
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as PriorityLevel | 'all')}
        >
          <TabsList className="h-8">
            <TabsTrigger value="all" className="flex items-center gap-1 h-7 text-xs">
              All
              <Badge variant="secondary" className="ml-1 text-xs">
                {needAttention}
              </Badge>
            </TabsTrigger>
            {overdueCount > 0 && (
              <TabsTrigger value="overdue" className="flex items-center gap-1 h-7 text-xs">
                Overdue
                <Badge variant="destructive" className="ml-1 text-xs">
                  {overdueCount}
                </Badge>
              </TabsTrigger>
            )}
            {watchCount > 0 && (
              <TabsTrigger value="watch" className="flex items-center gap-1 h-7 text-xs">
                Watch
                <Badge
                  variant="outline"
                  className="bg-amber-100 text-amber-800 border-amber-300 ml-1 text-xs"
                >
                  {watchCount}
                </Badge>
              </TabsTrigger>
            )}
            {upcomingCount > 0 && (
              <TabsTrigger value="upcoming" className="flex items-center gap-1 h-7 text-xs">
                Upcoming
                <Badge
                  variant="outline"
                  className="bg-blue-100 text-blue-800 border-blue-300 ml-1 text-xs"
                >
                  {upcomingCount}
                </Badge>
              </TabsTrigger>
            )}
          </TabsList>
        </Tabs>
      </div>

      {/* Grid Display */}
      <div className={`
        grid
        ${getGridColsClass()}
        ${getGapClass()}
        relative
        w-full
        max-w-[calc(100%-64px)]
        xl:max-w-[calc(100%-80px)]
        overflow-visible
        pb-2
      `}>
        {filteredBills.map(({ bill, priority }) => (
          <div
            key={bill._id}
            className="relative hover:z-[999] transition-all"
          >
            <PriorityBillsCard
              bill={bill}
              priority={priority}
              onProcessBill={onProcessBill}
              onViewDetails={onViewDetails}
              onUpdateStatus={onUpdateStatus}
              // Tint the card background slightly if "overdue" or "watch".
              tint={
                priority.level === 'overdue'
                  ? { color: '#ef4444', opacity: 0.2 }
                  : priority.level === 'watch'
                  ? { color: '#f59e0b', opacity: 0.2 }
                  : undefined
              }
            />
          </div>
        ))}
      </div>
    </div>
  );
}
