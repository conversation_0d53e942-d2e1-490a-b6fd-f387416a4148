'use client';

import React, { useEffect, useRef, useState } from 'react'; // Added useState
import { useThreadRuntime } from '@assistant-ui/react';
import { Thread } from '@/components/assistant-ui/thread';
import { useChatFilters } from '@/app/ConvexClientProvider';

interface InitializableThreadProps {
  initialMessage?: string | null; // Keep initialMessage prop
  initialFilters?: string[];
}

// Define a global variable to store the current filters
// This ensures filters are available even if React context updates are delayed
let currentGlobalFilters: string[] = [];

export const InitializableThread: React.FC<InitializableThreadProps> = ({
  initialMessage: initialMessageProp, // Rename prop to avoid conflict
  initialFilters = [],
  ...threadProps
}) => {
  const threadRuntime = useThreadRuntime();
  const { filters, setFilters } = useChatFilters(); // Get filters and setFilters
  const [messageToAppend, setMessageToAppend] = useState<string | null>(null); // State to hold message after filters are set
  const processedMessageRef = useRef<string | null>(null); // Ref to track which message was processed
  const filtersRef = useRef<string[]>(initialFilters); // Ref to track filters directly

  // Effect to set filters as early as possible
  useEffect(() => {
    // Apply filters immediately when component mounts or initialFilters changes
    const filtersToUse = initialFilters.length > 0 
      ? initialFilters 
      : ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT'];
    
    console.log("InitializableThread: Setting initial filters:", filtersToUse);
    setFilters(filtersToUse);
    filtersRef.current = filtersToUse;
    currentGlobalFilters = [...filtersToUse];
    
    // Return cleanup function
    return () => {
      console.log("InitializableThread: Cleanup - resetting filters");
      setFilters([]);
    };
  }, [initialFilters, setFilters]); // Only depend on initialFilters and setFilters

  // Effect 1: Set filters and prepare message when initial props change
  useEffect(() => {
    // Only run if we have a new initial message prop that we haven't processed yet
    if (initialMessageProp && initialMessageProp !== processedMessageRef.current) {
      console.log("InitializableThread Effect 1: Setting filters and preparing message", { initialMessageProp, initialFilters });
      
      // Update both the context filters and our local references
      setFilters(initialFilters);
      filtersRef.current = initialFilters;
      currentGlobalFilters = [...initialFilters]; // Update global variable
      
      console.log("InitializableThread: Updated filter references", {
        filtersRef: filtersRef.current,
        globalFilters: currentGlobalFilters,
        contextFilters: filters,
        timestamp: new Date().toISOString()
      });
      
      setMessageToAppend(initialMessageProp); // Set state to trigger the next effect
      processedMessageRef.current = initialMessageProp; // Mark this message as processed
    }

    // Cleanup: Reset filters when the component unmounts or the initial message prop changes
    return () => {
      if (initialMessageProp && initialMessageProp === processedMessageRef.current) {
        console.log("InitializableThread Effect 1 Cleanup: Resetting filters.");
        setFilters([]);
        filtersRef.current = [];
        currentGlobalFilters = [];
        setMessageToAppend(null); // Clear pending message
        processedMessageRef.current = null; // Reset processed marker
      }
    };
  }, [initialMessageProp, initialFilters, setFilters, filters]); // Depend on props and setFilters

  // Effect to update filters when initialFilters prop changes
  useEffect(() => {
    // If initialFilters is provided, update the filters context
    if (initialFilters.length > 0) {
      console.log("InitializableThread: Updating filters from props:", initialFilters);
      setFilters(initialFilters);
      filtersRef.current = initialFilters;
      currentGlobalFilters = [...initialFilters]; // Update global variable
    } else {
      // Default to all document types if no filters are provided
      const defaultFilters = ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT'];
      console.log("InitializableThread: No filters provided, using defaults:", defaultFilters);
      setFilters(defaultFilters);
      filtersRef.current = defaultFilters;
      currentGlobalFilters = [...defaultFilters]; // Update global variable
    }
  }, [initialFilters, setFilters]);

  // Custom function to append a message with filters
  const appendMessageWithFilters = (message: string, filtersToUse: string[]) => {
    if (!threadRuntime) {
      console.error("Thread runtime not available");
      return;
    }
    
    try {
      // Update the filters context first
      setFilters(filtersToUse);
      
      // Log what we're doing
      console.log("InitializableThread: Appending message and updating filters context:", {
        message: message.substring(0, 20) + "...",
        filtersToUse: filtersToUse,
        timestamp: new Date().toISOString()
      });
      
      // Simply append the message without custom properties
      // The filters will be picked up from context in the ConvexClientProvider
      threadRuntime.append({ 
        role: 'user', 
        content: [{ type: 'text', text: message }]
      });
      
    } catch (error) {
      console.error("Error in appendMessageWithFilters:", error);
    }
  };

  // Effect 2: Append message with filters
  useEffect(() => {
    if (threadRuntime && messageToAppend) {
      console.log("InitializableThread Effect 2: Preparing to append message", {
        messageToAppend,
        filtersFromRef: filtersRef.current,
        globalFilters: currentGlobalFilters,
        contextFilters: filters,
        timestamp: new Date().toISOString()
      });
      
      // Use a combination of all available filter sources to ensure we have filters
      const filtersToUse = filtersRef.current.length > 0 ? 
        filtersRef.current : 
        (currentGlobalFilters.length > 0 ? 
          currentGlobalFilters : 
          (filters.length > 0 ? filters : initialFilters));
      
      // Ensure filters are set in context before appending message
      if (JSON.stringify(filters) !== JSON.stringify(filtersToUse)) {
        console.log("InitializableThread: Updating filters before sending message:", filtersToUse);
        setFilters(filtersToUse);
      }
      
      console.log("InitializableThread: Using filters", {
        filtersToUse,
        source: filtersRef.current.length > 0 ? 
          "filtersRef" : 
          (currentGlobalFilters.length > 0 ? 
            "globalFilters" : 
            (filters.length > 0 ? "contextFilters" : "initialFilters"))
      });
      
      // Use setTimeout to ensure this runs after state updates have been processed
      setTimeout(() => {
        appendMessageWithFilters(messageToAppend, filtersToUse);
        setMessageToAppend(null); // Clear the pending message
      }, 100);
    }
  }, [threadRuntime, messageToAppend, filters, initialFilters]);

  // Render the actual Thread component, passing down any extra props
  return <Thread {...threadProps} />;
};

export default InitializableThread;
