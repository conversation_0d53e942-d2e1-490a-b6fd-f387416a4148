/**
 * J<PERSON><PERSON><PERSON> Similarity with normalization for tag deduplication.
 * - Normalizes: lowercase, trims, strips punctuation.
 * - Returns a value between 0 (completely different) and 1 (identical).
 */

function normalizeTagName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s]/g, "") // Remove punctuation
    .replace(/\s+/g, " ");   // Collapse multiple spaces
}

function jaro(s1: string, s2: string): number {
  if (s1 === s2) return 1.0;
  const len1 = s1.length;
  const len2 = s2.length;
  if (len1 === 0 || len2 === 0) return 0.0;

  const matchDistance = Math.floor(Math.max(len1, len2) / 2) - 1;
  const s1Matches = new Array(len1).fill(false);
  const s2Matches = new Array(len2).fill(false);

  let matches = 0;
  let transpositions = 0;

  // Find matches
  for (let i = 0; i < len1; i++) {
    const start = Math.max(0, i - matchDistance);
    const end = Math.min(i + matchDistance + 1, len2);
    for (let j = start; j < end; j++) {
      if (s2Matches[j]) continue;
      if (s1[i] !== s2[j]) continue;
      s1Matches[i] = true;
      s2Matches[j] = true;
      matches++;
      break;
    }
  }
  if (matches === 0) return 0.0;

  // Count transpositions
  let k = 0;
  for (let i = 0; i < len1; i++) {
    if (!s1Matches[i]) continue;
    while (!s2Matches[k]) k++;
    if (s1[i] !== s2[k]) transpositions++;
    k++;
  }
  transpositions = transpositions / 2;

  return (
    (matches / len1 +
      matches / len2 +
      (matches - transpositions) / matches) /
    3
  );
}

export function jaroWinkler(s1: string, s2: string, prefixScale = 0.1): number {
  const norm1 = normalizeTagName(s1);
  const norm2 = normalizeTagName(s2);
  const jaroScore = jaro(norm1, norm2);

  // Winkler boost for common prefix up to 4 chars
  let prefix = 0;
  for (let i = 0; i < Math.min(4, norm1.length, norm2.length); i++) {
    if (norm1[i] === norm2[i]) prefix++;
    else break;
  }
  return jaroScore + prefix * prefixScale * (1 - jaroScore);
}

export { normalizeTagName };
