import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Tag, TaggableId, TaggableType } from "@/zod/tags-schema"; // Import Taggable types
import { Check, Plus, Search } from 'lucide-react';

import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';

interface AddOrganizationTagPopoverProps {
  organizationId: Id<'organizations'>;
  onSuccess?: () => void;
}

export function AddOrganizationTagPopover({ 
  organizationId,
  onSuccess 
}: AddOrganizationTagPopoverProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  // 1. Fetch the ID for the "general-tags" type
  const generalTagsType = useQuery(api.tags.getTagTypeBySlug, { slug: "general-tags" });
  const generalTagsTypeId = generalTagsType?._id;

  // 2. Fetch only tags of the "general-tags" type (if the type ID was found)
  const allTagsData = useQuery(
    api.tags.fetchTags,
    generalTagsTypeId ? { filter: { tag_type: generalTagsTypeId } } : "skip" // Use "skip" instead of undefined
  );
  const allTags = allTagsData ?? []; // Default to empty array if undefined or query skipped

  // 3. Fetch tags already assigned to the organization to filter them out
  const organizationTagsData = useQuery(
    api.tags.getTagsForTaggable, 
    { 
      taggable_type: "organization",
      taggable_id: organizationId
    }
  );
  const organizationTagIds = new Set(organizationTagsData?.map((tag: Tag) => tag._id));

  // 4. Add mutation hook for adding tags using the generic function
  const addTagMutation = useMutation(api.tags.addTagToTaggable);

  // Filter tags based on search query and exclude already assigned tags
  const availableTags = allTags
    .filter(tag => !organizationTagIds.has(tag._id))
    .filter(tag => {
      if (!searchQuery) return true;
      return tag.name.toLowerCase().includes(searchQuery.toLowerCase());
    });

  const handleAddTag = async (tagId: Id<'tags'>) => {
    try {
      await addTagMutation({ 
        taggable_type: "organization", // Specify the type
        taggable_id: organizationId,    // Pass the ID
        tagId: tagId                   // Pass the tag ID
      });
      
      toast({
        title: "Success",
        description: "Tag added to organization"
      });
      
      if (onSuccess) onSuccess();
      setOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add tag",
        variant: "destructive"
      });
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-6 px-2 text-xs">
          <Plus className="h-3 w-3 mr-1" />
          Add Tag
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput 
            placeholder="Search tags..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No matching tags found</CommandEmpty>
            <CommandGroup>
              {availableTags.length > 0 ? (
                availableTags.map((tag) => (
                  <CommandItem
                    key={tag._id}
                    value={tag.name}
                    onSelect={() => handleAddTag(tag._id)}
                    className="flex items-center"
                  >
                    <div 
                      className={cn(
                        "w-2 h-2 rounded-full mr-2", 
                        tag.color || "bg-gray-400"
                      )} 
                    />
                    {tag.name}
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))
              ) : (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  {allTags.length === organizationTagIds.size ? 'All tags assigned' : 'No matching tags found.'}
                </div>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
