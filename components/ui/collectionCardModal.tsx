"use client"

import { ReactNode, useEffect, useState } from "react"
import { createPortal } from "react-dom"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"
import { formatDisplayDate, formatRelativeTime } from "@/lib/date-utils"

// ----------------------------------
// COLLECTION CARD MODAL PROPS
// ----------------------------------
interface CollectionCardModalProps {
  heading: string
  subheading?: string
  buttonText?: string
  buttonIcon?: LucideIcon
  onButtonClick?: () => void
  children: ReactNode
  className?: string
  showFooter?: boolean
  footerContent?: ReactNode
  primaryCTA?: {
    text: string
    onClick: () => void
  }
  secondaryCTA?: {
    text: string
    onClick: () => void
  }
  dateInfo?: {
    createdAt?: Date | number
    updatedAt?: Date | number
  }
  onClose: () => void
}

// ----------------------------------
// COLLECTION CARD MODAL COMPONENT
// ----------------------------------
export function CollectionCardModal({
  heading,
  subheading,
  buttonText,
  buttonIcon,
  onButtonClick,
  children,
  className,
  showFooter = true,
  footerContent,
  primaryCTA,
  secondaryCTA,
  dateInfo,
  onClose
}: CollectionCardModalProps) {
  const [mounted, setMounted] = useState(false)
  // Calculate available width for the modal by accounting for sidebar and padding
  const [maxModalWidth, setMaxModalWidth] = useState("calc(100vw - 8rem)") // Default with some padding

  useEffect(() => {
    // Only set mounted to true on the client-side
    if (typeof window !== 'undefined') {
      setMounted(true)
      
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden'
      
      // Cleanup function to restore scrolling when modal is closed
      return () => {
        document.body.style.overflow = ''
      }
    }
  }, [])

  // The actual modal content
  const modalContent = (
    <>
      {/* Background overlay - covers entire screen including nav */}
      <div className="fixed inset-0 bg-amber-100/10 backdrop-blur-md pointer-events-none z-[1000]" />

      {/* 
        Modal container - positioned fixed to viewport
      */}
      <div
        className="fixed inset-0 flex items-center justify-center p-4 pointer-events-auto z-[1010]"
      >
        {/* Main modal container - positioned relative to create stacking context */}
        <div
          className={cn(
            "relative w-full rounded-3xl overflow-hidden transform-gpu will-change-transform",
            "bg-zinc-100/10 border border-white/40",
            "shadow-deep backdrop-blur-xl",
            "max-w-md max-h-[85vh] flex flex-col modal-form-fix z-[1020]", // Added max-w-md
            className
          )}
          style={{
            isolation: 'isolate',
            transform: 'translate3d(0, 0, 0)',
            backfaceVisibility: 'hidden',
          }}
        >
          {/*
            Close button (red, top right)
            COMMENTED OUT: This button is currently not working, so it is commented out per user instruction.
            To re-enable, uncomment this block.
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 transition-[background-color] duration-100 flex items-center justify-center group z-[1030]"
            aria-label="Close modal"
          >
            <span className="text-[10px] font-medium text-red-900/0 group-hover:text-red-900/90">
              ×
            </span>
          </button>
          */}

          {/* Card Content */}
          <div className="p-6 relative z-1 overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-neutral-800">{heading}</h2>
                {subheading && <p className="text-neutral-600 text-sm">{subheading}</p>}
              </div>
              <div className="flex items-center gap-3">
                {dateInfo && (
                  <div className="text-right">
                    <div className="text-xs text-gray-500">
                      {dateInfo.createdAt && (
                        <span>Created: {formatDisplayDate(new Date(dateInfo.createdAt))}</span>
                      )}
                      {dateInfo.createdAt && dateInfo.updatedAt && (
                        <span className="mx-1">•</span>
                      )}
                      {dateInfo.updatedAt && (
                        <span>Updated: {formatRelativeTime(new Date(dateInfo.updatedAt).getTime())}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4 relative safari-modal-fix" style={{transform: 'translateZ(0)'}}>
              {children}
            </div>
          </div>

          {/* Footer with buttons */}
          {showFooter && (
            <div 
              className="flex-none h-16 px-6 border-t border-white/40 bg-stone-500/10 flex items-center justify-end gap-4 rounded-b-3xl -mb-[1px] -mx-[1px] z-[9020] relative"
            >
              {footerContent || (
                <>
                  {secondaryCTA && (
                    <SecondaryButton onClick={secondaryCTA.onClick}>
                      {secondaryCTA.text}
                    </SecondaryButton>
                  )}
                  {primaryCTA && (
                    <PrimaryButton onClick={primaryCTA.onClick}>
                      {primaryCTA.text}
                    </PrimaryButton>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  )

  if (!mounted) return null

  return createPortal(modalContent, document.body)
}

// Primary CTA Button
export function PrimaryButton({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-lg transform-gpu",
        "bg-white/30 hover:bg-white/40 text-gray-800 font-medium",
        "transition-colors relative z-1",
        className
      )}
      style={{ transform: 'translate3d(0, 0, 0)' }}
      {...props}
    >
      {children}
    </button>
  )
}

// Secondary CTA Button
export function SecondaryButton({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-lg transform-gpu",
        "bg-white/30 hover:bg-white/40 text-neutral-800 font-medium",
        "transition-colors relative z-1",
        className
      )}
      style={{ transform: 'translate3d(0, 0, 0)' }}
      {...props}
    >
      {children}
    </button>
  )
}

export default CollectionCardModal
