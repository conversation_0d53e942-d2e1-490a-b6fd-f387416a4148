"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

// GlassmorphicHero component props interface
interface GlassmorphicHeroProps {
  badge?: {
    text: string;
    action?: {
      text: string;
      href: string;
    };
  };
  title: string;
  description: string;
  actions?: Array<{
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary" | "ghost";
    icon?: React.ReactNode;
  }>;
  image?: {
    src: string;
    alt: string;
  };
}

/**
 * GlassmorphicHero component
 * 
 * A modern, glassmorphic hero section with customizable content,
 * background effects, and responsive design.
 */
export function GlassmorphicHero({
  badge,
  title,
  description,
  actions,
  image,
}: GlassmorphicHeroProps) {
  return (
    <section className="relative overflow-hidden bg-background py-20 px-4 sm:py-32">
      {/* Glassmorphic background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 h-[500px] w-[500px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-indigo-500/10 blur-3xl" />
        <div className="absolute top-3/4 right-1/4 h-[600px] w-[600px] translate-x-1/2 -translate-y-1/2 rounded-full bg-purple-500/10 blur-3xl" />
        <div className="absolute top-1/2 left-1/2 h-[300px] w-[300px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-blue-500/10 blur-3xl" />
      </div>

      {/* Content container */}
      <div className="relative z-10 mx-auto max-w-6xl">
        <div className="flex flex-col items-center gap-8 text-center">
          {/* Badge */}
          {badge && (
            <div className="backdrop-blur-md bg-background/30 border border-white/40 rounded-full px-4 py-1 animate-fade-in">
              <span className="text-gray-700">{badge.text}</span>
              {badge.action && (
                <Link href={badge.action.href} className="ml-2 text-indigo-600 hover:text-indigo-800 font-medium">
                  {badge.action.text}
                </Link>
              )}
            </div>
          )}

          {/* Title */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight text-gray-900 max-w-4xl">
            {title}
          </h1>

          {/* Description */}
          <p className="text-xl text-gray-600 max-w-2xl">
            {description}
          </p>

          {/* Actions */}
          {actions && actions.length > 0 && (
            <div className="flex flex-wrap items-center justify-center gap-4 mt-4">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || "default"}
                  asChild
                  className={cn(
                    "rounded-lg text-base px-6 py-3 h-auto",
                    action.variant === "outline" && "backdrop-blur-md bg-background/30 border-white/40"
                  )}
                >
                  <Link href={action.href} className="flex items-center gap-2">
                    {action.text}
                    {!action.icon && action.variant === "default" && <ArrowRight className="h-4 w-4 ml-1" />}
                    {action.icon}
                  </Link>
                </Button>
              ))}
            </div>
          )}

          {/* Image */}
          {image && (
            <div className="relative mt-12 w-full max-w-5xl overflow-hidden rounded-lg border border-white/40 backdrop-blur-sm bg-background/30 shadow-xl">
              <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/5 to-purple-500/5 opacity-30" />
              <div className="relative p-2">
                <Image
                  src={image.src}
                  alt={image.alt}
                  width={1200}
                  height={675}
                  className="w-full rounded-md object-cover"
                  priority
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
