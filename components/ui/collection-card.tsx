"use client"

import type { ReactNode } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"
import { formatDisplayDate, formatRelativeTime } from "@/lib/date-utils";

interface CollectionCardProps {
  heading: string
  subheading?: string
  buttonText?: string
  buttonIcon?: LucideIcon
  onButtonClick?: () => void
  children: ReactNode
  className?: string
  showFooter?: boolean
  footerContent?: ReactNode
  primaryCTA?: {
    text: string
    onClick: () => void
  }
  secondaryCTA?: {
    text: string
    onClick: () => void
  }
  dateInfo?: {
    createdAt?: Date | number
    updatedAt?: Date | number
  }
}

export function CollectionCard({
  heading,
  subheading,
  buttonText,
  buttonIcon: ButtonIcon = ChevronRight,
  onButtonClick,
  children,
  className,
  showFooter = false,
  footerContent,
  primaryCTA,
  secondaryCTA,
  dateInfo,
}: CollectionCardProps) {
  return (
    <div 
      className={cn(
        "relative w-full rounded-3xl overflow-hidden transform-gpu will-change-transform",
        "bg-zinc-100/10 border border-white/40",
        "shadow-medium hover:shadow-medium-hover hover:-translate-y-1",
        "transition-all duration-200 ease-in-out",
        className
      )}
      style={{
        isolation: 'isolate',
        transform: 'translate3d(0, 0, 0)',
        backfaceVisibility: 'hidden',
      }}
    >
      {/* Card Content */}
      <div className="p-5 relative z-1">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-neutral-800">{heading}</h2>
            {subheading && <p className="text-neutral-600 text-sm">{subheading}</p>}
          </div>
          <div className="flex items-center gap-3">
            {dateInfo && (
              <div className="text-right">
                <div className="text-xs text-gray-500">
                  {dateInfo.createdAt && (
                    <span>Created: {formatDisplayDate(new Date(dateInfo.createdAt))}</span>
                  )}
                  {dateInfo.createdAt && dateInfo.updatedAt && (
                    <span className="mx-1">•</span>
                  )}
                  {dateInfo.updatedAt && (
                    <span>Updated: {formatRelativeTime(new Date(dateInfo.updatedAt).getTime())}</span>
                  )}
                </div>
              </div>
            )}
            {buttonText && onButtonClick && (
              <Button
                variant="ghost"
                className="relative bg-white/10 hover:bg-white/20 text-gray-700 transform-gpu"
                style={{ transform: 'translate3d(0, 0, 0)' }}
                onClick={onButtonClick}
              >
                {buttonText}
                <ButtonIcon className="w-4 h-4 ml-1" />
              </Button>
            )}
          </div>
        </div>

        <div className="space-y-4 relative">{children}</div>
      </div>

      {/* Modified Footer Section with CTA buttons */}
      {showFooter && (
        <div 
          className="h-16 px-6 border-t border-white/40 relative z-1"
          style={{
            background: 'linear-gradient(to bottom, rgba(255,255,255,0.2), rgba(255,255,255,0.2))',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
          }}
        >
          <div className="flex items-center justify-end gap-4 h-full">
            {footerContent || (
              <>
                {secondaryCTA && (
                  <SecondaryButton onClick={secondaryCTA.onClick}>
                    {secondaryCTA.text}
                  </SecondaryButton>
                )}
                {primaryCTA && (
                  <PrimaryButton onClick={primaryCTA.onClick}>
                    {primaryCTA.text}
                  </PrimaryButton>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Primary CTA Button
export function PrimaryButton({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-lg transform-gpu",
        "bg-white/30 hover:bg-white/40 text-gray-800 font-medium",
        "transition-colors relative z-1",
        className
      )}
      style={{ transform: 'translate3d(0, 0, 0)' }}
      {...props}
    >
      {children}
    </button>
  )
}

// Secondary CTA Button
export function SecondaryButton({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-lg transform-gpu",
        "bg-white/30 hover:bg-white/40 text-neutral-800 font-medium",
        "transition-colors relative z-1",
        className
      )}
      style={{ transform: 'translate3d(0, 0, 0)' }}
      {...props}
    >
      {children}
    </button>
  )
}

export default CollectionCard
