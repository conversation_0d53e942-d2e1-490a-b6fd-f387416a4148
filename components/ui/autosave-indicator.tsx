'use client';

import * as React from 'react';
import { Check, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AutosaveIndicatorProps {
  saving?: boolean;
  status?: 'saving' | 'saved' | 'error';
  className?: string;
}

export function AutosaveIndicator({
  saving = false,
  status,
  className
}: AutosaveIndicatorProps) {
  // For backward compatibility, if status is provided, use it instead of saving
  const isSaving = status ? status === 'saving' : saving;
  const hasError = status === 'error';
  const [show, setShow] = React.useState(false);

  // Show the indicator whenever saving status changes
  React.useEffect(() => {
    if (isSaving || hasError) {
      setShow(true);
    } else {
      // Keep showing for a brief moment after saving completes
      const timer = setTimeout(() => setShow(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isSaving, hasError]);

  if (!show) {
    return null;
  }

  return (
    <div
      className={cn(
        'text-xs text-muted-foreground flex items-center gap-1.5 animate-in fade-in',
        className
      )}
    >
      {hasError ? (
        <>
          <span className="text-destructive">⚠️</span>
          <span className="text-destructive">Error saving</span>
        </>
      ) : isSaving ? (
        <>
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Saving...</span>
        </>
      ) : (
        <>
          <Check className="h-3 w-3" />
          <span>Saved</span>
        </>
      )}
    </div>
  );
}
