'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check, X, Pencil } from 'lucide-react';
import { cn } from '@/lib/utils'; // Import cn utility

interface InlineEditFieldProps {
  label: string;
  value: string;
  onSave: (value: string) => Promise<void>;
  formatValue?: (value: string) => string;
  className?: string; // For the outer container
  inputClassName?: string; // For the input element
  displayClassName?: string; // For the display span element
}

export function InlineEditField({
  label,
  value,
  onSave,
  formatValue,
  className,
  inputClassName,
  displayClassName
}: InlineEditFieldProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleSave = async () => {
    if (inputValue === value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    try {
      await onSave(inputValue);
      setIsEditing(false);
    } catch (error) {
      // Revert to original value on error
      setInputValue(value);
      console.error('Failed to save:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setInputValue(value);
    setIsEditing(false);
  };

  const displayValue = formatValue ? formatValue(value) : value;

  return (
    <div className={`flex flex-col space-y-1 ${className}`}>
      <div className="text-sm text-muted-foreground">{label}</div>
      {isEditing ? (
        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className={cn("h-8", inputClassName)} // Apply inputClassName
            disabled={isSaving}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSave();
              } else if (e.key === 'Escape') {
                handleCancel();
              }
            }}
          />
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleCancel}
              disabled={isSaving}
            >
              <X className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleSave}
              disabled={isSaving}
            >
              <Check className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        // Removed justify-between and group class, added onClick to span
        <div className="flex items-center"> 
          <span 
            className={cn("text-sm cursor-pointer", displayClassName)} // Added cursor-pointer
            onClick={() => setIsEditing(true)} // Added onClick handler
          >
            {displayValue || 'Not set'}
          </span>
          {/* Removed Pencil Button */}
        </div>
      )}
    </div>
  );
}
