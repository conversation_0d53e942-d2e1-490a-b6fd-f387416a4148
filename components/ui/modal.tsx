"use client"

import { ButtonHTMLAttributes, ReactNode, useEffect, useState } from "react"
import { createPortal } from "react-dom"
import { cn } from "@/lib/utils"

// ----------------------------------
// BUTTON COMPONENT
// ----------------------------------
interface <PERSON><PERSON><PERSON><PERSON> extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode
  variant?: "primary" | "secondary"
}

const Button = ({ children, variant = "primary", className, ...props }: ButtonProps) => {
  return (
    <button
      className={cn(
        "px-6 py-2 rounded-full font-medium transition-all duration-300 backdrop-blur-sm focus:outline-none focus:ring-2 relative group",
        variant === "primary"
          ? "bg-gray-300/70 hover:bg-gray-400/20 text-zinc-600 focus:ring-gray-900 overflow-hidden"
          : "bg-gray-200/30 hover:bg-gray-400/20 text-zinc-500 focus:ring-gray-900",
        className
      )}
      {...props}
    >
      {variant === "primary" && (
        <span className="absolute inset-0 overflow-hidden rounded-full pointer-events-none">
          <span className="absolute -top-1 -left-1 w-1/2 h-1/2 bg-gradient-to-br from-white/30 to-transparent rounded-full transform rotate-12 transition-transform duration-300 group-hover:translate-x-full" />
        </span>
      )}
      {children}
    </button>
  )
}

// ----------------------------------
// MODAL PROPS
// ----------------------------------
interface ModalProps {
  heading: string
  heading_badge?: ReactNode
  subheading?: string
  sidebar?: boolean
  ctaButton?: {
    text: string
    onClick: () => void
  }
  secondaryCta?: {
    text: string
    onClick: () => void
  }
  destructiveCta?: {
    text: string
    onClick: () => void
  }
  onClose: () => void
  children: ReactNode
  className?: string
}

// ----------------------------------
// MODAL COMPONENT
// ----------------------------------
export default function Modal({
  heading,
  heading_badge,
  subheading,
  sidebar = false,
  ctaButton,
  secondaryCta,
  onClose,
  children,
  className
}: ModalProps) {
  const [mounted, setMounted] = useState(false)
  // Calculate available width for the modal by accounting for sidebar and padding
  const [maxModalWidth, setMaxModalWidth] = useState("calc(100vw - 8rem)") // Default with some padding

  useEffect(() => {
    // Only set mounted to true on the client-side
    if (typeof window !== 'undefined') {
      setMounted(true)
      
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden'
      
      // Cleanup function to restore scrolling when modal is closed
      return () => {
        document.body.style.overflow = ''
      }
    }
  }, [])

  // The actual modal content
  const modalContent = (
    <>
      {/* Background overlay - covers entire screen including nav */}
      <div className="fixed inset-0 bg-amber-100/10 backdrop-blur-md pointer-events-none z-[9000]" />

      {/* 
        Modal container - positioned fixed to viewport
      */}
      <div
        className="fixed inset-0 flex items-center justify-center p-4 pointer-events-auto z-[9010]"
      >
        {/* Main modal container - positioned relative to create stacking context */}
        <div
          className={cn(
            "relative w-full rounded-3xl bg-zinc-200/40 backdrop-blur-xl border border-zinc-300/40 shadow-deep",
            "max-h-[85vh] flex flex-col modal-form-fix z-[9020]",
            className
          )}
          style={{
            isolation: 'isolate',
            maxWidth: `min(${maxModalWidth}, 80rem)`
          }}
        >
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 transition-[background-color] duration-100 flex items-center justify-center group z-[9030]"
            aria-label="Close modal"
          >
            <span className="text-[10px] font-medium text-red-900/0 group-hover:text-red-900/90">
              ×
            </span>
          </button>

          {/* Main Content Area */}
          <div className="flex flex-1 min-h-0 overflow-y-auto">
            {/* Sidebar (optional) */}
            {sidebar && (
              <div className="hidden sm:flex flex-col w-20 py-6 items-center border-r border-white/40 flex-none">
                <div className="flex flex-col items-center gap-6 mt-4">
                  {/* Sidebar content can be customized */}
                  <div className="w-10 h-10 rounded-full bg-white/30" />
                  <div className="w-10 h-10 rounded-full bg-white/10" />
                  <div className="w-10 h-10 rounded-full bg-white/10" />
                </div>
              </div>
            )}

            {/* Main content with header */}
            <div className="flex-1">
              {/* Header Section */}
              <div className="p-6 pb-2">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <h2 className="text-xl font-semibold text-neutral-800">
                      {heading}
                    </h2>
                    {heading_badge && heading_badge}
                  </div>
                </div>
                {subheading && (
                  <p className="text-sm text-neutral-600">{subheading}</p>
                )}
              </div>

            {/* Content - Added safari-fix class and transform to fix stacking */}
            <div className="p-6 pt-0 safari-modal-fix" style={{transform: 'translateZ(0)'}}>{children}</div>
            </div>
          </div>

          {/* Footer with buttons */}
          {(ctaButton || secondaryCta) && (
            <div className="flex-none h-16 px-6 border-t border-white/40 backdrop-blur-lg bg-stone-500/10 flex items-center justify-end gap-4 rounded-b-3xl -mb-[1px] -mx-[1px] z-[9020] relative">
              {secondaryCta && (
                <Button variant="secondary" onClick={secondaryCta.onClick}>
                  {secondaryCta.text}
                </Button>
              )}
              {ctaButton && (
                <Button variant="primary" onClick={ctaButton.onClick}>
                  {ctaButton.text}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  )

  if (!mounted) return null

  return createPortal(modalContent, document.body)
}