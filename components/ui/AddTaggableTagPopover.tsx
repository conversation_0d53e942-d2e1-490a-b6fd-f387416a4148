import { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Tag, TaggableId, TaggableType } from "@/zod/tags-schema"; // Use the specific TaggableId union type
import { Check, Plus, Search, Tag as TagIcon, Pencil, X } from 'lucide-react'; // Removed Save, Check is already imported

import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';

// Define the props for the generic modal
interface AddTaggableTagPopoverProps {
  taggableId: TaggableId; // Use the specific union type for ID
  taggableType: TaggableType; // Use the specific union type for type
  tagTypeSlug: string; // Slug to fetch the correct tag type
  triggerLabel?: string; // Optional custom label for the trigger button
  allowCreation?: boolean; // Optional prop to allow/disallow tag creation
  allowEditing?: boolean; // Optional prop to allow/disallow tag editing
  onSuccess?: () => void;
}

export function AddTaggableTagPopover({
  taggableId,
  taggableType,
  tagTypeSlug,
  triggerLabel = "Add Tag", // Default trigger label
  allowCreation = true, // Default to true
  allowEditing = true, // Default to true
  onSuccess
}: AddTaggableTagPopoverProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingTagId, setEditingTagId] = useState<Id<'tags'> | null>(null);
  const [editingTagName, setEditingTagName] = useState('');
  const [editingTagType, setEditingTagType] = useState<Id<'tag_types'> | undefined>(undefined); // State to store tag_type during edit
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement>(null);

  // 1. Fetch the ID for the specified tag type slug
  const specificTagType = useQuery(api.tags.getTagTypeBySlug, { slug: tagTypeSlug });
  const specificTagTypeId = specificTagType?._id;

  // 2. Fetch only tags of the specified type (if the type ID was found)
  const allTagsData = useQuery(
    api.tags.fetchTags,
    specificTagTypeId ? { filter: { tag_type: specificTagTypeId } } : "skip"
  );
  const allTags = allTagsData ?? [];

  // 3. Fetch tags already assigned to the specific taggable entity
  const assignedTagsData = useQuery(
    api.tags.getTagsForTaggable,
    {
      taggable_type: taggableType,
      taggable_id: taggableId
    }
  );
  const assignedTagIds = new Set(assignedTagsData?.map((tag) => tag._id));

  // 4. Add mutation hooks for adding, creating, and updating tags
  const addTagMutation = useMutation(api.tags.addTagToTaggable);
  const saveTagMutation = useMutation(api.tags.saveTag); // Renamed for clarity (handles create & update)

  // Filter tags based on search query and exclude already assigned tags
  const availableTags = allTags
    .filter(tag => !assignedTagIds.has(tag._id))
    .filter(tag => {
      if (!searchQuery) return true;
      return tag.name.toLowerCase().includes(searchQuery.toLowerCase());
    });

  const handleAddTag = async (tagId: Id<'tags'>) => {
    try {
      await addTagMutation({
        taggable_type: taggableType,
        taggable_id: taggableId,
        tagId: tagId
      });

      toast({
        title: "Success",
        description: `Tag added to ${taggableType}`
      });

      if (onSuccess) onSuccess();
      setOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add tag",
        variant: "destructive"
      });
    }
  };

  // New handler for creating and adding a tag in one step
  const handleCreateAndAddTag = async (tagName: string) => {
    if (!specificTagTypeId) {
      toast({
        title: "Error",
        description: "Tag type not found. Please try again later.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Step 1: Create the new tag
      const newTag = await saveTagMutation({
        tag: {
          name: tagName,
          tag_type: specificTagTypeId,
          // Optionally inherit color from tag type
          color: specificTagType?.color
        }
      });

      // Step 2: Add the newly created tag to the taggable entity
      await addTagMutation({
        taggable_type: taggableType,
        taggable_id: taggableId,
        tagId: newTag._id
      });

      toast({
        title: "Success",
        description: `Created and added tag "${tagName}"`
      });

      if (onSuccess) onSuccess();
      setOpen(false);
      setSearchQuery(''); // Reset search query
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create tag",
        variant: "destructive"
      });
    }
  };

  // Handler for starting edit
  const handleStartEdit = (tag: Tag) => {
    setEditingTagId(tag._id);
    setEditingTagName(tag.name);
    setEditingTagType(tag.tag_type); // Store the tag_type
  };

  // Handler for cancelling edit
  const handleCancelEdit = () => {
    setEditingTagId(null);
    setEditingTagName('');
    setEditingTagType(undefined); // Clear the stored tag_type
  };

  // Handler for saving edited tag name
  const handleSaveEdit = async () => {
    // Ensure we have the tag_type needed for the schema validation
    if (!editingTagId || !editingTagName.trim() || !editingTagType) {
       toast({ title: "Error", description: "Cannot save tag, missing required information.", variant: "destructive" });
       return;
    }

    try {
      // Save the edited tag
      const updatedTag = await saveTagMutation({
        tag: {
          id: editingTagId,
          name: editingTagName.trim(),
          tag_type: editingTagType, // Provide the required tag_type
          // Include color if it exists in the original tag
          color: allTags.find(t => t._id.toString() === editingTagId.toString())?.color
        }
      });

      toast({
        title: "Success",
        description: `Tag renamed to "${editingTagName.trim()}"`
      });

      // Force a refresh of the tags list
      // This ensures we're working with the latest tag data
      await new Promise(resolve => setTimeout(resolve, 100));

      handleCancelEdit(); // Exit edit mode
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to rename tag",
        variant: "destructive"
      });
    }
  };

  // Focus input when editing starts
  useEffect(() => {
    if (editingTagId && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select(); // Select text for easy replacement
    }
  }, [editingTagId]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="inline-flex items-center justify-center rounded-full px-1.5 text-xs font-medium leading-normal hover:bg-accent hover:text-accent-foreground h-auto py-0 gap-0"
        >
          <Plus className="h-3 w-3 mr-1" />
          {triggerLabel}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search tags..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>
              {searchQuery.trim() && allowCreation ? ( // Check allowCreation
                <div
                  className="flex items-center cursor-pointer text-foreground p-2 hover:bg-accent" // Changed text-primary to text-foreground, added hover
                  onClick={() => handleCreateAndAddTag(searchQuery)}
                >
                  <TagIcon className="h-4 w-4 mr-2" />
                  Create new tag: "{searchQuery}"
                </div>
              ) : (
                <div className="p-2 text-sm text-muted-foreground">
                  {searchQuery.trim() ? 'No matching tags found' : 'No tags available'} {/* Adjusted message */}
                </div>
              )}
            </CommandEmpty>
            <CommandGroup>
              {availableTags.length > 0 ? (
                availableTags.map((tag) => (
                  <CommandItem
                    key={tag._id}
                    value={tag.name}
                    // Prevent selection when clicking edit/save/cancel buttons
                    onSelect={(currentValue) => {
                      if (editingTagId !== tag._id) {
                        handleAddTag(tag._id);
                      }
                    }}
                    className={cn(
                      "flex items-center justify-between cursor-pointer group", // Added group for hover effects
                      editingTagId === tag._id && "bg-accent" // Highlight item being edited
                    )}
                  >
                    {editingTagId === tag._id ? (
                      // Edit Mode
                      <div className="flex items-center gap-1 w-full" onClick={(e) => e.stopPropagation()}>
                        <Input
                          ref={inputRef}
                          value={editingTagName}
                          onChange={(e) => setEditingTagName(e.target.value)}
                          className="h-7 flex-1 px-1 text-sm"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') handleCancelEdit();
                          }}
                        />
                        {/* Use Check icon instead of Save, adjust styling for subtlety */}
                        <Button variant="ghost" size="icon" className="h-5 w-5 text-green-600 hover:bg-green-100/50 rounded" onClick={handleSaveEdit}>
                          <Check className="h-3.5 w-3.5" />
                        </Button>
                        {/* Adjust styling for subtlety */}
                        <Button variant="ghost" size="icon" className="h-5 w-5 text-muted-foreground hover:bg-gray-100/50 rounded" onClick={handleCancelEdit}>
                          <X className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    ) : (
                      // Display Mode
                      <>
                        <div className="flex items-center flex-1 overflow-hidden">
                          <div
                            className={cn(
                              "w-2 h-2 rounded-full mr-2 flex-shrink-0",
                              tag.color || "bg-gray-400"
                            )}
                          />
                          <span className="truncate">{tag.name}</span>
                        </div>
                        {/* Adjust styling for subtlety - Conditionally render Edit button */}
                        {allowEditing && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 text-muted-foreground opacity-0 group-hover:opacity-70 hover:!opacity-100 hover:bg-gray-100/50 rounded transition-opacity"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent item selection
                              handleStartEdit(tag);
                            }}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                        )}
                      </>
                    )}
                  </CommandItem>
                ))
              ) : (
                 !searchQuery.trim() && // Only show this if not searching
                 <div className="py-6 text-center text-sm text-muted-foreground">
                   {allTags.length === assignedTagIds.size ? 'All tags assigned' : 'No tags available for this type.'}
                 </div>
              )}
              {/* Add "Create new tag" option when search query exists but no exact matches - Check allowCreation */}
              {allowCreation && searchQuery.trim() && availableTags.length > 0 &&
               !availableTags.some(tag => tag.name.toLowerCase() === searchQuery.toLowerCase()) && (
                <div
                  className="flex items-center cursor-pointer text-foreground border-t p-2 hover:bg-accent" // Changed text-primary to text-foreground, added hover
                  onClick={() => handleCreateAndAddTag(searchQuery)}
                >
                  <TagIcon className="h-4 w-4 mr-2" />
                  Create new tag: "{searchQuery}"
                </div>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
