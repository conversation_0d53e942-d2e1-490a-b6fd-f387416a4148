'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react'; // For loading indicator

interface InlineEditProps {
  initialValue: string | number | null | undefined;
  onSave: (newValue: string | number | null) => Promise<void>; // Returns promise to handle async save
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  displayClassName?: string;
  inputType?: 'text' | 'number';
  required?: boolean;
  disabled?: boolean;
  customDisplay?: React.ReactNode; // Add prop for custom display element
}

export const InlineEdit: React.FC<InlineEditProps> = ({
  initialValue,
  onSave,
  placeholder = 'Click to edit',
  className,
  inputClassName,
  displayClassName,
  inputType = 'text',
  required = false,
  disabled = false,
  customDisplay, // Destructure the new prop
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState<string | number>(initialValue ?? '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const initialValueRef = useRef(initialValue); // Store initial value to detect changes

  // Update internal state if initialValue prop changes externally
  useEffect(() => {
    if (initialValue !== initialValueRef.current) {
      setValue(initialValue ?? '');
      initialValueRef.current = initialValue;
    }
  }, [initialValue]);

  const handleSave = async () => {
    // Don't save if disabled or loading
    if (disabled || isLoading) return;

    // Trim value if it's a string
    const processedValue = typeof value === 'string' ? value.trim() : value;

    // Validate required field
    if (required && (processedValue === '' || processedValue === null || processedValue === undefined)) {
      setError('This field is required.');
      // Optionally revert to initial value or keep the input open
      // For now, just show error and keep editing
      return;
    }

    // Check if value actually changed
    const initialProcessed = typeof initialValueRef.current === 'string'
      ? initialValueRef.current.trim()
      : initialValueRef.current;

    if (processedValue === initialProcessed) {
      setIsEditing(false); // Exit editing if no change
      setError(null); // Clear any previous error
      return;
    }

    setIsLoading(true);
    setError(null); // Clear previous errors

    try {
      // Convert to number if inputType is number
      const valueToSave = inputType === 'number' && typeof processedValue === 'string'
        ? parseFloat(processedValue)
        : processedValue;

      // Handle potential NaN for number conversion
      if (inputType === 'number' && isNaN(valueToSave as number)) {
         if (required || processedValue !== '') { // Only error if required or non-empty invalid input
            throw new Error('Invalid number format.');
         } else {
            // Allow saving null/undefined if not required and input is empty
            await onSave(null);
         }
      } else {
         await onSave(valueToSave as string | number | null); // Pass potentially null value
      }

      initialValueRef.current = valueToSave as string | number | null | undefined; // Update initial value ref after successful save
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save');
      // Keep editing mode open on error to allow correction
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSave();
    } else if (event.key === 'Escape') {
      setValue(initialValueRef.current ?? ''); // Revert to initial value on Escape
      setIsEditing(false);
      setError(null); // Clear error on cancel
    }
  };

  const handleBlur = () => {
    // Delay blur handling slightly to allow potential click on save/cancel buttons if added later
    setTimeout(() => {
      // Check if the input element still has focus (e.g., if an error occurred)
      if (document.activeElement !== inputRef.current) {
        handleSave();
      }
    }, 100);
  };

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select(); // Select text for easy replacement
    }
  }, [isEditing]);

  if (disabled) {
     return (
       <span className={cn("text-muted-foreground italic", displayClassName)}>
         {initialValue ?? placeholder}
       </span>
     );
  }

  return (
    <div className={cn("relative w-full", className)}>
      {isEditing ? (
        <div className="relative">
          <Input
            ref={inputRef}
            type={inputType} // Use inputType prop
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={cn("h-8 text-sm", error ? "border-red-500" : "", inputClassName)}
            disabled={isLoading}
          />
          {isLoading && (
            <Loader2 className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground" />
          )}
          {error && (
             <p className="absolute top-full left-0 mt-1 text-xs text-red-600">{error}</p>
          )}
        </div>
      ) : (
        <div
          onClick={() => setIsEditing(true)}
          className={cn(
            "cursor-pointer rounded px-1 py-1 min-h-[32px] flex items-center text-sm hover:bg-muted/50", // Ensure minimum height and padding
            !initialValue && "text-muted-foreground italic",
            displayClassName
          )}
          role="button"
          tabIndex={0} // Make it focusable
          onKeyDown={(e) => { if (e.key === 'Enter' && !disabled) setIsEditing(true); }} // Allow editing on Enter key only if not disabled
        >
          {/* Render custom display if provided, otherwise default */}
          {customDisplay ? customDisplay : (
             initialValue !== null && initialValue !== undefined && initialValue !== ''
               ? String(initialValue)
               : <span className="italic text-muted-foreground">{placeholder}</span>
          )}
        </div>
      )}
    </div>
  );
};
