'use client';

import {
  useState,
  useId,
  useRef,
  useEffect,
  useLayoutEffect,
  createContext,
  useContext,
  isValidElement,
} from 'react';
import {
  AnimatePresence,
  MotionConfig,
  motion,
  Transition,
  Variants,
} from 'motion/react';
import { useClickOutside } from '@/components/hooks/use-click-outside';
import { cn } from '@/components/lib/utils';
import { createPortal } from 'react-dom';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useToast } from '@/components/hooks/use-toast';

const TRANSITION: Transition = {
  type: 'spring',
  bounce: 0.1,
  duration: 0.4,
};

type PopoverWithSearchContextValue = {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  uniqueId: string;
  variants?: Variants;
};

const PopoverWithSearchContext =
  createContext<PopoverWithSearchContextValue | null>(null);

function usePopoverLogic({
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
}: {
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
} = {}) {
  const uniqueId = useId();
  const [uncontrolledOpen, setUncontrolledOpen] = useState(defaultOpen);

  const isOpen = controlledOpen ?? uncontrolledOpen;

  const open = () => {
    if (controlledOpen === undefined) {
      setUncontrolledOpen(true);
    }
    onOpenChange?.(true);
  };

  const close = () => {
    if (controlledOpen === undefined) {
      setUncontrolledOpen(false);
    }
    onOpenChange?.(false);
  };

  return { isOpen, open, close, uniqueId };
}

export type PopoverWithSearchProps = {
  children: React.ReactNode;
  transition?: Transition;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  variants?: Variants;
  className?: string;
  teamId: Id<'teams'>;
} & React.ComponentProps<'div'>;

function PopoverWithSearch({
  children,
  transition = TRANSITION,
  defaultOpen,
  open,
  onOpenChange,
  variants,
  className,
  teamId,
  ...props
}: PopoverWithSearchProps) {
  const popoverLogic = usePopoverLogic({ defaultOpen, open, onOpenChange });

  return (
    <PopoverWithSearchContext.Provider value={{ ...popoverLogic, variants }}>
      <MotionConfig transition={transition}>
        <div
          className={cn('relative flex items-center justify-center', className)}
          key={popoverLogic.uniqueId}
          {...props}
        >
          {children}
        </div>
      </MotionConfig>
    </PopoverWithSearchContext.Provider>
  );
}

export type PopoverWithSearchTriggerProps = {
  asChild?: boolean;
  children: React.ReactNode;
  className?: string;
} & React.ComponentProps<typeof motion.button>;

function PopoverWithSearchTrigger({
  children,
  className,
  asChild = false,
  ...props
}: PopoverWithSearchTriggerProps) {
  const context = useContext(PopoverWithSearchContext);
  if (!context) {
    throw new Error(
      'PopoverWithSearchTrigger must be used within PopoverWithSearch'
    );
  }

  if (asChild && isValidElement(children)) {
    const MotionComponent = motion.create(
      children.type as React.ForwardRefExoticComponent<any>
    );
    const childProps = children.props as Record<string, unknown>;

    // Merge the className props
    const mergedClassName = cn(childProps.className as string | undefined, className);

    // Create merged props
    const mergedProps = {
      ...childProps,
      ...props,
      onClick: context.open,
      layoutId: `popover-trigger-${context.uniqueId}`,
      id: `popover-trigger-${context.uniqueId}`,
      className: mergedClassName,
      'aria-expanded': context.isOpen,
      'aria-controls': `popover-content-${context.uniqueId}`,
    };

    return <MotionComponent {...mergedProps} />;
  }

  return (
    <motion.div
      key={context.uniqueId}
      layoutId={`popover-trigger-${context.uniqueId}`}
      id={`popover-trigger-${context.uniqueId}`}
      onClick={context.open}
    >
      <motion.button
        {...props}
        layoutId={`popover-label-${context.uniqueId}`}
        className={className}
        aria-expanded={context.isOpen}
        aria-controls={`popover-content-${context.uniqueId}`}
      >
        {children}
      </motion.button>
    </motion.div>
  );
}

export type PopoverWithSearchContentProps = {
  children: React.ReactNode;
  className?: string;
  teamId: Id<'teams'>;
} & React.ComponentProps<typeof motion.div>;

function PopoverWithSearchContent({
  children,
  className,
  style,
  teamId,
  ...props
}: PopoverWithSearchContentProps) {
  const context = useContext(PopoverWithSearchContext);
  if (!context) {
    throw new Error(
      'PopoverWithSearchContent must be used within PopoverWithSearch'
    );
  }

  const ref = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);
  const [position, setPosition] = useState<{
    top: number;
    left: number;
    placement: 'top' | 'bottom';
  }>({
    top: 0,
    left: 0,
    placement: 'bottom'
  });

  // Handle mounting for SSR compatibility
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Calculate position when popover opens and ensure it stays within the viewport
  useLayoutEffect(() => {
    if (context.isOpen) {
      const triggerElement = document.getElementById(`popover-trigger-${context.uniqueId}`);
      if (triggerElement && ref.current) {
        const rect = triggerElement.getBoundingClientRect();
        const popoverWidth = ref.current.offsetWidth;
        const popoverHeight = ref.current.offsetHeight;
        let top = rect.bottom;
        let left = rect.left;
        let placement: 'top' | 'bottom' = 'bottom';

        // If the popover would overflow below, position it above the trigger
        if (top + popoverHeight > window.innerHeight) {
          top = rect.top - popoverHeight;
          placement = 'top';
        }
        // Ensure top is not negative
        if (top < 0) {
          top = 0;
          placement = 'bottom';
        }
        // Adjust left if the popover overflows to the right
        if (left + popoverWidth > window.innerWidth) {
          left = window.innerWidth - popoverWidth;
        }
        // Ensure left is not negative
        if (left < 0) {
          left = 0;
        }
        setPosition({
          top,
          left,
          placement
        });
      }
    }
  }, [context.isOpen, context.uniqueId]);


  useClickOutside(ref as React.RefObject<HTMLElement>, context.close);

  useEffect(() => {
    if (!context.isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') context.close();
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [context.isOpen, context.close]);

  useEffect(() => {
    if (context.isOpen) {
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [context.isOpen]);
  // Define LocalUser type
  type LocalUser = {
    id: Id<"users">;
    name: string;
    email: string;
    role: string;
    teams: Id<"teams">[];
  };

  // Fetch users from Convex
  const usersData = useQuery(api.users.listUsers, {});
  const users = usersData?.users ?? [];

  // Map Convex user data to LocalUser type.
  const mappedUsers: LocalUser[] = users.map((user: any): LocalUser => ({
    id: user._id,
    name: user.name ?? '',
    email: user.email,
    role: (Array.isArray(user.roles) && user.roles.length > 0) ? user.roles[0] : '',
    teams: user.teams ? user.teams : []
  }));

  // State for search query
  const [searchQuery, setSearchQuery] = useState<string>('');

  // State for selected users
  const [selectedUsers, setSelectedUsers] = useState<Id<'users'>[]>([]);
  
  // Setup mutation
  const updateUserMutation = useMutation(api.users.updateUsers);

  // Get toast function
  const { toast } = useToast();

  // Filtered users
  const filteredUsers = mappedUsers.filter((user) =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle selecting/deselecting users
  const handleSelectUser = (userId: Id<'users'>) => {
    setSelectedUsers((prevSelectedUsers) => {
      if (prevSelectedUsers.includes(userId)) {
        return prevSelectedUsers.filter((id) => id !== userId);
      } else {
        return [...prevSelectedUsers, userId];
      }
    });
  };

  // Handle adding users to the team
  const handleAddUsersToTeam = async () => {
    if (selectedUsers.length === 0) {
      return; // Nothing to do
    }

    // For each selected user, update their teams array
    const updatePromises = selectedUsers.map(async (userId) => {
      const user = mappedUsers.find((u) => u.id === userId);
      if (!user) return;
      const updatedTeams = user.teams.includes(teamId) ? user.teams : [...user.teams, teamId];
      return updateUserMutation({ id: userId, updates: { teams: updatedTeams } });
    });

    const results = await Promise.allSettled(updatePromises);
    results.forEach((result) => {
      if (
          result.status === "rejected" ||
          (result.status === "fulfilled" && result.value?.failed && result.value.failed.length > 0)
        ) {
          // handle error per individual update
        }
      });
    
    // Display success message
    toast({
      title: 'Success',
      description: 'Users added to team successfully.',
    });

    // Clear selection and close popover
    setSelectedUsers([]);
    context.close();
  };

  // Create portal content
  const content = (
    <AnimatePresence>
      {context.isOpen && (
        <div className="fixed inset-0 z-[9999]">
          {/* Semi-transparent overlay */}
          <div className="fixed inset-0 bg-indigo-200/10" onClick={context.close} />

          {/* The actual popover content */}
          <motion.div
            {...props}
            ref={ref}
            layoutId={`popover-trigger-${context.uniqueId}`}
            key={context.uniqueId}
            id={`popover-content-${context.uniqueId}`}
            role='dialog'
            aria-modal='true'
            style={{
              position: 'absolute',
              zIndex: 9999,
              top: position.top,
              left: position.left,
              ...style,
            }}
            className={cn(
              'pointer-events-auto overflow-hidden rounded-lg border bg-card/10 backdrop-blur-lg border-zinc-950/10 p-2 shadow-deep',
              className
            )}
            initial='initial'
            animate='animate'
            exit='exit'
            variants={context.variants ?? {
              initial: { opacity: 0, y: position.placement === 'top' ? 10 : -10 },
              animate: { opacity: 1, y: 0 },
              exit: { opacity: 0, y: position.placement === 'top' ? 10 : -10 },
            }}
          >
            <div key={`content-${context.uniqueId}`} className="p-2">
              <Input
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="mb-2"
              />
              <ScrollArea className="h-60">
                <div className="space-y-1">
                  {filteredUsers.map((user) => (
                    <div key={user.id} className="flex items-center space-x-2 p-1 hover:bg-gray-100 rounded-lg">
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={() => handleSelectUser(user.id)}
                        id={`user-${user.id}`}
                      />
                      <label htmlFor={`user-${user.id}`} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {user.name} ({user.email})  
                      </label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              <Button className="mt-2 w-full" onClick={handleAddUsersToTeam}>
                Add Members
              </Button>
            </div>
            {children}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );

  // Return null during SSR, or portal when mounted
  return mounted ? createPortal(content, document.getElementById('__next') || document.body) : null;
}

export { PopoverWithSearch, PopoverWithSearchTrigger, PopoverWithSearchContent };