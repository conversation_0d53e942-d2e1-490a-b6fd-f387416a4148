import { useDropzone } from 'react-dropzone';
import { Upload } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DropzoneProps {
  onDrop: (acceptedFiles: File[]) => void;
  accept?: Record<string, string[]>;
  className?: string;
  children?: React.ReactNode;
}

export function Dropzone({
  onDrop,
  accept,
  className,
  children
}: DropzoneProps) {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxFiles: 1
  });

  return (
    <div
      {...getRootProps()}
      className={cn(
        'h-full w-full border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-6 cursor-pointer',
        isDragActive ? 'border-primary bg-primary/5' : 'border-gray-200',
        className
      )}
    >
      <input {...getInputProps()} />
      {children}
    </div>
  );
}

export function DropzoneEmptyState() {
  return (
    <>
      <Upload className="h-10 w-10 text-gray-400 mb-4" />
      <p className="text-sm text-gray-600 text-center">
        Drag and drop your file here, or click to select
      </p>
      <p className="text-xs text-gray-400 mt-2">Supports PDF, PNG, JPG</p>
    </>
  );
}
