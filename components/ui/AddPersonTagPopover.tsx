'use client';

import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/hooks/use-toast';
import { Plus } from 'lucide-react';
import { Tag } from '@/zod/tags-schema';

interface AddPersonTagPopoverProps {
  personId: Id<'people'>;
  trigger?: React.ReactNode; // Optional custom trigger
}

export function AddPersonTagPopover({ personId, trigger }: AddPersonTagPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  // 1. Fetch the ID for the "general-tags" type
  const generalTagsType = useQuery(api.tags.getTagTypeBySlug, { slug: "general-tags" });
  const generalTagsTypeId = generalTagsType?._id;

  // 2. Fetch only tags of the "general-tags" type (if the type ID was found)
  const allTagsData = useQuery(
    api.tags.fetchTags, 
    generalTagsTypeId ? { filter: { tag_type: generalTagsTypeId } } : "skip" // Use "skip" instead of undefined
  );
  const allTags = allTagsData ?? []; // Default to empty array if undefined or query skipped

  // 3. Fetch tags already assigned to the person to filter them out
  const personTagsData = useQuery(api.directory.personTags.listTagsByPerson, { personId });
  const personTagIds = new Set(personTagsData?.map((tag: any) => tag?._id));

  // Mutation to add tag
  const addTagMutation = useMutation(api.directory.personTags.addTagToPerson);

  // Filter tags based on search query and exclude already assigned tags
  const availableTags = allTags
    .filter(tag => tag && !personTagIds.has(tag._id)) // Ensure tag exists and is not already assigned
    .filter(tag => 
      tag.name?.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const handleAddTag = async (tagId: Id<'tags'>) => {
    try {
      await addTagMutation({ personId, tagId });
      toast({
        title: 'Success',
        description: 'Tag added successfully.',
      });
      setSearchQuery(''); // Clear search
      setIsOpen(false); // Close popover
    } catch (error) {
      toast({
        title: 'Error adding tag',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger ? (
          trigger
        ) : (
          <Button variant="outline" size="sm" className="h-6 px-2 text-xs">
            <Plus className="h-3 w-3 mr-1" /> Add Tag
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2">
        <div className="space-y-2">
          <h4 className="font-medium leading-none text-sm px-1">Add Tag</h4>
          <Input
            placeholder="Search tags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8"
          />
          <ScrollArea className="h-40">
            <div className="space-y-1 p-1">
              {availableTags.length > 0 ? (
                availableTags.map((tag) => (
                  <Button
                    key={tag._id}
                    variant="ghost"
                    className="w-full justify-start h-8 px-2 text-sm"
                    onClick={() => handleAddTag(tag._id)}
                  >
                    {tag.name}
                  </Button>
                ))
              ) : (
                <div className="text-center text-xs text-muted-foreground py-2">
                  {allTags.length === personTagIds.size ? 'All tags assigned' : 'No matching tags found.'}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
}
