import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { TagWithChildren, TaggableId, TaggableType } from "@/zod/tags-schema";
import { Plus, ChevronLeft, ChevronRight } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/hooks/use-toast";

interface AddInvestmentThemeTagPopoverProps {
  taggableId: TaggableId;
  taggableType: TaggableType;
  tagTypeSlug: string;
  triggerLabel?: string;
  onSuccess?: () => void;
}

export function AddInvestmentThemeTagPopover({
  taggableId,
  taggableType,
  tagTypeSlug,
  triggerLabel = "Add Investment Theme",
  onSuccess,
}: AddInvestmentThemeTagPopoverProps) {
  const [open, setOpen] = useState(false);
  const [selectedParent, setSelectedParent] = useState<TagWithChildren | null>(null);
  const { toast } = useToast();

  // 1. Get the tag type doc for the slug
  const tagType = useQuery(api.tags.getTagTypeBySlug, { slug: tagTypeSlug });
  const tagTypeId = tagType?._id;

  // 2. Get the tag hierarchy for this tag type
  const hierarchy = useQuery(
    api.tags.getHierarchy,
    tagTypeId ? { params: { tag_type: tagTypeId } } : "skip"
  ) as TagWithChildren[] | undefined;

  // 3. Get assigned tags for this taggable
  const assignedTags = useQuery(
    api.tags.getTagsForTaggable,
    {
      taggable_type: taggableType,
      taggable_id: taggableId,
    }
  ) ?? [];
  const assignedTagIds = new Set(assignedTags.map((t) => t._id));

  // 4. Mutation to add tag
  const addTagMutation = useMutation(api.tags.addTagToTaggable);

  // 5. Stage logic
  const parentTags: TagWithChildren[] = Array.isArray(hierarchy)
    ? hierarchy
    : hierarchy
    ? [hierarchy]
    : [];

  // Only show parents with parent_id undefined (top-level)
  const rootParents = parentTags.filter((t) => !t.parent_id);

  // Handler to add a child tag
  const handleAddTag = async (tagId: Id<"tags">) => {
    try {
      await addTagMutation({
        taggable_type: taggableType,
        taggable_id: taggableId,
        tagId,
      });
      toast({
        title: "Success",
        description: "Theme tag added",
      });
      setOpen(false);
      setSelectedParent(null);
      if (onSuccess) onSuccess();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add tag",
        variant: "destructive",
      });
    }
  };

  // UI rendering
  return (
    <Popover open={open} onOpenChange={(v) => { setOpen(v); if (!v) setSelectedParent(null); }}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="inline-flex items-center justify-center rounded-full px-1.5 text-xs font-medium leading-normal hover:bg-accent hover:text-accent-foreground h-auto py-0 gap-0"
        >
          <Plus className="h-3 w-3 mr-1" />
          {triggerLabel}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        {!selectedParent ? (
          <div>
            <div className="p-2 font-semibold text-sm">Select Theme Category</div>
            <div className="max-h-60 overflow-y-auto">
              {rootParents.length === 0 ? (
                <div className="p-4 text-muted-foreground text-sm">
                  No investment theme categories found
                </div>
              ) : (
                rootParents.map((parent) => (
                  <button
                    key={parent._id}
                    className={cn(
                      "w-full text-left px-4 py-2 hover:bg-accent focus:bg-accent rounded transition-colors",
                      "flex items-center gap-2"
                    )}
                    onClick={() => setSelectedParent(parent)}
                  >
                    <span
                      className={cn(
                        "w-2 h-2 rounded-full",
                        parent.color || "bg-gray-400"
                      )}
                      style={parent.color ? { backgroundColor: parent.color } : undefined}
                    />
                    <span className="truncate">{parent.name}</span>
                    <ChevronRight className="ml-auto h-4 w-4 text-muted-foreground" />
                  </button>
                ))
              )}
            </div>
          </div>
        ) : (
          <div>
            <div className="flex items-center p-2 border-b">
              <button
                className="mr-2 text-muted-foreground hover:text-foreground"
                onClick={() => setSelectedParent(null)}
                aria-label="Back"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <span className="font-semibold text-sm truncate">
                {selectedParent.name}
              </span>
            </div>
            <div className="max-h-60 overflow-y-auto">
              {selectedParent.children.length === 0 ? (
                <div className="p-4 text-muted-foreground text-sm">
                  No themes available under this category
                </div>
              ) : (
                selectedParent.children.map((child) => {
                  const assigned = assignedTagIds.has(child._id);
                  return (
                    <button
                      key={child._id}
                      className={cn(
                        "w-full text-left px-4 py-2 rounded transition-colors flex items-center gap-2",
                        assigned
                          ? "bg-muted text-muted-foreground cursor-not-allowed opacity-60"
                          : "hover:bg-accent focus:bg-accent"
                      )}
                      onClick={() => !assigned && handleAddTag(child._id)}
                      disabled={assigned}
                    >
                      <span
                        className={cn(
                          "w-2 h-2 rounded-full",
                          child.color || "bg-gray-400"
                        )}
                        style={child.color ? { backgroundColor: child.color } : undefined}
                      />
                      <span className="truncate">{child.name}</span>
                      {assigned && (
                        <span className="ml-auto text-xs">Assigned</span>
                      )}
                    </button>
                  );
                })
              )}
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}