"use client"

"use client";

import type { LucideIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight, Plus, FileUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface ItemCardProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  onClick?: () => void;
  onCreateClick?: () => void;
  onUploadClick?: () => void;
  className?: string;
  canCreateFromScratch?: boolean;
}

export function ItemCard({
  icon: Icon,
  title,
  onClick,
  className,
  onCreateClick,
  onUploadClick,
  description,
  canCreateFromScratch
}: ItemCardProps) {
  return (
    <div
      className={cn(
        "group p-3 rounded-lg hover:bg-foreground/5 transition-colors cursor-pointer border border-transparent hover:border-foreground/10",
        className,
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {Icon && (
            <div className="p-2 rounded-md bg-muted-foreground/5 text-muted-foreground group-hover:bg-muted-foreground/10 transition-colors">
              <Icon className="w-5 h-5" />
            </div>
          )}
          <div>
            <span className="text-foreground group-hover:text-foreground/90">{title}</span>
            {description && (
              <p className="text-muted-foreground text-sm">{description}</p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-1">
          {canCreateFromScratch && (
            <Button variant="ghost" size="sm" onClick={onCreateClick}>
              <Plus className="h-4 w-4 mr-1" />
              Create Manually
            </Button>
          )}
          { (
            <Button variant="ghost" size="sm" onClick={onUploadClick}>
              <FileUp className="h-4 w-4 mr-1" />
              Create from Upload
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

export default ItemCard;
