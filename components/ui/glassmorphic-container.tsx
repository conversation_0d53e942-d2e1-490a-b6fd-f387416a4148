"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface GlassmorphicContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  blur?: string;
  opacity?: string;
  border?: boolean;
  rounded?: string;
  shadow?: string;
  className?: string;
  intensity?: "light" | "medium" | "heavy";
}

const GlassmorphicContainer = React.forwardRef<HTMLDivElement, GlassmorphicContainerProps>(
  (
    {
      children,
      blur = "backdrop-blur-md",
      opacity = "bg-background/70",
      border = true,
      rounded = "rounded-lg",
      shadow = "shadow-lg",
      className,
      intensity = "medium",
      ...props
    },
    ref
  ) => {
    // Intensity presets
    const intensityMap = {
      light: {
        bg: "bg-background/30",
        blur: "backdrop-blur-sm",
        border: "border-white/10",
      },
      medium: {
        bg: "bg-background/40",
        blur: "backdrop-blur-md",
        border: "border-white/20",
      },
      heavy: {
        bg: "bg-background/50",
        blur: "backdrop-blur-lg",
        border: "border-white/30",
      },
    };

    // Use intensity preset if provided
    const intensitySettings = intensityMap[intensity];
    const bgOpacity = intensitySettings ? intensitySettings.bg : opacity;
    const blurAmount = intensitySettings ? intensitySettings.blur : blur;
    const borderStyle = intensitySettings && border ? intensitySettings.border : "border-border/20";

    return (
      <div
        ref={ref}
        className={cn(
          "relative",
          bgOpacity,
          blurAmount,
          rounded,
          border && "border",
          border && borderStyle,
          shadow,
          className
        )}
        {...props}
      >
        <div className="relative z-10">{children}</div>
        <div className="absolute inset-0 -z-10">
          <svg className="hidden">
            <defs>
              <filter id="fractal-noise">
                <feTurbulence
                  type="fractalNoise"
                  baseFrequency="0.12 0.12"
                  numOctaves="1"
                  result="noise"
                />
                <feDisplacementMap
                  xChannelSelector="R"
                  yChannelSelector="G"
                  scale="15"
                  in="SourceGraphic"
                  in2="noise"
                />
              </filter>
            </defs>
          </svg>
        </div>
      </div>
    );
  }
);

GlassmorphicContainer.displayName = "GlassmorphicContainer";

export { GlassmorphicContainer };
