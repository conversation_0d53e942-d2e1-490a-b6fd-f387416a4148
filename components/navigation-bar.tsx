'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation'; // Import useRouter
import Image from 'next/image';
import {
  Home,
  Briefcase,
  LineChart,
  FileText,
  FolderTree,
  Settings,
  Search,
  User,
  PanelLeftClose,
  Plus,
  LightbulbIcon,
  CheckSquare,
  History,
  Users
} from 'lucide-react';
import confetti from 'canvas-confetti';
import { isChangelogUpdatedToday } from '@/lib/changelogData';
import { UserInfo } from './user-info';
import { GlobalSearchModal } from './global-search-modal';
import { QuickCreate } from './quickCreateButton';
import { Separator } from './ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";

//the expanded width must match the globals.css
const SIDEBAR_WIDTH_EXPANDED = '210px';
const SIDEBAR_WIDTH_COLLAPSED = '74px';

export function NavigationBar() {
  const pathname = usePathname();
  const router = useRouter(); // Get router instance
  const [isExpanded, setIsExpanded] = useState(false); // Default to collapsed
  const [keepExpanded, setKeepExpanded] = useState(false); // Add state to keep sidebar expanded
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Add keyboard shortcut listener for search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Add click handler to detect clicks outside the sidebar
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // Only reset keepExpanded if clicking outside the sidebar and any open dropdown
      const target = e.target as HTMLElement;
      
      // Check if the click is inside a dropdown menu, popover, or other UI element 
      // that should not cause the sidebar to collapse
      const isInsideDropdownOrPopover = 
        !!target.closest('[data-state="open"]') || // Target inside an open dropdown/popover
        !!target.closest('[role="dialog"]') ||     // Target inside a dialog
        !!target.closest('.quick-create-container') || // Target inside QuickCreate container
        !!target.closest('.dropdown-menu-item') || // Target is a dropdown menu item
        target.classList.contains('quick-create-trigger') || // Target is a QuickCreate button
        target.tagName.toLowerCase() === 'button'; // Target is a button element
        
      // Prevent collapsing when clicking on interactive elements
      if (!isInsideDropdownOrPopover) {
        setKeepExpanded(false);
      }
    };

    // Add global click handler to reset keepExpanded
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Custom handlers for sidebar expand/collapse
  const handleSidebarMouseEnter = () => {
    // Only expand if it's not already being kept expanded
    if (!keepExpanded) {
      setIsExpanded(true);
    }
  };

  const handleSidebarMouseLeave = () => {
    // Only collapse if we're not keeping it expanded
    if (!keepExpanded) {
      setIsExpanded(false);
    }
  };

  // Navigation items
  const navItems = [
    { href: '/home', label: 'Homepage', icon: Home },
    { href: '/projects', label: 'Projects', icon: Briefcase },
    { href: '/tasks', label: 'Tasks', icon: CheckSquare },
    { href: '/decisions', label: 'Decisions', icon: LightbulbIcon },
    { href: '/documents', label: 'Documents', icon: FileText },
    { href: '/bills', label: 'Bills', icon: Briefcase },
    { href: '/clients', label: 'Clients', icon: Users },
    { href: '/directory', label: 'Directory', icon: FolderTree },
    { href: '/reports', label: 'Reports', icon: LineChart },
  ];

  return (
    <>
      {/* Side Navigation Panel */}
      <div
        onMouseEnter={handleSidebarMouseEnter}
        onMouseLeave={handleSidebarMouseLeave}
        className={`${isExpanded ? 'w-[170px]' : 'w-[74px]'} fixed left-0 top-0 p-3 h-full flex flex-col transition-all duration-100 ease-in-out rounded-r-3xl overflow-hidden bg-zinc-100/10 backdrop-blur-xl border-r border-border/40 shadow-medium z-50`}
      >
        {/* Logo */}
        <Link
          href="/home"
          className={`flex items-center mb-4 ${!isExpanded && 'justify-center'} relative`}
        >
          <div className="relative w-[50px] h-[27px]">
            <Image
              src="/logo.svg"
              alt="FOJO Logo"
              fill
              style={{
                objectFit: 'contain',
                padding: '.1px'
              }}
              className="text-primary"
            />
          </div>
          {isExpanded && (
            <span
              className="ml-3 text-[19px] font-bold text-foreground"
              style={{ height: '27px' }}
            >
              FOJO
            </span>
          )}
        </Link>

        {/* Search Button */}
        <button
          onClick={() => setIsSearchOpen(true)}
          className={`flex items-center h-[30px] mt-3 mb-5 ${isExpanded ? 'px-2 w-full justify-between' : 'justify-center px-1'} text-sm rounded-lg bg-muted/50 hover:bg-foreground/5 text-muted-foreground border border-border transition-colors`}
        >
          <div className="flex items-center">
            <Search className="h-5 w-5" />
            {isExpanded && <span className="ml-3">Search...</span>}
          </div>
          {isExpanded && <span className="text-xs">⌘K</span>}
        </button>

        {/* Navigation Links */}
        <nav className="flex-1 flex flex-col">
          <div className="space-y-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center ${isExpanded ? 'px-3' : 'justify-center px-1'} h-10 text-sm rounded-lg hover:bg-foreground/5 text-muted-foreground transition-colors ${pathname === item.href ? 'bg-muted' : ''}`}
                >
                  <Icon className="h-5 w-5" strokeWidth={1.5} />
                  {isExpanded && <span className="ml-3">{item.label}</span>}
                </Link>
              );
            })}
          </div>
        </nav>

        {/* Bottom Section */}
        <div className="border-t pt-3 space-y-1 -mx-3 px-3">
          {/* Changelog Link */}
          <Link
            href="/changelog"
            className={`w-full flex items-center ${isExpanded ? 'px-4' : 'justify-center px-1'} h-10 text-sm rounded-lg hover:bg-foreground/5 text-muted-foreground ${pathname === '/changelog' ? 'bg-muted' : ''}`}
            onClick={(e) => {
              // Get the position of the clicked element for the confetti origin
              const rect = e.currentTarget.getBoundingClientRect();

              // Create a fireworks effect
              confetti({
                particleCount: 100,
                spread: 70,
                origin: {
                  x: (rect.left + rect.width / 2) / window.innerWidth,
                  y: (rect.top + rect.height / 2) / window.innerHeight
                },
                colors: ['#FF0000', '#FFA500', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3'],
                startVelocity: 30,
                gravity: 0.5,
                scalar: 0.7,
                ticks: 60
              });
            }}
          >
            {isExpanded ? (
              <>
                <History className="h-5 w-5" strokeWidth={1.75} />
                <div className="ml-3 flex items-center">
                  <span>Changelog</span>
                  {isChangelogUpdatedToday() && (
                    <div className="ml-2 text-[9px] bg-muted text-muted-foreground px-1 py-0 rounded-sm">
                      NEW
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="relative">
                <History className="h-5 w-5" strokeWidth={1.75} />
                {isChangelogUpdatedToday() && (
                  <div className="absolute -top-1 -right-1 text-[8px] bg-muted text-muted-foreground px-1 py-0 rounded-sm">
                    NEW
                  </div>
                )}
              </div>
            )}
          </Link>

          {/* Admin Link */}
          <Link
            href="/admin"
            className={`w-full flex items-center ${isExpanded ? 'px-4' : 'justify-center px-1'} h-10 text-sm rounded-lg hover:bg-foreground/5 text-muted-foreground ${pathname === '/admin' ? 'bg-muted' : ''}`}
          >
            <Settings className="h-5 w-5" strokeWidth={1.75} />
            {isExpanded && <span className="ml-3">Admin</span>}
          </Link>

          {/* Quick Create Button */}
          {isExpanded ? (
            <div className="mt-4 mb-2">
              <div className="space-y-1 px-1">
                <QuickCreate
                  domain="task"
                  className="w-full"
                  isCollapsed={false}
                  onCreated={(domain, id) => {
                    console.log(`${domain} created:`, id);
                    // Navigate to the new task page - DISABLED
                    // if (domain === 'task') {
                    //   router.push(`/tasks/${id}`);
                    // }
                  }}
                  onDropdownStateChange={(isOpen) => {
                    // Keep sidebar expanded when dropdown is open
                    setKeepExpanded(isOpen);
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="mt-4 mb-2 flex flex-col items-center">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <QuickCreate
                        domain="task"
                        isCollapsed={true}
                        onCreated={(domain, id) => {
                          console.log(`${domain} created:`, id);
                          // Navigate to the new task page - DISABLED
                          // if (domain === 'task') {
                          //   router.push(`/tasks/${id}`);
                          // }
                        }}
                        onDropdownStateChange={(isOpen) => {
                          // Keep sidebar expanded when dropdown is open
                          setKeepExpanded(isOpen);
                        }}
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Quick Create</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}

          <Separator className="my-2" />

          {/* User Info */}
          {isExpanded ? (
            <div 
              className="w-full flex items-center px-4 h-10" 
              onClick={(e) => {
                // Prevent sidebar from collapsing when clicking on user info
                e.stopPropagation();
              }}
              onMouseEnter={(e) => keepExpanded && e.stopPropagation()}
              onMouseLeave={(e) => keepExpanded && e.stopPropagation()}
            >
              <UserInfo 
                isCollapsed={false} 
                onDropdownStateChange={(isOpen) => {
                  // Keep sidebar expanded when user dropdown is open
                  setKeepExpanded(isOpen);
                }}
              />
            </div>
          ) : (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className="w-full flex items-center justify-center px-1 h-10 text-sm rounded-lg hover:bg-foreground/5 text-muted-foreground"
                    onClick={(e) => {
                      // Prevent sidebar expansion/collapse when clicking on user info
                      e.stopPropagation();
                    }}
                    onMouseEnter={(e) => keepExpanded && e.stopPropagation()}
                    onMouseLeave={(e) => keepExpanded && e.stopPropagation()}
                  >
                    <UserInfo 
                      isCollapsed={true} 
                      onDropdownStateChange={(isOpen) => {
                        // Keep sidebar expanded when user dropdown is open
                        setKeepExpanded(isOpen);
                      }}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>User Profile</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* Search Modal */}
      <GlobalSearchModal open={isSearchOpen} onOpenChange={setIsSearchOpen} />
    </>
  );
}
