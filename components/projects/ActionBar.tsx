import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, CheckCircle, XCircle, AlertCircle, PauseCircle, CircleIcon, ArrowUp, Minus, ArrowDown, AlertOctagon, Slash, Clock, Play, Check, Ban, Repeat, Tag as TagIcon, X as XIcon, Plus } from "lucide-react"; // Added TagIcon, XIcon, Plus
import { useToast } from "@/components/hooks/use-toast";
import { Id } from '@/convex/_generated/dataModel';
import { ProjectStatusEnum, ProjectPriorityEnum, ProjectStatus, ProjectPriority } from '@/zod/projects-schema';
import { Tag } from '@/zod/tags-schema'; // Import Tag type
import { AddTaggableTagPopover } from '@/components/ui/AddTaggableTagPopover'; // Import popover
import { cn } from '@/lib/utils'; // Import cn utility

// Define color mappings from the project detail page
const PROJECT_STATUS_COLORS: Record<ProjectStatus, string> = {
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
  perpetual: 'bg-purple-100 text-purple-800 border-purple-200',
  not_started: 'bg-gray-200 text-gray-700 border-gray-300',
  completed: 'bg-green-100 text-green-800 border-green-200',
  paused: 'bg-yellow-200 text-yellow-800 border-yellow-300',
  cancelled: 'bg-red-100 text-red-800 border-red-200'
};

const PROJECT_PRIORITY_COLORS: Record<ProjectPriority, string> = {
  low: 'bg-gray-100 text-gray-800 border-gray-200',
  medium: 'bg-blue-100 text-blue-800 border-blue-200',
  high: 'bg-orange-100 text-orange-800 border-orange-200',
  urgent: 'bg-red-100 text-red-800 border-red-200'
};

// Helper function to get status icon (adapted for project statuses)
const getStatusIcon = (status: ProjectStatus | undefined) => {
  switch (status) {
    case 'in_progress':
      return <Clock className="h-3.5 w-3.5" />;
    case 'perpetual':
      return <Repeat className="h-3.5 w-3.5" />;
    case 'not_started':
      return <Play className="h-3.5 w-3.5" />;
    case 'completed':
      return <Check className="h-3.5 w-3.5" />;
    case 'paused':
      return <PauseCircle className="h-3.5 w-3.5" />;
    case 'cancelled':
      return <Ban className="h-3.5 w-3.5" />;
    default:
      return <CircleIcon className="h-3.5 w-3.5" />; // Default for undefined or unexpected
  }
};

// Helper function to get priority icon (adapted for project priorities)
const getPriorityIcon = (priority: ProjectPriority | undefined) => {
  switch (priority) {
    case 'urgent':
      return <AlertOctagon className="h-3.5 w-3.5" />;
    case 'high':
      return <ArrowUp className="h-3.5 w-3.5" />;
    case 'medium':
      return <Minus className="h-3.5 w-3.5" />;
    case 'low':
      return <ArrowDown className="h-3.5 w-3.5" />;
    default:
      return null; // No icon for undefined
  }
};

interface ProjectActionBarProps {
  project: {
    status?: ProjectStatus;
    priority?: ProjectPriority;
    _id: Id<'projects'>;
  };
  updateProject: (args: { updates: { id: Id<'projects'>; updates: Partial<{ status: ProjectStatus; priority: ProjectPriority }> } }) => Promise<any>;
  // Add tag props
  tags?: Tag[];
  onRemoveTag?: (tagId: Id<'tags'>) => Promise<void>;
}

const ProjectActionBar: React.FC<ProjectActionBarProps> = ({
  project,
  updateProject,
  tags = [], // Default tags to empty array
  onRemoveTag
}) => {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false); // Used for status/priority updates

  // Handler for status change
  const handleStatusChange = async (newStatus: ProjectStatus) => {
    if (newStatus === project.status) return; // No change
    try {
      setIsUpdating(true);
      await updateProject({
        updates: {
          id: project._id,
          updates: { status: newStatus }
        }
      });
      toast({
        title: "Status updated",
        description: `Project status changed to ${ProjectStatusEnum.enum[newStatus].replace('_', ' ')}`
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating status",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handler for priority change
  const handlePriorityChange = async (newPriority: ProjectPriority) => {
     if (newPriority === project.priority) return; // No change
    try {
      setIsUpdating(true);
      await updateProject({
        updates: {
          id: project._id,
          updates: { priority: newPriority }
        }
      });
      toast({
        title: "Priority updated",
        description: `Project priority changed to ${ProjectPriorityEnum.enum[newPriority]}`
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating priority",
        description: error instanceof Error ? error.message : "Failed to update priority",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const currentStatus = project.status || 'not_started';
  const currentPriority = project.priority || 'medium';
  const currentStatusLabel = ProjectStatusEnum.enum[currentStatus].replace(/_/g, ' ');
  const currentPriorityLabel = ProjectPriorityEnum.enum[currentPriority];

  return (
    <div className="flex items-center gap-4 px-4 pt-1 pb-4 w-full border-b border-gray-200"> {/* Changed p-4 to px-4 pt-1 pb-4 */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Status</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${PROJECT_STATUS_COLORS[currentStatus]}`}
              // disabled={isUpdating} // Removed disabled prop
            >
              {getStatusIcon(currentStatus)}
              <span className="capitalize">{currentStatusLabel}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {Object.entries(ProjectStatusEnum.enum).map(([key, value]) => (
              <DropdownMenuItem
                key={key}
                onClick={() => handleStatusChange(key as ProjectStatus)}
                className={currentStatus === key ? 'bg-muted' : ''}
                disabled={isUpdating}
              >
                <span className="capitalize">{value.replace('_', ' ')}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Priority</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${PROJECT_PRIORITY_COLORS[currentPriority]}`}
              // disabled={isUpdating} // Removed disabled prop
            >
              {getPriorityIcon(currentPriority)}
              <span className="capitalize">{currentPriorityLabel}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {Object.entries(ProjectPriorityEnum.enum).map(([key, value]) => (
              <DropdownMenuItem
                key={key}
                onClick={() => handlePriorityChange(key as ProjectPriority)}
                className={currentPriority === key ? 'bg-muted' : ''}
                disabled={isUpdating}
              >
                <span className="capitalize">{value}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tags Section - Copied from TaskActionBar */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Tags</span>
        <div className="flex items-start gap-1 flex-wrap">
          {tags.map((tag) => (
            <Badge
              key={tag._id}
              variant="outline"
              className={cn(
                "relative group pr-6", // Add padding for X button
                tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
              )}
              style={{ // Apply color styles directly if needed, or rely on Tailwind JIT
                backgroundColor: tag.color ? `${tag.color}20` : undefined,
                borderColor: tag.color ? `${tag.color}40` : undefined,
                color: tag.color || undefined,
              }}
            >
              {tag.name}
              {/* Remove Tag Button */}
              {onRemoveTag && (
                <button
                  onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                  className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={`Remove tag ${tag.name}`}
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              )}
            </Badge>
          ))}
          {/* Add Tag Button */}
          <AddTaggableTagPopover
            taggableId={project._id as Id<'projects'>} // Explicitly cast to Id<'projects'>
            taggableType="project" // Set type to project
            tagTypeSlug="general-tags" // Using general-tags for project tags
            triggerLabel="Add Tag"
            onSuccess={() => {
              toast({
                title: "Tag added",
                description: "The tag has been added to this project."
              });
            }}
          />
        </div>
      </div>

      {/* Placeholder for other actions if needed */}
      {/* <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Other Action</span>
        <Button variant="outline" size="sm" disabled={isUpdating}>Action</Button>
      </div> */}
    </div>
  );
};

export default ProjectActionBar;
