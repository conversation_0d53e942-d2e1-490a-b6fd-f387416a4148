"use client";

import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tag } from 'lucide-react'; // Using a generic tag icon
import { TaggableType } from '@/zod/tags-schema'; // Import TaggableType

interface ProjectTagsProps {
  projectId: Id<'projects'>;
}

const ProjectTags: React.FC<ProjectTagsProps> = ({ projectId }) => {
  // Use the generic getTagsForTaggable query
  const tags = useQuery(api.tags.getTagsForTaggable, {
    taggable_id: projectId,
    taggable_type: 'project' as TaggableType // Cast to the specific literal type
  });

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
        <CardTitle className="text-base font-medium">Tags</CardTitle>
        {/* Placeholder for Add/Edit Tag button if needed later */}
        {/* <Button variant="ghost" size="icon" className="w-6 h-6">
          <Plus className="h-4 w-4" />
        </Button> */}
      </CardHeader>
      <CardContent>
        {tags === undefined && (
          // Loading Skeleton
          <div className="flex flex-wrap gap-2">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-24 rounded-full" />
          </div>
        )}
        {tags && tags.length === 0 && (
          <p className="text-sm text-muted-foreground">No tags assigned.</p>
        )}
        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag._id}
                variant="secondary"
                className="cursor-default" // Make non-interactive for now
                style={{
                  backgroundColor: tag.color ? `${tag.color}20` : undefined, // Use tag color with transparency
                  borderColor: tag.color ? `${tag.color}40` : undefined,
                  color: tag.color || undefined,
                }}
              >
                <Tag className="h-3 w-3 mr-1" />
                {tag.name}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProjectTags;
