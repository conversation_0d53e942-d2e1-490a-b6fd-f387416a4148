import { useAction } from "convex/react";
import { useState, useMemo, memo } from "react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/hooks/use-toast";
import { ConvexError } from "convex/values";
import { Wand2 } from 'lucide-react';
import { Id } from "@/convex/_generated/dataModel";
import { Spinner } from "@/components/ui/spinner";

// Define a more permissive interface for lineItems
interface LineItem {
  _id: Id<'lineItems'>;
  spending_category?: Id<'tags'> | undefined;
  [key: string]: any; // Allow any other properties
}

// Define interface for the result returned by aiCategorize
interface AICategorizeResult {
  success: boolean;
  categorized: number;
  mappings: Record<string, { category: string }>;
}

interface AutoCategorizeButtonProps {
  isLoading: boolean;
  onCategorize: () => Promise<void>;
  variant?: 'default' | 'compact';
  error?: Error | null;
  onRetry?: () => void;
  progress?: number;
  itemCount?: number;
}

export const AutoCategorizeButton = memo(function AutoCategorizeButton({
  isLoading,
  onCategorize,
  variant = 'default',
  error,
  onRetry,
  progress,
  itemCount
}: AutoCategorizeButtonProps) {
  const isCompact = variant === 'compact';
  
  return (
    <div className={`flex items-center justify-between rounded-lg 
      bg-gradient-to-r from-primary/5 via-primary/5 to-transparent 
      ${isCompact ? 'p-2' : 'p-4'} border border-primary/10
      ${isLoading ? 'border-primary/20' : 'border-primary/10'}`}>
      <div className="flex items-center gap-4">
        <div className={`flex h-12 w-12 items-center justify-center rounded-full
          ${isLoading ? 'bg-primary/20 ring-2' : 'bg-primary/10 ring-1'}
          ring-primary/20 transition-all duration-200`}>
          {isLoading ? (
            <Spinner size="sm" className="text-primary" />
          ) : (
            <Wand2 className="h-6 w-6 text-primary" aria-hidden="true" />
          )}
        </div>
        {!isCompact && (
          <div className="max-w-lg">
            <h3 className="text-lg font-medium tracking-tight">
              {isLoading ? 'Categorizing...' : `Auto Categorize ${itemCount ? `(${itemCount})` : ''}`}
            </h3>
            <p className="mt-1 text-sm mr-3 text-muted-foreground">
              {isLoading 
                ? 'AI is analyzing and categorizing your transactions...'
                : "Let AI analyze and categorize your transactions. Items that can't be confidently classified will be marked as \"AI Uncertain\""
              }
            </p>
            {error && (
              <p className="mt-1 text-sm text-destructive" role="alert">
                {error.message}
              </p>
            )}
          </div>
        )}
      </div>
      <div className="flex flex-col items-end gap-2">
        {progress !== undefined && (
          <div className="text-sm text-muted-foreground">
            Progress: {Math.round(progress)}%
          </div>
        )}
        <Button
          onClick={error ? onRetry : onCategorize}
          disabled={isLoading}
          className="min-w-[120px] shadow-sm"
          variant={error ? "destructive" : "default"}
          size={isCompact ? "default" : "lg"}
          aria-label={isLoading ? "Categorizing transactions..." : "Auto categorize transactions"}
        >
          {isLoading ? (
            <>
              <Spinner size="xs" className="mr-2" />
              <span>Processing...</span>
            </>
          ) : error ? (
            <>
              <span>Retry</span>
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-5 w-5" aria-hidden="true" />
              <span>Categorize</span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
});

export function AICategorizeButton({ lineItems, onSuccess }: { 
  lineItems: LineItem[],
  onSuccess?: (result: AICategorizeResult) => void 
}) {
  const { toast } = useToast();
  const aiCategorize = useAction(api.actions.aiCategorizeAction.aiCategorize);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Filter only uncategorized line items
  const uncategorizedLineItems = useMemo(() => {
    return lineItems.filter(item => item.spending_category === undefined);
  }, [lineItems]);

  const handleAICategorize = async () => {
    // Skip if no uncategorized items
    if (uncategorizedLineItems.length === 0) {
      toast({
        title: "No Action Needed",
        description: "All line items are already categorized",
      });
      return;
    }
    
    // Set loading state before starting AI categorization
    setIsProcessing(true);
    setError(null);
    try {
      const result = await aiCategorize({
        lineItems: uncategorizedLineItems.map(item => ({
          _id: item._id,
          amount: item.amount,
          description: item.description,
          merchant_name: item.merchant_name
        })),
        skipDatabaseUpdate: false
      });

      toast({
        title: "Success",
        description: `Successfully categorized ${result.categorized} items`,
      });

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      // Handle specific category hierarchy errors
      if (error instanceof ConvexError) {
        let errorMessage = "";
        switch (error.data?.code) {
          case "CATEGORY_CONFIG_NOT_FOUND":
            errorMessage = "Expense categories are not set up. Please configure expense categories first.";
            break;
          case "EMPTY_CATEGORY_HIERARCHY":
            errorMessage = "No expense categories found. Please add some expense categories before using AI categorization.";
            break;
          case "AI_GENERATION_ERROR":
            errorMessage = "Failed to generate AI categorizations. Please try again later.";
            break;
          default:
            errorMessage = error.data?.message || "An unexpected error occurred";
        }
        setError(new Error(errorMessage));
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } else {
        const errorMessage = "An unexpected error occurred while categorizing items";
        setError(new Error(errorMessage));
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // Only render the button if there are uncategorized line items
  if (uncategorizedLineItems.length === 0) {
    return null;
  }

  return (
    <AutoCategorizeButton 
      isLoading={isProcessing}
      onCategorize={handleAICategorize}
      error={error}
      onRetry={handleAICategorize}
      itemCount={uncategorizedLineItems.length}
    />
  );
} 