/**
 * Schema for the update_entity_fields function used by the RealtimeConversationAgent
 */
export const UPDATE_ENTITY_FIELDS_SCHEMA = {
  type: "function",
  name: "update_entity_fields",
  description: "Updates entity fields based on the conversation transcript.",
  parameters: {
    type: "object",
    properties: {
      entityType: {
        type: "string",
        description: "Entity type (e.g., 'project', 'task')."
      },
      entityId: {
        type: "string",
        description: "The ID of the entity."
      },
      currentTranscript: {
        type: "string",
        description: "The entire conversation transcript."
      }
    },
    required: ["entityType", "entityId", "currentTranscript"]
  }
};
