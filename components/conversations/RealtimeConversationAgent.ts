"use client";
import React, { useState, useEffect, useRef } from 'react';
import { Id } from '@/convex/_generated/dataModel';
import { api } from '@/convex/_generated/api';
import { useMutation, useQuery, useAction } from 'convex/react'; // Import useAction

// Type definitions for OpenAI Realtime API session configuration
interface InputAudioNoiseReduction {
  type: 'near_field' | 'far_field';
}

interface InputAudioTranscription {
  language?: string;
  model?: 'gpt-4o-transcribe' | 'gpt-4o-mini-transcribe' | 'whisper-1';
  prompt?: string;
}

interface TurnDetection {
  type?: 'server_vad' | 'semantic_vad';
  create_response?: boolean;
  eagerness?: 'low' | 'medium' | 'high' | 'auto';
  interrupt_response?: boolean;
  prefix_padding_ms?: number;
  silence_duration_ms?: number;
  threshold?: number;
}

interface Tool {
  type: string;
  name?: string;
  description?: string;
  parameters?: any;
  function?: {
    name: string;
    description?: string;
    parameters?: any;
  };
  strict?: boolean;
}

interface RealtimeSessionConfig {
  // Audio format options
  input_audio_format?: 'pcm16' | 'g711_ulaw' | 'g711_alaw';
  output_audio_format?: 'pcm16' | 'g711_ulaw' | 'g711_alaw';

  // Audio processing
  input_audio_noise_reduction?: InputAudioNoiseReduction | null;
  input_audio_transcription?: InputAudioTranscription | null;
  turn_detection?: TurnDetection | null;

  // Model behavior
  model?: 'gpt-4o-realtime-preview' | 'gpt-4o-mini-transcribe' | 'gpt-4o-transcribe' | 'whisper-1';
  instructions?: string;
  temperature?: number;
  max_response_output_tokens?: number | 'inf';

  // Response configuration
  modalities?: Array<'audio' | 'text'>;
  voice?: 'alloy' | 'ash' | 'ballad' | 'coral' | 'echo' | 'fable' | 'onyx' | 'nova' | 'sage' | 'shimmer' | 'verse';

  // Tools configuration
  tools?: Tool[];
  tool_choice?: 'auto' | 'none' | 'required' | string;
}

// Tool schemas will be passed via props or imported from lib/openai-tools.ts

interface RealtimeConversationAgentProps {
  // Session configuration
  sessionConfig?: Partial<RealtimeSessionConfig>;

  // Standard props
  entityType?: string;
  entityId?: string | Id<any>;
  onUpdateMessage?: (message: string, role: string) => void;
  onUpdateComplete?: (result: any) => void;
  onError?: (error: Error) => void;
  voiceOption?: string;
  noiseReductionType?: 'near_field' | 'far_field' | null;
}

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected';

// Define the return type for the agent
interface RealtimeConversationAgentReturn {
  // State
  connectionStatus: ConnectionStatus;
  isRecording: boolean;
  messageHistory: Array<{ role: string; content: string }>;

  // Methods
  startConversation: () => Promise<void>;
  stopConversation: () => void;
  sendUserMessage: (text: string) => Promise<void>;

  // Refs
  dataChannelRef: React.RefObject<RTCDataChannel | null>;
  peerConnection: RTCPeerConnection | null;
  mediaStream: MediaStream | null;
}

// Export the agent as a custom hook instead of a React component
export const useRealtimeConversationAgent = (props: RealtimeConversationAgentProps): RealtimeConversationAgentReturn => {
  const {
    sessionConfig = {},
    entityType,
    entityId,
    onUpdateMessage,
    onUpdateComplete,
    onError,
    voiceOption,
    noiseReductionType,
  } = props;

  // State
  const [messageHistory, setMessageHistory] = useState<Array<{ role: string; content: string }>>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [currentNoiseReduction, setCurrentNoiseReduction] = useState<'near_field' | 'far_field' | null>(
    noiseReductionType !== undefined ? noiseReductionType : 'near_field'
  );

  // Refs
  const dataChannelRef = useRef<RTCDataChannel | null>(null);

  // Compute if connected
  const isConnected = connectionStatus === 'connected';

  // Extract to component scope for access by all methods
  const dataChannel = dataChannelRef.current;

  // Basic state management for connection status, recording status, and transcript
  const [peerConnection, setPeerConnection] = useState<RTCPeerConnection | null>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const audioTracksRef = useRef<MediaStreamTrack[]>([]); // Ref to store audio tracks
  const unmuteTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for the unmute timeout

  // Get Convex mutations/actions directly using hooks
  const updateProjectMutation = useMutation(api.openai_tools.openai_projects.updateProjectFromAITool);
  // Use useAction to call the search action imperatively from the event handler
  const searchDirectoryAction = useAction(api.directory.directoryActions.searchDirectoryAction);
  // We'll handle entity fields updates differently since we're not sure of the exact path

  // Function to fetch ephemeral token/session details directly from Convex HTTP endpoint
  async function getEphemeralToken() {
    try {
      // Get Convex URL directly - Next.js makes NEXT_PUBLIC_ vars available directly
      if (typeof window === 'undefined') {
        throw new Error("This component should only be used in the browser.");
      }

      // Check for Next.js environment variable
      const convexSiteUrl = process.env.NEXT_PUBLIC_CONVEX_SITE_URL;
      if (!convexSiteUrl) {
        throw new Error(
          "NEXT_PUBLIC_CONVEX_SITE_URL is not available. Make sure you have a .env.local file " +
          "with NEXT_PUBLIC_CONVEX_SITE_URL defined at the project root."
        );
      }

      // Construct the full URL for the session endpoint
      const url = `${convexSiteUrl}/openai/realtime/conversation/session`;

      console.log("Fetching from URL:", url);

      // Merge the default configuration with provided config options
      const completeSessionConfig: RealtimeSessionConfig = {
        // Default configuration
        model: "gpt-4o-realtime-preview",
        modalities: ["audio", "text"],
        temperature: 0.8, // don't change this
        max_response_output_tokens: 4096,
        voice: "alloy",
        turn_detection: {
          type: "semantic_vad",
          create_response: true,
          eagerness: "auto",
          interrupt_response: true
        },
        input_audio_noise_reduction: currentNoiseReduction === null
          ? null
          : { type: currentNoiseReduction || "far_field" },

        // Override with any provided sessionConfig
        ...sessionConfig
      };

      console.log("Sending complete session config to server:", completeSessionConfig);

      // Call the Convex HTTP endpoint with Origin header for CORS validation
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin
        },
        body: JSON.stringify(completeSessionConfig)
      });

      console.log("Fetch response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Failed response:", errorText);
        throw new Error(`Failed to fetch ephemeral token: ${errorText}`);
      }

      const data = await response.json();
      console.log("Successfully received ephemeral token from Convex");

      if (!data.client_secret || !data.client_secret.value) {
        console.error("Invalid token response structure:", JSON.stringify(data));
        throw new Error('Invalid token response structure');
      }

      return data; // Contains client_secret needed for SDP exchange
    } catch (error: any) {
      console.error("Error in getEphemeralToken:", error);
      if (onError) onError(error);
      throw error; // Re-throw to be caught by startConversation
    }
  }

  // Function to start the realtime conversation session using WebRTC
  async function startConversation() {
    setConnectionStatus('connecting');
    try {
      // Fetch ephemeral token/session data via proxy
      const sessionData = await getEphemeralToken();

      // Create a new RTCPeerConnection
      const pc = new RTCPeerConnection();
      setPeerConnection(pc);

      // Create data channel for receiving events from the server
      const dc = pc.createDataChannel('oai-events');
      dataChannelRef.current = dc;

      // Set up data channel event handlers
      dc.onopen = () => {
        console.log('Data channel open');

        // Only send session.update for instructions if needed
        if (sessionConfig?.instructions) {
          const instructionUpdateEvent = {
            type: "session.update",
            session: {
              instructions: sessionConfig.instructions,
            }
          };

          console.log("Sending session.update event for instructions:", instructionUpdateEvent);
          dc.send(JSON.stringify(instructionUpdateEvent));
        }

        // Only send response.create after the data channel is open
        console.log("Creating initial response with audio and text");
        const createResponseEvent = {
          type: 'response.create',
          response: {
            modalities: ["audio", "text"] // Explicitly request both audio and text
          }
        };
        dc.send(JSON.stringify(createResponseEvent));

        // Signal that we're connected
        setConnectionStatus('connected');
        setIsRecording(true);
      };

      // Set up onmessage handler to process server events
      dc.onmessage = (event) => {
        try {
          // Ensure event.data exists and is parseable JSON
          if (!event.data) {
            console.warn("Received empty message from server");
            return;
          }

          let serverEvent;
          try {
            serverEvent = JSON.parse(event.data);
          } catch (parseError) {
            console.error("Failed to parse server message:", event.data, parseError);
            return;
          }

          // Ensure serverEvent has a type property
          if (!serverEvent || !serverEvent.type) {
            console.warn("Received malformed server event (missing type):", serverEvent);
            return;
          }

          console.log("Server event received:", serverEvent.type);

          // Add specific monitoring for tool call events
          if (serverEvent.type === 'response.tool_calls') {
            console.log("%c🔧 TOOL CALL DETECTED 🔧", "background: #4CAF50; color: white; padding: 4px; border-radius: 4px;");
            console.log("Tool call details:", serverEvent);
          }

          // Handle incoming transcript delta for user speech
          if (serverEvent.type === 'response.audio_transcript.delta') {
            if (serverEvent.delta) {
              // Append to the last user message if it exists, otherwise create a new entry
              setMessageHistory(prev => {
                const lastMsg = prev.length > 0 ? prev[prev.length - 1] : null;
                if (lastMsg && lastMsg.role === 'user') {
                  // Update the last message
                  const newHistory = [...prev];
                  newHistory[newHistory.length - 1] = {
                    role: 'user',
                    content: lastMsg.content + serverEvent.delta
                  };
                  return newHistory;
                } else {
                  // Create a new message
                  return [...prev, { role: 'user', content: serverEvent.delta }];
                }
              });

              // Notify parent component
              if (onUpdateMessage) {
                onUpdateMessage(serverEvent.delta, 'user');
              }
            }
          }
          // Handle completed transcript for user speech
          else if (serverEvent.type === 'response.audio_transcript.completed') {
            if (serverEvent.transcript) {
              // Replace the last user message if it exists, otherwise create a new entry
              setMessageHistory(prev => {
                const lastMsg = prev.length > 0 ? prev[prev.length - 1] : null;
                if (lastMsg && lastMsg.role === 'user') {
                  // Replace the last message with the complete transcript
                  const newHistory = [...prev];
                  newHistory[newHistory.length - 1] = {
                    role: 'user',
                    content: serverEvent.transcript
                  };
                  return newHistory;
                } else {
                  // Create a new message
                  return [...prev, { role: 'user', content: serverEvent.transcript }];
                }
              });

              // Notify parent component
              if (onUpdateMessage) {
                onUpdateMessage(serverEvent.transcript, 'user');
              }
            }
          }
          // Handle assistant text delta events
          else if (serverEvent.type === 'response.text.delta') {
            if (serverEvent.delta) {
              // Append to the last assistant message if it exists, otherwise create a new entry
              setMessageHistory(prev => {
                const lastMsg = prev.length > 0 ? prev[prev.length - 1] : null;
                if (lastMsg && lastMsg.role === 'assistant') {
                  // Update the last message
                  const newHistory = [...prev];
                  newHistory[newHistory.length - 1] = {
                    role: 'assistant',
                    content: lastMsg.content + serverEvent.delta
                  };
                  return newHistory;
                } else {
                  // Create a new message
                  return [...prev, { role: 'assistant', content: serverEvent.delta }];
                }
              });

              // Notify parent component
              if (onUpdateMessage) {
                onUpdateMessage(serverEvent.delta, 'assistant');
              }
            }
          }
          // Handle completed assistant text response
          else if (serverEvent.type === 'response.text.completed') {
            if (serverEvent.text) {
              // Replace the last assistant message if it exists, otherwise create a new entry
              setMessageHistory(prev => {
                const lastMsg = prev.length > 0 ? prev[prev.length - 1] : null;
                if (lastMsg && lastMsg.role === 'assistant') {
                  // Replace the last message with the complete text
                  const newHistory = [...prev];
                  newHistory[newHistory.length - 1] = {
                    role: 'assistant',
                    content: serverEvent.text
                  };
                  return newHistory;
                } else {
                  // Create a new message
                  return [...prev, { role: 'assistant', content: serverEvent.text }];
                }
              });

              // Notify parent component
              if (onUpdateMessage) {
                onUpdateMessage(serverEvent.text, 'assistant');
              }
            }
          }
          // Handle function call response from the model
          else if (
            serverEvent.type === 'response.done' &&
            serverEvent.response?.output &&
            serverEvent.response.output[0]?.type === 'function_call'
          ) {
            console.log("%c🧩 FUNCTION CALL COMPLETED 🧩", "background: #2196F3; color: white; padding: 4px; border-radius: 4px;");
            console.log("Function call output:", serverEvent.response.output[0]);

            // Track which function was called
            const functionName = serverEvent.response.output[0].name;
            const callId = serverEvent.response.output[0].call_id;

            try {
              const args = JSON.parse(serverEvent.response.output[0].arguments);
              console.log(`Function "${functionName}" called with args:`, args);

              // Add debugging logs
              console.log("%c🔍 DEBUGGING FUNCTION CALL 🔍", "background: #FF5722; color: white; padding: 4px; border-radius: 4px;");
              console.log("Function name:", functionName);
              console.log("Call ID:", callId);
              console.log("Arguments:", args);
              console.log("updateProjectMutation available:", !!updateProjectMutation);
              console.log("searchDirectoryAction available:", !!searchDirectoryAction); // Check new action

              // For update_project function specifically
              if (functionName === 'update_project') {
                console.log("%c📝 PROJECT UPDATE REQUESTED 📝", "background: #9C27B0; color: white; padding: 4px; border-radius: 4px;");
                console.log("Project update args:", args);

                // Check if we have the mutation available
                if (updateProjectMutation) {
                  console.log("Calling project update mutation with args:", args);
                  // The backend mutation `updateProjectFromAITool` expects a flat structure
                  // with 'id' and other fields directly in the args object.
                  // The schema in lib/openai-tools.ts has been updated to reflect this.
                  // No flattening is needed here anymore.

                  // Use the Convex mutation with the received args directly
                  updateProjectMutation(args)
                    .then((result: any) => {
                      console.log("Project update result:", result);
                      const outputEvent = {
                        type: "conversation.item.create",
                        item: {
                          type: "function_call_output",
                          call_id: callId,
                          output: JSON.stringify(result)
                        }
                      };
                      if (dc.readyState === 'open') {
                        dc.send(JSON.stringify(outputEvent));
                      }
                      if (onUpdateComplete) onUpdateComplete(result);
                    })
                    .catch((err: any) => {
                      console.error("Error updating project:", err);
                      if (onError) onError(err);
                    });
                } else {
                  console.warn("Cannot call updateProjectMutation: function not available");
                }
              }
              // For directory_search_people_and_teams function specifically
              else if (functionName === 'directory_search_people_and_teams') {
                console.log("%c🔍 DIRECTORY SEARCH REQUESTED 🔍", "background: #2196F3; color: white; padding: 4px; border-radius: 4px;");
                console.log("Search term:", args.search);
                console.log("Type:", args.type || "all"); // Log the type filter
                console.log("Limit:", args.limit || "default");

                // Use the action hook to call the search action dynamically
                searchDirectoryAction({
                  search: args.search,
                  type: args.type || 'all', // Pass type, default to 'all'
                  limit: args.limit // Pass limit if provided
                })
                .then((result: any) => {
                  console.log("Directory search result:", result);
                  const outputEvent = {
                    type: "conversation.item.create",
                    item: {
                      type: "function_call_output",
                      call_id: callId,
                      output: JSON.stringify(result) // Send the actual result
                    }
                  };
                  if (dc.readyState === 'open') {
                    dc.send(JSON.stringify(outputEvent));
                  }
                  if (onUpdateComplete) onUpdateComplete(result);
                })
                .catch((err: any) => {
                  console.error("Error searching directory:", err);
                  const errorEvent = {
                    type: "conversation.item.create",
                    item: {
                      type: "function_call_output",
                      call_id: callId,
                      output: JSON.stringify({
                        error: "Failed to search directory",
                        message: err instanceof Error ? err.message : "Unknown error"
                      })
                    }
                  };

                  if (dc.readyState === 'open') {
                    dc.send(JSON.stringify(errorEvent));
                  }

                  if (onError) onError(err instanceof Error ? err : new Error("Unknown error in directory search"));
                });
              }
              // We'll handle other function types as they're identified
              else {
                console.warn(`No handler found for function "${functionName}"`);

                // Send error message back to the model
                const errorEvent = {
                  type: "conversation.item.create",
                  item: {
                    type: "function_call_output",
                    call_id: callId,
                    output: JSON.stringify({ error: `No handler found for function "${functionName}"` })
                  }
                };

                if (dc.readyState === 'open') {
                  dc.send(JSON.stringify(errorEvent));
                }
              }
            } catch (parseError) {
              console.error("Error parsing function arguments:", parseError);
            }
          }
        } catch (err) {
          console.error("Error processing DataChannel message:", err);
        }
      };

      // Peer connection tracks handler to play audio from the model
      pc.ontrack = (event) => {
        console.log("Received track from server:", event.track.kind);
        // Create audio element to play the audio from the model
        const audioElement = document.createElement('audio');
        audioElement.srcObject = event.streams[0];
        audioElement.autoplay = true;
        document.body.appendChild(audioElement);
        console.log("Added audio element to DOM for model voice output");
      };

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMediaStream(stream);

      // Get audio tracks and store them
      const audioTracks = stream.getAudioTracks();
      audioTracksRef.current = audioTracks;

      // --- Mute tracks initially ---
      console.log("Muting microphone tracks initially...");
      audioTracks.forEach(track => {
        track.enabled = false;
      });

      // --- Add tracks to peer connection ---
      audioTracks.forEach(track => {
        pc.addTrack(track, stream);
        console.log(`Added track: ${track.id}, initial enabled state: ${track.enabled}`);
      });

      // --- Schedule unmuting after 5 seconds ---
      unmuteTimeoutRef.current = setTimeout(() => {
        console.log("Unmuting microphone tracks after 5 seconds...");
        audioTracksRef.current.forEach(track => {
          track.enabled = true;
          console.log(`Unmuted track: ${track.id}, current enabled state: ${track.enabled}`);
        });
        unmuteTimeoutRef.current = null; // Clear the ref after execution
      }, 5000);

      // Create SDP offer
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // Exchange SDP with OpenAI realtime endpoint using the ephemeral token
      const sdpResponse = await fetch('https://api.openai.com/v1/realtime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sdp',
          'Authorization': `Bearer ${sessionData.client_secret.value}`
        },
        body: offer.sdp
      });
      if (!sdpResponse.ok) {
        const errorText = await sdpResponse.text();
        throw new Error(`SDP exchange failed: ${errorText}`);
      }
      const answerSdp = await sdpResponse.text();
      await pc.setRemoteDescription({ type: 'answer', sdp: answerSdp });
    } catch (err: any) {
      console.error("Error starting conversation:", err);
      setConnectionStatus('disconnected');
      if (onError) onError(err);
    }
  }

  // Function to stop the realtime conversation session and clean up WebRTC resources
  function stopConversation() {
    // Clear the unmute timeout if it's still pending
    if (unmuteTimeoutRef.current) {
      clearTimeout(unmuteTimeoutRef.current);
      unmuteTimeoutRef.current = null;
      console.log("Cleared pending unmute timeout.");
    }

    if (dataChannel) {
      dataChannel.close();
      dataChannelRef.current = null;
    }
    if (peerConnection) {
      peerConnection.close();
      setPeerConnection(null);
    }
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      setMediaStream(null);
    }
    setConnectionStatus('disconnected');
    setIsRecording(false);
  }

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Ensure stopConversation is called on unmount
      // This will also clear any pending unmute timeout
      stopConversation();
    };

  }, []); // Keep dependencies empty to run only on mount/unmount

  // Sync noiseReductionType prop with state
  useEffect(() => {
    if (noiseReductionType !== undefined && noiseReductionType !== currentNoiseReduction) {
      setCurrentNoiseReduction(noiseReductionType);
    }
  }, [noiseReductionType]);

  // Helper function to send messages through the data channel
  const sendDataChannelMessage = async (message: any) => {
    // Check if we have a data channel reference
    if (!dataChannelRef.current) {
      const error = new Error('Cannot send message: data channel does not exist');
      console.error(error.message, message);
      throw error;
    }

    // Check if data channel is in open state
    if (dataChannelRef.current.readyState !== 'open') {
      const error = new Error(`Cannot send message: data channel not open (state: ${dataChannelRef.current.readyState})`);
      console.error(error.message, message);
      throw error;
    }

    try {
      const messageJson = JSON.stringify(message);
      dataChannelRef.current.send(messageJson);
      console.log('Successfully sent data channel message:', message);
      return true;
    } catch (error) {
      console.error('Error serializing or sending data channel message:', error);
      throw error;
    }
  };

  // Function to send user message and get a response
  const sendUserMessage = async (text: string) => {
    // Ensure the data channel exists and is in the 'open' state
    if (!dataChannel) {
      console.error('Cannot send message, data channel does not exist');
      if (onError) onError(new Error('Connection not established - data channel does not exist'));
      return;
    }

    if (dataChannel.readyState !== 'open') {
      console.error('Cannot send message, data channel not open. State:', dataChannel.readyState);
      if (onError) onError(new Error(`Connection not ready - data channel state: ${dataChannel.readyState}`));
      return;
    }

    try {
      console.log('Data channel ready, sending message:', text);

      // Create a conversation item with the user's message
      await sendDataChannelMessage({
        type: 'conversation.item.create',
        item: {
          type: 'message',
          role: 'user',
          content: [
            {
              type: 'input_text',
              text
            }
          ]
        }
      });

      console.log('Sent user message:', text);

      // Request a response from the model with both audio and text
      await sendDataChannelMessage({
        type: 'response.create',
        response: {
          modalities: ['audio', 'text']
        }
      });

      console.log('Requested model response with audio and text');
    } catch (error) {
      console.error('Error sending user message:', error);
      if (onError) onError(error as Error);
    }
  };

  // Return the public API for the component
  return {
    // State
    connectionStatus,
    isRecording,
    messageHistory,

    // Methods
    startConversation,
    stopConversation,
    sendUserMessage,

    // Refs
    dataChannelRef,
    peerConnection,
    mediaStream
  };
};
