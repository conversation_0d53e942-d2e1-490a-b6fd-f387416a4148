'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import {
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  BarChart,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  DollarSign
} from 'lucide-react';

interface SpendingInsight {
  type: 'trend' | 'anomaly' | 'suggestion';
  title: string;
  description: string;
  impact: 'positive' | 'negative' | 'neutral';
  value?: number;
}

interface VendorTrend {
  vendor: string;
  monthlySpend: {
    month: string;
    amount: number;
  }[];
  trend: 'increasing' | 'decreasing' | 'stable';
  averageSpend: number;
  isNew: boolean;
}

interface PatternRecognitionProps {
  insights: SpendingInsight[];
  vendorTrends: VendorTrend[];
}

export function PatternRecognition({
  insights,
  vendorTrends
}: PatternRecognitionProps) {
  const getInsightIcon = (type: SpendingInsight['type']) => {
    switch (type) {
      case 'trend':
        return <TrendingUp className="text-blue-500" size={18} />;
      case 'anomaly':
        return <AlertTriangle className="text-amber-500" size={18} />;
      case 'suggestion':
        return <Lightbulb className="text-purple-500" size={18} />;
      default:
        return <BarChart className="text-gray-500" size={18} />;
    }
  };

  const getImpactBadgeVariant = (impact: SpendingInsight['impact']) => {
    switch (impact) {
      case 'positive':
        return 'default' as const;
      case 'negative':
        return 'destructive' as const;
      default:
        return 'secondary' as const;
    }
  };

  // Get impact badge color class
  const getImpactColorClass = (impact: SpendingInsight['impact']) => {
    switch (impact) {
      case 'positive':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'negative':
        return 'bg-red-100 text-red-800 hover:bg-red-100';
      default:
        return '';
    }
  };

  const getTrendIcon = (trend: VendorTrend['trend']) => {
    switch (trend) {
      case 'increasing':
        return <ArrowUp className="text-red-500" size={14} />;
      case 'decreasing':
        return <ArrowDown className="text-green-500" size={14} />;
      default:
        return <ArrowRight className="text-gray-500" size={14} />;
    }
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="p-4">
        <h2 className="text-sm font-semibold mb-2">Spending Insights</h2>
        <div className="space-y-2">
          {insights.map((insight, index) => (
            <div key={index} className="p-2 bg-gray-50 rounded-lg">
              <div className="flex items-start space-x-2">
                <div className="mt-0.5">{getInsightIcon(insight.type)}</div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-sm truncate">
                      {insight.title}
                    </h3>
                    <Badge
                      variant={getImpactBadgeVariant(insight.impact)}
                      className={`text-xs py-0 h-5 ${getImpactColorClass(insight.impact)}`}
                    >
                      {insight.impact}
                    </Badge>
                  </div>
                  <p className="text-xs line-clamp-2 text-gray-600">
                    {insight.description}
                  </p>
                  {insight.value && (
                    <div className="flex items-center mt-1 text-xs font-medium">
                      <DollarSign size={12} className="mr-0.5" />
                      <span>{insight.value.toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      <Card className="p-4">
        <h2 className="text-sm font-semibold mb-2">Vendor Analysis</h2>
        <div className="space-y-4">
          {vendorTrends.map((vendor) => (
            <div key={vendor.vendor} className="space-y-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-sm">{vendor.vendor}</h3>
                  <div className="flex space-x-2 items-center">
                    <Badge
                      variant={
                        vendor.trend === 'increasing'
                          ? 'destructive'
                          : 'secondary'
                      }
                      className="text-xs py-0 h-5"
                    >
                      <span className="flex items-center">
                        {getTrendIcon(vendor.trend)}
                        <span className="ml-1">{vendor.trend}</span>
                      </span>
                    </Badge>
                    {vendor.isNew && (
                      <Badge variant="outline" className="text-xs py-0 h-5">
                        New
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  Avg. ${vendor.averageSpend.toLocaleString()}/mo
                </p>
              </div>

              <div className="h-[70px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={vendor.monthlySpend}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="month" tick={{ fontSize: 10 }} />
                    <YAxis tick={{ fontSize: 10 }} />
                    <Tooltip contentStyle={{ fontSize: '12px' }} />
                    <Line
                      type="monotone"
                      dataKey="amount"
                      stroke="#2563eb"
                      strokeWidth={2}
                      dot={{ r: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
