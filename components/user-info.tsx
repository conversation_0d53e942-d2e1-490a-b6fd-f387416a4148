'use client';

import { useConvexAuth } from 'convex/react';
import { useAuthActions } from '@convex-dev/auth/react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { LogOut, User, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/hooks/use-toast';
import { useUserData } from './user-data-provider';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';

interface UserInfoProps {
  isCollapsed?: boolean;
  onDropdownStateChange?: (isOpen: boolean) => void;
}

export function UserInfo({ isCollapsed = false, onDropdownStateChange }: UserInfoProps) {
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  const { signOut } = useAuthActions();
  const { toast } = useToast();
  const { user, person, isLoading } = useUserData();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  
  // Notify parent when dropdown state changes
  useEffect(() => {
    if (onDropdownStateChange) {
      onDropdownStateChange(dropdownOpen);
    }
  }, [dropdownOpen, onDropdownStateChange]);
  
  // Handle sign out
  const handleSignOut = async () => {
    await signOut();
    toast({
      title: 'Signed out',
      description: 'You have been signed out successfully.'
    });
  };

  // Show loading state
  if (isLoading || authLoading) {
    return (
      <div className={`flex items-center ${!isCollapsed ? 'w-full' : ''}`}>
        <Skeleton className="size-6 rounded-full" />
        {!isCollapsed && (
          <div className="space-y-1 ml-3">
            <Skeleton className="h-4 w-20" />
          </div>
        )}
      </div>
    );
  }

  // Show not authenticated state
  if (!isAuthenticated) {
    return (
      <div className={`flex items-center ${!isCollapsed ? 'w-full' : ''}`}>
        <Avatar className="size-6">
          <AvatarFallback>
            <User className="size-4" />
          </AvatarFallback>
        </Avatar>
        {!isCollapsed && (
          <div className="text-sm ml-3">
            <p className="font-medium">Not signed in</p>
          </div>
        )}
      </div>
    );
  }

  // If collapsed, only render the avatar
  if (isCollapsed) {
    return (
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="size-6 p-0 rounded-full"
            onClick={(e) => e.stopPropagation()}
          >
            <Avatar className="size-6">
              {user === null ? (
                <AvatarFallback>
                  <AlertCircle className="size-4" />
                </AvatarFallback>
              ) : (
                <>
                  {person?.image ? (
                    <AvatarImage src={person.image} alt={person.name || user.name || 'User'} />
                  ) : user?.image ? (
                    <AvatarImage src={user.image} alt={user.name || 'User'} />
                  ) : null}
                  <AvatarFallback>
                    {(user.name || user.email?.charAt(0) || 'U')
                      .charAt(0)
                      .toUpperCase()}
                  </AvatarFallback>
                </>
              )}
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          className="w-56" 
          align="end" 
          forceMount
          onMouseEnter={(e) => e.stopPropagation()}
          onMouseLeave={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
        >
          {user === null ? (
            <>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none text-amber-500">
                    Authentication Issue
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    Authenticated but no user record found
                  </p>
                </div>
              </DropdownMenuLabel>
            </>
          ) : (
            <>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user.name || 'User'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email || 'No email provided'}
                  </p>
                </div>
              </DropdownMenuLabel>
            </>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleSignOut}
            className="dropdown-menu-item"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Render user dropdown with name displayed (or auth issue)
  return (
    <div className="flex items-center w-full">
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex items-center gap-2 px-1 rounded-full hover:bg-accent"
            onClick={(e) => e.stopPropagation()}
          >
            <Avatar className="size-6">
              {user === null ? (
                <AvatarFallback>
                  <AlertCircle className="size-4" />
                </AvatarFallback>
              ) : (
                <>
                  {person?.image ? (
                    <AvatarImage src={person.image} alt={person.name || user.name || 'User'} />
                  ) : user?.image ? (
                    <AvatarImage src={user.image} alt={user.name || 'User'} />
                  ) : null}
                  <AvatarFallback>
                    {(user.name || user.email?.charAt(0) || 'U')
                      .charAt(0)
                      .toUpperCase()}
                  </AvatarFallback>
                </>
              )}
            </Avatar>
            <span className="text-sm font-medium ml-3">
              {user === null ? (
                <span className="text-amber-500">Auth issue</span>
              ) : (
                <>
                  {user.name
                    ? user.name.split(' ')[0]
                    : user.email
                      ? user.email.split('@')[0]
                      : 'User'}
                </>
              )}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          className="w-56" 
          align="end" 
          forceMount
          onMouseEnter={(e) => e.stopPropagation()}
          onMouseLeave={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
        >
          {user === null ? (
            <>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none text-amber-500">
                    Authentication Issue
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    Authenticated but no user record found
                  </p>
                </div>
              </DropdownMenuLabel>
            </>
          ) : (
            <>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user.name || 'User'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email || 'No email provided'}
                  </p>
                </div>
              </DropdownMenuLabel>
            </>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleSignOut}
            className="dropdown-menu-item"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
