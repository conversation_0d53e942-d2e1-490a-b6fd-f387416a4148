'use client';

import React, { ReactNode, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/hooks/use-toast';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  RowSelectionState
} from '@tanstack/react-table';

// Types for the component
export type CardListColumn<T extends object> = {
  id: string;
  header: string;
  width?: string;
  cell: (item: T) => ReactNode;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  accessorFn?: (item: T) => string | number | Date; // For sorting
  className?: string;
  headerClassName?: string;
  minWidth?: string;
  maxWidth?: string;
};

export type CardListAction<T extends object> = {
  label: string;
  icon?: ReactNode;
  onClick: (item: T) => void;
  variant?: 'default' | 'destructive' | 'secondary' | 'outline' | 'ghost';
  disabled?: boolean | ((item: T) => boolean);
  tooltip?: string;
  confirmMessage?: string; // For actions that need confirmation
  hideOnCondition?: (item: T) => boolean;
};

export type CardListSortState = {
  column: string;
  direction: 'asc' | 'desc';
};

export type CardListProps<T extends object> = {
  items: T[];
  columns: CardListColumn<T>[];
  actions?: CardListAction<T>[];
  keyExtractor: (item: T) => string;
  onItemClick?: (item: T) => void;
  selectable?: boolean;
  selectedItems?: Set<string>;
  onSelectionChange?: (selectedIds: Set<string>) => void;
  isLoading?: boolean;
  emptyMessage?: string;
  loadingSkeletonCount?: number;
  className?: string;
  headerClassName?: string;
  rowClassName?: string | ((item: T, index: number) => string);
  highlightOnHover?: boolean;
  sortable?: boolean;
  defaultSort?: CardListSortState;
  onSortChange?: (sortState: CardListSortState) => void;
  compact?: boolean;
  maxHeight?: string;
  showHeader?: boolean;
  actionButtonVariant?: 'icon' | 'dropdown' | 'button';
  stickyHeader?: boolean;
  zebra?: boolean; // Alternating row colors
  pagination?: boolean;
  pageSize?: number;
  manualSorting?: boolean;
  manualPagination?: boolean;
  pageCount?: number;
  pageIndex?: number;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  size?: 'xs' | 'sm' | 'md'; // Control overall component size
  horizontalScroll?: boolean; // Enable horizontal scrolling for wide tables
};

/**
 * Default props for the CardList component
 * This makes it easier to understand and maintain default values
 */
const defaultCardListProps = {
  // Array of actions that can be performed on each item
  actions: [] as CardListAction<any>[],
  
  // Whether items can be selected with checkboxes
  selectable: true,
  
  // Set of selected item IDs
  selectedItems: new Set<string>(),
  
  // Whether the component is in a loading state
  isLoading: false,
  
  // Message to display when there are no items
  emptyMessage: 'No items found.',
  
  // Number of skeleton rows to display when loading
  loadingSkeletonCount: 5,
  
  // Whether rows should be highlighted on hover
  highlightOnHover: true,
  
  // Whether columns can be sorted
  sortable: true,
  
  // Whether to use compact spacing
  compact: true,
  
  // Whether to show the header row
  showHeader: true,
  
  // How to display action buttons ('icon' or 'dropdown')
  actionButtonVariant: 'dropdown' as const,
  
  // Whether the header should stick to the top when scrolling
  stickyHeader: false,
  
  // Whether to use alternating row colors
  zebra: false,
  
  // Whether to show pagination controls
  pagination: true,
  
  // Number of items to show per page
  pageSize: 25,
  
  // Whether sorting is handled externally
  manualSorting: true,
  
  // Whether pagination is handled externally
  manualPagination: true,
  
  // Current page index (0-based)
  pageIndex: 0,
  
  // Overall size of the component ('xs', 'sm', or 'md')
  size: 'xs' as const,
  
  // Whether to enable horizontal scrolling for wide tables
  horizontalScroll: false
};

/**
 * CardList - A reusable component that displays items in a table-like card list format
 * 
 * Features:
 * - Displays data in a clean, table-like format
 * - Supports selection with checkboxes
 * - Configurable columns with custom cell renderers
 * - Action menu for each item
 * - Sorting capabilities
 * - Loading states and empty states
 * - Powered by TanStack Table for advanced features
 * 
 * @example
 * ```tsx
 * <CardList
 *   items={tasks}
 *   columns={[
 *     { 
 *       id: 'name', 
 *       header: 'Task', 
 *       cell: (task) => <div>{task.name}</div>,
 *       sortable: true,
 *       accessorFn: (task) => task.name
 *     },
 *     { 
 *       id: 'status', 
 *       header: 'Status', 
 *       cell: (task) => <Badge>{task.status}</Badge>,
 *       align: 'center' 
 *     }
 *   ]}
 *   actions={[
 *     { 
 *       label: 'View', 
 *       icon: <Eye />, 
 *       onClick: (task) => router.push(`/tasks/${task._id}`),
 *       tooltip: 'View task details'
 *     }
 *   ]}
 *   keyExtractor={(task) => task._id}
 *   onItemClick={(task) => router.push(`/tasks/${task._id}`)}
 *   selectable
 *   selectedItems={selectedTasks}
 *   onSelectionChange={setSelectedTasks}
 *   sortable
 *   size="xs"
 * />
 * ```
 */
export function CardList<T extends object>({
  items,
  columns,
  actions = defaultCardListProps.actions,
  keyExtractor,
  onItemClick,
  selectable = defaultCardListProps.selectable,
  selectedItems = defaultCardListProps.selectedItems,
  onSelectionChange,
  isLoading = defaultCardListProps.isLoading,
  emptyMessage = defaultCardListProps.emptyMessage,
  loadingSkeletonCount = defaultCardListProps.loadingSkeletonCount,
  className,
  headerClassName,
  rowClassName,
  highlightOnHover = defaultCardListProps.highlightOnHover,
  sortable = defaultCardListProps.sortable,
  defaultSort,
  onSortChange,
  compact = defaultCardListProps.compact,
  maxHeight,
  showHeader = defaultCardListProps.showHeader,
  actionButtonVariant = defaultCardListProps.actionButtonVariant,
  stickyHeader = defaultCardListProps.stickyHeader,
  zebra = defaultCardListProps.zebra,
  pagination = defaultCardListProps.pagination,
  pageSize = defaultCardListProps.pageSize,
  manualSorting = defaultCardListProps.manualSorting,
  manualPagination = defaultCardListProps.manualPagination,
  pageCount,
  pageIndex = defaultCardListProps.pageIndex,
  onPaginationChange,
  size = defaultCardListProps.size,
  horizontalScroll = defaultCardListProps.horizontalScroll
}: CardListProps<T>) {
  const { toast } = useToast();
  const [confirmAction, setConfirmAction] = useState<{ action: CardListAction<T>, item: T } | null>(null);
  
  // Get size-specific classes
  const getSizeClasses = useMemo(() => {
    switch (size) {
      case 'xs':
        return {
          text: 'text-xs',
          padding: compact ? 'py-1.5 px-3' : 'py-2 px-3',
          headerPadding: compact ? 'py-1.5 px-3' : 'py-2 px-3',
          checkbox: 'h-3.5 w-3.5',
          iconButton: 'h-6 w-6',
          icon: 'h-3 w-3'
        };
      case 'sm':
        return {
          text: 'text-sm',
          padding: compact ? 'py-2 px-3' : 'py-2.5 px-4',
          headerPadding: compact ? 'py-2 px-3' : 'py-2.5 px-4',
          checkbox: 'h-4 w-4',
          iconButton: 'h-7 w-7',
          icon: 'h-3.5 w-3.5'
        };
      case 'md':
      default:
        return {
          text: 'text-sm',
          padding: compact ? 'py-2.5 px-4' : 'py-3 px-4',
          headerPadding: compact ? 'py-2.5 px-4' : 'py-3 px-4',
          checkbox: 'h-4 w-4',
          iconButton: 'h-8 w-8',
          icon: 'h-4 w-4'
        };
    }
  }, [size, compact]);

  // Convert CardList columns to TanStack Table columns
  const tableColumns = useMemo<ColumnDef<T>[]>(() => {
    const cols: ColumnDef<T>[] = [];
    
    // Add selection column if selectable
    if (selectable) {
      cols.push({
        id: 'select',
        header: ({ table }) => (
          <div className="flex items-center justify-center w-full">
            <Checkbox
              checked={
                table.getIsAllRowsSelected() ||
                (table.getIsSomeRowsSelected() && 'indeterminate')
              }
              onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
              aria-label="Select all"
              className={getSizeClasses.checkbox}
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center justify-center w-full">
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              onClick={(e) => e.stopPropagation()}
              aria-label="Select row"
              className={getSizeClasses.checkbox}
            />
          </div>
        ),
        enableSorting: false,
        size: 50,
        meta: {
          width: '50px',
          minWidth: '50px',
          align: 'center',
          isSticky: true,
          stickyPosition: 0
        } as any
      });
    }
    
    // Add data columns
    columns.forEach((column, index) => {
      // First column should be sticky if selectable is enabled
      const isFirstDataColumn = index === 0;
      const stickyPosition = selectable ? 50 : 0;
      
      cols.push({
        id: column.id,
        accessorFn: column.accessorFn || (row => row[column.id as keyof T]),
        header: column.header,
        cell: ({ row }) => column.cell(row.original),
        enableSorting: column.sortable ?? sortable,
        meta: {
          align: column.align,
          className: column.className,
          headerClassName: column.headerClassName,
          width: column.width,
          minWidth: column.minWidth,
          maxWidth: column.maxWidth,
          isSticky: isFirstDataColumn,
          stickyPosition: isFirstDataColumn ? stickyPosition : undefined
        } as any
      });
    });
    
    // Add actions column if there are actions
    if (actions.length > 0) {
      cols.push({
        id: 'actions',
        cell: ({ row }) => {
          const item = row.original;
          // Filter actions based on hideOnCondition
          const visibleActions = actions.filter(action => 
            !action.hideOnCondition || !action.hideOnCondition(item)
          );
          
          if (visibleActions.length === 0) return null;
          
          return (
            <div className="flex justify-end">
              {actionButtonVariant === 'dropdown' ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="icon" className={getSizeClasses.iconButton}>
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className={getSizeClasses.icon} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className={getSizeClasses.text}>
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    {visibleActions.map((action, actionIndex) => {
                      const isDisabled = typeof action.disabled === 'function' 
                        ? action.disabled(item) 
                        : action.disabled;
                        
                      return (
                        <React.Fragment key={actionIndex}>
                          {actionIndex > 0 && action.variant === 'destructive' && <DropdownMenuSeparator />}
                          <DropdownMenuItem 
                            className={cn(
                              "cursor-pointer",
                              action.variant === 'destructive' && "text-destructive",
                              isDisabled && "opacity-50 pointer-events-none"
                            )}
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!isDisabled) {
                                handleActionClick(action, item);
                              }
                            }}
                            disabled={isDisabled}
                          >
                            {action.icon && <span className="mr-2">{action.icon}</span>}
                            {action.label}
                          </DropdownMenuItem>
                        </React.Fragment>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : actionButtonVariant === 'button' ? (
                <div className="flex gap-1">
                  {visibleActions.map((action, actionIndex) => {
                    const isDisabled = typeof action.disabled === 'function' 
                      ? action.disabled(item) 
                      : action.disabled;
                      
                    return (
                      <Button
                        key={actionIndex}
                        variant={action.variant || "ghost"}
                        size="sm"
                        className={getSizeClasses.text}
                        disabled={isDisabled}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isDisabled) {
                            handleActionClick(action, item);
                          }
                        }}
                        title={action.tooltip}
                      >
                        {action.icon && <span className="mr-2">{action.icon}</span>}
                        {action.label}
                      </Button>
                    );
                  })}
                </div>
              ) : (
                <div className="flex gap-1">
                  {visibleActions.map((action, actionIndex) => {
                    const isDisabled = typeof action.disabled === 'function' 
                      ? action.disabled(item) 
                      : action.disabled;
                      
                    return (
                      <Button
                        key={actionIndex}
                        variant={action.variant || "ghost"}
                        size="icon"
                        className={getSizeClasses.iconButton}
                        disabled={isDisabled}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isDisabled) {
                            handleActionClick(action, item);
                          }
                        }}
                        title={action.tooltip}
                      >
                        {action.icon ? 
                          <span className={getSizeClasses.icon}>{action.label.charAt(0)}</span> : 
                          action.label.charAt(0)
                        }
                      </Button>
                    );
                  })}
                </div>
              )}
            </div>
          );
        },
        enableSorting: false,
        size: 50,
        meta: {
          width: '50px',
          align: 'right'
        } as any
      });
    }
    
    return cols;
  }, [columns, actions, selectable, sortable, actionButtonVariant, getSizeClasses]);
  
  // Initialize sorting state from defaultSort
  const [sorting, setSorting] = useState<SortingState>(() => {
    if (!defaultSort) return [];
    return [{ id: defaultSort.column, desc: defaultSort.direction === 'desc' }];
  });
  
  // Initialize row selection state from selectedItems
  const [rowSelection, setRowSelection] = useState<RowSelectionState>(() => {
    const selection: RowSelectionState = {};
    items.forEach((item, index) => {
      const id = keyExtractor(item);
      if (selectedItems.has(id)) {
        selection[index] = true;
      }
    });
    return selection;
  });
  
  // Initialize pagination state
  const [{ pageIndex: localPageIndex, pageSize: localPageSize }, setPagination] = useState({
    pageIndex: pageIndex,
    pageSize: pageSize
  });
  
  // Create the table instance
  const table = useReactTable({
    data: items,
    columns: tableColumns,
    state: {
      sorting,
      rowSelection,
      pagination: {
        pageIndex: localPageIndex,
        pageSize: localPageSize
      }
    },
    enableRowSelection: selectable,
    enableMultiRowSelection: true,
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      
      if (onSortChange && newSorting.length > 0) {
        onSortChange({
          column: newSorting[0].id,
          direction: newSorting[0].desc ? 'desc' : 'asc'
        });
      }
    },
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      
      if (onSelectionChange) {
        const selectedIds = new Set<string>();
        Object.entries(newSelection).forEach(([index, selected]) => {
          if (selected) {
            const item = items[parseInt(index)];
            selectedIds.add(keyExtractor(item));
          }
        });
        onSelectionChange(selectedIds);
      }
    },
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' 
        ? updater({ pageIndex: localPageIndex, pageSize: localPageSize }) 
        : updater;
      
      setPagination(newPagination);
      
      if (onPaginationChange) {
        onPaginationChange(newPagination.pageIndex, newPagination.pageSize);
      }
    },
    manualSorting,
    manualPagination,
    pageCount: pageCount ?? -1,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });
  
  // Handle action click with confirmation if needed
  const handleActionClick = (action: CardListAction<T>, item: T) => {
    if (action.confirmMessage) {
      setConfirmAction({ action, item });
    } else {
      action.onClick(item);
    }
  };
  
  // Confirm action execution
  const confirmActionExecution = () => {
    if (confirmAction) {
      confirmAction.action.onClick(confirmAction.item);
      setConfirmAction(null);
    }
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className={cn("flex flex-col border rounded-lg overflow-hidden bg-card", className)}>
        {/* Table with fixed layout to maintain column widths */}
        <div className="table w-full table-fixed">
          {/* Table header */}
          {showHeader && (
            <div className="table-header-group">
              <div className="table-row">
                {selectable && (
                  <div className="table-cell align-middle bg-muted text-muted-foreground font-medium sticky z-10 left-0" style={{ width: '50px', minWidth: '50px' }}>
                    <div className={getSizeClasses.headerPadding}></div>
                  </div>
                )}
                
                {columns.map((column, index) => {
                  const isFirstDataColumn = index === 0;
                  const stickyPosition = selectable ? 50 : 0;
                  
                  return (
                    <div 
                      key={column.id}
                      className={cn(
                        "table-cell align-middle bg-muted text-muted-foreground font-medium",
                        getSizeClasses.text,
                        getSizeClasses.headerPadding,
                        column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left',
                        isFirstDataColumn && 'sticky z-10',
                        column.headerClassName
                      )}
                      style={{
                        width: column.width,
                        minWidth: column.minWidth,
                        maxWidth: column.maxWidth,
                        ...(isFirstDataColumn ? { left: `${stickyPosition}px`, backgroundColor: 'transparent' } : {})
                      }}
                    >
                      {column.header}
                    </div>
                  );
                })}
                
                {actions.length > 0 && (
                  <div className="table-cell align-middle bg-muted text-muted-foreground font-medium" style={{ width: '50px', minWidth: '50px' }}>
                    <div className={getSizeClasses.headerPadding}></div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Loading skeleton rows */}
          <div className="table-row-group">
            {Array.from({ length: loadingSkeletonCount }).map((_, index) => (
              <div 
                key={index}
                className="table-row border-b border-border"
              >
                {selectable && (
                  <div className="table-cell align-middle sticky z-10 left-0" style={{ width: '50px', minWidth: '50px', backgroundColor: 'var(--card)' }}>
                    <div className={cn("flex justify-center items-center", getSizeClasses.padding)}>
                      <Skeleton className={cn("rounded-sm", getSizeClasses.checkbox)} />
                    </div>
                  </div>
                )}
                
                {columns.map((column, colIndex) => {
                  const isFirstDataColumn = colIndex === 0;
                  const stickyPosition = selectable ? 50 : 0;
                  
                  return (
                    <div 
                      key={colIndex}
                      className={cn(
                        "table-cell align-middle",
                        getSizeClasses.text,
                        getSizeClasses.padding,
                        column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left',
                        isFirstDataColumn && 'sticky z-10',
                        column.className
                      )}
                      style={{
                        ...(isFirstDataColumn ? { 
                          left: `${stickyPosition}px`, 
                          backgroundColor: 'var(--card)' 
                        } : {})
                      }}
                    >
                      <Skeleton className={`h-4 ${colIndex === 0 ? 'w-3/4' : 'w-1/2'} rounded-md`} />
                    </div>
                  );
                })}
                
                {actions.length > 0 && (
                  <div className="table-cell align-middle">
                    <div className="flex justify-center items-center">
                      <Skeleton className={cn("rounded-full", getSizeClasses.iconButton)} />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  // Render empty state
  if (items.length === 0) {
    return (
      <div className={cn("border rounded-lg overflow-hidden bg-card", className)}>
        {showHeader && (
          <div className="table w-full table-fixed">
            <div className="table-header-group">
              <div className="table-row">
                {selectable && (
                  <div className="table-cell align-middle bg-muted text-muted-foreground font-medium sticky z-10 left-0" style={{ width: '50px', minWidth: '50px' }}>
                    <div className={getSizeClasses.headerPadding}></div>
                  </div>
                )}
                
                {columns.map((column, index) => {
                  const isFirstDataColumn = index === 0;
                  const stickyPosition = selectable ? 50 : 0;
                  
                  return (
                    <div 
                      key={column.id}
                      className={cn(
                        "table-cell align-middle bg-muted text-muted-foreground font-medium",
                        getSizeClasses.text,
                        getSizeClasses.headerPadding,
                        column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left',
                        isFirstDataColumn && 'sticky z-10',
                        column.headerClassName
                      )}
                      style={{
                        width: column.width,
                        minWidth: column.minWidth,
                        maxWidth: column.maxWidth,
                        ...(isFirstDataColumn ? { left: `${stickyPosition}px`, backgroundColor: 'transparent' } : {})
                      }}
                    >
                      {column.header}
                    </div>
                  );
                })}
                
                {actions.length > 0 && (
                  <div className="table-cell align-middle bg-muted text-muted-foreground font-medium" style={{ width: '50px', minWidth: '50px' }}>
                    <div className={getSizeClasses.headerPadding}></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        <div className="h-32 flex flex-col items-center justify-center text-muted-foreground p-4 text-center">
          {emptyMessage}
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn(
      "flex flex-col border rounded-lg overflow-hidden bg-card",
      className
    )}>
      <div className={cn(
        "flex flex-col relative", // Relative positioning for sticky columns
        maxHeight && `max-h-[${maxHeight}]`,
        horizontalScroll && "overflow-auto" // Apply overflow to this container
      )}>
        {/* Table with fixed layout to maintain column widths */}
        <div className="table w-full table-fixed">
          {/* Table header */}
          {showHeader && (
            <div className={cn(
              "table-header-group",
              stickyHeader && "sticky top-0 z-10"
            )}>
              <div className="table-row">
                {table.getHeaderGroups().map(headerGroup => (
                  <React.Fragment key={headerGroup.id}>
                    {headerGroup.headers.map(header => {
                      const meta = header.column.columnDef.meta as any;
                      const width = meta?.width;
                      const minWidth = meta?.minWidth;
                      const maxWidth = meta?.maxWidth;
                      const isSticky = meta?.isSticky;
                      const stickyPosition = meta?.stickyPosition;
                      
                      return (
                        <div
                          key={header.id}
                          className={cn(
                            "table-cell align-middle bg-muted text-muted-foreground font-medium",
                            getSizeClasses.text,
                            getSizeClasses.headerPadding,
                            meta?.align === 'center' ? 'text-center' : meta?.align === 'right' ? 'text-right' : 'text-left',
                            header.column.getCanSort() && 'cursor-pointer select-none',
                            isSticky && 'sticky z-10',
                            meta?.headerClassName
                          )}
                          style={{
                            width,
                            minWidth,
                            maxWidth,
                            ...(isSticky ? { 
                              left: `${stickyPosition}px`, 
                              backgroundColor: 'transparent' 
                            } : {})
                          }}
                          onClick={header.column.getCanSort() 
                            ? header.column.getToggleSortingHandler() 
                            : undefined}
                        >
                          <div className="flex items-center gap-1 justify-start">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <div className="flex flex-col ml-1">
                                {header.column.getIsSorted() ? (
                                  header.column.getIsSorted() === 'asc' ? (
                                    <ChevronUp className={getSizeClasses.icon} />
                                  ) : (
                                    <ChevronDown className={getSizeClasses.icon} />
                                  )
                                ) : (
                                  <div className={cn("opacity-0", getSizeClasses.icon)}>•</div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
          
          {/* Table body */}
          <div className="table-row-group">
            {table.getRowModel().rows.map((row, rowIndex) => {
              const item = row.original;
              const isSelected = row.getIsSelected();
              
              return (
                <div
                  key={row.id}
                  className={cn(
                    "table-row border-b border-border",
                    highlightOnHover && "hover:bg-muted/50 transition-colors",
                    onItemClick && "cursor-pointer",
                    isSelected && "bg-muted/70",
                    zebra && rowIndex % 2 === 1 && "bg-muted/20",
                    typeof rowClassName === 'function' ? rowClassName(item, rowIndex) : rowClassName
                  )}
                  onClick={onItemClick ? () => onItemClick(item) : undefined}
                >
                  {row.getVisibleCells().map(cell => {
                    const meta = cell.column.columnDef.meta as any;
                    const isSticky = meta?.isSticky;
                    const stickyPosition = meta?.stickyPosition;
                    
                    return (
                      <div
                        key={cell.id}
                        className={cn(
                          "table-cell align-middle whitespace-nowrap",
                          getSizeClasses.text,
                          getSizeClasses.padding,
                          meta?.align === 'center' ? 'text-center' : meta?.align === 'right' ? 'text-right' : 'text-left',
                          isSticky && 'sticky z-10',
                          meta?.className
                        )}
                        style={{
                          ...(isSticky ? { 
                            left: `${stickyPosition}px`, 
                            backgroundColor: isSelected ? 'var(--muted-70)' : 
                                             zebra && rowIndex % 2 === 1 ? 'var(--muted-20)' : 
                                             'var(--card)' 
                          } : {})
                        }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* Pagination controls */}
      {pagination && (
        <div className={cn(
          "flex items-center justify-between border-t border-border",
          getSizeClasses.text,
          getSizeClasses.padding
        )}>
          <div className="flex-1 text-muted-foreground">
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} entries
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className={getSizeClasses.text}
            >
              Previous
            </Button>
            <div className="text-muted-foreground">
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className={getSizeClasses.text}
            >
              Next
            </Button>
          </div>
        </div>
      )}
      
      {/* Confirmation dialog */}
      {confirmAction && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-2">Confirm Action</h3>
            <p className="mb-4">{confirmAction.action.confirmMessage}</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setConfirmAction(null)}>
                Cancel
              </Button>
              <Button variant={confirmAction.action.variant || "default"} onClick={confirmActionExecution}>
                Confirm
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 