import { z } from "zod";
import { FileSchema } from "@/zod/files-schema";
import { File } from "lucide-react";

type Document = z.infer<typeof FileSchema>;

interface LinkedDocumentProps {
  document: Document;
}

// Utility function for date formatting
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

export default function LinkedDocument({ document }: LinkedDocumentProps) {
  const getFileIcon = () => {
    switch (document.fileExtension?.toLowerCase()) {
      case "pdf":
        return <File className="h-4 w-4 text-red-700 dark:text-red-400" />;
      case "docx":
        return <File className="h-4 w-4 text-blue-700 dark:text-blue-400" />;
      default:
        return <File className="h-4 w-4 text-gray-700 dark:text-gray-400" />;
    }
  };

  const getFileColor = () => {
    switch (document.fileExtension?.toLowerCase()) {
      case "pdf":
        return "bg-red-100 dark:bg-red-900/20";
      case "docx":
        return "bg-blue-100 dark:bg-blue-900/20";
      default:
        return "bg-gray-100 dark:bg-gray-800";
    }
  };

  return (
    <div className="fojo-related-item group">
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 rounded-md ${getFileColor()} p-2 mt-1`}>
          {getFileIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-2">
            <p className="font-medium text-sm group-hover:text-primary transition-colors">
              {document.name}
            </p>
            <span className="text-xs text-muted-foreground">
              {document.fileExtension?.toUpperCase() || 'UNKNOWN'}
            </span>
          </div>
          
          {document.updated_at && (
            <p className="text-xs text-muted-foreground mt-1">
              Updated {formatDate(document.updated_at)}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
