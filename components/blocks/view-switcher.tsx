'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { LayoutGrid, LayoutList, Trello } from 'lucide-react';

export interface ViewSwitcherProps {
  viewMode: 'card' | 'list' | 'kanban';
  setViewMode: (mode: 'card' | 'list' | 'kanban') => void;
  className?: string;
}

/**
 * ViewSwitcher - A component to toggle between different view modes
 * 
 * This reusable component provides a standardized way to switch 
 * between different view modes in dashboard pages.
 * 
 * Supports:
 * - Card view: Grid layout of cards
 * - List view: Tabular data presentation
 * - Kanban view: Drag-and-drop board with columns
 */
export default function ViewSwitcher({
  viewMode,
  setViewMode,
  className = ''
}: ViewSwitcherProps) {
  return (
    <div className={`flex space-x-2 ${className}`}>
      <Button
        variant="outline"
        size="icon"
        onClick={() => setViewMode('card')}
        className={
          viewMode === 'card'
            ? 'bg-border hover:bg-border/90'
            : 'bg-background hover:bg-background/90'
        }
        aria-label="Card view"
      >
        <LayoutGrid className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={() => setViewMode('list')}
        className={
          viewMode === 'list'
            ? 'bg-border hover:bg-border/90'
            : 'bg-background hover:bg-background/90'
        }
        aria-label="List view"
      >
        <LayoutList className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={() => setViewMode('kanban')}
        className={
          viewMode === 'kanban'
            ? 'bg-border hover:bg-border/90'
            : 'bg-background hover:bg-background/90'
        }
        aria-label="Kanban view"
      >
        <Trello className="h-4 w-4" />
      </Button>
    </div>
  );
}
