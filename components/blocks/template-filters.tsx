'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

export interface FilterOption {
  value: string;
  label: string;
}

export interface TemplateFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  // Primary filter (usually status)
  primaryFilter: string | null;
  onPrimaryFilterChange: (value: string | null) => void;
  primaryFilterOptions: FilterOption[];
  primaryFilterLabel?: string;
  // Secondary filter (usually priority)
  secondaryFilter: string | null;
  onSecondaryFilterChange: (value: string | null) => void;
  secondaryFilterOptions: FilterOption[];
  secondaryFilterLabel?: string;
  // Optional additional filters
  additionalFilters?: {
    id: string;
    value: string | null;
    onChange: (value: string | null) => void;
    options: FilterOption[];
    label: string;
  }[];
  title?: string;
  description?: string;
}

/**
 * TemplateFilters - A reusable filter component
 * 
 * This component provides a standard way to filter items by search term,
 * and dropdown filters (typically status and priority).
 */
export default function TemplateFilters({
  searchTerm,
  onSearchChange,
  primaryFilter,
  onPrimaryFilterChange,
  primaryFilterOptions,
  primaryFilterLabel = 'Status',
  secondaryFilter,
  onSecondaryFilterChange,
  secondaryFilterOptions,
  secondaryFilterLabel = 'Priority',
  additionalFilters = [],
}: TemplateFiltersProps) {
  return (
    <div className="mb-6">

      {/* Filters section */}
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search field */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
        </div>

        {/* Primary filter (usually status) */}
        <div className="w-full md:w-48">
          <Select
            value={primaryFilter || 'all'}
            onValueChange={(value) =>
              onPrimaryFilterChange(value === 'all' ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={primaryFilterLabel} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All {primaryFilterLabel}es</SelectItem>
              {primaryFilterOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Secondary filter (usually priority) */}
        <div className="w-full md:w-48">
          <Select
            value={secondaryFilter || 'all'}
            onValueChange={(value) =>
              onSecondaryFilterChange(value === 'all' ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={secondaryFilterLabel} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All {secondaryFilterLabel}es</SelectItem>
              {secondaryFilterOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Additional filters */}
        {additionalFilters.map((filter) => (
          <div key={filter.id} className="w-full md:w-48">
            <Select
              value={filter.value || 'all'}
              onValueChange={(value) =>
                filter.onChange(value === 'all' ? null : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.label} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All {filter.label}s</SelectItem>
                {filter.options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}
      </div>
    </div>
  );
}
