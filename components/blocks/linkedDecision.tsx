import { z } from "zod";
import { Decision as DecisionSchema } from "@/zod/decisions-schema";
import { GitBranchPlus } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

type Decision = z.infer<typeof DecisionSchema>;

interface LinkedDecisionProps {
  decision: Decision;
}

export default function LinkedDecision({ decision }: LinkedDecisionProps) {
  const getStatusBadge = () => {
    switch (decision.status) {
      case "approved":
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">Approved</Badge>;
      case "rejected":
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800">Rejected</Badge>;
      case "in_review":
      case "on_hold":
      case "escalated":
      case "cancelled":
      default:
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800">Pending</Badge>;
    }
  };

  return (
    <Link href={`/decisions/${decision._id}`} className="block no-underline text-foreground">
      <div className="fojo-related-item group">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 rounded-md bg-purple-100 dark:bg-purple-900/20 p-2 mt-1">
            <GitBranchPlus className="h-4 w-4 text-purple-700 dark:text-purple-400" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-2">
              <p className="font-medium text-sm group-hover:text-primary transition-colors">
                {decision.title}
              </p>
              {decision.status && getStatusBadge()}
            </div>
            
            {decision.summary && (
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {decision.summary}
              </p>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
