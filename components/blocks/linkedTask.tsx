import type { z } from "zod";
import { Project, ProjectSchema } from "@/zod/projects-schema";
import { Task } from "@/zod/tasks-schema";
import { Card, CardContent } from "@/components/ui/card";
import { Layers } from "lucide-react";
import Link from "next/link";

type TaskType = z.infer<typeof Task>;

interface LinkedTaskProps {
  task: TaskType;
}

export default function LinkedTask({ task }: LinkedTaskProps) {
  return (
    <Link href={`/tasks/${task._id}`} className="block no-underline text-foreground">
      <div className="fojo-related-item group">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 rounded-md bg-blue-100 dark:bg-blue-900/20 p-2 mt-1">
            <Layers className="h-4 w-4 text-blue-700 dark:text-blue-400" />
          </div>
          
          <div className="flex-1 min-w-0">
            <p className="font-medium text-sm group-hover:text-primary transition-colors">
              {task.name}
            </p>
            
            {task.description && (
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
