'use client';
import { Card } from '@/components/ui/card';
import React, { useMemo } from 'react';
import Link from 'next/link';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChevronUp, ChevronDown, ArrowUpDown, ArrowUp, ArrowDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  PaginationState
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';

// Define a generic item interface that can be extended for specific domains
export interface TemplateTableItem {
  id: string;
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  progress?: number;
  lastUpdated?: string | number;
  team?: { _id: string; name: string; image?: string }[];
  [key: string]: any; // Allow for additional properties based on domain needs
}

interface TemplateTableProps {
  items: TemplateTableItem[];
  selectedItems: string[];
  onItemCheckboxChange: (checked: boolean, id: string) => void;
  onSelectAll: (checked: boolean) => void;
  // Optional props to customize the table behavior
  baseRoute?: string;
  columns?: ColumnDef<TemplateTableItem>[];
  paginationEnabled?: boolean;
}

/**
 * TemplateTable - A reusable table component
 * 
 * This component can be used to display any collection of items in a table format
 * with support for sorting, selection, and customization of columns.
 */
const TemplateTable = ({
  items,
  selectedItems,
  onItemCheckboxChange,
  onSelectAll,
  baseRoute = '/items',
  columns: customColumns,
  paginationEnabled = false
}: TemplateTableProps) => {
  // State for sorting
  const [sorting, setSorting] = React.useState<SortingState>([]);
  // State for pagination (if paginationEnabled is true)
  const [pagination, setPagination] = React.useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  // State for search term
  const [searchTerm, setSearchTerm] = React.useState("");

  // Filter items based on search term (searching in title)
  const filteredItems = useMemo(() => {
    return items.filter(item =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [items, searchTerm]);

  /**
   * Helper function to get status color based on item status
   */
  const getStatusColor = (status?: string) => {
    if (!status) return 'bg-gray-200 text-gray-700';

    switch (status.toLowerCase()) {
      case 'active':
      case 'in_progress':
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-200 text-yellow-800';
      case 'draft':
      case 'not_started':
      case 'not started':
        return 'bg-gray-200 text-gray-700';
      case 'completed':
      case 'done':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
      case 'canceled':
        return 'bg-red-100 text-red-800';
      case 'perpetual':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  /**
   * Helper function to get priority color
   */
  const getPriorityColor = (priority?: string) => {
    if (!priority) return 'text-gray-600';
    
    switch (priority.toLowerCase()) {
      case 'high':
      case 'urgent':
        return 'text-orange-600';
      case 'medium':
        return 'text-blue-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  /**
   * Component to display priority with chevrons
   */
  const PriorityChevrons = ({ priority }: { priority?: string }) => {
    if (!priority) return null;
    
    const color = getPriorityColor(priority);
    const className = `h-4 w-4 ${color} stroke-[3]`;

    switch (priority.toLowerCase()) {
      case 'high':
      case 'urgent':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-[-2px]">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[4px]">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[10px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      case 'medium':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-0">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[6px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      case 'low':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-[4px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  /**
   * Format status for display
   */
  const formatStatus = (status?: string) => {
    if (!status) return '';
    // Handle both schema enum values and string values
    const displayStatus = String(status).replace('_', ' ');
    return displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1);
  };

  /**
   * Define default columns that can be overridden by custom columns
   */
  const defaultColumns = useMemo<ColumnDef<TemplateTableItem>[]>(
    () => [
      // Selection column
      {
        id: 'select',
        header: ({ table }) => (
          <div className="flex items-center">
            <Checkbox
              checked={
                selectedItems.length === items.length &&
                items.length > 0
              }
              onCheckedChange={(checked: boolean) => {
                onSelectAll(checked);
              }}
              className="border-muted-foreground"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center">
            <Checkbox
              checked={selectedItems.includes(row.original.id.toString())}
              onCheckedChange={(checked: boolean) =>
                onItemCheckboxChange(checked, row.original.id)
              }
              className="border-muted-foreground"
            />
          </div>
        ),
        enableSorting: false,
        size: 50
      },

      // Title and description
      {
        accessorKey: 'title',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Title
              {sort === 'asc' && <ArrowUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ArrowDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          <div className="flex flex-col">
            <Link
              href={`${baseRoute}/${row.original.id}`}
              className="hover:underline"
            >
              <div className="font-medium truncate">{row.original.title}</div>
            </Link>
            {row.original.description && (
              <div className="text-sm text-muted-foreground line-clamp-1 mt-1">
                {row.original.description}
              </div>
            )}
          </div>
        ),
        size: 400
      },

      // Status
      {
        accessorKey: 'status',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Status
              {sort === 'asc' && <ChevronUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ChevronDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          row.original.status ? (
            <span
              className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(row.original.status)}`}
            >
              {formatStatus(row.original.status)}
            </span>
          ) : null
        )
      },

      // Priority
      {
        accessorKey: 'priority',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Priority
              {sort === 'asc' && <ChevronUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ChevronDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          <PriorityChevrons priority={row.original.priority} />
        ),
        sortingFn: (rowA, rowB, columnId) => {
          // Custom sorting function for priority
          const priorityOrder = { low: 0, medium: 1, high: 2, urgent: 3 };
          const priorityA = rowA.original.priority?.toLowerCase() as keyof typeof priorityOrder;
          const priorityB = rowB.original.priority?.toLowerCase() as keyof typeof priorityOrder;

          if (!priorityA) return priorityB ? -1 : 0;
          if (!priorityB) return 1;
          
          return priorityOrder[priorityA] - priorityOrder[priorityB];
        }
      },

      // Progress
      {
        accessorKey: 'progress',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Progress
              {sort === 'asc' && <ChevronUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ChevronDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          row.original.progress !== undefined ? (
            <div className="flex items-center space-x-2">
              <Progress
                value={row.original.progress}
                className={`w-24 h-3 ${
                  row.original.status === 'paused'
                    ? '[&>div]:bg-gray-400'
                    : '[&>div]:bg-blue-600'
                }`}
              />
              <span className="text-sm text-muted-foreground whitespace-nowrap">
                {row.original.progress}%
              </span>
            </div>
          ) : null
        )
      },

      // Team
      {
        id: 'team',
        header: 'Team',
        cell: ({ row }) => (
          row.original.team && row.original.team.length > 0 ? (
            <div className="flex -space-x-2">
              {row.original.team.map((member) => (
                <Avatar
                  key={member._id}
                  className="border-2 border-background w-8 h-8"
                >
                  <AvatarImage src={member.image} alt={member.name} />
                  <AvatarFallback>
                    {member.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
          ) : null
        ),
        enableSorting: false
      }
    ],
    [items, selectedItems, onItemCheckboxChange, onSelectAll, baseRoute]
  );

  // Use custom columns if provided, otherwise use default columns
  const tableColumns = customColumns || defaultColumns;

  // Initialize TanStack table with optional pagination using filteredItems
  const table = useReactTable({
    data: filteredItems,
    columns: tableColumns,
    state: {
      sorting,
      ...(paginationEnabled ? { pagination } : {})
    },
    onSortingChange: setSorting,
    ...(paginationEnabled
      ? { getPaginationRowModel: getPaginationRowModel(), onPaginationChange: setPagination }
      : {}),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel()
  });

  return (
    <>
         {/* Search Bar */}
         <div className="mb-4">
         <Input
           placeholder="Search..."
           value={searchTerm}
           onChange={(e) => setSearchTerm(e.target.value)}
           className="w-64"
         />
       </div>


      <Card>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} className="border-b bg-muted/50">
              {headerGroup.headers.map((header) => (
                <TableHead
                  key={header.id}
                  className={`text-left p-1 text-sm font-medium ${
                    header.id === 'select' ? 'w-[50px] px-5'  : ''
                  } ${header.id === 'title' ? 'pl-2 pr-3 w-[400px]' : ''}`}
                  style={{
                    width:
                      header.getSize() !== 150 ? header.getSize() : undefined
                  }}
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : ''
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </div>
                  )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={tableColumns.length}
                className="p-4 text-center text-muted-foreground"
              >
                No items found
              </TableCell>
            </TableRow>
          ) : (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className="border-b last:border-b-0 hover:bg-muted/50"
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={`p-1 ${
                      cell.column.id === 'select' ? 'w-[50px] px-5' : ''
                    } ${
                      cell.column.id === 'title'
                        ? 'pl-2 pr-3 max-w-[400px]'
                        : ''
                    } ${cell.column.id === 'progress' ? 'pl-3 pr-1' : ''}`}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      {paginationEnabled && (
        <div className="flex items-center justify-center space-x-2 py-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="bg-white/50"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm text-slate-600">
            Page {pagination.pageIndex + 1} of {table.getPageCount()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="bg-white/50"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
      </Card>
      </>
  );
};

export default TemplateTable;
