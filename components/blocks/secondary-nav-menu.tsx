'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Define the interface for navigation items with generic props
interface NavItem {
  label: string;
  href: string;
  onClick?: () => void;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  isExternal?: boolean;
  className?: string;
  [key: string]: any; // Allow any additional props
}

interface SecondaryNavMenuProps {
  items: NavItem[];
  className?: string;
  activeItemClassName?: string;
  activeItem?: string;
  onItemClick?: (item: NavItem) => void;
  size?: 'default' | 'sm' | 'xs';
}

function SecondaryNavMenu({
  items,
  className = '',
  activeItemClassName = '',
  activeItem,
  onItemClick,
  size = 'default'
}: SecondaryNavMenuProps) {
  const pathname = usePathname();

  // Early return if no items are provided
  if (!items || items.length === 0) {
    return null;
  }

  // Determine the current active tab based on pathname or activeItem prop
  const currentTab =
    activeItem ||
    items.find((item) => pathname === item.href)?.href ||
    items[0].href;

  return (
    <Tabs value={currentTab} className={cn('w-full', className)}>
      <div className="relative mb-2 sm:mb-4">
        <TabsList className="flex flex-wrap gap-1 bg-muted/50 p-1 h-auto">
          {items.map((item, index) => (
            <TabsTrigger
              key={index}
              value={item.href}
              className={cn(
                'cursor-pointer whitespace-nowrap text-sm py-1.5 px-2.5 h-auto data-[state=active]:bg-background rounded-md',
                item.className,
                pathname === item.href && activeItemClassName
              )}
              asChild
            >
              {item.isExternal ? (
                <a
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => onItemClick?.(item)}
                  className="flex items-center gap-1.5"
                >
                  {item.icon}
                  {item.label}
                  {item.badge}
                </a>
              ) : (
                <Link
                  href={item.href}
                  onClick={() => onItemClick?.(item)}
                  className="flex items-center gap-1.5"
                >
                  {item.icon}
                  {item.label}
                  {item.badge}
                </Link>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
      </div>
    </Tabs>
  );
}

export default SecondaryNavMenu;
