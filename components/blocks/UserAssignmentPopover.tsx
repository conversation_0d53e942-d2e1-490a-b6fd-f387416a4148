"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel';
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/hooks/use-toast";
import { useDebounce } from 'use-debounce';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Users, Loader2, Check } from 'lucide-react'; // Import Users icon for teams, Loader, and Check
import { Badge } from "@/components/ui/badge"; // Import Badge for non-user indicator
import { cn } from "@/lib/utils"; // Import cn for conditional classes

type DciRoleAssign = 'Driver' | 'Contributor' | 'Informed' | 'Requestor';
type AssigneeType = 'user' | 'team' | 'person'; // Add person type for Requestor role

// Define the structure for search results explicitly
interface SearchResult {
  _id: Id<'people'> | Id<'teams'>;
  _creationTime: number;
  type: AssigneeType;
  name: string;
  image?: string;
  user_id?: Id<'users'>;
  email?: string;
  phone?: string;
}

interface UserAssignmentPopoverProps {
  entityId: string;
  entityType: 'task' | 'project' | 'decision';
  targetRole: DciRoleAssign;
  triggerButton: React.ReactNode;
  existingMemberIds?: Set<Id<'users'> | Id<'teams'> | Id<'people'>>; // Allow all types
}

const UserAssignmentPopover = ({
  entityId,
  entityType,
  targetRole,
  triggerButton,
  existingMemberIds = new Set(),
}: UserAssignmentPopoverProps) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 50); // Reduced to 50ms as requested
  const [isAssigning, setIsAssigning] = useState<string | null>(null); // Track which item is being assigned
  const [focusedIndex, setFocusedIndex] = useState<number>(-1); // State for keyboard navigation
  const listRef = React.useRef<HTMLDivElement>(null); // Ref for the list container

  // Determine query parameters based on targetRole
  const getQueryParams = (): { search?: string; limit: number; includeNonUsers: boolean; type: 'user' | 'all' } => {
    if (targetRole === 'Driver') {
      return { 
        search: debouncedSearchTerm, 
        limit: 20,
        includeNonUsers: false,
        type: 'user' as const
      };
    } else if (targetRole === 'Requestor') {
      return { 
        search: debouncedSearchTerm, 
        limit: 20,
        includeNonUsers: true,
        type: 'all' as const
      };
    } else {
      // Contributor or Informed
      return { 
        search: debouncedSearchTerm, 
        limit: 20,
        includeNonUsers: false,
        type: 'all' as const
      };
    }
  };

  // Combined search for all entity types
  const searchResultsData = useQuery(
    api.directory.directory.searchPeopleAndTeams,
    isOpen ? getQueryParams() : 'skip'
  );

  const assignRoleMutation = useMutation(api.assignments.assignRoleToEntity);

  // Filter available entities based on role and existing members
  const availableEntities: SearchResult[] = React.useMemo(() => {
    if (!searchResultsData || !Array.isArray(searchResultsData)) return [];

    return searchResultsData.filter(entity => {
      // Common check: exclude already assigned members
      let isExisting = false;
      if (entity.type === 'user' && entity.user_id) {
        isExisting = existingMemberIds?.has(entity.user_id);
      } else if (entity.type === 'team') {
        isExisting = existingMemberIds?.has(entity._id as Id<'teams'>);
      } else if (entity.type === 'person') {
        isExisting = existingMemberIds?.has(entity._id as Id<'people'>);
      }
      if (isExisting) return false;

      // Role-specific filtering
      if (targetRole === 'Driver') {
        return entity.type === 'user'; // Only users
      }
      if (targetRole === 'Requestor') {
        // Show ALL people (users and non-users) and ALL teams for requestor assignment
        return entity.type === 'user' || entity.type === 'person' || entity.type === 'team';
      }
      // Contributor/Informed: Allow users and teams (already filtered by query for includeNonUsers=false)
      return entity.type === 'user' || entity.type === 'team';
    });
  }, [searchResultsData, existingMemberIds, targetRole]);

  // Handle direct assignment on click
  const handleAssign = async (id: Id<'users'> | Id<'teams'> | Id<'people'>, type: AssigneeType) => {
    // Validate assignment based on role
    if (targetRole === 'Driver' && (type === 'team' || type === 'person')) {
      toast({ title: "Invalid Selection", description: "Only users can be assigned as Driver.", variant: "destructive" });
      return;
    }
    
    // For Requestor, allow any person (user or non-user) and teams. No restriction needed here.

    // Set the ID being assigned to show loading state
    setIsAssigning(id.toString());
    
    try {
      // Prepare the mutation arguments
      const mutationArgs = {
        entityId,
        entityType,
        assigneeId: id,
        dciRole: targetRole,
      };
      
      await assignRoleMutation(mutationArgs);
      
      toast({
        title: "Success",
        description: `Assigned as ${targetRole}.`,
      });
      setIsOpen(false); // Close popover on success
    } catch (error) {
      console.error("Failed to assign role:", error);
      toast({
        title: "Assignment Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(null);
    }
  };

  // Reset state and handle focus when popover opens or closes
  useEffect(() => {
    if (!isOpen) {
      // Reset state when closing
      setSearchTerm('');
      setIsAssigning(null);
      setFocusedIndex(-1);
    } else {
      // Reset focus index when opening
      setFocusedIndex(-1);
      // We don't auto-focus the list anymore, focus starts in Input
    }
  }, [isOpen]); // Dependency is only isOpen

  // Handle Input KeyDown for transferring focus to list
  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'ArrowDown' && availableEntities.length > 0) {
      event.preventDefault();
      setFocusedIndex(0); // Focus the first item
      listRef.current?.focus(); // Focus the list container
    }
    // Allow other keys (like typing) to function normally
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (!availableEntities.length) return;

    let nextIndex = focusedIndex;

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      nextIndex = (focusedIndex + 1) % availableEntities.length;
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      nextIndex = (focusedIndex - 1 + availableEntities.length) % availableEntities.length;
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (focusedIndex >= 0 && focusedIndex < availableEntities.length) {
        const entity = availableEntities[focusedIndex];
        const { assignId, assignType } = getAssignDetails(entity);
        if (!isAssigning) { // Prevent assigning if already in progress
          handleAssign(assignId, assignType);
        }
      }
      return; // Don't update focus on Enter/Space
    } else if (event.key === 'Home') {
       event.preventDefault();
       nextIndex = 0;
    } else if (event.key === 'End') {
       event.preventDefault();
       nextIndex = availableEntities.length - 1;
    } else {
      return; // Ignore other keys
    }

    setFocusedIndex(nextIndex);

    // Scroll the focused item into view
    const focusedElement = listRef.current?.children[nextIndex] as HTMLElement;
    focusedElement?.scrollIntoView({ block: 'nearest' });
  };

  // Helper to get assignment details from entity
  const getAssignDetails = (entity: SearchResult): { assignId: Id<'users'> | Id<'teams'> | Id<'people'>; assignType: AssigneeType } => {
    if (entity.type === 'person') {
      // Always use the people ID
      return { assignId: entity._id as Id<'people'>, assignType: 'person' };
    } else if (entity.type === 'team') {
      return { assignId: entity._id as Id<'teams'>, assignType: 'team' };
    } else {
      // entity.type === 'user'
      // For Requestor: use the people._id (not user_id), otherwise use user_id
      if (targetRole === 'Requestor' && entity._id) {
        return { assignId: entity._id as Id<'people'>, assignType: 'person' };
      }
      return { assignId: entity.user_id as Id<'users'>, assignType: 'user' };
    }
  };

  // Render an entity item (user, team, or person)
  const renderEntityItem = (entity: SearchResult, index: number) => {
    // Get assignment details once
    const { assignId: currentAssignId, assignType: currentAssignType } = getAssignDetails(entity);
    const isPersonType = currentAssignType === 'person';
    const isTeamType = currentAssignType === 'team';

    const isLoading = isAssigning === currentAssignId.toString();
    const isFocused = index === focusedIndex;

    return (
      // Use button for better accessibility and focus management
      <button
        key={currentAssignId} // Use the correct ID for the key
        type="button"
        role="option"
        aria-selected={isFocused}
        onClick={() => !isLoading && handleAssign(currentAssignId, currentAssignType)} // Use correct variables
        onFocus={() => setFocusedIndex(index)} // Update focus state on manual focus
        className={cn(
          "flex items-center gap-2 p-1.5 rounded cursor-pointer hover:bg-muted w-full text-left",
          isLoading ? 'opacity-70 cursor-not-allowed' : '',
          isFocused ? 'bg-muted ring-1 ring-ring' : '' // Highlight focused item
        )}
        disabled={isLoading}
      >
        <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
          {isLoading ? (
            <Loader2 className="h-3 w-3 animate-spin text-foreground" />
          ) : (
            // Optionally show a checkmark or other indicator if needed
            <div className="w-3 h-3" /> // Placeholder for alignment
          )}
        </div>

        {isTeamType ? (
          <div className="flex items-center justify-center h-5 w-5 bg-muted rounded-full flex-shrink-0" title={`Team: ${entity.name}`}>
            <Users className="h-3 w-3 text-muted-foreground" />
          </div>
        ) : (
          <Avatar className="h-5 w-5 flex-shrink-0">
            <AvatarImage src={entity.image ?? undefined} alt={entity.name ?? 'User'} />
            <AvatarFallback className="text-xs">{entity.name?.substring(0, 1).toUpperCase() || '?'}</AvatarFallback>
          </Avatar>
        )}

        <span className="text-sm truncate flex-grow">{entity.name}</span>

        {isPersonType && (
          <Badge variant="outline" className="ml-auto text-[10px] py-0 flex-shrink-0">Non-User</Badge>
        )}
      </button>
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {triggerButton}
      </PopoverTrigger>
      {/* Adjusted width to w-80 */}
       <PopoverContent className="w-80 p-0" side="bottom" align="start">
         <div className="p-2 border-b">
           <Input
             autoFocus // Restore autoFocus
             aria-label="Search assignees"
             value={searchTerm}
             onChange={(e) => setSearchTerm(e.target.value)}
             onKeyDown={handleInputKeyDown} // Add keydown handler to input
            placeholder="Search..."
            className="h-8 text-sm"
          />
        </div>
        {/* Added onKeyDown to ScrollArea's parent div for event delegation */}
        <ScrollArea className="h-[220px]">
          {searchResultsData === undefined && isOpen && (
            <p className="text-xs text-muted-foreground text-center p-2">Loading...</p>
          )}

          {availableEntities.length === 0 && isOpen && searchResultsData !== undefined && (
            <p className="text-xs text-muted-foreground text-center p-2">
              {debouncedSearchTerm ? "No matching results found." : "No results available."}
            </p>
          )}

          {/* Added ref and onKeyDown */}
          <div
            ref={listRef}
             className="space-y-1 p-2 outline-none" // Added outline-none
             onKeyDown={handleKeyDown}
              role="listbox" // ARIA role for the list container
              tabIndex={-1} // Make programmatically focusable but not via tab
              aria-activedescendant={focusedIndex >= 0 ? availableEntities[focusedIndex]?._id.toString() : undefined} // Indicate active descendant for screen readers - ensure ID is string
            >
              {availableEntities.map((entity, index) => renderEntityItem(entity, index))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

export default UserAssignmentPopover;
