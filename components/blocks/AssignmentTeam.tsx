import React, { useState } from 'react';
import { Users, Plus, UserCheck, UsersRound, Info, X, UserRound } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel';
import UserAssignmentPopover from './UserAssignmentPopover'; // Assuming this will be updated/replaced
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { useToast } from "@/components/hooks/use-toast";
import CollectionCardModal from '@/components/ui/collectionCardModal';
import { useEffect } from 'react'; // Import useEffect

// Define the type for team members using DCI model
type DciRole = 'Driver' | 'Contributor' | 'Informed' | 'Requestor' | 'None';
type DciRoleAssign = 'Driver' | 'Contributor' | 'Informed' | 'Requestor';

// Use the AssignmentMember interface (assuming it's defined or imported from assignments.ts if possible)
// If not defined elsewhere, define it here based on getTeamForEntity's output
interface AssignmentMember {
  id: Id<'users'> | Id<'teams'> | Id<'people'>;
  type: 'user' | 'team' | 'person';
  name: string;
  avatar?: string; // Optional for users and people
  initials: string;
  dciRole: DciRole;
  // Add other fields if needed from getTeamForEntity's return type
}

// Interface for member being considered for removal
interface MemberToRemove {
  id: Id<'users'> | Id<'teams'> | Id<'people'>;
  name: string;
  role: DciRole;
  type: 'user' | 'team' | 'person';
}


interface AssignmentTeamProps {
  entityId: string;
  entityType: 'task' | 'project' | 'decision';
}

const AssignmentTeam = ({ entityId, entityType }: AssignmentTeamProps) => {
  const { toast } = useToast();
  const [memberToRemove, setMemberToRemove] = useState<MemberToRemove | null>(null); // Updated state name
  const [optimisticMembers, setOptimisticMembers] = useState<AssignmentMember[] | undefined>(undefined); // Use AssignmentMember

  // Fetch the combined list of users and teams
  const assignmentResult = useQuery(api.assignments.getTeamForEntity, {
    entityId: entityId,
    entityType: entityType,
  });

  // Sync local state with query results
  useEffect(() => {
    // Ensure assignmentResult is treated as AssignmentMember[]
    setOptimisticMembers(assignmentResult as AssignmentMember[] | undefined); 
  }, [assignmentResult]);

  const assignRoleMutation = useMutation(api.assignments.assignRoleToEntity);
  const removeRoleMutation = useMutation(api.assignments.removeRoleFromEntity);

  // --- Drag and Drop Logic ---
  const onDragStart = (start: any) => {
    // Find the member being dragged to determine its type
    const draggedMember = optimisticMembers?.find(m => m.id === start.draggableId);
    if (draggedMember) {
      // Update the ref with the member type
      draggedItemTypeRef.current = draggedMember.type;
    }
  };

  const onDragEnd = (result: DropResult) => {
    const { source, destination, draggableId } = result;

    // Reset the dragged item type ref
    draggedItemTypeRef.current = null;

    if (!destination || (source.droppableId === destination.droppableId && source.index === destination.index)) {
      return;
    }

    let newRole: DciRoleAssign | null = null;
    if (destination.droppableId === 'droppable-driver') newRole = 'Driver';
    else if (destination.droppableId === 'droppable-contributor') newRole = 'Contributor';
    else if (destination.droppableId === 'droppable-informed') newRole = 'Informed';
    else if (destination.droppableId === 'droppable-requestor') newRole = 'Requestor';

    if (newRole && optimisticMembers) { // Use optimisticMembers
      const assigneeId = draggableId as Id<'users'> | Id<'teams'> | Id<'people'>;
      const draggedMember = optimisticMembers.find(m => m.id === assigneeId);

      // Prevent assigning a team as Driver
      if (newRole === 'Driver' && draggedMember?.type === 'team') {
        toast({ title: "Invalid Assignment", description: "Teams cannot be assigned as Driver.", variant: "destructive" });
        return; 
      }

      // Prevent assigning a user as Requestor
      if (newRole === 'Requestor' && draggedMember?.type !== 'person' && draggedMember?.type !== 'team') {
        toast({ title: "Invalid Assignment", description: "Only people or teams can be assigned as Requestor.", variant: "destructive" });
        return;
      }

      // --- Optimistic Update ---
      const previousMembers = optimisticMembers;
      let oldDriverId: Id<'users'> | null = null;

      // Handle Driver change logic
      if (newRole === 'Driver') {
        const currentDriver = previousMembers.find(m => m.dciRole === 'Driver');
        // Ensure assigneeId is a user ID before proceeding
        if (draggedMember?.type === 'user' && currentDriver && currentDriver.id !== assigneeId) {
          oldDriverId = currentDriver.id as Id<'users'>; // Driver must be user
        }
      }

      const updatedMembers = previousMembers.map(member => {
        // Assign new role to dragged member
        if (member.id === assigneeId) {
          return { ...member, dciRole: newRole! };
        }
        // If assigning a new Driver, move the old Driver to Contributor
        if (member.id === oldDriverId) {
          return { ...member, dciRole: 'Contributor' as DciRole };
        }
        return member;
      }).filter(Boolean) as AssignmentMember[]; // Ensure correct type after map

      setOptimisticMembers(updatedMembers);
      // --- End Optimistic Update ---

      // --- Call Mutations ---
      const mutationPromises = [];
      mutationPromises.push(assignRoleMutation({
        entityId,
        entityType,
        assigneeId: assigneeId, // Pass the correct ID (user or team)
        dciRole: newRole,
      }));

      if (oldDriverId) {
        mutationPromises.push(assignRoleMutation({
          entityId,
          entityType,
          assigneeId: oldDriverId, // Pass the old driver's user ID
          dciRole: 'Contributor',
        }));
      }

      Promise.all(mutationPromises).catch(error => {
        console.error("Failed to reassign role(s) via DnD:", error);
        toast({ title: "Reassignment Failed", description: "Could not update roles. Reverting change.", variant: "destructive" });
        setOptimisticMembers(previousMembers); // Revert UI
      });
    } else {
       console.warn("Dropped in unhandled zone:", destination.droppableId);
    }
  };

  // --- Removal Logic ---
  const handleConfirmRemove = async () => {
    if (!memberToRemove) return; // Use memberToRemove
    try {
      // Only users and teams can be removed with the current mutation
      if (memberToRemove.type === 'person') {
        toast({
          title: "Cannot Remove",
          description: "Removing people directly is not supported yet. Please contact the administrator.",
          variant: "destructive",
        });
        return;
      }
      
      await removeRoleMutation({
        entityId,
        entityType,
        assigneeId: memberToRemove.id as Id<'users'> | Id<'teams'>, // Cast to expected types
      });
      toast({
        title: "Success",
        description: `${memberToRemove.name} removed from ${memberToRemove.role} role.`,
      });
    } catch (error) {
      console.error("Failed to remove role:", error);
      toast({
        title: "Removal Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setMemberToRemove(null); // Use memberToRemove
    }
  };

  // Handle loading state
  if (optimisticMembers === undefined) { // Use optimisticMembers
    return <Card><CardHeader className="px-3 py-1.5"><CardTitle className="text-sm">Loading Team...</CardTitle></CardHeader></Card>; 
  }

  // Group members by DCI role using optimistic state
  const sortedMembers = optimisticMembers ? [...optimisticMembers].sort((a, b) => a.name.localeCompare(b.name)) : [];
  const groupedMembers = sortedMembers.reduce((acc, member) => {
    const role = member.dciRole;
    if (!acc[role]) acc[role] = [];
    acc[role].push(member);
    return acc;
  }, {} as Record<DciRole, AssignmentMember[]>);

  // Include all existing member IDs for the UserAssignmentPopover
  const existingMemberIds = new Set(
    optimisticMembers?.map(m => {
      if (m.type === 'user') return m.id as Id<'users'>;
      if (m.type === 'team') return m.id as Id<'teams'>;
      if (m.type === 'person') return m.id as Id<'people'>;
      return m.id;
    }) ?? []
  );

  // Determine which roles to display based on entity type
  const rolesToDisplay: DciRoleAssign[] = entityType === 'task' 
    ? ['Driver', 'Contributor', 'Informed', 'Requestor']
    : ['Driver', 'Contributor', 'Informed'];

  // Helper to render a single member item (draggable)
  const renderMember = (member: AssignmentMember, index: number) => ( // Use AssignmentMember
    <Draggable key={member.id} draggableId={member.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps} 
          className={`flex items-center justify-between rounded-lg border p-1.5 mb-1 bg-card group relative ${snapshot.isDragging ? 'bg-muted shadow-md' : ''} cursor-grab active:cursor-grabbing select-none`} 
        >
          <div className="flex items-center gap-1.5 overflow-hidden pl-1.5">
            {/* Conditional rendering for User/Person Avatar or Team Icon */}
            {member.type === 'user' || member.type === 'person' ? (
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="relative flex-shrink-0">
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                      </Avatar>
                    </div>
                  </TooltipTrigger>
                </Tooltip>
              </TooltipProvider>
            ) : (
              // Team Icon
              <div className="flex items-center justify-center h-5 w-5 bg-muted rounded-full flex-shrink-0" title={`Team: ${member.name}`}>
                 <Users className="h-3 w-3 text-muted-foreground" />
              </div>
            )}
            <div className="flex-grow overflow-hidden">
              <div className="text-sm truncate">{member.name}</div>
            </div>
          </div>
          <div className="flex items-center pr-0.5 flex-shrink-0">
             {/* Remove Button */}
             {member.dciRole !== 'None' && (
                 <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive" 
                    onClick={(e) => { 
                      e.stopPropagation(); 
                      setMemberToRemove({ id: member.id, name: member.name, role: member.dciRole, type: member.type }); // Pass type
                    }}
                    aria-label={`Remove ${member.name}`}
                 >
                    <X className="h-3 w-3" /> 
                 </Button>
             )}
          </div>
        </div>
      )}
    </Draggable>
  );

  // Helper to render the trigger button for the popover
   const renderAddButton = (role: DciRoleAssign, isEmpty: boolean) => (
    <UserAssignmentPopover
      entityId={entityId}
      entityType={entityType}
      targetRole={role}
      existingMemberIds={existingMemberIds} // Pass all existing member IDs
      triggerButton={
        isEmpty ? (
          <Button
            variant="ghost"
            className="w-full h-7 justify-center items-center border-2 border-dashed border-muted-foreground/30 text-muted-foreground hover:bg-muted/50 hover:text-foreground text-xs"
          >
            <Plus className="h-3.5 w-3.5 mr-1" /> {role === 'Driver' ? 'Replace Driver' : `Add ${role}`} 
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-xs h-5 mt-1 text-muted-foreground hover:text-foreground"
          >
            <Plus className="h-3 w-3 mr-1" /> {role === 'Driver' ? 'Replace' : 'Add'} 
          </Button>
        )
      }
    />
  );


  return (
    <DragDropContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
      <Card>
        <CardHeader className="px-3 py-1.5 flex flex-row items-center justify-between"> 
          <div className="flex items-center gap-1.5">
            <Users className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">{entityType.charAt(0).toUpperCase() + entityType.slice(1)} Team</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="pt-1 pb-1"> 
          <div className="space-y-1"> 
            {rolesToDisplay.map((role) => {
              const membersInRole = groupedMembers[role] || [];
              const isEmpty = membersInRole.length === 0;
              const minHeightStyle = isEmpty ? (role === 'Driver' ? '40px' : '40px') : 'auto';
              
              return (
                <div key={role}>
                  <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-.5 px-0.5 flex items-center gap-1"> 
                     {role === 'Driver' && <UserCheck className="h-3 w-3 text-blue-500" />}
                     {role === 'Contributor' && <UsersRound className="h-3 w-3 text-green-500" />}
                     {role === 'Informed' && <Info className="h-3 w-3 text-gray-500" />}
                     {role === 'Requestor' && <UserRound className="h-3 w-3 text-purple-500" />}
                     {role}
                     <span className="text-xs font-normal ml-0.5">
                       {role === 'Driver' || role === 'Requestor'
                         ? membersInRole.length > 0 ? '(Max 1)' : '(0)'
                         : `(${membersInRole.length})`}
                     </span>
                  </h4>
                  <Droppable
                    droppableId={`droppable-${role.toLowerCase()}`}
                    // Prevent dropping teams into the Driver column
                    isDropDisabled={(role === 'Driver' && draggedItemTypeRef.current === 'team') || 
                                   (role === 'Requestor' && draggedItemTypeRef.current === 'user')} 
                    renderClone={(provided, snapshot, rubric) => {
                      const member = optimisticMembers?.find(m => m.id === rubric.draggableId); // Use optimisticMembers
                      if (!member) return null; 

                      return ( // Render clone based on type
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className={`flex items-center justify-between rounded-lg border p-1.5 mb-1 bg-card group relative bg-muted shadow-md cursor-grabbing select-none`} 
                          style={provided.draggableProps.style} 
                        >
                          <div className="flex items-center gap-1.5 overflow-hidden pl-1.5">
                            {member.type === 'user' || member.type === 'person' ? (
                              <Avatar className="h-5 w-5">
                                <AvatarImage src={member.avatar} alt={member.name} />
                                <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                              </Avatar>
                            ) : (
                              <div className="flex items-center justify-center h-5 w-5 bg-muted rounded-full flex-shrink-0" title={`Team: ${member.name}`}>
                                <Users className="h-3 w-3 text-muted-foreground" />
                              </div>
                            )}
                            <div className="flex-grow overflow-hidden">
                              <div className="text-sm truncate">{member.name}</div>
                            </div>
                          </div>
                          {/* No extra details needed in clone */}
                        </div>
                      );
                    }}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="p-0.5 rounded-md border border-transparent transition-colors" 
                        style={{ minHeight: minHeightStyle }} 
                      >
                        {membersInRole.map((member: AssignmentMember, index: number) => renderMember(member, index))}
                        {provided.placeholder}
                        {/* Disable adding more drivers or requestors if one exists */}
                        {!((role === 'Driver' || role === 'Requestor') && !isEmpty) && !snapshot.isDraggingOver && renderAddButton(role, isEmpty)}
                      </div>
                    )}
                  </Droppable>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
      {/* Confirmation Modal */}
      {memberToRemove && ( // Use memberToRemove
        <CollectionCardModal
            heading="Confirm Removal"
            subheading={`Are you sure you want to remove ${memberToRemove.name} as ${memberToRemove.role}?`}
            onClose={() => setMemberToRemove(null)}
            className="max-w-sm"
            primaryCTA={{
              text: "Remove",
              onClick: handleConfirmRemove,
            }}
            secondaryCTA={{
              text: "Cancel",
              onClick: () => setMemberToRemove(null),
            }}
          >
           <p className="text-sm text-muted-foreground">This will remove the {memberToRemove.type} from their assigned role for this {entityType}.</p>
        </CollectionCardModal>
      )}
    </DragDropContext>
  );
};

// Ref for storing the type of the item being dragged
const draggedItemTypeRef = React.createRef<'user' | 'team' | 'person' | null>();

// Initialize the ref with null
draggedItemTypeRef.current = null;

export default AssignmentTeam;
