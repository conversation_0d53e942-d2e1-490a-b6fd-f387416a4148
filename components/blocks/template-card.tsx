'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  ChevronUp,
  MoreHorizontal,
  Clock
} from 'lucide-react';

// Generic interface that can be extended for specific item types
export interface TemplateCardProps {
  id: string;
  title: string;
  description?: string;
  status?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  progress?: number;
  lastUpdated?: string | number;
  team?: { _id: string; name: string; image?: string }[];
  metadata?: Record<string, any>; // For additional data specific to each domain
  onClick?: () => void;
}

/**
 * Helper function to get the appropriate color for the status badge
 */
const getStatusColor = (status?: string) => {
  if (!status) return 'bg-gray-200 text-gray-700';

  switch (status.toLowerCase()) {
    case 'active':
    case 'in_progress':
    case 'in progress':
      return 'bg-blue-100 text-blue-800';
    case 'paused':
      return 'bg-yellow-200 text-yellow-800';
    case 'draft':
    case 'not_started':
    case 'not started':
      return 'bg-gray-200 text-gray-700';
    case 'completed':
    case 'done':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
    case 'canceled':
      return 'bg-red-100 text-red-800';
    case 'perpetual':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-200 text-gray-700';
  }
};

/**
 * Helper function to get the appropriate color for the priority indicator
 */
const getPriorityColor = (priority?: string) => {
  if (!priority) return 'text-gray-600';

  switch (priority.toLowerCase()) {
    case 'high':
    case 'urgent':
      return 'text-orange-600';
    case 'medium':
      return 'text-blue-600';
    case 'low':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};

/**
 * Component to display priority level using chevron icons
 */
const PriorityChevrons = ({ priority }: { priority?: string }) => {
  if (!priority) return null;

  const color = getPriorityColor(priority);
  const className = `h-4 w-4 ${color} stroke-[3]`;

  switch (priority.toLowerCase()) {
    case 'high':
    case 'urgent':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[-2px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[10px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'medium':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-0">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[6px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'low':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    default:
      return null;
  }
};

/**
 * Helper function to format a timestamp as a relative time string
 */
function formatTimeAgo(timestamp?: string | number) {
  if (!timestamp) return 'Unknown';

  const date = new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  }
}

/**
 * TemplateCard component
 * 
 * A reusable card component that displays information in a standard format.
 * This can be used for any domain entity that needs to be displayed in a card format.
 */
export function TemplateCard({
  id,
  title,
  description,
  status,
  priority,
  progress = 0,
  lastUpdated,
  team = [],
  metadata = {},
  onClick
}: TemplateCardProps) {
  // Format the status text for display
  const statusText = status
    ? status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
    : '';

  // Determine the base route for the item based on the ID
  // This can be customized per implementation
  const baseRoute = '/items';

  return (
    <Card className="p-6 hover:shadow-md transition-all rounded-lg" onClick={onClick}>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-grow">
            <Link href={`${baseRoute}/${id}`} className="hover:underline">
              <CardTitle className="text-lg">{title}</CardTitle>
            </Link>
            {status && (
              <span
                className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}
              >
                {statusText}
              </span>
            )}
            {priority && (
              <div className="flex items-center">
                <PriorityChevrons priority={priority} />
              </div>
            )}
          </div>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>

        {description && (
          <div className="text-sm text-muted-foreground line-clamp-2 mt-2">
            {description}
          </div>
        )}

        {/* Progress indicator - can be conditionally rendered based on domain needs */}
        {typeof progress === 'number' && (
          <Progress
            value={progress}
            className={`h-3 mt-2 ${status === 'paused' ? '[&>div]:bg-gray-400' : '[&>div]:bg-blue-600'}`}
          />
        )}
      </CardHeader>

      <CardContent className="p-0 pt-4">
        <div className="flex items-center">
          {/* Team members avatars */}
          {team.length > 0 && (
            <div className="flex -space-x-2">
              {team.map((member) => (
                <Avatar
                  key={member._id}
                  className="border-2 border-background w-8 h-8"
                >
                  <AvatarImage src={member.image} alt={member.name} />
                  <AvatarFallback>
                    {member.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
          )}

          {/* Last updated timestamp */}
          <div className="ml-auto flex items-center space-x-6">
            {lastUpdated && (
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {formatTimeAgo(lastUpdated)}
                </span>
              </div>
            )}

            {/* Additional metadata display - can be customized */}
            <div className="flex items-center space-x-4">
              {metadata.primaryMetric && (
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground mr-2">
                    {metadata.primaryMetricLabel || 'Items'}:
                  </span>
                  <span className="text-sm font-medium">
                    {metadata.primaryMetric}
                  </span>
                </div>
              )}

              {metadata.secondaryMetric && (
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground mr-2">
                    {metadata.secondaryMetricLabel || 'Status'}:
                  </span>
                  <span className="text-sm font-medium">
                    {metadata.secondaryMetric}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Optional footer content */}
        {metadata.footerContent && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm max-w-[80%]">
                <span className="text-muted-foreground shrink-0">
                  {metadata.footerLabel || 'Note'}:
                </span>
                <span className="ml-1.5 truncate">{metadata.footerContent}</span>
              </div>
              {metadata.badge && (
                <div className={`bg-${metadata.badgeColor || 'blue'}-100 text-${metadata.badgeColor || 'blue'}-700 rounded text-xs px-2 py-0.5 shrink-0 font-medium`}>
                  {metadata.badge}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
