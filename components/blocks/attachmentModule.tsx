import React, { useState } from 'react';
import { Paperclip, File, Image, FileText, Video, Download, X, ChevronDown, ChevronUp, Plus, LayoutList, LayoutGrid } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Id } from '@/convex/_generated/dataModel';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

// Define attachment type
interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'document' | 'spreadsheet' | 'video' | 'other';
  size: string;
  uploadDate: Date;
  uploadedBy: string;
  thumbnailUrl?: string;
}

interface AttachmentModuleProps {
  entityId: Id<'decisions' | 'tasks' | 'projects' | 'bills'>;
  entityType: 'decision' | 'task' | 'project' | 'bill';
}

const AttachmentModule = ({ entityId, entityType }: AttachmentModuleProps) => {
  // Sample attachment data
  const [attachments, setAttachments] = useState<Attachment[]>([
    {
      id: 'att1',
      name: 'project-requirements.pdf',
      type: 'document',
      size: '1.2 MB',
      uploadDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
      uploadedBy: 'Emma Thompson',
      thumbnailUrl: '/api/placeholder/40/40'
    },
    {
      id: 'att2',
      name: 'design-mockup.png',
      type: 'image',
      size: '3.7 MB',
      uploadDate: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
      uploadedBy: 'Alex Rodriguez',
      thumbnailUrl: '/api/placeholder/40/40'
    },
    {
      id: 'att3',
      name: 'budget-allocation.xlsx',
      type: 'spreadsheet',
      size: '578 KB',
      uploadDate: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      uploadedBy: 'Sarah Kim',
      thumbnailUrl: '/api/placeholder/40/40'
    },
    {
      id: 'att4',
      name: 'presentation.pptx',
      type: 'document',
      size: '2.1 MB',
      uploadDate: new Date(Date.now() - 1000 * 60 * 60 * 36), // 36 hours ago
      uploadedBy: 'Marcus Chen',
      thumbnailUrl: '/api/placeholder/40/40'
    },
    {
      id: 'att5',
      name: 'demo-video.mp4',
      type: 'video',
      size: '8.3 MB',
      uploadDate: new Date(Date.now() - 1000 * 60 * 60 * 48), // 48 hours ago
      uploadedBy: 'Rachel Johnson',
      thumbnailUrl: '/api/placeholder/40/40'
    }
  ]);

  const [sortBy, setSortBy] = useState('recent');
  const [showAll, setShowAll] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'preview'>('list');

  // Sort attachments based on selected criteria
  const sortedAttachments = [...attachments].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return b.uploadDate.getTime() - a.uploadDate.getTime();
      case 'oldest':
        return a.uploadDate.getTime() - b.uploadDate.getTime();
      case 'name':
        return a.name.localeCompare(b.name);
      case 'size':
        return parseFloat(a.size) - parseFloat(b.size);
      case 'type':
        return a.type.localeCompare(b.type);
      default:
        return 0;
    }
  });

  const displayedAttachments = showAll ? sortedAttachments : sortedAttachments.slice(0, 3);
  const hiddenCount = attachments.length - 3;

  // Function to get file icon based on type
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-500" />;
      case 'document':
        return <FileText className="h-4 w-4 text-purple-500" />;
      case 'spreadsheet':
        return <FileText className="h-4 w-4 text-green-500" />;
      case 'video':
        return <Video className="h-4 w-4 text-red-500" />;
      default:
        return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format the date to a relative string
  const formatRelativeDate = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInSeconds = Math.floor(diffInMs / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInDays > 7) {
      return date.toLocaleDateString();
    } else if (diffInDays > 0) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes > 0) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <Card>
      <CardHeader className="px-4 py-2 flex flex-row items-center justify-between">
        <div className="flex items-center gap-1.5">
          <Paperclip className="h-4 w-4 text-muted-foreground" />
          <CardTitle className="text-sm font-medium">Attachments (Sorry this doesn't work yet!)</CardTitle>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                {sortBy === 'recent' ? 'Recent' : 
                 sortBy === 'oldest' ? 'Oldest' : 
                 sortBy === 'name' ? 'Name' : 
                 sortBy === 'size' ? 'Size' : 'Type'}
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy('recent')}>Recent</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('oldest')}>Oldest</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('name')}>Name</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('size')}>Size</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('type')}>Type</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* View Mode Switcher */}
          <div className="flex items-center rounded-md border overflow-hidden">
            <Button 
              variant={viewMode === 'list' ? "default" : "ghost"} 
              size="sm" 
              className={`h-6 px-2 rounded-none ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
              onClick={() => setViewMode('list')}
            >
              <LayoutList className="h-3.5 w-3.5" />
            </Button>
            <Button 
              variant={viewMode === 'preview' ? "default" : "ghost"} 
              size="sm" 
              className={`h-6 px-2 rounded-none ${viewMode === 'preview' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
              onClick={() => setViewMode('preview')}
            >
              <LayoutGrid className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-3">
        {viewMode === 'list' ? (
          <div className="space-y-2">
            {displayedAttachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between rounded-lg border p-2"
              >
                <div className="flex items-center gap-2">
                  <div className="flex h-9 w-9 items-center justify-center rounded-md bg-muted">
                    {getFileIcon(attachment.type)}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{attachment.name}</div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{attachment.size}</span>
                      <span>•</span>
                      <span>{formatRelativeDate(attachment.uploadDate)}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Download className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Download</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground hover:text-destructive">
                          <X className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Remove</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}

            {!showAll && hiddenCount > 0 && (
              <Button
                variant="ghost"
                className="w-full justify-center text-muted-foreground py-1 h-7 text-xs"
                onClick={() => setShowAll(true)}
              >
                Show {hiddenCount} more
                <ChevronDown className="h-3.5 w-3.5 ml-1" />
              </Button>
            )}

            {showAll && (
              <Button
                variant="ghost"
                className="w-full justify-center text-muted-foreground py-1 h-7 text-xs"
                onClick={() => setShowAll(false)}
              >
                Show less
                <ChevronUp className="h-3.5 w-3.5 ml-1" />
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-2">
            {sortedAttachments.map((attachment) => (
              <div key={attachment.id} className="flex flex-col border rounded-lg p-3 hover:shadow-md transition-shadow">
                <div className="w-full aspect-square bg-muted flex items-center justify-center rounded-md overflow-hidden mb-2">
                  {attachment.thumbnailUrl ? (
                    <img src={attachment.thumbnailUrl} alt={attachment.name} className="object-cover w-full h-full" />
                  ) : (
                    <div className="flex items-center justify-center w-full h-full">
                      {getFileIcon(attachment.type)}
                    </div>
                  )}
                </div>
                <div className="text-sm font-medium truncate">{attachment.name}</div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-muted-foreground">{attachment.size}</span>
                  <span className="text-xs text-muted-foreground">{formatRelativeDate(attachment.uploadDate)}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add Attachment Button */}
        <div className="flex items-center rounded-lg border p-2 border-dashed cursor-pointer hover:bg-muted/50 transition-colors mt-2">
          <div className="flex items-center gap-2">
            <div className="flex h-9 w-9 items-center justify-center rounded-md bg-muted">
              <Plus className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="font-medium text-sm text-muted-foreground">
              Add Attachment
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AttachmentModule;
