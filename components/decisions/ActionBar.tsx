import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider, // Import TooltipProvider
  TooltipTrigger
} from "@/components/ui/tooltip";
import { ChevronDown, CheckCircle, XCircle, AlertCircle, PauseCircle, CircleIcon, ArrowUp, Minus, ArrowDown, AlertOctagon, Slash, Tag as TagIcon, X as XIcon, Plus, Info as InfoIcon } from "lucide-react"; // Added InfoIcon
import { useToast } from "@/components/hooks/use-toast";
import { Id } from '@/convex/_generated/dataModel';
import { getStatusStyle, formatStatus } from '@/lib/utils/status-styles';
import { Tag } from '@/zod/tags-schema'; // Import Tag type
import { AddTaggableTagPopover } from '@/components/ui/AddTaggableTagPopover'; // Import popover
import { AddInvestmentThemeTagPopover } from '@/components/ui/AddInvestmentThemeTagPopover';
import { cn } from '@/lib/utils'; // Import cn utility
import DecisionCategorySelect from './DecisionCategorySelect';
import { TagType } from '@/zod/tagTypes-schema'; // Import TagType type

// Constants for decision statuses and importance levels
const DECISION_STATUSES = ['draft', 'in_review', 'approved', 'rejected', 'escalated', 'cancelled', 'on_hold'] as const;
const DECISION_IMPORTANCE = ['low', 'medium', 'high', 'critical'] as const;

// Helper function to get importance styling
const getImportanceStyle = (importance: 'low' | 'medium' | 'high' | 'critical' | 'urgent' | undefined) => {
  switch (importance) {
    case 'critical':
    case 'urgent': // Treat 'urgent' the same as 'critical'
      return 'bg-red-100 text-red-700 border-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-700 border-green-200';
    default:
      return 'bg-gray-100 text-gray-500 border-gray-200'; // Style for undefined
  }
};

// Helper function to get status icon
const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'approved':
      return <CheckCircle className="h-3.5 w-3.5" />;
    case 'in_review':
      return <AlertCircle className="h-3.5 w-3.5" />;
    case 'rejected':
      return <XCircle className="h-3.5 w-3.5" />;
    case 'escalated':
      return <AlertOctagon className="h-3.5 w-3.5" />;
    case 'cancelled':
      return <Slash className="h-3.5 w-3.5" />;
    case 'on_hold':
      return <PauseCircle className="h-3.5 w-3.5" />;
    case 'draft':
      return <CircleIcon className="h-3.5 w-3.5" />;
    default:
      return null;
  }
};

// Helper function to get importance icon
const getImportanceIcon = (importance: 'low' | 'medium' | 'high' | 'critical' | 'urgent' | undefined) => {
  switch (importance) {
    case 'critical':
    case 'urgent': // Treat 'urgent' the same as 'critical'
      return <ArrowUp className="h-3.5 w-3.5" />;
    case 'high':
      return <ArrowUp className="h-3.5 w-3.5" />;
    case 'medium':
      return <Minus className="h-3.5 w-3.5" />;
    case 'low':
      return <ArrowDown className="h-3.5 w-3.5" />;
    default:
      return null;
  }
};

interface ActionBarProps {
  item: {
    status?: string; // Accept any string, as Convex schema uses string, not enum
    importance?: 'low' | 'medium' | 'high' | 'critical' | 'urgent' | undefined; // Allow 'urgent' as well
    _id: string;
    decision_category_id: Id<'decision_categories'>;
  };
  updateDecision: (args: { updates: { id: Id<'decisions'>; updates: any } }) => Promise<any>;
  tags?: Tag[];
  onRemoveTag?: (tagId: Id<'tags'>) => Promise<void>;
  decisionCategory?: any;
  tagTypes?: TagType[]; // New: array of tag type objects for dynamic tag sections
  generalTagType?: TagType; // Always show General Tags section
  isActive?: boolean;
}

const ActionBar: React.FC<ActionBarProps> = ({
  item,
  updateDecision,
  tags = [],
  onRemoveTag,
  decisionCategory,
  tagTypes = [], // Default to empty array
  generalTagType,
  isActive
}) => {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);

  // Local state for optimistic status update (allow any string)
  const [optimisticStatus, setOptimisticStatus] = useState<string | undefined>(item.status);

  // Update local status if item.status changes from parent
  React.useEffect(() => {
    setOptimisticStatus(item.status);
  }, [item.status]);

  // Check if this is an investment decision
  const isInvestmentDecision = decisionCategory?.subtable_type === 'investment_decisions';

  // Handler for status change (optimistic update)
  const handleStatusChange = async (newStatus: string) => {
    const prevStatus = optimisticStatus;
    setOptimisticStatus(newStatus); // Optimistically update UI
    setIsUpdating(true);
    try {
      await updateDecision({
        updates: {
          id: item._id as Id<'decisions'>,
          updates: { status: newStatus }
        }
      });
      toast({
        title: "Status updated",
        description: `Decision status changed to ${decisionCategory?.statuses?.find((s: { value: string; label: string }) => s.value === newStatus)?.label || newStatus.replace('_', ' ')}`
      });
    } catch (error: unknown) {
      setOptimisticStatus(prevStatus); // Revert on error
      toast({
        title: "Error updating status",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handler for importance change
  const handleImportanceChange = async (newImportance: 'low' | 'medium' | 'high' | 'critical' | 'urgent' | undefined) => {
    try {
      setIsUpdating(true);
      await updateDecision({
        updates: {
          id: item._id as Id<'decisions'>,
          updates: { importance: newImportance }
        }
      });
      toast({
        title: "Importance updated",
        description: `Decision importance changed to ${newImportance || 'none'}`
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating importance",
        description: error instanceof Error ? error.message : "Failed to update importance",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Import the updateInvestmentDecision mutation
  const updateInvestmentDecisionMutation = useMutation(api.decisions.updateInvestmentDecision);

  // Handler for investment status change
  const handleInvestmentStatusChange = async (isActive: boolean) => {
    try {
      setIsUpdating(true);
      // Use the dedicated mutation for updating investment decision fields
      await updateInvestmentDecisionMutation({
        decisionId: item._id as Id<'decisions'>,
        updates: { 
          is_active: isActive
        }
      });
      toast({
        title: "Investment status updated",
        description: `Investment status changed to ${isActive ? 'Active' : 'Inactive'}`
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating investment status",
        description: error instanceof Error ? error.message : "Failed to update investment status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-4 px-4 pt-1 pb-4 w-full min-w-0 border-b border-gray-200">
      {/* Category Dropdown - New Component */}
      <DecisionCategorySelect 
        decisionId={item._id as Id<'decisions'>}
        currentCategoryId={item.decision_category_id}
      />

      {/* Conditionally render Active/Inactive toggle for Investment Decisions */}
      {isInvestmentDecision && typeof isActive === 'boolean' && (
         <div className="flex flex-col items-start min-w-0 flex-shrink">
           <div className="flex items-center gap-1">
             <span className="text-xs text-muted-foreground mb-1">Investment Status</span>
             <TooltipProvider>
               <Tooltip>
                 <TooltipTrigger asChild>
                   <InfoIcon className="h-3 w-3 text-muted-foreground cursor-help" />
                 </TooltipTrigger>
               <TooltipContent>
                 <p className="w-[200px] text-xs">
                   "Active" refers to investments that are ongoing or operational. "Inactive" refers to those that are dormant, no longer pursued, or not generating activity.
                 </p>
               </TooltipContent>
               </Tooltip>
             </TooltipProvider>
           </div>
           <DropdownMenu>
             <DropdownMenuTrigger asChild>
               <Badge
                 variant="outline"
                 className={cn(
                   "flex items-center gap-1 cursor-pointer",
                   isActive ? "bg-green-100 text-green-700 border-green-200" : "bg-gray-100 text-gray-500 border-gray-200"
                 )}
               >
                 {isActive ? "Active" : "Inactive"}
                 <ChevronDown className="h-3 w-3 ml-1" />
               </Badge>
             </DropdownMenuTrigger>
             <DropdownMenuContent align="end">
               <DropdownMenuItem
                 onClick={() => handleInvestmentStatusChange(true)}
                 className={isActive ? 'bg-muted' : ''}
               >
                 Active
               </DropdownMenuItem>
               <DropdownMenuItem
                 onClick={() => handleInvestmentStatusChange(false)}
                 className={!isActive ? 'bg-muted' : ''}
               >
                 Inactive
               </DropdownMenuItem>
             </DropdownMenuContent>
           </DropdownMenu>
         </div>
      )}

      <div className="flex flex-col items-start min-w-0 flex-shrink">
        <span className="text-xs text-muted-foreground mb-1">Status</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${getStatusStyle(optimisticStatus)}`}
            >
              {getStatusIcon(optimisticStatus || 'draft')}
              <span className="capitalize truncate max-w-[120px] overflow-hidden whitespace-nowrap">
                {decisionCategory?.statuses?.find((s: { value: string; label: string }) => s.value === optimisticStatus)?.label ||
                  (optimisticStatus || 'draft').replace('_', ' ')}
              </span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {decisionCategory?.statuses && decisionCategory.statuses.length > 0
              ? decisionCategory.statuses.map((status: { value: string; label: string }) => (
                  <DropdownMenuItem
                    key={status.value}
                    onClick={() => handleStatusChange(status.value)}
                    className={optimisticStatus === status.value ? 'bg-muted' : ''}
                  >
                    <span className="capitalize">{status.label}</span>
                  </DropdownMenuItem>
                ))
              : DECISION_STATUSES.map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onClick={() => handleStatusChange(status)}
                    className={optimisticStatus === status ? 'bg-muted' : ''}
                  >
                    <span className="capitalize">{status.replace('_', ' ')}</span>
                  </DropdownMenuItem>
                ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex flex-col items-start min-w-0 flex-shrink">
        <span className="text-xs text-muted-foreground mb-1">Importance</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${getImportanceStyle(item.importance)}`}
            >
              {getImportanceIcon(item.importance)}
              <span className="capitalize truncate max-w-[120px] overflow-hidden whitespace-nowrap">{item.importance || 'No importance'}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {[undefined, 'low', 'medium', 'high', 'critical', 'urgent'].map((importance) => (
              <DropdownMenuItem
                key={importance || 'none'}
                onClick={() => handleImportanceChange(importance as 'low' | 'medium' | 'high' | 'critical' | 'urgent' | undefined)}
                className={item.importance === importance ? 'bg-muted' : ''}
              >
                {importance || 'No importance'}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Always show General Tags section if available */}
      {generalTagType && (
        <div className="flex flex-col items-start">
          <span className="text-xs text-muted-foreground mb-1">{generalTagType.name || 'General Tags'}</span>
          <div className="flex items-start gap-1 flex-wrap min-w-0 overflow-x-auto">
            {tags.filter(tag => tag.tag_type === generalTagType._id).map((tag) => (
              <Badge
                key={tag._id}
                variant="outline"
                className={cn(
                  "relative group pr-6",
                  tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                )}
                style={{
                  backgroundColor: tag.color ? `${tag.color}20` : undefined,
                  borderColor: tag.color ? `${tag.color}40` : undefined,
                  color: tag.color || undefined,
                }}
              >
                <span className="truncate max-w-[120px] overflow-hidden whitespace-nowrap">{tag.name}</span>
                {onRemoveTag && (
                  <button
                    onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                    className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                    aria-label={`Remove tag ${tag.name}`}
                  >
                    <XIcon className="h-2.5 w-2.5" />
                  </button>
                )}
              </Badge>
            ))}
            {/* Add Tag Button for General Tags */}
            <AddTaggableTagPopover
              taggableId={item._id as Id<'decisions'>}
              taggableType="decision"
              tagTypeSlug={generalTagType.immutable_slug || generalTagType.name}
              triggerLabel={`Add ${generalTagType.name}`}
            />
          </div>
        </div>
      )}

      {/* Dynamically render a tag section for each tag type in tagTypes, excluding generalTagType */}
      {tagTypes
        .filter(tagType => !generalTagType || tagType._id !== generalTagType._id)
        .map((tagType) => (
          <div key={tagType._id} className="flex flex-col items-start">
            <span className="text-xs text-muted-foreground mb-1">{tagType.name}</span>
            <div className="flex items-start gap-1 flex-wrap min-w-0 overflow-x-auto">
              {tags.filter(tag => tag.tag_type === tagType._id).map((tag) => (
                <Badge
                  key={tag._id}
                  variant="outline"
                  className={cn(
                    "relative group pr-6",
                    tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                  )}
                  style={{
                    backgroundColor: tag.color ? `${tag.color}20` : undefined,
                    borderColor: tag.color ? `${tag.color}40` : undefined,
                    color: tag.color || undefined,
                  }}
                >
                  <span className="truncate max-w-[120px] overflow-hidden whitespace-nowrap">{tag.name}</span>
                  {onRemoveTag && (
                    <button
                      onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                      className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                      aria-label={`Remove tag ${tag.name}`}
                    >
                      <XIcon className="h-2.5 w-2.5" />
                    </button>
                  )}
                </Badge>
              ))}
              {/* Add Tag Button for this tag type */}
              {tagType.immutable_slug === "theme-tags" ? (
                <AddInvestmentThemeTagPopover
                  taggableId={item._id as Id<'decisions'>}
                  taggableType="decision"
                  tagTypeSlug={tagType.immutable_slug || tagType.name}
                  triggerLabel={`Add ${tagType.name}`}
                />
              ) : (
                <AddTaggableTagPopover
                  taggableId={item._id as Id<'decisions'>}
                  taggableType="decision"
                  tagTypeSlug={tagType.immutable_slug || tagType.name}
                  triggerLabel={`Add ${tagType.name}`}
                />
              )}
            </div>
          </div>
        ))}
    </div>
  );
};

export default ActionBar;
