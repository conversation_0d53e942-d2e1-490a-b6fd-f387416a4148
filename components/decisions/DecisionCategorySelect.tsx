'use client';

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useToast } from '@/components/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { ChevronDown } from "lucide-react";

interface DecisionCategorySelectProps {
  decisionId: Id<'decisions'>;
  currentCategoryId: Id<'decision_categories'>;
}

const DecisionCategorySelect: React.FC<DecisionCategorySelectProps> = ({
  decisionId,
  currentCategoryId
}) => {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Fetch all decision categories
  const categories = useQuery(api.decisions.listDecisionCategories);
  
  // Get the current category name
  const currentCategory = categories?.find(
    category => category._id === currentCategoryId
  );
  
  // Mutation to update the decision
  const updateDecision = useMutation(api.decisions.updateDecisions);

  // Handle category change
  const handleCategoryChange = async (categoryId: Id<'decision_categories'>) => {
    if (categoryId === currentCategoryId) return;
    
    try {
      setIsUpdating(true);
      await updateDecision({
        updates: {
          id: decisionId,
          updates: { 
            decision_category_id: categoryId 
          }
        }
      });
      toast({
        title: "Category updated",
        description: "Decision category has been updated successfully."
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating category",
        description: error instanceof Error ? error.message : "Failed to update category",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // If categories are still loading, show a loading state
  if (!categories) {
    return (
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Category</span>
        <Badge variant="outline" className="flex items-center gap-1">
          Loading...
        </Badge>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-start">
      <span className="text-xs text-muted-foreground mb-1">Category</span>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isUpdating}>
          <Badge
            variant="outline"
            className="flex items-center gap-1 cursor-pointer"
          >
            <span>{currentCategory?.name || 'Unknown Category'}</span>
            <ChevronDown className="h-3 w-3 ml-1" />
          </Badge>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {categories
            .filter(category => category.immutable_slug !== 'all-decisions-kanban')
            .map((category) => (
            <DropdownMenuItem
              key={category._id}
              onClick={() => handleCategoryChange(category._id as Id<'decision_categories'>)}
              className={category._id === currentCategoryId ? 'bg-muted' : ''}
            >
              {category.name}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default DecisionCategorySelect;
