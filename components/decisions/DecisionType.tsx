"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Check, List, X } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

type DecisionTypeOption = "approval" | "selection" | null;

interface FinancialData {
  hasImplications: boolean;
  amount?: string;
  currency?: string;
  timePeriod?: string;
  years?: string;
}

interface ImplicationsData {
  yesMeans?: string;
  noMeans?: string;
}

interface DecisionTypeProps {
  onTypeSelect?: (type: DecisionTypeOption) => void;
  selectedType?: DecisionTypeOption;
  initialFinancialData?: FinancialData;
  initialImplications?: ImplicationsData;
  onSave?: (
    type: DecisionTypeOption,
    financialData: FinancialData,
    implications: ImplicationsData
  ) => void;
}

function ApprovalDecisionForm({
  selectedType,
  initialFinancialData,
  initialImplications,
  onSave
}: {
  selectedType: DecisionTypeOption;
  initialFinancialData?: FinancialData;
  initialImplications?: ImplicationsData;
  onSave?: (
    type: DecisionTypeOption,
    financialData: FinancialData,
    implications: ImplicationsData
  ) => void;
}) {
  // Controlled values from props
  const hasImplications = initialFinancialData?.hasImplications || false;
  const amount = initialFinancialData?.amount || "";
  const currency = initialFinancialData?.currency || "USD";
  const timePeriod = initialFinancialData?.timePeriod || "Monthly";
  const years = initialFinancialData?.years || "1";
  const yesMeans = initialImplications?.yesMeans || "";
  const noMeans = initialImplications?.noMeans || "";

  const [isAmountFocused, setIsAmountFocused] = useState(false);
  const [draftYesMeans, setDraftYesMeans] = useState(yesMeans);
  const [draftNoMeans, setDraftNoMeans] = useState(noMeans);
  const [draftAmount, setDraftAmount] = useState(amount);
  const [draftYears, setDraftYears] = useState(years);

  const handleSave = (
    updatedFinancial: FinancialData,
    updatedImplications: ImplicationsData
  ) => {
    onSave?.(selectedType, updatedFinancial, updatedImplications);
  };

  const handleToggleChange = (checked: boolean) => {
    handleSave(
      { hasImplications: checked, amount, currency, timePeriod, years },
      { yesMeans, noMeans }
    );
  };

  const formatDisplayAmount = () => {
    if (isAmountFocused) {
      return draftAmount;
    }
    const value = parseFloat(draftAmount || "0");
    if (isNaN(value)) {
      return "0.00";
    }
    return value.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const calculateNormalizedCost = () => {
    if (!hasImplications || !amount || isNaN(parseFloat(amount))) {
      return { monthly: "$0.00", annual: "$0.00" };
    }
    const value = parseFloat(amount);
    let monthlyValue = 0;
    switch (timePeriod) {
      case "One-time":
        monthlyValue = value / 12;
        break;
      case "Daily":
        monthlyValue = value * 30;
        break;
      case "Weekly":
        monthlyValue = value * 4.33;
        break;
      case "Monthly":
        monthlyValue = value;
        break;
      case "Quarterly":
        monthlyValue = value / 3;
        break;
      case "Yearly":
        monthlyValue = value / 12;
        break;
      case "Multi-year":
        monthlyValue = value / (parseInt(years || "1") || 1) / 12;
        break;
      default:
        monthlyValue = value;
    }
    const annualValue = monthlyValue * 12;
    const formatCurrency = (v: number) =>
      v.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    return {
      monthly: `$${formatCurrency(monthlyValue)}`,
      annual: `$${formatCurrency(annualValue)}`
    };
  };

  const normalizedCost = calculateNormalizedCost();

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 border border-primary/40 rounded-lg px-3 py-1.5 bg-white/60">
        <Label htmlFor="financial-implications" className="text-sm font-medium">
          This decision has financial implications
        </Label>
        <Switch
          id="financial-implications"
          checked={hasImplications}
          onCheckedChange={handleToggleChange}
          className="border-2 shadow-sm data-[state=checked]:bg-green-500 data-[state=unchecked]:border-foreground/30 data-[state=unchecked]:border-1 data-[state=unchecked]:bg-white"
        />
      </div>

      {hasImplications && (
        <div className="bg-slate-50 p-1 rounded-lg space-y-1">
          <div className="grid grid-cols-12 gap-1.5">
            <div className="col-span-4 space-y-0.5">
              <Label htmlFor="amount" className="text-sm font-medium">
                Amount
              </Label>
              <div className="relative flex w-full">
                <div className="absolute inset-y-0 left-0 flex items-center px-2 bg-slate-100 border border-r-0 rounded-l-md">
                  <span className="text-sm">$</span>
                </div>
                <input
                  type={isAmountFocused ? "number" : "text"}
                  id="amount"
                  placeholder="0.00"
                  className="w-full pl-8 rounded-md border focus:ring-0 focus:ring-offset-0"
                  step="0.01"
                  min="0"
                  value={formatDisplayAmount()}
                  onFocus={() => setIsAmountFocused(true)}
                  onBlur={() => {
                    setIsAmountFocused(false);
                    handleSave(
                      { hasImplications, amount: draftAmount, currency, timePeriod, years: draftYears },
                      { yesMeans: draftYesMeans, noMeans: draftNoMeans }
                    );
                  }}
                  onChange={(e) => {
                    const raw = e.target.value.replace(/,/g, "");
                    setDraftAmount(raw);
                  }}
                />
              </div>
            </div>
            <div className="col-span-2 space-y-0.5">
              <Label htmlFor="currency" className="text-sm font-medium">
                Currency
              </Label>
              <select
                id="currency"
                className="w-full rounded-md border focus:ring-0 focus:ring-offset-0"
                value={currency}
                onChange={(e) =>
                  handleSave(
                    { hasImplications, amount, currency: e.target.value, timePeriod, years },
                    { yesMeans, noMeans }
                  )
                }
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CAD">CAD</option>
                <option value="AUD">AUD</option>
              </select>
            </div>
            <div className="col-span-3 space-y-0.5">
              <Label htmlFor="time-period" className="text-sm font-medium">
                Time Period
              </Label>
              <select
                id="time-period"
                className="w-full rounded-md border focus:ring-0 focus:ring-offset-0"
                value={timePeriod}
                onChange={(e) =>
                  handleSave(
                    { hasImplications, amount, currency, timePeriod: e.target.value, years },
                    { yesMeans, noMeans }
                  )
                }
              >
                <option value="One-time">One-time</option>
                <option value="Monthly">Monthly</option>
                <option value="Quarterly">Quarterly</option>
                <option value="Yearly">Yearly</option>
                <option value="Multi-year">Multi-year</option>
              </select>
            </div>
            {timePeriod === "Multi-year" && (
              <div className="col-span-2 space-y-0.5">
                <Label htmlFor="years" className="text-sm font-medium">
                  Num. Years
                </Label>
                <input
                  type="number"
                  id="years"
                  placeholder="1"
                  className="w-full rounded-md border focus:ring-0 focus:ring-offset-0"
                  min="1"
                  step="1"
                  value={draftYears}
                  onBlur={() =>
                    handleSave(
                      { hasImplications, amount: draftAmount, currency, timePeriod, years: draftYears },
                      { yesMeans: draftYesMeans, noMeans: draftNoMeans }
                    )
                  }
                  onChange={(e) => setDraftYears(e.target.value)}
                />
              </div>
            )}
          </div>

          <div className="mt-0.5 border rounded-lg bg-white p-1.5">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-sm text-muted-foreground">Monthly Cost</p>
                <p className="text-lg font-medium text-muted-foreground italic select-none leading-tight">
                  {normalizedCost.monthly}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Annual Cost</p>
                <p className="text-lg font-medium text-muted-foreground italic select-none leading-tight">
                  {normalizedCost.annual}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-0.5">
        <h4 className="text-sm font-medium mb-1">Option Implications</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-1.5">
          <div className="border rounded-lg p-1.5 bg-white">
            <div className="flex items-center gap-1 mb-0.5">
              <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-50">
                <Check className="h-2.5 w-2.5 text-green-500" />
              </div>
              <span className="font-medium text-sm">Yes means</span>
            </div>
            <textarea
              value={draftYesMeans}
              onChange={(e) => setDraftYesMeans(e.target.value)}
              onBlur={() =>
                handleSave(
                  { hasImplications, amount, currency, timePeriod, years },
                  { yesMeans: draftYesMeans, noMeans: draftNoMeans }
                )
              }
              placeholder="Proceeding with the project"
              className="w-full whitespace-normal break-words resize-y border-0 bg-transparent p-0 text-sm text-muted-foreground focus:outline-none focus:ring-0"
            />
          </div>
          <div className="border rounded-lg p-1.5 bg-white">
            <div className="flex items-center gap-1 mb-0.5">
              <div className="flex h-4 w-4 items-center justify-center rounded-full bg-red-50">
                <X className="h-2.5 w-2.5 text-red-500" />
              </div>
              <span className="font-medium text-sm">No means</span>
            </div>
            <textarea
              value={draftNoMeans}
              onChange={(e) => setDraftNoMeans(e.target.value)}
              onBlur={() =>
                handleSave(
                  { hasImplications, amount, currency, timePeriod, years },
                  { yesMeans: draftYesMeans, noMeans: draftNoMeans }
                )
              }
              placeholder="Postponing until next quarter"
              className="w-full whitespace-normal break-words resize-y border-0 bg-transparent p-0 text-sm text-muted-foreground focus:outline-none focus:ring-0"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export function DecisionType({
  onTypeSelect,
  selectedType: externalSelectedType,
  initialFinancialData,
  initialImplications,
  onSave
}: DecisionTypeProps) {
  const [internalSelectedType, setInternalSelectedType] = useState<DecisionTypeOption>(
    null
  );
  const selectedType =
    externalSelectedType !== undefined ? externalSelectedType : internalSelectedType;

  const handleTypeSelect = (type: DecisionTypeOption) => {
    if (externalSelectedType === undefined) {
      setInternalSelectedType(type);
    }
    onTypeSelect?.(type);
    onSave?.(type, { hasImplications: false }, {});
  };

  return (
    <div className="space-y-2">
      {selectedType === "approval" && (
        <ApprovalDecisionForm
          selectedType={selectedType}
          initialFinancialData={initialFinancialData}
          initialImplications={initialImplications}
          onSave={onSave}
        />
      )}
      {!selectedType && (
        <>
          <Card
            className="cursor-pointer transition-all shadow-medium hover:shadow-medium-hover"
            onClick={() => handleTypeSelect("approval")}
          >
            <CardContent className="flex items-center gap-3 p-4">
              <div className="flex h-6 w-6 items-center justify-center rounded-full border border-green-500 bg-green-50">
                <Check className="h-4 w-4 text-green-500" />
              </div>
              <div>
                <h4 className="font-medium">Approval Decision</h4>
                <p className="text-sm text-muted-foreground">
                  Yes/No decision
                </p>
              </div>
            </CardContent>
          </Card>
          <Card
            className="cursor-pointer transition-all shadow-medium hover:shadow-medium-hover"
            onClick={() => handleTypeSelect("selection")}
          >
            <CardContent className="flex items-center gap-3 p-4">
              <div className="flex h-6 w-6 items-center justify-center rounded-full border border-indigo-500 bg-indigo-50">
                <List className="h-4 w-4 text-indigo-500" />
              </div>
              <div>
                <h4 className="font-medium">Selection Decision</h4>
                <p className="text-sm text-muted-foreground">
                  Choose from multiple options
                </p>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

export default DecisionType;
