'use client';
import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { useConvex } from 'convex/react';
import TagReporterCard from './TagReporterCard';
import { api } from '@/convex/_generated/api';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { MoreVertical, Pencil, Plus, Loader2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import ReactMarkdown from 'react-markdown';
import { Skeleton } from '@/components/ui/skeleton';

type Tag = {
  _id: string;
  name: string;
  [key: string]: any;
};

type TagWithChildren = Tag & { children?: TagWithChildren[] };

type Column = {
  id: string;
  name: string;
  tagIds: string[];
};

type KanbanState = {
  tagsById: { [id: string]: Tag };
  columns: Column[];
  pool: string[];
};

const initialColumnId = 'initial-column';
const initialColumnName = () => 'New Grouping';

// Helper to generate the next unique column ID (e.g., 'column-2', 'column-3', ...)
const getNextColumnId = (columns: Column[]) => {
  // Find all IDs matching 'column-N' and get the max N
  const regex = /^column-(\d+)$/;
  let max = 1;
  columns.forEach(col => {
    const match = regex.exec(col.id);
    if (match) {
      const num = parseInt(match[1], 10);
      if (num > max) max = num;
    }
  });
  // Next column is max+1
  return `column-${max + 1}`;
};

const TagKanbanBoard: React.FC = () => {
  const tags = useQuery(api.tags.fetchTags, { filter: {} }) ?? [];
  // Fetch all tag types (ID and name)
  const tagTypes = useQuery(api.tags.listTagTypes, {}) ?? {};
  // Build a mapping from tag_type ID to name
  const tagTypeMap = useMemo(() => {
    const map: Record<string, string> = {};
    const arr = Array.isArray(tagTypes) ? tagTypes : (tagTypes && Array.isArray((tagTypes as any).page) ? (tagTypes as any).page : []);
    arr.forEach((tt: any) => {
      if (tt && tt._id && tt.name) map[tt._id] = tt.name;
    });
    return map;
  }, [tagTypes]);

  // --- Tag Type Filter State ---
  // Find the tag_type whose name includes 'investment' (case-insensitive)
  const tagTypeArrRaw = useMemo(
    () =>
      Array.isArray(tagTypes)
        ? tagTypes
        : tagTypes && Array.isArray((tagTypes as any).page)
        ? (tagTypes as any).page
        : [],
    [tagTypes]
  );
  const defaultSelectedTagTypeIds = useMemo(() => {
    const arr = tagTypeArrRaw;
    const found = arr.find(
      (tt: any) =>
        typeof tt.name === "string" &&
        tt.name.toLowerCase().includes("investment")
    );
    return found ? [found._id] : [];
  }, [tagTypeArrRaw]);
  const [selectedTagTypeIds, setSelectedTagTypeIds] = useState<string[]>(defaultSelectedTagTypeIds);

  // When tagTypes load, set default selection if not already set
  React.useEffect(() => {
    if (selectedTagTypeIds.length === 0 && defaultSelectedTagTypeIds.length > 0) {
      setSelectedTagTypeIds(defaultSelectedTagTypeIds);
    }
     
  }, [defaultSelectedTagTypeIds]);

  // Only show tag types that are selected
  const tagTypeArr = useMemo(
    () =>
      tagTypeArrRaw.filter((tt: any) => selectedTagTypeIds.includes(tt._id)),
    [tagTypeArrRaw, selectedTagTypeIds]
  );

  const convex = useConvex();

  // Report results: { [columnId]: { [tagId]: Entity[] } }
  const [reportResults, setReportResults] = useState<{ [colId: string]: { [tagId: string]: any[] } }>({});
  const [reportLoading, setReportLoading] = useState(false);
  const [reportError, setReportError] = useState<string | null>(null);
  
  // --- AI Actions State ---
  const [columnSummaries, setColumnSummaries] = useState<
    { columnId: string; columnName: string; summary: string }[]
  >([]);
  const [columnComparison, setColumnComparison] = useState<string | null>(null);
  const [showSkeletons, setShowSkeletons] = useState(false);

  // Add individual loading states
  const [columnLoadingStates, setColumnLoadingStates] = useState<Record<string, boolean>>({});
  const [comparisonLoading, setComparisonLoading] = useState(false);

  // Column stats: { [colId]: { sum: number, count: number } }
  const [columnStats, setColumnStats] = useState<{ [colId: string]: { sum: number, count: number } }>({});

  // Handler for Generate Report button
  const handleGenerateReport = async () => {
    // Clear previous results and set loading states
    setReportResults({});
    setReportLoading(true);
    setShowSkeletons(true);
    setReportError(null);
    setColumnSummaries([]);
    setColumnComparison(null);
    setColumnStats({}); // Clear previous stats

    // Set individual loading states for each column
    const loadingStates: Record<string, boolean> = {};
    state.columns.forEach(col => {
      if (col.tagIds.length > 0) {
        loadingStates[col.id] = true;
      }
    });
    setColumnLoadingStates(loadingStates);
    setComparisonLoading(true);

    // Start processing after UI has updated with loading states
    const newResults: { [colId: string]: { [tagId: string]: any[] } } = {};
    const newStats: { [colId: string]: { sum: number, count: number } } = {};
    try {
      // 1. Existing report logic (per-tag report)
      await Promise.all(
        state.columns.map(async (col) => {
          if (!col.tagIds.length) return;
          newResults[col.id] = {};
          await Promise.all(
            col.tagIds.map(async (tagId) => {
              if (!state.tagsById[tagId]) return;
              try {
                const result = await convex.query(api.tags.tagKanbanReport, { tag: tagId });
                const entities: any[] = [];
                Object.values(result).forEach((arr) => {
                  if (Array.isArray(arr)) entities.push(...arr);
                });
                newResults[col.id][tagId] = entities;
              } catch (err: any) {
                newResults[col.id][tagId] = [];
                setReportError(
                  `Failed to generate report for tag "${state.tagsById[tagId]?.name || tagId}": ${err?.message || err}`
                );
              }
            })
          );
          // After all tags for this column are fetched, flatten all entities for the column
          const allEntities: any[] = [];
          Object.values(newResults[col.id]).forEach((arr) => {
            if (Array.isArray(arr)) allEntities.push(...arr);
          });
          // Debug: Log allEntities for this column to verify amount field
          console.log(`[TagKanbanBoard] allEntities for column ${col.id}:`, JSON.stringify(allEntities, null, 2));
          // Sum of amounts (use amount for decision entities)
          const sum = allEntities.reduce((acc, entity) => {
            if (entity.type === "decision") {
              let amt = 0;
              if (typeof entity.amount === "number") {
                amt = entity.amount;
              } else if (entity.amount !== undefined) {
                amt = parseFloat(entity.amount);
              }
              console.log(`[TagKanbanBoard] entity.id=${entity.id}, amount=`, amt, "raw amount field:", entity.amount);
              return acc + (isNaN(amt) ? 0 : amt);
            }
            return acc;
          }, 0);
          console.log(`[TagKanbanBoard] Calculated sum for column ${col.id}:`, sum);
          // Count of entities
          const count = allEntities.length;
          newStats[col.id] = { sum, count };
        })
      );
      setReportResults(newResults);
      setColumnStats(newStats);

      // 2. AI Actions: Process each column summary independently
      state.columns.forEach(async (col) => {
        if (!col.tagIds.length) return;
        // Get tag names for this column
        const tagNames = col.tagIds
          .map((tagId) => state.tagsById[tagId]?.name)
          .filter(Boolean);
        if (tagNames.length === 0) return;

        try {
          // Extract decision IDs from newResults for this column
          const decisionIdsSet = new Set<string>();
          col.tagIds.forEach((tagId) => {
            const entities = newResults?.[col.id]?.[tagId] || [];
            entities.forEach((entity: any) => {
              if (entity.type === "decision" && entity.id) {
                decisionIdsSet.add(entity.id);
              }
            });
          });
          const columnItems = Array.from(decisionIdsSet);

          // Get summary for this column
          const summaryRes = await convex.action(api.actions.aiActions.tagReporterColumnSummaryAction, {
            columnName: col.name,
            columnItems,
          });

          if (summaryRes && summaryRes.report) {
            // Update summaries immediately when each completes
            setColumnSummaries(prev => [
              ...prev,
              {
                columnId: col.id,
                columnName: col.name,
                summary: summaryRes.report,
              }
            ]);
          }

          // Update loading state for this column
          setColumnLoadingStates(prev => ({
            ...prev,
            [col.id]: false
          }));
        } catch (err: any) {
          setReportError(
            `Failed to summarize column "${col.name}": ${err?.message || err}`
          );
          // Update loading state even on error
          setColumnLoadingStates(prev => ({
            ...prev,
            [col.id]: false
          }));
        }
      });

      // 3. AI Action: Process column comparison will now be triggered by useEffect when all summaries are ready

    } catch (err: any) {
      setReportError(`Report generation failed: ${err?.message || err}`);
      // Clear all loading states on failure
      setColumnLoadingStates({});
      setComparisonLoading(false);
    } finally {
      setReportLoading(false);
    }
  };

  // 💤 Use a single query to fetch all tag hierarchies at once
  const allHierarchies = useQuery(api.tags.getAllHierarchies, {}) ?? {};

  // Build tagsById and pool from fetched tags
  const [state, setState] = useState<KanbanState>(() => {
    const tagsById: { [id: string]: Tag } = {};
    tags.forEach((tag: Tag) => {
      tagsById[tag._id] = tag;
    });
    // Always start with one column by default, using a constant id for hydration safety
    return {
      tagsById,
      columns: [
        { id: initialColumnId, name: initialColumnName(), tagIds: [] },
      ],
      pool: tags.map((tag: Tag) => tag._id),
    };
  });

  // Sync tags if they change
  React.useEffect(() => {
    if (!tags) return;
    setState((prev) => {
      const tagsById: { [id: string]: Tag } = {};
      tags.forEach((tag: Tag) => {
        tagsById[tag._id] = tag;
      });
      // Remove deleted tags from pool/columns
      const allTagIds = tags.map((t: Tag) => t._id);
      const pool = prev.pool.filter((id) => allTagIds.includes(id));
      const columns = prev.columns.map((col) => ({
        ...col,
        tagIds: col.tagIds.filter((id) => allTagIds.includes(id)),
      }));
      // Add new tags to pool
      allTagIds.forEach((id) => {
        if (
          !pool.includes(id) &&
          !columns.some((col) => col.tagIds.includes(id))
        ) {
          pool.push(id);
        }
      });
      // --- Clean up reportResults to remove stale columns/tags ---
      setReportResults((prevResults) => {
        const validColIds = new Set(columns.map(col => col.id));
        const validTagIds = new Set(allTagIds);
        const cleanedResults: typeof prevResults = {};
        for (const colId of Object.keys(prevResults)) {
          if (!validColIds.has(colId)) continue;
          cleanedResults[colId] = {};
          for (const tagId of Object.keys(prevResults[colId])) {
            if (validTagIds.has(tagId)) {
              cleanedResults[colId][tagId] = prevResults[colId][tagId];
            }
          }
        }
        return cleanedResults;
      });
      // --- End cleanup ---
      return { tagsById, columns, pool };
    });
  }, [JSON.stringify(tags)]);

  // Update useEffect to detect when real results come in
  React.useEffect(() => {
    console.log("columnComparison changed:", columnComparison);
    if (columnSummaries.length > 0 || columnComparison) {
      // If we have real results, hide the skeletons
      setShowSkeletons(false);
    }
  }, [columnSummaries, columnComparison]);

  // New useEffect: Trigger column comparison only when all column summaries are ready
  React.useEffect(() => {
    // Only run if a report is loading or comparison is loading
    if (!reportLoading && !comparisonLoading) return;

    // Only consider columns with tags
    const columnsWithTags = state.columns.filter(col => col.tagIds.length > 0);
    if (columnsWithTags.length < 2) return; // Need at least 2 columns for comparison

    // Check if all relevant columns are done loading
    const allDone = columnsWithTags.every(col => columnLoadingStates[col.id] === false);

    if (
      allDone &&
      comparisonLoading &&
      columnSummaries.length >= 2 &&
      !columnComparison
    ) {
      // Only include summaries for columns that are present and non-empty
      const summaries = columnsWithTags
        .map(col => columnSummaries.find(s => s.columnId === col.id))
        .filter((s): s is { columnId: string; columnName: string; summary: string } => !!s);

      if (summaries.length >= 2) {
        (async () => {
          try {
            const comparisonRes = await convex.action(api.actions.aiActions.tagReporterColumnComparisonAction, {
              columnSummaries: summaries.map(s => s.summary),
            });
            if (comparisonRes && comparisonRes.comparison) {
              console.log("Setting column comparison:", comparisonRes.comparison);
              setColumnComparison(comparisonRes.comparison);
            } else {
              console.error("Column comparison response invalid:", comparisonRes);
            }
          } catch (err: any) {
            setReportError(
              `Failed to compare/contrast columns: ${err?.message || err}`
            );
          } finally {
            setComparisonLoading(false);
          }
        })();
      } else {
        setComparisonLoading(false);
      }
    }
   
  }, [columnLoadingStates, columnSummaries, comparisonLoading, reportLoading, state.columns, columnComparison]);

  // Column editing state
  const [editingColId, setEditingColId] = useState<string | null>(null);
  const [colNameDraft, setColNameDraft] = useState('');
  
  // Add column
  const handleAddColumn = () => {
    setState((prev) => {
      // Use deterministic incrementing ID for new columns
      const nextId = getNextColumnId(prev.columns);
      return {
        ...prev,
        columns: [
          ...prev.columns,
          { id: nextId, name: initialColumnName(), tagIds: [] },
        ],
      };
    });
  };

  // Remove column
  const handleRemoveColumn = (colId: string) => {
    setState((prev) => {
      const col = prev.columns.find((c) => c.id === colId);
      const pool = [...prev.pool, ...(col ? col.tagIds : [])];
      return {
        ...prev,
        columns: prev.columns.filter((c) => c.id !== colId),
        pool,
      };
    });
    // --- Clean up reportResults for removed column ---
    setReportResults((prevResults) => {
      const newResults = { ...prevResults };
      delete newResults[colId];
      return newResults;
    });
    // --- End cleanup ---
  };
  // Stub: Download column
  const handleDownloadColumn = async (colId: string) => {
    const col = state.columns.find(c => c.id === colId);
    if (!col) return;
    const tagIds = col.tagIds;
    if (!tagIds || tagIds.length === 0) {
      window.alert('No tags in this column.');
      return;
    }
    try {
      // Fetch all decisions with joined data for these tagIds
      // @ts-ignore: Convex codegen may not be up to date, but this will work at runtime
      const results = await convex.query((api as any).decisions.exportDecisionsByTags, { tagIds });
      if (!results || results.length === 0) {
        window.alert('No decisions found for this column.');
        return;
      }
      // Flatten each result: merge decision, general_decision, investment_decision fields
      const flatResults: Record<string, any>[] = results.map((row: any) => {
        const { general_decision, investment_decision, ...decisionFields } = row;
        // Prefix fields to avoid collisions
        const generalFields: Record<string, any> = {};
        if (general_decision) {
          for (const [k, v] of Object.entries(general_decision)) {
            (generalFields as any)[`general_${k}`] = v;
          }
        }
        const investmentFields: Record<string, any> = {};
        if (investment_decision) {
          for (const [k, v] of Object.entries(investment_decision)) {
            (investmentFields as any)[`investment_${k}`] = v;
          }
        }
        return { ...decisionFields, ...generalFields, ...investmentFields };
      });
      // Collect all unique keys for CSV header
      const allKeys = Array.from(
        flatResults.reduce((set: Set<string>, obj: Record<string, any>) => {
          Object.keys(obj).forEach(k => set.add(k));
          return set;
        }, new Set<string>())
      );
      // Generate CSV
      const csvRows = [
        allKeys.join(','), // header
        ...flatResults.map((obj: Record<string, any>) =>
          allKeys.map(k => {
            const val = obj[k];
            if (val === null || val === undefined) return '';
            // Escape quotes and commas
            if (typeof val === 'string') {
              return `"${val.replace(/"/g, '""')}"`;
            }
            return String(val);
          }).join(',')
        ),
      ];
      const csvContent = csvRows.join('\r\n');
      // Trigger download
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${col.name || 'kanban-column'}.csv`;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    } catch (err: any) {
      window.alert('Failed to download CSV: ' + (err?.message || err));
    }
  };


  // Start renaming column
  const handleStartRename = (colId: string, currentName: string) => {
    setEditingColId(colId);
    setColNameDraft(currentName);
  };

  // Commit rename
  const handleRename = (colId: string) => {
    setState((prev) => ({
      ...prev,
      columns: prev.columns.map((col) =>
        col.id === colId ? { ...col, name: colNameDraft.trim() || col.name } : col
      ),
    }));
    setEditingColId(null);
    setColNameDraft('');
  };

  // Enhanced onDragEnd: support group drag for parent tags (draggableId starts with 'group:')
  const onDragEnd = (result: DropResult) => {
    const { source, destination, draggableId, type } = result;
    // Debug output for drag events
    console.log('onDragEnd:', {
      draggableId,
      source: { droppableId: source.droppableId, index: source.index },
      destination: destination ? { droppableId: destination.droppableId, index: destination.index } : null,
      type,
      pool: state.pool,
      columns: state.columns.map(col => ({ id: col.id, tagIds: col.tagIds }))
    });
    if (!destination) return;

    // Helper: is this a pool group droppable?
    const isPoolGroup = (id: string) => id.startsWith('pool-parent-') || id.startsWith('pool-ungrouped-');
    const isColumn = (id: string) => !!state.columns.find((col) => col.id === id);

    // Only allow pool group <-> column moves
    if (
      (isPoolGroup(source.droppableId) && isPoolGroup(destination.droppableId)) ||
      (isColumn(source.droppableId) && isColumn(destination.droppableId)) ||
      (isPoolGroup(source.droppableId) && !isColumn(destination.droppableId)) ||
      (!isPoolGroup(source.droppableId) && !isColumn(source.droppableId))
    ) {
      // Prevent cross-group moves and within-pool moves
      return;
    }

    setState((prev) => {
      // Handle column reordering (unchanged)
      if (type === 'COLUMN') {
        const newColumns = Array.from(prev.columns);
        const [removed] = newColumns.splice(source.index, 1);
        newColumns.splice(destination.index, 0, removed);
        return { ...prev, columns: newColumns };
      }

      let pool = Array.from(prev.pool);
      let columns = prev.columns.map((col) => ({
        ...col,
        tagIds: Array.from(col.tagIds),
      }));

      // Group drag: if dragging a parent tag (draggableId starts with 'group:')
      if (draggableId.startsWith('group:')) {
        const parentId = draggableId.replace('group:', '');
        // Find all children of this parent in the pool
        let groupChildren: string[] = [];
        // Find the tagType for this parent
        let parentTag: any = null;
        let tagTypeId: string | null = null;
        for (const tagType of Object.keys(allHierarchies)) {
          const hierarchy = allHierarchies[tagType];
          if (Array.isArray(hierarchy)) {
            for (const tag of hierarchy) {
              if (tag._id === parentId) {
                parentTag = tag;
                tagTypeId = tagType;
                break;
              }
            }
          }
          if (parentTag) break;
        }
        if (parentTag && parentTag.children) {
          groupChildren = parentTag.children.map((child: any) => child._id);
        }

        // Only allow group drag if all children are in the pool or all are in the column
        if (isPoolGroup(source.droppableId) && isColumn(destination.droppableId)) {
          // Move all children from pool to column
          // Remove all children from pool
          pool = pool.filter((id) => !groupChildren.includes(id));
          // Add all children to the column at the drop index, but skip any already present
          const colIdx = columns.findIndex((c) => c.id === destination.droppableId);
          if (colIdx !== -1) {
            // Filter out children already in the column
            const existing = new Set(columns[colIdx].tagIds);
            const toAdd = groupChildren.filter((id) => !existing.has(id));
            columns[colIdx].tagIds.splice(destination.index, 0, ...toAdd);
            // Defensive: deduplicate after insert
            columns[colIdx].tagIds = Array.from(new Set(columns[colIdx].tagIds));
          }
        } else if (isColumn(source.droppableId) && isPoolGroup(destination.droppableId)) {
          // Remove all group children from the column
          const colIdx = columns.findIndex((c) => c.id === source.droppableId);
          if (colIdx !== -1) {
            columns[colIdx].tagIds = columns[colIdx].tagIds.filter(
              (id) => !groupChildren.includes(id)
            );
          }
          // Add all children back to pool at the drop index, but skip any already present
          const poolSet = new Set(pool);
          const toAdd = groupChildren.filter((id) => !poolSet.has(id));
          pool.splice(destination.index, 0, ...toAdd);
          // Defensive: deduplicate after insert
          pool = Array.from(new Set(pool));
        }
        return { ...prev, pool, columns };
      }

      // Handle individual tag drag
      // Remove from source
      if (isPoolGroup(source.droppableId)) {
        pool.splice(source.index, 1);
      } else {
        const colIdx = columns.findIndex((c) => c.id === source.droppableId);
        if (colIdx !== -1) columns[colIdx].tagIds.splice(source.index, 1);
      }

      // Add to destination
      if (isPoolGroup(destination.droppableId)) {
        pool.splice(destination.index, 0, draggableId);
      } else {
        const colIdx = columns.findIndex((c) => c.id === destination.droppableId);
        if (colIdx !== -1)
          columns[colIdx].tagIds.splice(destination.index, 0, draggableId);
      }

      return { ...prev, pool, columns };
    });
  };

  // Responsive column width
  const colWidth = useMemo(() => {
    const count = Math.max(1, state.columns.length);
    if (count >= 4) return 'min-w-[220px] max-w-[1fr]';
    if (count === 3) return 'min-w-[260px]';
    if (count === 2) return 'min-w-[320px]';
    return 'min-w-[380px]';
  }, [state.columns.length]);

  console.log('[TagKanbanBoard] Rendering accordion:', { columnSummaries, columnComparison });
  state.columns.forEach(col => {
    if (reportResults[col.id]) {
      console.log(`[TagKanbanBoard] Rendering reportResults for column ${col.id}:`, reportResults[col.id]);
    }
  });
  return (
    <div className="flex flex-col gap-4 w-full">
      <h2 className="text-lg font-semibold mb-2">Tag Reporter</h2>
      {/* Tag Type Filter UI */}
      <div className="mb-2 flex flex-wrap gap-4 items-center">
        <span className="text-sm font-medium">Show tag types:</span>
        {tagTypeArrRaw.map((tt: any) => (
          <label key={tt._id} className="flex items-center gap-1 text-xs font-medium cursor-pointer">
            <input
              type="checkbox"
              checked={selectedTagTypeIds.includes(tt._id)}
              onChange={e => {
                setSelectedTagTypeIds(prev =>
                  e.target.checked
                    ? [...prev, tt._id]
                    : prev.filter(id => id !== tt._id)
                );
              }}
              className="accent-blue-600"
            />
            {tt.name}
          </label>
        ))}
      </div>
      <div className="mb-2 text-sm text-muted-foreground">
        Drag tags from the pool into columns. 
      </div>
      <DragDropContext onDragEnd={onDragEnd}>
        {/*
          Refactored: Each parent tag (and its children) is now its own Droppable zone.
          This prevents cross-group reordering and placeholder shifting.
        */}
        <div className="flex flex-wrap gap-1 p-3 mb-4 rounded-lg border min-h-[56px] bg-gray-50">
          {state.pool.length === 0 && (
            <span className="text-xs text-gray-400 italic">All tags assigned</span>
          )}
          {tagTypeArr.map((tagType: any) => {
            const hierarchyRaw = allHierarchies[tagType._id] ?? [];
            const hierarchy = Array.isArray(hierarchyRaw) ? hierarchyRaw : [hierarchyRaw];
            if (!hierarchy || hierarchy.length === 0) return null;
            // Parent tags: only those with children
            const parentTags = hierarchy.filter(
              (tag: any) => !tag.parentId && Array.isArray(tag.children) && tag.children.length > 0
            );
            // Ungrouped tags: tags with no parentId and no children (orphans or true ungrouped)
            // This includes orphaned parents (tags with no parentId and no children)
            const ungroupedTags = hierarchy.filter(
              (tag: any) => !tag.parentId && (!Array.isArray(tag.children) || tag.children.length === 0)
            );
            return (
              <div key={tagType._id} className="w-full mb-3">
                <div className="w-full text-xs font-bold text-gray-500 mb-1 uppercase tracking-wide">
                  {tagType.name}
                </div>
                <div className="flex flex-col gap-1">
                  {/* Each parent tag group is its own Droppable */}
                  {parentTags.map((parent: any, parentIdx: number) => {
                    // Only allow group drag if all children are in the pool
                    const allChildrenInPool = parent.children && parent.children.every((child: any) => state.pool.includes(child._id));
                    // Use a valid index for Draggable (use parentIdx)
                    return (
                      <Droppable
                        key={`pool-parent-${parent._id}`}
                        droppableId={`pool-parent-${parent._id}`}
                        direction="horizontal"
                        type="TAG"
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className="border rounded-sm mb-0 w-full overflow-hidden flex bg-white"
                          >
                            {/* Parent tag as left-side label (group drag handle) */}
                            <Draggable
                              key={`group:${parent._id}`}
                              draggableId={`group:${parent._id}`}
                              index={parentIdx} // Use parentIdx for a valid index
                              isDragDisabled={!allChildrenInPool}
                            >
                              {(groupProvided, groupSnapshot) => (
                                <div
                                  ref={groupProvided.innerRef}
                                  {...groupProvided.draggableProps}
                                  {...groupProvided.dragHandleProps}
                                  className={`flex-shrink-0 px-2 py-0.5 font-semibold text-blue-900 bg-blue-100 min-w-[100px] max-w-[120px] truncate border-r flex items-center text-xs leading-tight cursor-grab select-none ${!allChildrenInPool ? 'opacity-50' : ''}`}
                                  style={groupProvided.draggableProps.style}
                                >
                                  {parent.name}
                                </div>
                              )}
                            </Draggable>
                            {/* Children tags as draggables in this group */}
                            <div className="flex gap-1 flex-wrap px-1 py-0.5 flex-1 items-center">
                              {parent.children &&
                                // Only include children present in the pool, and use consecutive indices
                                parent.children.filter((child: any) => state.pool.includes(child._id)).map((child: any, idx: number) => (
                                  <Draggable
                                    key={child._id}
                                    draggableId={child._id}
                                    index={idx}
                                  >
                                    {(provided, snapshot) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className={`px-1.5 py-0 rounded-full text-xs font-medium cursor-grab bg-blue-50 text-blue-800 border border-blue-200 leading-tight ${snapshot.isDragging ? 'ring-1 ring-blue-400' : ''}`}
                                        style={{
                                          ...provided.draggableProps.style,
                                        }}
                                      >
                                        {child.name || child._id}
                                      </div>
                                    )}
                                  </Draggable>
                                ))}
                              {provided.placeholder}
                              {(!parent.children || parent.children.length === 0) && (
                                <span className="text-xs text-gray-400 italic">No child tags</span>
                              )}
                            </div>
                          </div>
                        )}
                      </Droppable>
                    );
                  })}
                  {/* All ungrouped tags for this tag type are in a single Droppable */}
                  {ungroupedTags.length > 0 && (
                    <Droppable
                      key={`pool-ungrouped-${tagType._id}`}
                      droppableId={`pool-ungrouped-${tagType._id}`}
                      direction="horizontal"
                      type="TAG"
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="border rounded-sm mb-0 w-full overflow-hidden flex bg-white"
                        >
                          <div className="flex-shrink-0 px-2 py-0.5 font-semibold text-gray-700 bg-gray-100 min-w-[100px] max-w-[120px] truncate border-r flex items-center text-xs leading-tight">
                            Ungrouped
                          </div>
                          <div className="flex gap-1 flex-wrap px-1 py-0.5 flex-1 items-center">
                            {ungroupedTags.map((tag: { _id: string; name: string }, idx: number) => (
                              state.pool.includes(tag._id) && (
                                <Draggable
                                  key={tag._id}
                                  draggableId={tag._id}
                                  index={idx}
                                >
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`px-1.5 py-0 rounded-full text-xs font-medium cursor-grab bg-gray-50 text-gray-600 border border-gray-200 leading-tight ${
                                        snapshot.isDragging ? 'ring-1 ring-gray-400' : ''
                                      }`}
                                      style={{
                                        ...provided.draggableProps.style
                                      }}
                                    >
                                      {tag.name || tag._id}
                                    </div>
                                  )}
                                </Draggable>
                              )
                            ))}
                            {provided.placeholder}
                          </div>
                        </div>
                      )}
                    </Droppable>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Show Generate Report button if there are tags in pool or columns */}
        {(state.pool.length > 0 || state.columns.some(col => col.tagIds.length > 0)) && (
          <div className="mb-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateReport}
              data-testid="generate-report-btn"
              disabled={reportLoading}
            >
              Generate Report
            </Button>
          </div>
        )}
        
        {/* Show error message if report generation fails */}
        {reportError && (
          <div className="mb-4 text-red-600 bg-red-50 border border-red-200 rounded px-3 py-2 text-sm">
            {reportError}
          </div>
        )}
        
        {/* Actual results - shown when not loading and not showing skeletons */}
        {(columnSummaries.length > 0 || columnComparison || comparisonLoading || Object.values(columnLoadingStates).some(loading => loading)) && (
          <div className="flex flex-col gap-4 mb-4 w-full">
            <Accordion type="multiple" className="w-full">
              {/* AI Comparison as first AccordionItem if there are multiple columns */}
              {state.columns.length > 1 && (columnComparison || (comparisonLoading && columnSummaries.length > 1)) && (
                <AccordionItem key="ai-comparison" value="ai-comparison">
                  <AccordionTrigger className="border border-blue-400 bg-blue-50 rounded-md px-3 py-2 my-1 w-full flex items-center">
                    <span className="font-semibold text-base flex items-center text-blue-900">
                      Overall Analysis {(comparisonLoading && !columnComparison) && <Loader2 className="w-4 h-4 ml-2 animate-spin" />}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="border border-blue-200 bg-blue-50 rounded-md p-4 mt-2">
                      {columnComparison ? (
                        <div className="text-blue-900 prose prose-sm max-w-none">
                          <ReactMarkdown>{columnComparison}</ReactMarkdown>
                        </div>
                      ) : (
                        <div className="text-sm text-blue-700">
                          Waiting for column summaries to generate comparison...
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              )}
              {/* Per-column summaries */}
              {state.columns.map((col) => {
                const summaryObj = columnSummaries.find(s => s.columnId === col.id);
                const isLoading = columnLoadingStates[col.id];
                return (
                  <AccordionItem key={col.id} value={col.id}>
                    <AccordionTrigger className="border border-blue-200 bg-blue-50 rounded-md px-3 py-2 my-1 w-full flex items-center">
                      <span className="font-semibold text-base flex items-center">
                        Analysis of Grouping: {col.name} {isLoading && <Loader2 className="w-4 h-4 ml-2 animate-spin" />}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="border border-blue-200 bg-blue-50 rounded-md p-4 mt-2">
                        {summaryObj ? (
                          <div className="prose prose-sm max-w-none">
                            <ReactMarkdown>{summaryObj.summary}</ReactMarkdown>
                          </div>
                        ) : (
                          <div className="text-sm text-blue-700">
                            {isLoading ? 'Generating summary...' : 'No summary available for this column.'}
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        )}
        
        {/* Columns */}
        <Droppable droppableId="columns" direction="horizontal" type="COLUMN">
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="flex gap-4 overflow-x-auto pb-2"
            >
              {state.columns.map((col, colIdx) => (
                <Draggable
                  key={col.id}
                  draggableId={col.id}
                  index={colIdx}
                >
                  {(dragProvided) => (
                    <div
                      ref={dragProvided.innerRef}
                      {...dragProvided.draggableProps}
                      className={`bg-white border rounded-lg flex flex-col ${colWidth} shadow-sm`}
                    >
                      <div className="flex items-center justify-between px-3 py-2 border-b bg-gray-50">
                        <div className="flex items-center gap-1">
                          <span {...dragProvided.dragHandleProps} className="cursor-move">
                            <Pencil size={14} className="inline-block text-gray-400" />
                          </span>
                          {editingColId === col.id ? (
                            <form
                              onSubmit={(e) => {
                                e.preventDefault();
                                handleRename(col.id);
                              }}
                              className="flex items-center gap-1"
                            >
                              <Input
                                value={colNameDraft}
                                onChange={(e) => setColNameDraft(e.target.value)}
                                onBlur={() => handleRename(col.id)}
                                autoFocus
                                className="h-7 text-xs px-2 py-1"
                              />
                            </form>
                          ) : (
                            <span
                              className="font-medium text-sm cursor-pointer"
                              onClick={() => handleStartRename(col.id, col.name)}
                            >
                              {col.name}
                            </span>
                          )}
                          {/* Column stats: sum and count */}
                          {columnStats[col.id] && (
                            <span className="ml-2 text-xs text-gray-500 font-normal">
                              (
                              {typeof columnStats[col.id].sum === "number"
                                ? columnStats[col.id].sum.toLocaleString(undefined, { style: "currency", currency: "USD", maximumFractionDigits: 0 })
                                : "$0"}
                              {" · "}
                              {columnStats[col.id].count} {columnStats[col.id].count === 1 ? "item" : "items"}
                              )
                            </span>
                          )}
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <button
                              className="ml-2 text-gray-400 hover:text-gray-600"
                              title="Column options"
                            >
                              <MoreVertical size={16} />
                            </button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onSelect={() => handleRemoveColumn(col.id)}
                              className="text-red-500 focus:text-red-600"
                            >
                              Delete column
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onSelect={() => handleDownloadColumn(col.id)}
                            >
                              Download
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {/* Tag drop area */}
                      <Droppable droppableId={col.id} type="TAG">
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={`flex flex-col gap-1 p-3 min-h-[56px] flex-1 ${snapshot.isDraggingOver ? 'bg-blue-50' : ''}`}
                          >
                            {col.tagIds.length === 0 && (
                              <span className="text-xs text-gray-300 italic">No tags</span>
                            )}
                            {col.tagIds.map((tagId, idx) => {
                              const tag = state.tagsById[tagId];
                              return (
                                <Draggable key={tagId} draggableId={tagId} index={idx}>
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`relative px-3 py-0.1 rounded-sm text-xs font-normal cursor-grab shadow bg-green-100 text-green-900 ${snapshot.isDragging ? 'ring-2 ring-green-400' : ''} group flex items-center`}
                                      style={provided.draggableProps.style}
                                    >
                                      {tag?.name || tagId}
                                      {/* Hover X button */}
                                      <button
                                        type="button"
                                        aria-label="Remove tag from column"
                                        className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-500 bg-white rounded-full p-0.5 h-5 w-5 flex items-center justify-center z-10"
                                        onClick={e => {
                                          e.stopPropagation();
                                          setState(prev => {
                                            // Remove tag from this column
                                            const columns = prev.columns.map(colInner =>
                                              colInner.id === col.id
                                                ? { ...colInner, tagIds: colInner.tagIds.filter(id => id !== tagId) }
                                                : colInner
                                            );
                                            // Add tag back to pool if not already present
                                            const pool = prev.pool.includes(tagId) ? prev.pool : [...prev.pool, tagId];
                                            return { ...prev, columns, pool };
                                          });
                                        }}
                                      >
                                        <span className="text-lg leading-none">×</span>
                                      </button>
                                    </div>
                                  )}
                                </Draggable>
                              );
                            })}
                            {provided.placeholder}
                            {/* Reported entities for this column, grouped by tag */}
                            {reportResults[col.id] &&
                              Object.entries(reportResults[col.id]).map(([tagId, entities]) =>
                                Array.isArray(entities) && entities.length > 0 ? (
                                  <div key={`report-${col.id}-${tagId}`} className="mt-2">
                                    <div className="text-xs font-semibold text-gray-500 mb-1">
                                      Report for tag: {state.tagsById[tagId]?.name || tagId}
                                    </div>
                                    <div className="flex flex-col gap-1">
                                      {entities.map((entity: any, idx: number) => (
                                        <React.Fragment key={entity.id || idx}>
                                          <TagReporterCard
                                            title={entity.name}
                                            badgeContent={entity.type}
                                          />
                                          {/* Render markdown if available */}
                                          {entity.markdown || entity.description ? (
                                            <div className="prose prose-sm max-w-none bg-gray-50 rounded p-2 mt-1 mb-2">
                                              <ReactMarkdown>{entity.markdown || entity.description}</ReactMarkdown>
                                            </div>
                                          ) : null}
                                        </React.Fragment>
                                      ))}
                                    </div>
                                  </div>
                                ) : null
                              )}
                          </div>
                        )}
                      </Droppable>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
              {/* Add column button */}
              <div className="flex flex-col justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-10 w-32 mt-2"
                  onClick={handleAddColumn}
                >
                  <Plus size={16} className="mr-1" />
                  Add Column
                </Button>
              </div>
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
    );
  };
  
  export default TagKanbanBoard;
