'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  AlertTriangle,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { BillStatus, BillType } from '@/zod/bills-schema';
import { useState } from 'react';
import { useToast } from '@/components/hooks/use-toast';
import { useConvex } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { format, isPast } from 'date-fns';

// Re-export the helper functions from priority-bills.tsx that we need
export type PriorityLevel = 'overdue' | 'watch' | 'upcoming' | 'handled';

interface PriorityInfo {
  level: PriorityLevel;
  reasons: string[];
  score: number;
  suggestedAction?: string;
}

interface Bill {
  _id: Id<'bills'>;
  dueDate: Date | number;
  billDate?: Date | number;
  amount: number;
  vendor: string;
  vendorId?: Id<'organizations'>;
  billStatus?: BillStatus;
  category?: string;
  type: BillType;
  isHighValue?: boolean;
  isUnusual?: boolean;
  percentageChange?: number;
  previousAmount?: number;
  billNo?: string;
  memo?: string;
}

interface PriorityBillsCardProps {
  bill: Bill;
  priority: PriorityInfo;
  onProcessBill: (billId: Id<'bills'>) => void;
  onViewDetails?: (billId: Id<'bills'>) => void;
  onUpdateStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<void>;
  tint?: {
    color: string;
    opacity?: number;
  };
}

// Helper function to get priority icon
function PriorityIcon({ level }: { level: PriorityLevel }) {
  switch (level) {
    case 'overdue':
      return (
        <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
      );
    case 'watch':
      return <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />;
    case 'upcoming':
      return <AlertTriangle className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
    case 'handled':
      return <AlertTriangle className="h-4 w-4 text-muted-foreground" />;
  }
}

// Helper function to format due days
function formatDueDays(bill: Bill): { text: string; className: string } {
  const now = new Date();
  const dueDateObj = new Date(bill.dueDate);
  const daysUntilDue = Math.ceil((dueDateObj.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

  if (isPast(dueDateObj)) {
    const daysOverdue = Math.abs(daysUntilDue);
    return {
      text: daysOverdue === 1 ? `1 day overdue` : `${daysOverdue} days overdue`,
      className: 'text-red-600 dark:text-red-400 font-medium'
    };
  }

  return {
    text: `Due ${format(dueDateObj, 'MMM d')}`,
    className: ''
  };
}

export function PriorityBillsCard({
  bill,
  priority,
  onProcessBill,
  onViewDetails,
  onUpdateStatus,
  tint
}: PriorityBillsCardProps) {
  const { level, reasons } = priority;
  const dueDateFormatted = formatDueDays(bill);
  const { toast } = useToast();
  const [isContactLoading, setIsContactLoading] = useState(false);
  const convex = useConvex();

  // Get the primary reason for the priority level (first reason)
  const primaryReason = reasons.length > 0 ? reasons[0] : '';

  // Handle contact button click
  const handleContactClick = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event

    // If we already have a vendorId, navigate directly
    if (bill.vendorId) {
      window.location.href = `/directory/organizations/${bill.vendorId}`;
      return;
    }

    // Otherwise search for the vendor by name
    try {
      setIsContactLoading(true);

      // Find the best matching vendor using the Convex client
      const result = await convex.action(api.directory.directoryOrganizations.findBestVendorMatch, {
        vendorName: bill.vendor
      });

      if (result.id) {
        // Navigate to the matched vendor
        window.location.href = `/directory/organizations/${result.id}`;
      } else {
        // No match found, show an error message
        toast({
          title: 'Vendor not found',
          description: `No matching vendor found for "${bill.vendor}"`,
          variant: 'destructive'
        });
        // Navigate to the directory as a fallback
        window.location.href = '/directory';
      }
    } catch (error) {
      console.error('Error looking up vendor:', error);
      toast({
        title: 'Error',
        description: 'Failed to find vendor information',
        variant: 'destructive'
      });
      // Navigate to the directory as a fallback
      window.location.href = '/directory';
    } finally {
      setIsContactLoading(false);
    }
  };

  return (
    <Card
      className={cn(
        'relative w-[360px] rounded-3xl transform-gpu will-change-transform',
        'shadow-deep transition-all duration-200 ease-in-out',
        'flex flex-col cursor-pointer overflow-hidden',
        'hover:shadow-deep-hover hover:-translate-y-1'
      )}
      style={{
        isolation: 'isolate',
        transform: 'translate3d(0, 0, 0)',
        backfaceVisibility: 'hidden',
      }}
      onClick={() => onViewDetails && onViewDetails(bill._id)}
    >
      <div className={cn('p-4 pb-1 relative z-1')}>
        <div className="flex items-center justify-between mb-1 gap-2">
          <div className="flex items-center gap-1.5 min-w-0">
            <p className="text-lg font-semibold text-neutral-800 truncate leading-tight">
              {bill.vendor}
            </p>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1.5 rounded-full bg-white/30 px-2.5 py-1 shadow-sm transform-gpu">
                  <PriorityIcon level={level} />
                  <span className="text-xs font-medium capitalize text-neutral-800">
                    {level}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p className="font-medium">Priority: {level}</p>
                <ul className="text-xs mt-1 list-disc list-inside">
                  {reasons.map((reason, i) => (
                    <li key={i}>{reason}</li>
                  ))}
                </ul>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex items-center justify-between relative">
          <div className="flex items-center gap-1 text-xs">
            <div className="flex items-center">
              <Calendar className="h-3 w-3 mr-0.5" />
              <span className={dueDateFormatted.className}>
                {format(new Date(bill.dueDate), 'MMM d')}
                {isPast(new Date(bill.dueDate)) && (
                  <span className="text-red-600 dark:text-red-400 ml-1">
                    • {dueDateFormatted.text}
                  </span>
                )}
              </span>
            </div>
          </div>

          {bill.billStatus && (
            <Badge
              variant="secondary"
              className="text-xs px-2 py-0 h-5 ml-auto font-medium bg-white/20 transform-gpu"
              style={{ transform: 'translate3d(0, 0, 0)' }}
            >
              {bill.billStatus}
            </Badge>
          )}
        </div>
      </div>

      <CardContent className="flex-1 p-4 pt-1 relative z-1">
        <div className="flex items-center justify-between mb-1">
          <p className="text-2xl font-bold text-neutral-800 truncate">
            ${bill.amount.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })}
          </p>
          <div className="flex flex-wrap gap-1.5 items-center shrink-0">
            {bill.category && (
              <span 
                className="text-xs bg-white/30 px-2 py-0.5 rounded-full border border-white/40 transform-gpu"
                style={{ transform: 'translate3d(0, 0, 0)' }}
              >
                {bill.category}
              </span>
            )}
            {bill.isHighValue && (
              <span 
                className="text-xs font-medium text-amber-600 bg-amber-50/50 px-2 py-0.5 rounded-full border border-amber-200/40 transform-gpu"
                style={{ transform: 'translate3d(0, 0, 0)' }}
              >
                High Value
              </span>
            )}
            {bill.isUnusual && (
              <span 
                className="text-xs font-medium text-purple-600 bg-purple-50/50 px-2 py-0.5 rounded-full border border-purple-200/40 transform-gpu"
                style={{ transform: 'translate3d(0, 0, 0)' }}
              >
                Unusual
              </span>
            )}
          </div>
        </div>

        {(primaryReason || bill.percentageChange || bill.memo) && (
          <div className="flex flex-wrap items-center gap-2 text-xs min-w-0">
            {primaryReason &&
              !primaryReason.includes('overdue') &&
              !primaryReason.includes('Due') && (
                <div className="flex items-center gap-1 font-medium min-w-0">
                  <AlertTriangle className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{primaryReason}</span>
                </div>
            )}

            {bill.percentageChange && bill.percentageChange !== 0 && (
              <div
                className={cn(
                  'flex items-center gap-1 rounded-full px-2 py-0.5 transform-gpu',
                  'bg-white/20 border border-white/40',
                  bill.percentageChange > 0
                    ? 'text-red-700'
                    : 'text-green-700'
                )}
                style={{ transform: 'translate3d(0, 0, 0)' }}
              >
                <TrendingUp className="h-3 w-3" />
                <span>
                  {bill.percentageChange > 0 ? '+' : ''}
                  {bill.percentageChange.toFixed(1)}%
                </span>
              </div>
            )}

            {bill.memo && (
              <div className="text-neutral-600 line-clamp-1">
                <span className="font-medium">Memo:</span> {bill.memo}
              </div>
            )}
          </div>
        )}
      </CardContent>

      <div 
        className={cn(
          'relative flex-none h-10 px-4',
          'flex items-center justify-between rounded-b-3xl',
          'bg-white/5'
        )}
        style={{
          isolation: 'isolate',
          transform: 'translate3d(0, 0, 0)',
        }}
      >
        {tint && (
          <div
            className="absolute inset-0 rounded-b-3xl pointer-events-none"
            style={{
              backgroundColor: tint.color,
              opacity: tint.opacity ?? 0.1,
              mixBlendMode: 'multiply',
              transform: 'translate3d(0, 0, 0)',
            }}
          />
        )}
        <div className="relative flex items-center gap-2 z-1">
          {bill.billNo && (
            <span className="text-xs text-neutral-600">#{bill.billNo}</span>
          )}
        </div>
        <div className="relative flex items-center gap-2 z-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs px-2 bg-white/30 hover:bg-white/40 text-gray-700 rounded-full transform-gpu"
            style={{ transform: 'translate3d(0, 0, 0)' }}
            onClick={handleContactClick}
            disabled={isContactLoading}
          >
            {isContactLoading ? 'Loading...' : 'Contact'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs px-2 bg-white/30 hover:bg-white/40 text-gray-700 rounded-full transform-gpu"
            style={{ transform: 'translate3d(0, 0, 0)' }}
            onClick={(e) => {
              e.stopPropagation();
              onProcessBill(bill._id);
            }}
          >
            Process
          </Button>
        </div>
      </div>
    </Card>
  );
}
