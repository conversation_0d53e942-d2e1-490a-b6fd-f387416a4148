import React from "react";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface TagReporterCardProps {
  title: React.ReactNode;
  description?: React.ReactNode;
  badgeContent?: React.ReactNode;
  children?: React.ReactNode;
}

const TagReporterCard: React.FC<TagReporterCardProps> = ({
  title,
  description,
  badgeContent,
  children,
}) => (
  <div
    className="px-2 py-0 rounded-sm text-xs font-normal bg-green-100 text-green-900 border border-green-200 flex items-center gap-1 justify-between truncate"
    style={{ minWidth: 0 }}
  >
    {/* 🤖 Title takes all available space, truncates only if needed before badge */}
    <span className="flex-grow min-w-0 truncate whitespace-nowrap">{title}</span>
    {badgeContent && (
      // 🤖 Badge styled to match pool tag chips for compactness
      <span className="px-1.5 py-0 rounded-full text-xs font-medium bg-blue-50 text-blue-800 border border-blue-200 leading-tight flex items-center">
        {badgeContent}
      </span>
    )}
  </div>
);

export default TagReporterCard;