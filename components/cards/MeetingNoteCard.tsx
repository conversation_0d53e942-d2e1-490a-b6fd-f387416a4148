'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { Doc } from '@/convex/_generated/dataModel';
import { format, formatDistanceToNow } from 'date-fns';
import { CalendarClock } from '@/components/icons'; // Import from centralized icons

// Define the expected props - initially based on the 'files' table document
// This might be enhanced later if the list query joins meeting_notes data
interface MeetingNoteCardProps {
  meetingNote: Doc<'files'> & { 
    // Optional fields if joined from meeting_notes table in list query
    meetingDate?: number | null; 
  }; 
  className?: string;
}

export const MeetingNoteCard = ({
  meetingNote,
  className,
}: MeetingNoteCardProps) => {
  
  const lastUpdated = meetingNote.updated_at
    ? formatDistanceToNow(new Date(meetingNote.updated_at), { addSuffix: true })
    : 'N/A';
    
  // Format meeting date if available (assuming it's passed via props later)
  const formattedMeetingDate = meetingNote.meetingDate
    ? format(new Date(meetingNote.meetingDate), 'MMM d, yyyy')
    : null;

  return (
    <Card className={`overflow-hidden h-full flex flex-col ${className}`}>
      {/* Removed flex layout */}
      <CardHeader className="pb-3">
         {/* Removed icon wrapper div */}
         {/* Removed title wrapper div */}
         <CardTitle className="text-base font-semibold leading-tight truncate">
           {meetingNote.title || 'Untitled Meeting Note'}
         </CardTitle>
         {/* Display Meeting Date if available */}
         {formattedMeetingDate && (
           <p className="text-xs text-muted-foreground mt-1">{formattedMeetingDate}</p>
         )}
      </CardHeader>
      <CardContent className="pt-2 pb-3 text-xs text-muted-foreground flex-grow">
         {/* Icon and short description in flex layout */}
         <div className="flex items-start">
           {/* Apply Meeting Note color (amber) */}
           <div>
             <CalendarClock className="h-5 w-5 text-amber-500" />
           </div>
           {meetingNote.short_description && (
             <CardDescription className="text-xs ml-2 line-clamp-5">
               {meetingNote.short_description}
             </CardDescription>
           )}
         </div>
         {/* Placeholder for Attendees count */}
         {/* <div>Attendees: Placeholder</div> */}
         {/* Placeholder for relationship count */}
         {/* <div>Related Items: Placeholder</div> */}
      </CardContent>
       <div className="border-t px-4 py-2 text-xs text-muted-foreground">
         Updated {lastUpdated}
       </div>
    </Card>
  );
};
