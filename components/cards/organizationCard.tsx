import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/components/lib/utils';
import { OrganizationWithPeopleCount, OrganizationCardProps } from '@/types/directory-ui';
import { Users } from 'lucide-react';

/**
 * OrganizationCard component
 * 
 * A modern, sleek, and compact card for displaying organization information
 * Shows organization details and people count
 */
export function OrganizationCard({
  organization,
  onClick,
  className,
  selectable = false,
  selected = false,
}: OrganizationCardProps) {
  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all duration-200 hover:shadow-md",
        "border border-border h-full",
        onClick && "cursor-pointer",
        selectable && "hover:border-primary/50",
        selected && "border-primary border-2",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="flex flex-col">
          {/* Name */}
          <h3 className="font-medium text-sm text-foreground truncate">
            {organization.name}
          </h3>

          {/* Description */}
          {organization.description && (
            <p className="text-xs text-muted-foreground mt-0.5 truncate">
              {organization.description}
            </p>
          )}

          {/* People Count */}
          <div className="mt-1 flex items-center gap-1">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {organization.peopleCount} Members
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
