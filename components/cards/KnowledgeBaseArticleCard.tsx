'use client';

'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Doc } from '@/convex/_generated/dataModel';
import { formatDistanceToNow } from 'date-fns';
import { PanelLeft } from '@/components/icons'; // Import the correct KB icon

// Define the expected props - initially based on the 'files' table document
interface KnowledgeBaseArticleCardProps {
  article: Doc<'files'>; // Expecting the document from the 'files' table
  className?: string;
}

export const KnowledgeBaseArticleCard = ({
  article,
  className,
}: KnowledgeBaseArticleCardProps) => {
  
  // Format the updated_at timestamp
  const lastUpdated = article.updated_at
    ? formatDistanceToNow(new Date(article.updated_at), { addSuffix: true })
    : 'N/A';

  return (
    <Card className={`overflow-hidden h-full flex flex-col ${className}`}>
      {/* Removed flex layout from CardHeader */}
      <CardHeader className="pb-3"> 
         {/* Removed icon wrapper div */}
         {/* Removed title wrapper div */}
         <CardTitle className="text-base font-semibold leading-tight truncate">
           {article.title || 'Untitled Article'}
         </CardTitle>
      </CardHeader>
      <CardContent className="pt-2 pb-3 text-xs text-muted-foreground flex-grow"> 
         {/* Icon floated left, description wraps */}
         {/* Apply KB color (blue) and use KB icon */}
         <div className="float-left mr-2">
           <PanelLeft className="h-5 w-5 text-blue-500" />
         </div>
         {article.short_description && (
           <CardDescription className="text-xs line-clamp-5">
             {article.short_description}
           </CardDescription>
         )}
         {/* Placeholder for owner/author */}
         {/* <div>Owner: Placeholder</div> */}
         {/* Placeholder for relationship count */}
         {/* <div>Related Items: Placeholder</div> */}
      </CardContent>
       <div className="border-t px-4 py-2 text-xs text-muted-foreground">
         Updated {lastUpdated}
       </div>
    </Card>
  );
};
