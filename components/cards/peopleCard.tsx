import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/components/lib/utils';
import { PersonWithOrganizations, PeopleCardProps } from '@/types/directory-ui';
import { Check, User, Users } from 'lucide-react';

/**
 * PeopleCard component
 * 
 * A modern, sleek, and compact card for displaying person information
 * Shows person details, user badge (if linked), and organizations
 */
export function PeopleCard({
  person,
  onClick,
  className,
  showOrganizations = true,
  showUserBadge = true,
  maxOrganizations = 2,
  selectable = false,
  selected = false,
}: PeopleCardProps) {
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Determine the number of organizations
  const organizationCount = person.organizations?.length || 0;

  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all duration-200 hover:shadow-md",
        "border border-border h-full",
        onClick && "cursor-pointer",
        selectable && "hover:border-primary/50",
        selected && "border-primary border-2",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="flex items-center gap-3">
          {/* Avatar */}
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={person.image} alt={person.name} />
            <AvatarFallback>{getInitials(person.name)}</AvatarFallback>
          </Avatar>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {/* Name and badges row */}
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="font-medium text-sm text-foreground truncate">
                {person.name}
              </h3>

              {/* User indicator */}
              {person.isLinkedToUser && (
                <span className="h-2 w-2 rounded-full bg-sky-500 inline-block" title="User"></span>
              )}

              {/* Selected indicator */}
              {selectable && selected && (
                <Badge className="ml-auto bg-primary text-primary-foreground px-1.5 h-5">
                  <Check className="h-3 w-3 mr-1" />
                  <span className="text-xs">Selected</span>
                </Badge>
              )}
            </div>

            {/* Title */}
            {person.title && (
              <p className="text-xs text-muted-foreground mt-0.5 truncate">
                {person.title}
              </p>
            )}

            {/* Email */}
            {person.email && (
              <p className="text-xs text-muted-foreground mt-0.5 truncate">
                {person.email}
              </p>
            )}

            {/* Organization count */}
            {showOrganizations && organizationCount > 0 && (
              <div className="mt-1 flex items-center gap-1">
                <Users className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">{organizationCount}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
