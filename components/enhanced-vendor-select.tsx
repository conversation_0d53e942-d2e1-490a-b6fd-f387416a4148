import { useState } from 'react';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Id } from '@/convex/_generated/dataModel';
import { OrganizationSchema } from '@/zod/directory-schema';
import { z } from 'zod';

type Organization = z.infer<typeof OrganizationSchema>;

export interface EnhancedVendorSelectProps {
  value?: Id<'organizations'>;
  onChange: (value: Id<'organizations'>) => void;
  onSearch: (query: string) => void;
  organizations: Organization[];
  suggestedVendor: {
    name: string;
    isNew: boolean;
    confidence: number;
  } | null;
}

export function EnhancedVendorSelect({
  value,
  onChange,
  onSearch,
  organizations,
  suggestedVendor
}: EnhancedVendorSelectProps) {
  const [open, setOpen] = useState(false);

  const selectedVendor = organizations.find((org) => org._id === value);
  const showingSuggestedVendor = suggestedVendor?.isNew && !selectedVendor;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild className="pointer-events-auto">
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'h-7 w-full justify-between',
            showingSuggestedVendor && 'border-primary/50 bg-primary/5'
          )}
        >
          <div className="flex items-center gap-2 truncate text-sm">
            {showingSuggestedVendor ? (
              <>
                <span className="truncate">{suggestedVendor.name}</span>
                <Badge variant="outline" className="bg-primary/5 text-[10px]">
                  New Vendor
                </Badge>
              </>
            ) : (
              <span className="text-sm">{selectedVendor?.name || 'Select vendor...'}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command>
          <CommandInput 
            placeholder="Search vendors..."
            onValueChange={onSearch}
            className="h-8"
          />
          <CommandEmpty className="py-2 text-sm">
            No vendor found.
          </CommandEmpty>
          <CommandGroup>
            {suggestedVendor?.isNew && (
              <CommandItem
                value={suggestedVendor.name}
                className="flex items-center gap-2 text-sm"
                onSelect={() => {
                  setOpen(false);
                }}
              >
                <Plus className="h-3 w-3" />
                <span>Create "{suggestedVendor.name}"</span>
                <Badge variant="outline" className="ml-auto text-[10px]">
                  New
                </Badge>
              </CommandItem>
            )}
            {organizations.map((vendor) => (
              <CommandItem
                key={vendor._id}
                value={vendor.name}
                className="text-sm"
                onSelect={() => {
                  onChange(vendor._id);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-3 w-3',
                    value === vendor._id ? 'opacity-100' : 'opacity-0'
                  )}
                />
                {vendor.name}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
