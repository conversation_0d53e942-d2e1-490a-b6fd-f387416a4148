"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { MicOff } from "lucide-react"
import { useRealtimeConversationAgent } from "./conversations/RealtimeConversationAgent"
import { Id } from "@/convex/_generated/dataModel"
import { useUserData } from './user-data-provider'

interface FojoAssistantProps {
  hasDescription: boolean
  entityType: string // 'project'
  entityId: Id<any> // Project ID
  className?: string
  sessionConfig?: {
    // Audio format options
    input_audio_format?: 'pcm16' | 'g711_ulaw' | 'g711_alaw'
    output_audio_format?: 'pcm16' | 'g711_ulaw' | 'g711_alaw'
    
    // Audio processing
    input_audio_noise_reduction?: { type: 'near_field' | 'far_field' } | null
    input_audio_transcription?: {
      language?: string
      model?: 'gpt-4o-transcribe' | 'gpt-4o-mini-transcribe' | 'whisper-1'
      prompt?: string
    } | null
    
    // Turn detection configuration
    turn_detection?: {
      type?: 'server_vad' | 'semantic_vad'
      create_response?: boolean
      eagerness?: 'low' | 'medium' | 'high' | 'auto'
      interrupt_response?: boolean
      prefix_padding_ms?: number
      silence_duration_ms?: number
      threshold?: number
    } | null
    
    // Model behavior
    model?: 'gpt-4o-realtime-preview' | 'gpt-4o-mini-transcribe' | 'gpt-4o-transcribe' | 'whisper-1'
    instructions?: string
    temperature?: number
    max_response_output_tokens?: number | 'inf'
    
    // Response configuration
    modalities?: Array<'audio' | 'text'>
    voice?: 'alloy' | 'ash' | 'ballad' | 'coral' | 'echo' | 'fable' | 'onyx' | 'nova' | 'sage' | 'shimmer' | 'verse'
    
    // Tools configuration
    tools?: Array<{
      type: string
      name: string
      description?: string
      parameters?: any
    } | {
      type: string
      function: {
        name: string
        description?: string
        parameters?: any
      }
      strict?: boolean
    }>
    tool_choice?: 'auto' | 'none' | 'required' | string
  }
}

export function FojoAssistantFriendlyV1({ 
  hasDescription = false, 
  entityType, 
  entityId, 
  className,
  sessionConfig
}: FojoAssistantProps) {
  const [isBlinking, setIsBlinking] = useState(false)
  const [isTalking, setIsTalking] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const { person } = useUserData();
  
  // Get the first name from person data
  const firstName = person?.name?.split(' ')[0] || 'there';
  
  // Initialize the realtime conversation agent
  const {
    startConversation,
    stopConversation,
    isRecording,
    connectionStatus
  } = useRealtimeConversationAgent({
    sessionConfig: sessionConfig || {},
    entityType,
    entityId,
    onError: (error) => {
      console.error("Conversation error:", error);
    }
  });

  // Determine if we're in connecting state
  const isConnecting = connectionStatus === 'connecting';

  // Blinking animation
  useEffect(() => {
    const blinkInterval = setInterval(() => {
      setIsBlinking(true)
      setTimeout(() => setIsBlinking(false), 200)
    }, 3000)
    return () => clearInterval(blinkInterval)
  }, [])

  // Talking animation
  useEffect(() => {
    const talkInterval = setInterval(() => {
      setIsTalking(true)
      setTimeout(() => setIsTalking(false), 300)
    }, 2000)
    return () => clearInterval(talkInterval)
  }, [])

  return (
    <>
      <Card
        className={cn(
          "mb-4 overflow-hidden border-2 rounded-xl transition-all duration-300 shadow-xl",
          "bg-gradient-to-br from-white/80 to-sky-50/80 backdrop-blur-sm backdrop-filter",
          hasDescription ? "border-sky-200" : "border-sky-300",
          isHovered ? "border-sky-400 shadow-2xl" : "",
          isRecording ? "border-green-500 shadow-[0_0_15px_5px_rgba(50,205,50,0.4)]" : "",
          "animate-slideIn", // Custom animation class
          className,
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
      <CardContent className="p-0">
        <div className="flex items-start justify-between p-4">
          <div className="flex gap-3">
            <div
              className={cn(
                "flex h-12 w-12 shrink-0 items-center justify-center rounded-full transition-all duration-300",
                hasDescription ? "bg-sky-100" : "bg-sky-200",
                isHovered ? "scale-110" : "",
              )}
            >
              {/* Animated Friendly Robot Face */}
              <div className="relative h-8 w-8">
                {/* Face */}
                <div className="h-8 w-8 rounded-full bg-white"></div>

                {/* Left Eye */}
                <div
                  className={cn(
                    "absolute left-1 top-2 h-1.5 w-1.5 rounded-full transition-all duration-150",
                    isBlinking ? "h-0.5" : "h-1.5",
                    isRecording ? "bg-green-500" : "bg-sky-500"
                  )}
                ></div>

                {/* Right Eye */}
                <div
                  className={cn(
                    "absolute right-1 top-2 h-1.5 w-1.5 rounded-full transition-all duration-150",
                    isBlinking ? "h-0.5" : "h-1.5",
                    isRecording ? "bg-green-500" : "bg-sky-500"
                  )}
                ></div>

                {/* Mouth */}
                <div
                  className={cn(
                    "absolute bottom-1.5 left-1/2 -translate-x-1/2 rounded-full transition-all duration-200",
                    isRecording 
                      ? "h-2 w-2 rounded-full bg-green-500" 
                      : (isTalking ? "h-2 w-2 rounded-full bg-sky-500" : "h-1 w-3 bg-sky-500")
                  )}
                ></div>

                {/* Antenna */}
                <div className={cn(
                  "absolute -top-2 left-1/2 h-2 w-0.5 -translate-x-1/2",
                  isRecording ? "bg-green-500" : "bg-sky-500"
                )}>
                  <div className={cn(
                    "absolute -top-1 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full",
                    isRecording ? "bg-green-500" : "bg-sky-500"
                  )}></div>
                </div>
              </div>
            </div>
            <div className="space-y-1">
              <h3 className={cn(
                "font-medium leading-none",
                isRecording ? "text-green-800" : "text-sky-800"
              )}>
                {isRecording 
                  ? "Listening... Click the button to stop." 
                  : (isConnecting
                      ? "Connecting to AI assistant..." 
                      : (hasDescription ? `Hi ${firstName}! Need any help?` : `Hi ${firstName}! I'm Fojo, your friendly assistant`))}
              </h3>
              <p className={cn(
                "text-sm",
                isRecording ? "text-green-600" : (isConnecting ? "text-amber-600" : "text-sky-600")
              )}>
                {isRecording
                  ? "Real-time conversation in progress."
                  : (isConnecting
                      ? "Starting a conversation. This may take a moment..."
                      : (hasDescription
                        ? "I can help improve your description or create updates for your project."
                        : "I'm here to help you describe your project and get organized!"))}
              </p>
            </div>
          </div>
          <div className="flex items-center justify-center">
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "rounded-full transition-all duration-300 border-2 h-10 cursor-pointer",
                isRecording 
                  ? "border-red-300 text-red-500 bg-red-50 hover:bg-red-50 hover:border-red-300 hover:text-red-500" 
                  : (isConnecting
                      ? "border-amber-400 text-amber-700 bg-amber-100"
                      : "border-sky-400 text-sky-800 bg-gradient-to-br from-white/90 to-sky-50/90"),
                !isRecording && !isConnecting && isHovered ? "shadow-md border-sky-500 translate-y-[-4px]" : "",
              )}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={() => isRecording ? stopConversation() : startConversation()}
              disabled={isConnecting}
            >
              <div className="flex items-center justify-center gap-2">
                {isRecording 
                  ? "Stop" 
                  : (isConnecting 
                      ? "Connecting..." 
                      : (hasDescription ? "Ask Fojo" : "Let's Get Started"))}
                
                {isConnecting ? (
                  <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : isRecording ? (
                  <MicOff className="w-4 h-4 relative z-10" />
                ) : (
                  <span
                    className={cn(
                      "relative transition-all duration-300 inline-flex items-center justify-center",
                      "w-4 h-4",
                    )}
                  >
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      viewBox="0 0 24 24" 
                      className="w-4 h-4 relative z-10"
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2"
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    >
                      {/* Microphone base */}
                      <rect x="9" y="2" width="6" height="12" rx="3" />
                      {/* Microphone stand */}
                      <path d="M5 10a7 7 0 0 0 14 0" />
                      <line x1="12" y1="19" x2="12" y2="22" />
                      <line x1="8" y1="22" x2="16" y2="22" />
                    </svg>
                    
                    {/* Circular Sound waves */}
                    <div
                      className={cn(
                        "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transition-opacity duration-300",
                        isHovered ? "opacity-100" : "opacity-0",
                      )}
                    >
                      <div className="absolute left-1/2 top-1/2 h-5 w-5 -translate-x-1/2 -translate-y-1/2 rounded-full border border-sky-400/60 opacity-0 animate-circleWave1"></div>
                      <div className="absolute left-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2 rounded-full border border-sky-400/60 opacity-0 animate-circleWave2"></div>
                      <div className="absolute left-1/2 top-1/2 h-7 w-7 -translate-x-1/2 -translate-y-1/2 rounded-full border border-sky-400/60 opacity-0 animate-circleWave3"></div>
                    </div>
                  </span>
                )}
              </div>
            </Button>
          </div>
        </div>
      </CardContent>

      <style jsx global>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes circleWave1 {
          0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.4; }
          100% { transform: translate(-50%, -50%) scale(1.1); opacity: 0; }
        }
        
        @keyframes circleWave2 {
          0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.4; }
          100% { transform: translate(-50%, -50%) scale(1.1); opacity: 0; }
        }
        
        @keyframes circleWave3 {
          0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.4; }
          100% { transform: translate(-50%, -50%) scale(1.1); opacity: 0; }
        }
        
        .animate-circleWave1 {
          animation: circleWave1 1.2s ease-out infinite;
        }
        
        .animate-circleWave2 {
          animation: circleWave2 1.2s ease-out infinite 0.3s;
        }
        
        .animate-circleWave3 {
          animation: circleWave3 1.2s ease-out infinite 0.6s;
        }
      `}</style>
      </Card>

      {/* Hidden audio element for playback */}
      <audio id="remote-audio" style={{ display: 'none' }}></audio>
    </>
  )
}
