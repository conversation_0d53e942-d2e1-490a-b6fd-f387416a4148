'use client';

'use client';

import React, { useState } from 'react'; // Removed useEffect
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useMutation, useQuery } from 'convex/react';
import { useToast } from '@/components/hooks/use-toast'; // Assuming correct path
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel'; // Import Id
import { AddPersonModal } from '@/components/directory/add-person-modal';
import { AddOrganizationModal } from '@/components/directory/add-organization-modal';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"; // Import Popover
import { Button } from "@/components/ui/button"; // Import Button
import {
  PanelLeftClose,
  Plus,
  Clock,
  Book,
  CalendarClock,
  FileText, // Added FileText icon
  File, // Added File icon
  Users,
  Building
} from '@/components/icons';

// Helper function to format time ago
function formatTimeAgo(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  }
}

export function HeaderBar() {
  const pathname = usePathname();
  const router = useRouter();
  // Removed isExpanded state as it's no longer needed for padding
  const [isAddPersonModalOpen, setIsAddPersonModalOpen] = useState(false);
  const [isAddOrganizationModalOpen, setIsAddOrganizationModalOpen] = useState(false);
  const [isAddDocPopoverOpen, setIsAddDocPopoverOpen] = useState(false); // State for Popover
  const [isAddDirectoryPopoverOpen, setIsAddDirectoryPopoverOpen] = useState(false);
  const currentUser = useQuery(api.users.currentUser);
  const createDecision = useMutation(api.decisions.createDecisions);
  const createTask = useMutation(api.tasks.createTasks); // Add mutation for creating tasks
  const createProject = useMutation(api.projects.createProjects); // Add mutation for creating projects
  const createKBArticle = useMutation(api.files.files.createKnowledgeBaseArticle); // Add mutation
  const createMeetingNote = useMutation(api.files.files.createMeetingNote); // Add mutation
  const generalDecisionCategory = useQuery(api.decisions.getDecisionCategoryBySubtableType, {
    subtableType: 'general_decisions'
  });
  const { toast } = useToast();

  // Removed useEffect for listening to sidebar state changes

  // Toggle sidebar (still needed to trigger the hover effect visually if desired, though layout won't change)
  const handleSidebarToggle = () => {
    const event = new CustomEvent('toggleSidebar');
    window.dispatchEvent(event);
  };

  // Get current page title
  const getPageTitle = () => {
    if (pathname === '/home') return 'Homepage';
    if (pathname === '/bills') return 'Bills';
    if (pathname === '/reports') return 'Reports';
    if (pathname === '/decisions') return 'Decisions';
    if (pathname === '/tasks') return 'Tasks';
    if (pathname === '/projects') return 'Projects';
    if (pathname === '/documents') return 'Documents';
    if (pathname === '/directory') return 'Directory';
    if (pathname === '/admin') return 'Admin';

    // Handle nested paths
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length > 1) {
      const base = segments[0];
      if (['bills', 'reports', 'decisions', 'tasks', 'projects', 'documents', 'directory', 'admin'].includes(base)) {
        return base.charAt(0).toUpperCase() + base.slice(1);
      }
    }

    return 'Page';
  };

  // Generate breadcrumb segments
  const getBreadcrumbs = () => {
    const segments = pathname.split('/').filter(Boolean);

    // Special case for dashboard/home
    if (segments.length === 2 && segments[0] === 'dashboard' && segments[1] === 'home') {
      return [{ label: 'Homepage', path: '/home', isLast: true }];
    }

    const breadcrumbs = [];

    // Always add Home as first breadcrumb
    breadcrumbs.push({ label: 'Home', path: '/home', isLast: false });

    // Process each path segment
    let currentPath = '';

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      currentPath += `/${segment}`;

      // Skip 'dashboard' segment in the breadcrumb display
      if (segment === 'dashboard') continue;

      // Format the label
      let label = segment;

      // Format main section names
      if (i === 0 || (i === 1 && segments[0] === 'dashboard')) {
        if (['bills', 'reports', 'decisions', 'tasks', 'projects', 'documents', 'directory', 'admin'].includes(segment)) {
          label = segment.charAt(0).toUpperCase() + segment.slice(1);
        }
      }

      // Check if this is the last segment
      const isLast = i === segments.length - 1;

      breadcrumbs.push({ label, path: currentPath, isLast });
    }

    return breadcrumbs;
  };

  // Get action button based on current path
  const getActionButton = () => {
    if (pathname === '/bills') {
      return (
        <button
          onClick={() => router.push('/bills?new=true')}
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Bill
        </button>
      );
    }

    if (pathname === '/reports') {
      return (
        <button
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Report
        </button>
      );
    }

    if (pathname === '/decisions') {
      return (
        <button
          onClick={async () => {
            try {
              // Check if we have the general category
              if (!generalDecisionCategory?._id) {
                toast({
                  title: 'Error',
                  description: 'Could not find general decision category',
                  variant: 'destructive'
                });
                return;
              }

              const { ids } = await createDecision({
                input: {
                  title: 'New Decision',
                  decision_category_id: generalDecisionCategory._id as Id<'decision_categories'>
                }
              });
              const id = ids[0];
              console.log('Navigating to decision:', id);
              router.push(`/decisions/${id}`);
            } catch (error: unknown) {
              toast({
                variant: 'destructive',
                title: 'Uh oh! Something went wrong.',
                description:
                  error instanceof Error
                    ? error.message
                    : 'Failed to create decision.'
              });
            }
          }}
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Decision
        </button>
      );
    }

    if (pathname === '/tasks') {
      return (
        <button
          onClick={async () => {
            try {
              // Create a new task with a default name
              const result = await createTask({
                input: { name: 'Untitled Task' } // Provide default name
              });
              // Ensure result.ids exists and has at least one ID
              if (result && result.ids && result.ids.length > 0) {
                const newTaskId = result.ids[0];
                // Redirect to the new task's detail page
                router.push(`/tasks/${newTaskId}`);
              } else {
                // Handle case where ID might not be returned as expected
                 toast({
                  variant: 'destructive',
                  title: 'Uh oh! Something went wrong.',
                  description: 'Failed to get new task ID after creation.'
                });
              }
            } catch (error: unknown) {
              // Display error toast if creation fails
              toast({
                variant: 'destructive',
                title: 'Uh oh! Something went wrong.',
                description:
                  error instanceof Error
                    ? error.message
                    : 'Failed to create task.'
              });
            }
          }}
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Task
        </button>
      );
    }

    if (pathname === '/projects') {
      return (
        <button
          onClick={async () => {
            try {
              // Create a new project with a default name and status
              const result = await createProject({
                input: { name: 'Untitled Project', status: 'not_started' } // Provide default name and status
              });
              // Ensure result.ids exists and has at least one ID
              if (result && result.ids && result.ids.length > 0) {
                const newProjectId = result.ids[0];
                // Redirect to the new project's overview page
                router.push(`/projects/${newProjectId}/overview`);
              } else {
                // Handle case where ID might not be returned as expected
                 toast({
                  variant: 'destructive',
                  title: 'Uh oh! Something went wrong.',
                  description: 'Failed to get new project ID after creation.'
                });
              }
            } catch (error: unknown) {
              // Display error toast if creation fails
              toast({
                variant: 'destructive',
                title: 'Uh oh! Something went wrong.',
                description:
                  error instanceof Error
                    ? error.message
                    : 'Failed to create project.'
              });
            }
          }}
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Project
        </button>
      );
    }

    // Add Document Popover for the main /documents page
    if (pathname === '/documents') {
      return (
        <Popover open={isAddDocPopoverOpen} onOpenChange={setIsAddDocPopoverOpen}>
          <PopoverTrigger asChild>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Document
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56 p-2">
            <div className="grid gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={async () => {
                  if (!currentUser) { toast({ title: "Authentication Error", description: "You must be logged in.", variant: "destructive" }); return; }
                  try {
                    const newArticleId = await createKBArticle({ title: "Untitled KB Article", ownerId: currentUser._id });
                    router.push(`/documents/knowledge-base/${newArticleId}`);
                    toast({ title: "Success", description: "New KB Article created." });
                  } catch (error) { console.error("Failed to create KB article:", error); toast({ title: "Error", description: "Failed to create KB article.", variant: "destructive" }); }
                  setIsAddDocPopoverOpen(false);
                }}
              >
                <Book className="mr-2 h-4 w-4" />
                Knowledge Base
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={async () => {
                  if (!currentUser) { toast({ title: "Authentication Error", description: "You must be logged in.", variant: "destructive" }); return; }
                  try {
                    const newNoteId = await createMeetingNote({ title: "Untitled Meeting Note", ownerId: currentUser._id });
                    router.push(`/documents/meeting-notes/${newNoteId}`);
                    toast({ title: "Success", description: "New Meeting Note created." });
                  } catch (error) { console.error("Failed to create meeting note:", error); toast({ title: "Error", description: "Failed to create meeting note.", variant: "destructive" }); }
                  setIsAddDocPopoverOpen(false);
                }}
              >
                <CalendarClock className="mr-2 h-4 w-4" />
                Meeting Note
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={() => {
                  console.log("Create Contract clicked - Not implemented");
                  toast({ title: "Not Implemented", description: "Contract creation is not yet available.", variant: "default" });
                  setIsAddDocPopoverOpen(false);
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                Contract
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={() => {
                   console.log("Create Document clicked - Not implemented");
                   toast({ title: "Not Implemented", description: "General document creation is not yet available.", variant: "default" });
                   setIsAddDocPopoverOpen(false);
                }}
              >
                <File className="mr-2 h-4 w-4" />
                Document
              </Button>
              {/* TODO: Add Upload option? */}
            </div>
          </PopoverContent>
        </Popover>
      );
    }

    if (pathname === '/directory') {
      return (
        <Popover open={isAddDirectoryPopoverOpen} onOpenChange={setIsAddDirectoryPopoverOpen}>
          <PopoverTrigger asChild>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56 p-2">
            <div className="grid gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={() => {
                  setIsAddPersonModalOpen(true);
                  setIsAddDirectoryPopoverOpen(false);
                }}
              >
                <Users className="mr-2 h-4 w-4" />
                Add Person
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="justify-start"
                onClick={() => {
                  setIsAddOrganizationModalOpen(true);
                  setIsAddDirectoryPopoverOpen(false);
                }}
              >
                <Building className="mr-2 h-4 w-4" />
                Add Organization
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      );
    }

    // Button for New KB Article on KB pages
    if (pathname.startsWith('/documents/knowledge-base')) {
       return (
         <button
           onClick={async () => {
             if (!currentUser) {
               toast({ title: "Authentication Error", description: "You must be logged in.", variant: "destructive" });
               return;
             }
             try {
               const newArticleId = await createKBArticle({
                 title: "Untitled KB Article",
                 ownerId: currentUser._id,
               });
               router.push(`/documents/knowledge-base/${newArticleId}`);
               toast({ title: "Success", description: "New KB Article created." });
             } catch (error) {
               console.error("Failed to create KB article:", error);
               toast({ title: "Error", description: "Failed to create KB article.", variant: "destructive" });
             }
           }}
           className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
         >
           <Book className="mr-2 h-4 w-4" />
           New KB Article
         </button>
       );
    }

    // Button for New Meeting Note on Meeting Note pages
    if (pathname.startsWith('/documents/meeting-notes')) {
       return (
         <button
           onClick={async () => {
             if (!currentUser) {
               toast({ title: "Authentication Error", description: "You must be logged in.", variant: "destructive" });
               return;
             }
             try {
               const newNoteId = await createMeetingNote({
                 title: "Untitled Meeting Note",
                 ownerId: currentUser._id,
               });
               router.push(`/documents/meeting-notes/${newNoteId}`);
               toast({ title: "Success", description: "New Meeting Note created." });
             } catch (error) {
               console.error("Failed to create meeting note:", error);
               toast({ title: "Error", description: "Failed to create meeting note.", variant: "destructive" });
             }
           }}
           className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
         >
           <CalendarClock className="mr-2 h-4 w-4" />
           New Meeting Note
         </button>
       );
    }


    if (pathname.startsWith('/projects/')) {
      return (
        <button
          className="px-3 py-1.5 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg flex items-center text-sm font-medium transition-colors"
        >
          <Plus className="mr-2 h-4 w-4" />
          Post update
        </button>
      );
    }

    return null;
  };

  return (
    <>
      {/* Removed transition-all from header as padding is fixed */}
      <header className="fixed top-0 left-0 right-0 h-10 backdrop-blur-xl z-50">
        {/* Apply fixed paddingLeft using Tailwind */}
        <div className="flex h-full w-full items-center pr-4 pl-[100px]">
          <div className="flex-1 flex items-center min-w-0">
            <button
              onClick={handleSidebarToggle}
              className="p-2 hover:bg-foreground/5 rounded-lg text-muted-foreground transition-colors flex-shrink-0 -ml-3"
              aria-label="Toggle sidebar"
            >
              <PanelLeftClose className="h-4 w-4" />
            </button>

            {/* Breadcrumbs with overflow handling */}
            <div className="flex items-center gap-2 min-w-0 overflow-hidden">
              {pathname === '/home' ? (
                <h1 className="text-lg font-semibold truncate">Homepage</h1>
              ) : (
                <div className="flex items-center gap-2 min-w-0 overflow-hidden">
                  {getBreadcrumbs().map((crumb, index) => (
                    <React.Fragment key={crumb.path}>
                      {index > 0 && (
                        <span className="text-muted-foreground flex-shrink-0">/</span>
                      )}
                      {crumb.isLast ? (
                        <span className="font-semibold truncate">{crumb.label}</span>
                      ) : (
                        <Link
                          href={crumb.path}
                          className="text-muted-foreground hover:text-foreground truncate flex-shrink-0"
                        >
                          {crumb.label}
                        </Link>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action button with flex-shrink-0 to prevent shrinking */}
          <div className="flex items-center flex-shrink-0 ml-4">
            {getActionButton()}
          </div>
        </div>
      </header>

      {/* Render Modals */}
      <AddPersonModal
        open={isAddPersonModalOpen}
        onOpenChange={setIsAddPersonModalOpen}
      />
      <AddOrganizationModal
        open={isAddOrganizationModalOpen}
        onOpenChange={setIsAddOrganizationModalOpen}
      />
    </>
  );
}
