import React from 'react';
import { Id } from '@/convex/_generated/dataModel';
import { AddTaggableTagPopover } from '@/components/ui/AddTaggableTagPopover';
import { Tag } from '@/zod/tags-schema';
import { Badge } from "@/components/ui/badge";
import { XIcon } from "lucide-react";
import { cn } from '@/lib/utils';

interface MeetingNoteActionBarProps {
  taggableId: Id<'files'>; // Changed from item._id and type to Id<'files'>
  tags?: Tag[];
  onRemoveTag?: (tagId: Id<'tags'>) => Promise<void>;
}

const MeetingNoteActionBar: React.FC<MeetingNoteActionBarProps> = ({
  taggableId,
  tags = [], // Default to empty array if not provided
  onRemoveTag
}) => {
  // Removed state and handlers related to status and importance

  return (
    <div className="flex items-center gap-4 p-2 border-b border-border w-full"> {/* Kept border */}

      {/* Removed Status Section */}
      {/* Removed Importance Section */}
      {/* Removed Placeholder Section */}

      {/* Redesigned Tags Section - Adapted for Meeting Notes */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Tags</span>
        <div className="flex items-start gap-1 flex-wrap"> {/* Changed items-center to items-start */}
          {tags.map((tag) => (
            <Badge
              key={tag._id}
              variant="outline" // Add outline variant for border
              className={cn(
                "relative group pr-6", // Add padding for X button, remove explicit height
                tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
              )}
            >
              {tag.name}
              {/* Remove Tag Button */}
              {onRemoveTag && (
                <button
                  onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                  className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={`Remove tag ${tag.name}`}
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              )}
            </Badge>
          ))}
          {/* Add Tag Button - Integrated with the tags */}
          <AddTaggableTagPopover
            taggableId={taggableId} // Use the passed taggableId
            taggableType="file" // Set type to 'file' for meeting notes
            tagTypeSlug="general-tags" // Assuming general tags apply
            triggerLabel="Add Tag"
          />
        </div>
      </div>
    </div>
  );
};

export default MeetingNoteActionBar;
