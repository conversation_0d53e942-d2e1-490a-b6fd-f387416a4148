import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import React from "react";
import Link from "next/link";

// Helper function to convert text color classes to background color classes
const getIconBgColor = (colorClass: string): string => {
  // Map text color classes to background color classes
  const colorMap: Record<string, string> = {
    'text-blue-500': 'bg-blue-500',
    'text-amber-500': 'bg-amber-500',
    'text-emerald-500': 'bg-emerald-500',
    'text-purple-500': 'bg-purple-500',
    'text-gray-500': 'bg-gray-500',
  };
  
  return colorMap[colorClass] || 'bg-gray-500'; // Default to gray if color not found
};

interface DocumentType {
  id: string;
  name: string;
  icon: React.ReactNode;
  count: number;
  color: string;
  route?: string; // Optional route for navigation
}

interface DocumentTypesProps {
  docTypes: DocumentType[];
}

export const DocumentTypes: React.FC<DocumentTypesProps> = ({ docTypes }) => {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Document Types</h2>
        <Button variant="link" size="sm">Manage Types</Button>
      </div>
      <div className="grid grid-cols-4 gap-4">
        {docTypes.map((type) => (
          <Link 
            key={type.id} 
            href={type.route || `/documents/${type.id.toLowerCase()}`} 
            className="block"
          >
            <Card className="border border-gray-200 hover:border-gray-300 transition-all hover:shadow-sm cursor-pointer">
              <CardContent className="p-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`${getIconBgColor(type.color)} p-1.5 rounded-md mr-3`}>
                    <div className="text-white">
                      {type.icon}
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-sm">{type.name}</p>
                    <p className="text-xs text-gray-500">{type.count} documents</p>
                  </div>
                </div>
                <ArrowRight size={14} className="text-gray-400" />
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
};
