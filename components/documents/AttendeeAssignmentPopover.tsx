"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from "@/components/hooks/use-toast";
import { useDebounce } from 'use-debounce';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, Plus, Users, CheckSquare, Square } from 'lucide-react';

interface AttendeeAssignmentPopoverProps {
  taggableId: Id<'files'>;
  existingAttendeeIds?: (Id<'people'> | Id<'organizations'>)[];
  triggerButton?: React.ReactNode;
}

type SearchResultEntity = {
  id: Id<'people'> | Id<'organizations'>;
  type: 'person' | 'organization';
  name: string;
  image?: string;
  email?: string;
  initials: string;
};

const AttendeeAssignmentPopover: React.FC<AttendeeAssignmentPopoverProps> = ({
  taggableId,
  existingAttendeeIds = [],
  triggerButton,
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  const [selectedAttendees, setSelectedAttendees] = useState<SearchResultEntity[]>([]);
  const [isAssigning, setIsAssigning] = useState(false);

  const searchResults: SearchResultEntity[] = useQuery(
    api.directory.directory.searchPeopleAndOrgs,
    isOpen ? { 
      search: debouncedSearchTerm,
      limit: 30, 
      excludeIds: existingAttendeeIds
    } : 'skip'
  ) || [];

  const updateMeetingNote = useMutation(api.files.files.updateMeetingNote);

  const handleSelect = (entity: SearchResultEntity) => {
    setSelectedAttendees(prevSelected => {
      const isSelected = prevSelected.some(attendee => attendee.id === entity.id);
      if (isSelected) {
        return prevSelected.filter(attendee => attendee.id !== entity.id);
      } else {
        return [...prevSelected, entity];
      }
    });
  };

  const handleAssign = async () => {
    if (selectedAttendees.length === 0) {
      toast({ title: "No attendees selected", variant: "destructive" });
      return;
    }

    setIsAssigning(true);
    try {
      const newAttendeeIds = selectedAttendees.map(a => a.id);
      const currentIds = Array.isArray(existingAttendeeIds) ? existingAttendeeIds : [];
      const combinedIds = Array.from(new Set([...currentIds, ...newAttendeeIds]));
      
      await updateMeetingNote({
        fileId: taggableId,
        attendees: combinedIds
      });
      
      toast({
        title: `${selectedAttendees.length} Attendee${selectedAttendees.length > 1 ? 's' : ''} added`,
        description: "Attendees have been added to the meeting",
      });
      
      setIsOpen(false); 
    } catch (error) {
      console.error("Failed to add attendee:", error);
      toast({
        title: "Failed to add attendee",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  // Handle popover close event
  const handleOpenChange = (open: boolean) => {
    // If closing and there are attendees selected, auto-save them
    if (!open && selectedAttendees.length > 0) {
      handleAssign();
    } else {
      setIsOpen(open);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setSelectedAttendees([]); 
    }
  }, [isOpen]);

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        {triggerButton || (
          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs h-8"
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            Add Attendee
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" side="top" align="start">
        <div className="p-2 border-b">
          <Input
            autoFocus
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search people or organizations..."
            className="h-7 text-xs" 
          />
        </div>
        <ScrollArea className="h-[340px] p-2"> 
          {searchResults.length === 0 && isOpen && (
            <p className="text-xs text-muted-foreground text-center py-2">
              {debouncedSearchTerm 
                ? "No matching results found" 
                : "Start typing to search..."}
            </p>
          )}
          
          <div className="space-y-0.5"> 
            {searchResults.map((entity) => {
              const isSelected = selectedAttendees.some(a => a.id === entity.id);
              return (
                <div
                  key={entity.id.toString()}
                  onClick={() => handleSelect(entity)}
                  className={`flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-muted ${
                    isSelected ? 'bg-muted' : '' 
                  }`}
                >
                  {/* Selection Indicator */}
                  <div className="w-3.5 h-3.5 flex items-center justify-center flex-shrink-0">
                    {isSelected ? (
                      <CheckSquare className="h-3.5 w-3.5 text-popover-foreground" /> 
                    ) : (
                      <Square className="h-3.5 w-3.5 text-muted-foreground" /> 
                    )}
                  </div>
                  
                  {/* Avatar/Icon */}
                  <div className="flex-shrink-0">
                    {entity.type === 'person' ? (
                      <Avatar className="h-5 w-5"> 
                        <AvatarImage src={entity.image ?? undefined} alt={entity.name} />
                        <AvatarFallback className="text-[11px]">{entity.initials}</AvatarFallback> 
                      </Avatar>
                    ) : (
                      <div className="flex items-center justify-center h-4 w-4 bg-muted rounded-sm flex-shrink-0"> 
                        <Users className="h-4 w-4 text-muted-foreground" /> 
                      </div>
                    )}
                  </div>
                  
                  {/* Name and Email (Inline) */}
                  <div className="flex items-baseline gap-2 flex-1 overflow-hidden">
                    <span className="text-xs truncate">{entity.name}</span>
                    {/* Corrected Conditional Email Span */}
                    {entity.email && 
                      <span className="text-[11px] text-muted-foreground truncate">{entity.email}</span>
                    }
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
        
        {selectedAttendees.length > 0 && (
          <div className="p-2 border-t flex justify-end">
            <Button size="sm" onClick={handleAssign} disabled={isAssigning}>
              {isAssigning ? 'Adding...' : `Add ${selectedAttendees.length} Attendee${selectedAttendees.length > 1 ? 's' : ''}`}
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default AttendeeAssignmentPopover;
