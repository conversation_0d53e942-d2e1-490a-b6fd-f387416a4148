import React, { useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { useEditor, EditorContent, BubbleMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Typeography from '@tiptap/extension-typography';
import { useDebouncedCallback } from 'use-debounce';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Mic, MicOff, Loader2, Bold, Italic, List, ListOrdered } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AutosaveIndicator } from '@/components/ui/autosave-indicator';
import '@/app/tiptap.scss';
import { Id } from '@/convex/_generated/dataModel';

// Helper function to check if HTML content is empty
const isHtmlContentEmpty = (html: string | null | undefined): boolean => {
  if (!html) return true;
  const text = html.replace(/<[^>]*>/g, '').trim();
  return text.length === 0;
};

interface RealtimeTranscriptEditorProps {
  initialContent: string;
  onSave: (content: string) => Promise<void>;
  fileId: Id<'files'>; // Required file ID
  placeholder?: string;
  minHeight?: string;
  maxHeight?: string;
  hideRecordButton?: boolean;
  onRecordingStateChange?: (isRecording: boolean, isConnecting: boolean) => void;
  isEditable?: boolean; // New prop to control editability externally
}

export const RealtimeTranscriptEditor = forwardRef<
  { startRecording: (transcriptionInstructions?: string) => Promise<void>; stopRecording: () => void },
  RealtimeTranscriptEditorProps
>(({
  initialContent,
  onSave,
  fileId,
  placeholder = 'Transcript will appear here...',
  minHeight = '300px',
  maxHeight = '800px',
  hideRecordButton = false,
  onRecordingStateChange,
  isEditable = false, // Default to not editable
}: RealtimeTranscriptEditorProps, ref) => {
  // State for recording status
  const [isRecording, setIsRecording] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [errorState, setErrorState] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Get the appendTranscriptSegment mutation
  const appendTranscriptSegment = useMutation(api.files.files.appendTranscriptSegment);

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    startRecording: async (transcriptionInstructions?: string) => {
      await startRecording(transcriptionInstructions);
    },
    stopRecording: () => {
      stopRecording();
    }
  }));

  // WebRTC state
  const [peerConnection, setPeerConnection] = useState<RTCPeerConnection | null>(null);
  const [dataChannel, setDataChannel] = useState<RTCDataChannel | null>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);

  // Initialize Tiptap editor
  const editor = useEditor({
    extensions: [
      StarterKit,
      Typeography,
      Placeholder.configure({
        placeholder: '', // We'll use our custom placeholder instead
        emptyEditorClass: 'is-editor-empty cursor-pointer',
      }),
    ],
    content: initialContent,
    editorProps: {
      attributes: {
        class: 'tiptap max-w-none focus:outline-none px-3 py-2 rounded-md border-2 border-gray-300 focus-within:border-blue-500 bg-background/10',
        style: `min-height: ${minHeight}; max-height: ${maxHeight}; overflow-y: auto;`
      },
    },
    immediatelyRender: false, // Explicitly set to false for SSR
    onUpdate: ({ editor }) => {
      // Reverted: Always call debounce on update. Debounce function itself will check isRecording.
      const newContent = editor.getHTML();
      debouncedSave(newContent);
    }
  });

  // Effect to update editor content when the 'initialContent' prop changes externally
  // This handles updates coming from Convex reactivity without triggering onUpdate/onSave
  useEffect(() => {
    // Removed 'editor.isEditable' check to allow reactive updates even when disabled during recording
    if (editor && initialContent !== editor.getHTML()) {
      // Update the editor's content without triggering the onUpdate handler
      editor.commands.setContent(initialContent, false);
    }
  // Watch editor instance and initialContent. Don't include editor.getHTML() here.
  }, [editor, initialContent]);

  // Debounced save function
  const debouncedSave = useDebouncedCallback(async (newContent: string) => {
    // Remove this condition - it's preventing saves during recording which affects reactivity
    // if (isRecording) {
    //   console.log("Skipping debounced save because recording is active.");
    //   return;
    // }

    try {
      setIsSaving(true);
      await onSave(newContent); // Call the actual save function passed via props
    } catch (error) {
      console.error('Error saving transcript:', error);
    } finally {
      setIsSaving(false);
    }
  }, 1000);

  // Effect to toggle editor editability based on the isEditable prop
  // and update the editor's classes based on content and editability
  useEffect(() => {
    if (editor) {
      // Control editability based on the prop, ignore recording state here
      editor.setEditable(isEditable);
      
      // Update editor classes based on content and editability
      const editorElement = editor.view.dom as HTMLElement;
      const isEmpty = !editor.getText().trim();
      
      // Remove existing state classes
      editorElement.classList.remove('border-dashed');
      editorElement.classList.remove('border-dotted');
      editorElement.classList.remove('bg-gray-50/50');
      editorElement.classList.remove('bg-muted/50');
      editorElement.classList.remove('cursor-not-allowed');
      
      // Apply appropriate classes based on state
      if (isEmpty) {
        // Empty state with dotted border - use both for better visibility
        editorElement.classList.add('border-dashed');
        editorElement.classList.add('border-dotted');
        // Make border more prominent
        editorElement.style.borderWidth = '2px';
        // Add blue border color to indicate clickable
        editorElement.style.borderColor = '#3b82f6';
        // Add cursor pointer to indicate clickable
        editorElement.style.cursor = 'pointer';
        // Add placeholder text
        editorElement.setAttribute('data-placeholder', 'Click here to add transcript or use the Record button...');
      } else if (!isEditable) {
        // Non-editable state with muted background
        editorElement.classList.add('bg-muted/50');
        editorElement.classList.add('cursor-not-allowed');
        editorElement.style.borderWidth = '0'; // Remove border completely
        editorElement.style.opacity = '0.8';
      } else {
        // Reset to default editable state
        editorElement.style.borderWidth = '';
        editorElement.style.borderColor = ''; 
        editorElement.style.cursor = '';
        editorElement.style.opacity = '';
      }
    }
  }, [editor, isEditable, editor?.getText().trim()]); // Depend on the prop and content

  // Function to fetch ephemeral token for TRANSCRIPTION directly from Convex
  const getEphemeralToken = async (transcriptionInstructions?: string): Promise<string> => {
    try {
      // For testing, use the direct URL that we know works from curl
      const url = 'https://avid-wildebeest-896.convex.site/openai/realtime/transcription/session';
      console.log("Fetching transcription token from direct URL:", url);
      
      // Configure the transcription session
      const sessionConfig = {
        input_audio_transcription: {
          model: "gpt-4o-transcribe",
          prompt: transcriptionInstructions || "Transcribe accurately with proper punctuation."
        },
        turn_detection: {
          type: "server_vad",
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: 200
        }
      };
      
      console.log("Requesting transcription session with config:", sessionConfig);
      
      // Call the Convex HTTP endpoint directly with minimal options
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionConfig)
      });
      
      console.log("Transcription fetch response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Failed transcription response:", errorText);
        throw new Error(`Failed to get token: ${errorText}`);
      }

      const data = await response.json();
      console.log("Successfully received transcription token from Convex:", data.client_secret?.value);
      
      if (!data.client_secret || !data.client_secret.value) {
        console.error("Invalid transcription token response:", JSON.stringify(data));
        throw new Error('Invalid token response');
      }

      // Return only the token value, not the entire object
      return data.client_secret.value;
    } catch (error: any) {
      console.error('Error fetching transcription token:', error);
      throw error; // Re-throw to be caught by startRecording
    }
  };

  // Function to start recording
  const startRecording = async (transcriptionInstructions?: string) => {
    try {
      setIsConnecting(true);
      setErrorState(null);

      // 1. Get ephemeral token with optional custom instructions
      const token = await getEphemeralToken(transcriptionInstructions);

      // 2. Create RTCPeerConnection
      const pc = new RTCPeerConnection();
      setPeerConnection(pc);

      // 3. Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMediaStream(stream);

      // 4. Add audio tracks to peer connection
      stream.getAudioTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      // 5. Create data channel for events
      const dc = pc.createDataChannel('oai-events');
      setDataChannel(dc);

      // 6. Set up data channel event handlers
      dc.onmessage = (event) => {
        console.log('Raw data channel message received:', event.data); // Log raw data
        try {
          const data = JSON.parse(event.data);
          console.log('Parsed data channel event:', data); // Log parsed data

          // Only process completed events (ignore delta events)
          if (data.type === 'conversation.item.input_audio_transcription.completed' && data.transcript) {
            // Call the appendTranscriptSegment mutation with the completed segment
            appendTranscriptSegment({
              fileId,
              segmentText: data.transcript
            });

            // No need to update the editor directly - it will update via Convex reactivity
            console.log(`Sent completed segment to database: "${data.transcript}"`);
          }
        } catch (err) {
          console.error('Error parsing event data:', err);
        }
      };

      dc.onopen = () => console.log('Data channel open');
      dc.onclose = () => console.log('Data channel closed');
      dc.onerror = (event) => {
        // Check if it's the expected User-Initiated Abort error when stopping recording
        if (event instanceof RTCErrorEvent && event.error?.message?.includes('User-Initiated Abort')) {
          console.log('Data channel closed intentionally on stop:', event.error.message); // Log as info, not error
        } else {
          // Log other, potentially real errors
          console.error('Data channel error:', event);
        }
      };

      // 7. Create and set local description (offer)
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // 8. Send offer to OpenAI and get answer (Intent/Model is set during session creation)
      const sdpResponse = await fetch(`https://api.openai.com/v1/realtime`, {
        method: 'POST',
        body: pc.localDescription?.sdp,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/sdp'
        },
      });

      if (!sdpResponse.ok) {
        const errorText = await sdpResponse.text();
        throw new Error(`OpenAI SDP error: ${errorText}`);
      }

      // 9. Set remote description (answer)
      const answer = {
        type: 'answer',
        sdp: await sdpResponse.text(),
      };
      await pc.setRemoteDescription(answer as RTCSessionDescriptionInit);

      // 10. Update state to recording
      setIsConnecting(false);
      setIsRecording(true);

      // Notify parent component if callback provided
      if (onRecordingStateChange) {
        onRecordingStateChange(true, false);
      }

      // // Add a visual indicator that recording has started - REMOVED to rely on reactivity
      // if (editor) {
      //   editor.commands.focus('end');
      //   editor.commands.insertContent('<p><em>Recording started...</em></p>');
      // }

    } catch (error: any) {
      console.error('Error starting recording:', error);
      setErrorState(error.message || 'Failed to start recording');
      setIsConnecting(false);

      // Clean up any partial setup
      stopRecording();
    }
  };

  // Function to stop recording
  const stopRecording = () => {
    // Close data channel
    if (dataChannel) {
      dataChannel.close();
      setDataChannel(null);
    }

    // Close peer connection
    if (peerConnection) {
      peerConnection.close();
      setPeerConnection(null);
    }

    // Stop media tracks
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      setMediaStream(null);
    }

    // Update state
    setIsRecording(false);

    // Explicitly save the current transcript to ensure the final version is processed
    if (editor) {
      const currentContent = editor.getHTML();
      console.log("[stopRecording] Explicitly saving final transcript", {
        contentLength: currentContent.length,
        firstChars: currentContent.substring(0, 50) + "..."
      });
      onSave(currentContent).catch(err => {
        console.error("[stopRecording] Error saving final transcript:", err);
      });
    }

    // Notify parent component if callback provided
    if (onRecordingStateChange) {
      onRecordingStateChange(false, false);
    }

    // // Add a visual indicator that recording has stopped - REMOVED to rely on reactivity
    // if (editor && editor.isEditable) {
    //   editor.commands.focus('end');
    //   editor.commands.insertContent('<p><em>Recording stopped.</em></p>');
    // }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isRecording) {
        stopRecording();
      }
    };
  }, [isRecording]);

  // Custom CSS for empty state
  const emptyStateStyles = `
    .is-editor-empty:not(:focus):before {
      content: attr(data-placeholder);
      color: #3b82f6;
      pointer-events: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px;
    }
    .is-editor-empty {
      position: relative;
      color: #3b82f6;
    }
    .ProseMirror {
      position: relative;
      min-height: ${minHeight};
    }
  `;

  // Render the component
  return (
    <div className="relative">
      <style>{emptyStateStyles}</style>
      {/* Recording button - only show if not hidden */}
      {!hideRecordButton && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
          {errorState && (
            <div className="text-xs text-red-500 bg-red-100 p-1 rounded">
              {errorState}
            </div>
          )}

          <Button
            size="sm"
            variant={isRecording ? "destructive" : "default"}
            onClick={isRecording ? 
              () => stopRecording() : 
              () => startRecording()}
            disabled={isConnecting}
            className="flex items-center gap-1"
          >
            {isConnecting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Connecting...</span>
              </>
            ) : isRecording ? (
              <>
                <MicOff className="h-4 w-4" />
                <span>Stop Recording</span>
              </>
            ) : (
              <>
                <Mic className="h-4 w-4" />
                <span>Record</span>
              </>
            )}
          </Button>
        </div>
      )}

      {/* Editor content with bubble menu */}
      <EditorContent editor={editor}>
        {editor && (
          <BubbleMenu
            editor={editor}
            tippyOptions={{ duration: 150 }}
            className="bg-background border rounded-md shadow-md flex overflow-hidden"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={cn(
                'px-2 py-1 h-8 rounded-none',
                editor.isActive('bold') ? 'bg-muted' : ''
              )}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={cn(
                'px-2 py-1 h-8 rounded-none',
                editor.isActive('italic') ? 'bg-muted' : ''
              )}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={cn(
                'px-2 py-1 h-8 rounded-none',
                editor.isActive('bulletList') ? 'bg-muted' : ''
              )}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={cn(
                'px-2 py-1 h-8 rounded-none',
                editor.isActive('orderedList') ? 'bg-muted' : ''
              )}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
          </BubbleMenu>
        )}
      </EditorContent>

      {/* Status indicator */}
      <div className="flex justify-between items-center h-5 mt-1">
        <div className="text-xs text-muted-foreground">
          {isRecording ? (
            <span className="flex items-center gap-1">
              <span className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></span>
              Recording...
            </span>
          ) : editor?.isFocused ? (
            'Editing...'
          ) : ''}
        </div>
        <AutosaveIndicator status={isSaving ? 'saving' : 'saved'} />
      </div>
    </div>
  );
});
