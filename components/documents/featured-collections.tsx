import { Button } from "@/components/ui/button";
import { FeaturedCollectionCard } from "./featured-collection-card";

interface Collection {
  id: string;
  title: string;
  description: string;
  docCount: number;
  docTypes: string[];
  lastUpdated: string;
}

interface FeaturedCollectionsProps {
  collections: Collection[];
}

export const FeaturedCollections: React.FC<FeaturedCollectionsProps> = ({ collections }) => {
  if (collections.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No featured collections found.
      </div>
    );
  }

  return (
    <div className="mb-8 pt-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Featured Collections (this doesnt work yet, sorry!)</h2>
        <Button variant="link" size="sm">View All Collections</Button>
      </div>
      <div className="grid grid-cols-3 gap-5">
        {collections.map((collection) => (
          <FeaturedCollectionCard key={collection.id} collection={collection} />
        ))}
      </div>
    </div>
  );
};
