import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { File } from "lucide-react";

interface Collection {
  id: string;
  title: string;
  description: string;
  docCount: number;
  docTypes: string[];
  lastUpdated: string;
}

interface FeaturedCollectionCardProps {
  collection: Collection;
}

const getDocTypeColor = (type: string) => {
    const docTypes = [
      { id: 'KNOWLEDGE_BASE', color: 'bg-blue-500' },
      { id: 'MEETING_NOTES', color: 'bg-amber-500' },
      { id: 'CONTRACT', color: 'bg-emerald-500' },
      { id: 'DOCUMENT', color: 'bg-purple-500' }
    ];
    const docType = docTypes.find(dt => dt.id === type);
    return docType ? docType.color : 'bg-gray-500';
  };

  const getDocTypeIcon = (type:string) => {
    const docTypes = [
        { id: 'KNOWLEDGE_BASE', icon: <File size={16} /> },
        { id: 'MEETING_NOTES', icon: <File size={16} /> },
        { id: 'CONTRACT', icon: <File size={16} /> },
        { id: 'DOCUMENT', icon: <File size={16} /> }
    ];
    const docType = docTypes.find(dt => dt.id === type);
    return docType ? docType.icon : <File size={16} />;
  };

export const FeaturedCollectionCard: React.FC<FeaturedCollectionCardProps> = ({ collection }) => {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader>
        <CardTitle>{collection.title}</CardTitle>
        <CardDescription>{collection.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-3">
          <div className="flex space-x-1">
            {collection.docTypes.map((type, idx) => (
              <div key={idx} className={`${getDocTypeColor(type)} w-6 h-6 rounded-full flex items-center justify-center text-white`}>
                {getDocTypeIcon(type)}
              </div>
            ))}
          </div>
          <Badge variant="outline">{collection.docCount} docs</Badge>
        </div>
        <p className="text-sm text-gray-500">Updated {collection.lastUpdated}</p>
      </CardContent>
      <CardFooter>
        <Button variant="outline" size="sm" className="w-full">Open Collection</Button>
      </CardFooter>
    </Card>
  );
};
