import { Button } from "@/components/ui/button";
import React from "react";
import { CardList, type CardListColumn } from "@/components/cardList";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Doc } from "@/convex/_generated/dataModel";
import { PanelLeft, CalendarClock, FileText, File, Receipt } from "@/components/icons"; // Import icons from the central file
import { useRouter } from 'next/navigation'; // Import useRouter

// Helper function to format timestamp to relative time
const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  // Convert milliseconds to minutes, hours, days
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return days === 1 ? '1 day ago' : `${days} days ago`;
  } else if (hours > 0) {
    return hours === 1 ? '1 hour ago' : `${hours} hours ago`;
  } else if (minutes > 0) {
    return minutes === 1 ? '1 minute ago' : `${minutes} minutes ago`;
  } else {
    return 'Just now';
  }
};

const getDocTypeColor = (docType: string) => {
  const docTypes = [
    { id: 'KNOWLEDGE_BASE', color: 'bg-blue-500' },
    { id: 'MEETING_NOTES', color: 'bg-amber-500' },
    { id: 'CONTRACT', color: 'bg-emerald-500' },
    { id: 'DOCUMENT', color: 'bg-purple-500' },
    { id: 'BILL', color: 'bg-red-500' }
  ];
  const foundDocType = docTypes.find(dt => dt.id === docType);
  return foundDocType ? foundDocType.color : 'bg-gray-500';
};

const getDocTypeIcon = (docType: string) => {
  const docTypes = [
    { id: 'KNOWLEDGE_BASE', icon: <div>KB</div> },
    { id: 'MEETING_NOTES', icon: <div>MN</div> },
    { id: 'CONTRACT', icon: <div>C</div> },
    { id: 'DOCUMENT', icon: <div>D</div> },
    { id: 'BILL', icon: <div>B</div> }
  ];
  const foundDocType = docTypes.find(dt => dt.id === docType);
  // Use the imported icons
  const iconMap: { [key: string]: React.ReactElement } = {
    'KNOWLEDGE_BASE': <PanelLeft size={24} />,
    'MEETING_NOTES': <CalendarClock size={24} />,
    'CONTRACT': <FileText size={24} />,
    'DOCUMENT': <File size={24} />,
    'BILL': <Receipt size={24} />
  };
  return iconMap[docType] || <File size={24} />; // Default to File icon with updated size
};

// Helper function to get the URL path segment based on docType
const getDocTypePathSegment = (docType: string): string | null => {
  const pathMap: { [key: string]: string } = {
    'KNOWLEDGE_BASE': 'knowledge-base',
    'MEETING_NOTES': 'meeting-notes',
    'CONTRACT': 'contracts',
    'DOCUMENT': 'general-docs',
    // 'BILL' is intentionally omitted as it likely navigates elsewhere
  };
  return pathMap[docType] || null;
};

export const RecentlyModified: React.FC = () => {
  const router = useRouter(); // Get router instance
  // Fetch recently modified files using the Convex query
  const recentFiles = useQuery(api.files.files.getRecentlyModifiedFiles, { limit: 5 });

  // Define columns for the CardList component
  const columns: CardListColumn<Doc<"files">>[] = [
    {
      id: 'document',
      header: 'Document',
      cell: (doc) => (
        <div className="flex items-center">
          <div className={`${getDocTypeColor(doc.docType)} w-10 h-10 rounded-md flex items-center justify-center text-white mr-4`}>
            {getDocTypeIcon(doc.docType)}
          </div>
          <div>
            <h3 className="font-medium">{doc.title || 'Untitled'}</h3>
            <div className="flex items-center text-sm text-gray-500">
              <p>Modified {doc.updated_at ? formatRelativeTime(doc.updated_at) : 'Unknown'}</p>
            </div>
          </div>
        </div>
      ),
      width: '100%'
    }
  ];

  const actions = [
    {
      label: 'View',
      onClick: (doc: Doc<"files">) => {
        // Handle view action based on document type
        console.log('View document:', doc._id);
      },
      variant: 'ghost' as const
    }
  ];

  // Show loading state if data is not yet available
  if (recentFiles === undefined) {
    return (
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Recently Modified</h2>
        </div>
        <div className="p-4 text-center text-gray-500">Loading recent documents...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Recently Modified</h2>
        <Button variant="link" size="sm">View All Recent</Button>
      </div>
      {recentFiles.length === 0 ? (
        <div className="p-4 text-center text-gray-500">No recent documents found</div>
      ) : (
        <CardList
          items={recentFiles}
          columns={columns}
          actions={actions}
          keyExtractor={(doc) => doc._id}
          onItemClick={(doc) => {
            const pathSegment = getDocTypePathSegment(doc.docType);
            if (pathSegment) {
              router.push(`/documents/${pathSegment}/${doc._id}`);
            } else {
              console.log(`Navigation not implemented for docType: ${doc.docType}`);
              // Optionally navigate to a different section like /bills/ or show a message
            }
          }}
          selectable={false}
          showHeader={false}
          highlightOnHover
          className="border-none"
          size="sm"
          pagination={false}
          actionButtonVariant="button"
        />
      )}
    </div>
  );
};
