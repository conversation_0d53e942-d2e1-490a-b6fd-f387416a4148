import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, Send, PanelLeft, CalendarClock, FileText, File } from "lucide-react";
import { AiChatModal } from "@/components/ui/AiChatModal"; // Import the new modal
// Removed Thread import
import InitializableThread from "@/components/assistant-ui/initializable-thread"; // Import the new wrapper component

const PrimaryChatCTA = () => {
  const [chatQuery, setChatQuery] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialMessage, setInitialMessage] = useState<string | null>(null);

  const docTypes = [
    { id: 'KNOWLEDGE_BASE', name: 'Knowledge Base', icon: <PanelLeft size={16} />, count: 24, color: 'text-blue-500', bgColor: 'bg-blue-100' },
    { id: 'MEETING_NOTES', name: 'Meeting Notes', icon: <CalendarClock size={16} />, count: 18, color: 'text-amber-500', bgColor: 'bg-amber-100' },
    { id: 'CONTRACT', name: 'Contracts', icon: <FileText size={16} />, count: 12, color: 'text-emerald-500', bgColor: 'bg-emerald-100' },
    { id: 'DOCUMENT', name: 'Documents', icon: <File size={16} />, count: 32, color: 'text-purple-500', bgColor: 'bg-purple-100' }
  ];

  // State for selected filters, default to all selected
  const [selectedFilters, setSelectedFilters] = useState<string[]>(docTypes.map(type => type.id));
  const [effectiveFilters, setEffectiveFilters] = useState<string[]>(docTypes.map(type => type.id)); // Initialize with all document types

  const handleFilterToggle = (typeId: string) => {
    const newFilters = selectedFilters.includes(typeId)
      ? selectedFilters.filter(id => id !== typeId) // Deselect: remove ID
      : [...selectedFilters, typeId]; // Select: add ID
    
    setSelectedFilters(newFilters);
    
    // Also update effectiveFilters with the new selection
    // If newFilters is empty, use all document types as fallback
    const filtersToUse = newFilters.length > 0 ? newFilters : docTypes.map(type => type.id);
    setEffectiveFilters(filtersToUse);
    
    console.log("Filter toggled:", {
      typeId,
      newFilterCount: newFilters.length,
      effectiveFilterCount: filtersToUse.length,
      isDefaultingToAll: newFilters.length === 0,
      timestamp: new Date().toISOString()
    });
  };

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!chatQuery.trim()) return;
    
    // Ensure we have at least one filter selected - if none, select all
    const filtersToUse = selectedFilters.length > 0 ? 
      selectedFilters : 
      docTypes.map(type => type.id);
    
    // Update the effective filters state
    setEffectiveFilters(filtersToUse);
    
    // Log filters being used with more detail
    console.log("PrimaryChatCTA: Chat modal opening with filters:", {
      selectedFilters: filtersToUse,
      filterCount: filtersToUse.length,
      query: chatQuery,
      filterNames: filtersToUse.map(id => {
        const docType = docTypes.find(type => type.id === id);
        return docType ? docType.name : id;
      }),
      timestamp: new Date().toISOString()
    });
    
    // Pass selectedFilters along with the message
    setInitialMessage(chatQuery);
    // Use the effective filters (either selected or all if none selected)
    setIsModalOpen(true);
    // Keep chatQuery for now, clear only if modal opens successfully? Or clear immediately? Let's clear immediately.
    setChatQuery("");
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setInitialMessage(null); // Reset initial message when modal closes
    
    // Reset effectiveFilters to match selectedFilters when modal closes
    const filtersToUse = selectedFilters.length > 0
      ? selectedFilters
      : docTypes.map(type => type.id);
    setEffectiveFilters(filtersToUse);
    
    console.log("Modal closed, filters reset");
  };

  return (
    <> {/* Wrap in fragment to allow modal rendering */}
      <div className="mb-8">
        <Card className="border-2 border-blue-100">
          <CardHeader className="pb-2">
          <CardTitle className="text-xl">What would you like to know?</CardTitle>
          <CardDescription>Ask anything about your documents. Filter by type below.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleChatSubmit} className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                value={chatQuery}
                onChange={(e) => setChatQuery(e.target.value)}
                placeholder="Ask about your documents..."
                className="pr-10"
              />
              <div className="absolute right-3 top-2.5 text-gray-400">
                <MessageSquare size={16} />
              </div>
            </div>
            <Button type="submit">
              <Send size={16} className="mr-2" />
              Ask
            </Button>
          </form>
        </CardContent>
        <CardFooter className="text-sm text-gray-500 pt-0">
          <div className="flex items-center gap-2">
            <span>Active Filters:</span>
            <div className="flex gap-1">
              {docTypes.map((type) => {
                const isSelected = selectedFilters.includes(type.id);
                return (
                  <div
                    key={type.id}
                    className={`w-7 h-7 rounded-full flex items-center justify-center border-2 border-white cursor-pointer hover:scale-110 transition-all duration-150 ${
                      isSelected ? `${type.bgColor} ${type.color}` : 'bg-gray-200 text-gray-400 opacity-60'
                    }`}
                    title={`${type.name} (${isSelected ? 'Selected' : 'Deselected'})`}
                    onClick={() => handleFilterToggle(type.id)}
                  >
                    {type.icon}
                  </div>
                );
              })}
            </div>
          </div>
          </CardFooter>
        </Card>
      </div>

      {/* Conditionally render the AiChatModal */}
      {/* Conditionally render the AiChatModal */}
      {isModalOpen && (
        <AiChatModal
          heading="Chat with your Documents"
          onClose={handleCloseModal} // Use the new handler
          showFooter={false} // Hide default footer
          className="w-[90vw] max-w-[1200px] h-[85vh]"
        >
          {/* Pass initial message and filters to the thread */}
          <InitializableThread
            initialMessage={initialMessage}
            initialFilters={effectiveFilters} // Pass effective filters
          />
        </AiChatModal>
      )}
    </>
  );
};

export default PrimaryChatCTA;
