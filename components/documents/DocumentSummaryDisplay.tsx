import React from 'react';
import { Card, CardContent } from '@/components/ui/card'; // Import Card components

interface DocumentSummaryDisplayProps {
  summary?: string | null;
}

/**
 * A component that displays a document's short description/summary
 * within a styled Card.
 * 
 * This component renders the summary text with appropriate styling
 * only if the summary prop is provided and not empty.
 */
export const DocumentSummaryDisplay: React.FC<DocumentSummaryDisplayProps> = ({ summary }) => {
  // Don't render anything if summary is undefined, null, or empty
  if (!summary) return null;
  
  return (
    <Card className="mb-4 mt-5 bg-blue-50 border-blue-100"> {/* Added margin, background, border */}
      <CardContent className="p-3"> {/* Added padding */}
        <p className="text-sm font-semibold text-blue-800 mb-1">Summary:</p> {/* Added label */}
        <p className="text-sm text-blue-900"> {/* Adjusted text color */}
          {summary}
        </p>
      </CardContent>
    </Card>
  );
};

export default DocumentSummaryDisplay;
