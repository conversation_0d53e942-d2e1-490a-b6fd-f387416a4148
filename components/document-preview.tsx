/**
 * Document Preview Component
 *
 * Displays a preview of a document (PDF or image) with download functionality.
 * <PERSON>les fetching the document URL from storage and rendering the appropriate
 * viewer based on document type.
 */

'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, FileText } from 'lucide-react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useState, useEffect } from 'react';

interface DocumentPreviewProps {
  documentStorageId: Id<'_storage'> | null;
  documentFilename: string | null;
  id: string;
}

export function DocumentPreview({
  documentStorageId,
  documentFilename,
  id
}: DocumentPreviewProps) {
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const getFileUrl = useMutation(api.files.fileManagement.getFileUrl);

  useEffect(() => {
    // Skip if no storage ID
    if (documentStorageId === null) {
      setDocumentUrl(null);
      return;
    }

    // Set loading state
    setIsLoading(true);
    
    // Use the promise directly without async/await
    getFileUrl({ storageId: documentStorageId })
      .then((url) => {
        setDocumentUrl(url);
      })
      .catch((error) => {
        console.error('Error fetching document URL:', error);
        setDocumentUrl(null);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [documentStorageId, getFileUrl]);

  // Determine document type from filename extension
  const documentType = documentFilename?.toLowerCase().endsWith('.pdf')
    ? 'pdf'
    : 'image';

  // Don't render anything if we don't have a URL or filename
  if (!documentUrl || !documentFilename) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Preview
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={() => window.open(documentUrl, '_blank')}
          >
            <Download className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {documentType === 'pdf' ? (
          <iframe
            src={documentUrl}
            className="w-full h-[600px] border-0"
            title="PDF Preview"
          />
        ) : (
          <img
            src={documentUrl}
            alt="Document Preview"
            className="max-w-full h-auto"
          />
        )}
      </CardContent>
    </Card>
  );
}
