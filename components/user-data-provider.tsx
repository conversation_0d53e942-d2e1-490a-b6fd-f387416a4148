'use client';

import { createContext, useContext, useEffect, useRef, useState, ReactNode } from 'react';
import { useConvexAuth, useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useToast } from '@/components/hooks/use-toast';
import { Id } from '@/convex/_generated/dataModel';

// Define the shape of the user data context
interface UserData {
  user: any | null | undefined;
  person: any | null | undefined;
  isLoading: boolean;
  hasCheckedNewUser: boolean;
  organizations: {
    list: Array<{
      name: string;
      role?: string;
      _id: Id<"organizations">;
    }>;
    formatted: string;
  };
}

// Create the context
const UserDataContext = createContext<UserData>({
  user: undefined,
  person: undefined,
  isLoading: true,
  hasCheckedNewUser: false,
  organizations: {
    list: [],
    formatted: 'No organizations'
  }
});

// Hook to consume the context
export const useUserData = () => useContext(UserDataContext);

export function UserDataProvider({ children }: { children: ReactNode }) {
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  const { toast } = useToast();
  const [hasCheckedNewUser, setHasCheckedNewUser] = useState(false);
  const isMounted = useRef(true);
  
  // Fetch current user data - this will only re-run when auth state changes
  const user = useQuery(api.users.currentUser);
  
  // Get person data using the dedicated query
  const person = useQuery(
    api.directory.directoryPeople.getPersonByUserId,
    user?._id ? { userId: user._id } : "skip"
  );
  
  // Fetch person's organizations
  const personOrgs = useQuery(
    api.directory.directoryRelationships.listOrganizationssByPerson,
    person ? { personId: person._id } : "skip"
  );

  // Format organizations data with proper defaults
  const organizations = {
    list: personOrgs?.organizations?.map(org => ({
      ...org,
      // Ensure required fields have defaults
      is_vendor: org.is_vendor ?? false,
      updated_at: org.updated_at ?? new Date().getTime()
    })) || [],
    formatted: personOrgs?.organizations?.length
      ? personOrgs.organizations
          .map(org => `${org.name}${org.relationship?.role ? ` (${org.relationship.role})` : ''}`)
          .join(', ')
      : 'No organizations'
  };
  
  // Check if a user is new - using a mutation
  const checkNewUser = useMutation(api.users.checkNewUser);
  
  // Set up cleanup effect to prevent memory leaks
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);
  
  // Handle new user check once when authenticated and data is loaded
  useEffect(() => {
    const handleCheckNewUser = async () => {
      if (!isMounted.current) return;
      
      try {
        const result = await checkNewUser();
        if (result?.isNewUser && isMounted.current) {
          toast({
            title: "Welcome!",
            description: "Thanks for joining. We're setting up your account.",
          });
        }
      } catch (error) {
        console.error("Error checking new user status:", error);
      }
    };
    
    // Only check for new users once when authenticated and user data exists
    if (!hasCheckedNewUser && isAuthenticated && !authLoading && user !== undefined) {
      console.log("Checking new user status - should only happen once");
      setHasCheckedNewUser(true);
      void handleCheckNewUser();
    }
  }, [isAuthenticated, authLoading, hasCheckedNewUser, checkNewUser, toast, user]);
  
  // Debug logging - happens once per mount
  const hasLoggedRef = useRef(false);
  useEffect(() => {
    if (!hasLoggedRef.current && !authLoading && user !== undefined) {
      console.log('UserDataProvider initialized:', {
        isAuthenticated,
        hasUser: !!user,
        hasPerson: !!person,
        organizations: organizations.list.length
      });
      hasLoggedRef.current = true;
    }
  }, [isAuthenticated, authLoading, user, person, organizations.list.length]);
  
  // Create the context value
  const contextValue: UserData = {
    user,
    person,
    isLoading: authLoading || user === undefined,
    hasCheckedNewUser,
    organizations
  };
  
  return (
    <UserDataContext.Provider value={contextValue}>
      {children}
    </UserDataContext.Provider>
  );
} 