import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, CheckCircle, XCircle, AlertCircle, PauseCircle, CircleIcon, ArrowUp, Minus, ArrowDown, AlertOctagon, Slash, Clock, Tag as TagIcon, X as XIcon, Plus } from "lucide-react"; // Added XIcon and Plus
import { useToast } from "@/components/hooks/use-toast";
import { Id } from '@/convex/_generated/dataModel';
import { AddTaggableTagPopover } from '@/components/ui/AddTaggableTagPopover'; // Import the generic popover
import { getStatusStyle, formatStatus } from '@/lib/utils/task-status-styles'; // Assuming this utility exists or will be created
import { getImportanceStyle, formatImportance } from '@/lib/utils/task-importance-styles'; // Assuming this utility exists or will be created
import { Tag } from '@/zod/tags-schema'; // Import Tag type
import { cn } from '@/lib/utils'; // Import cn utility

// Constants for task statuses and importance levels from zod/tasks-schema.ts
const TASK_STATUSES = ['todo', 'in_progress', 'blocked', 'completed', 'cancelled'] as const;
const TASK_IMPORTANCE = ['low', 'medium', 'high', 'critical'] as const;

// Helper function to get status icon (Adjusted for task statuses)
const getStatusIcon = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return <CheckCircle className="h-3.5 w-3.5" />;
    case 'in_progress':
      return <Clock className="h-3.5 w-3.5" />; // Using Clock for in_progress
    case 'blocked':
      return <XCircle className="h-3.5 w-3.5" />; // Using XCircle for blocked
    case 'cancelled':
      return <Slash className="h-3.5 w-3.5" />;
    case 'todo':
      return <CircleIcon className="h-3.5 w-3.5" />; // Using CircleIcon for todo
    default:
      return <CircleIcon className="h-3.5 w-3.5" />; // Default to todo icon
  }
};

// Helper function to get importance icon (Same as decisions)
const getImportanceIcon = (importance: 'low' | 'medium' | 'high' | 'critical' | undefined) => {
  switch (importance) {
    case 'critical':
      return <ArrowUp className="h-3.5 w-3.5" />;
    case 'high':
      return <ArrowUp className="h-3.5 w-3.5" />;
    case 'medium':
      return <Minus className="h-3.5 w-3.5" />;
    case 'low':
      return <ArrowDown className="h-3.5 w-3.5" />;
    default:
      return null;
  }
};

interface TaskActionBarProps {
  item: {
    status?: typeof TASK_STATUSES[number];
    importance?: typeof TASK_IMPORTANCE[number] | undefined;
    _id: string;
  };
  // Update prop name and type for tasks
  updateTask: (args: { updates: { id: Id<'tasks'>; updates: any } }) => Promise<any>;
  // Add new props for tags
  tags?: Tag[];
  onRemoveTag?: (tagId: Id<'tags'>) => Promise<void>;
}

const TaskActionBar: React.FC<TaskActionBarProps> = ({ 
  item, 
  updateTask, 
  tags = [], // Default to empty array if not provided
  onRemoveTag 
}) => {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);

  // Handler for status change
  const handleStatusChange = async (newStatus: typeof TASK_STATUSES[number]) => {
    if (isUpdating) return; // Prevent multiple updates
    try {
      setIsUpdating(true);
      await updateTask({ // Use updateTask prop
        updates: {
          id: item._id as Id<'tasks'>, // Cast ID to task ID
          updates: { status: newStatus }
        }
      });
      toast({
        title: "Status updated",
        description: `Task status changed to ${formatStatus(newStatus)}` // Use task formatter
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating status",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handler for importance change
  const handleImportanceChange = async (newImportance: typeof TASK_IMPORTANCE[number] | undefined) => {
    if (isUpdating) return; // Prevent multiple updates
    try {
      setIsUpdating(true);
      await updateTask({ // Use updateTask prop
        updates: {
          id: item._id as Id<'tasks'>, // Cast ID to task ID
          updates: { importance: newImportance }
        }
      });
      toast({
        title: "Importance updated",
        description: `Task importance changed to ${formatImportance(newImportance)}` // Use task formatter
      });
    } catch (error: unknown) {
      toast({
        title: "Error updating importance",
        description: error instanceof Error ? error.message : "Failed to update importance",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="flex items-center gap-4 p-4 border-b border-border w-full"> {/* Added border */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Status</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild disabled={isUpdating}>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${getStatusStyle(item.status)} ${isUpdating ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {getStatusIcon(item.status)}
              <span className="capitalize">{formatStatus(item.status)}</span> {/* Use formatter */}
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start"> {/* Changed align to start */}
            {TASK_STATUSES.map((status) => (
              <DropdownMenuItem
                key={status}
                onClick={() => handleStatusChange(status)}
                className={item.status === status ? 'bg-muted' : ''}
                disabled={isUpdating}
              >
                <span className="capitalize">{formatStatus(status)}</span> {/* Use formatter */}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Importance</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild disabled={isUpdating}>
            <Badge
              variant="outline"
              className={`flex items-center gap-1 cursor-pointer ${getImportanceStyle(item.importance)} ${isUpdating ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {getImportanceIcon(item.importance)}
              <span className="capitalize">{formatImportance(item.importance)}</span> {/* Use formatter */}
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start"> {/* Changed align to start */}
            {[...TASK_IMPORTANCE, undefined].map((importance) => ( // Add undefined option
              <DropdownMenuItem
                key={importance || 'none'}
                onClick={() => handleImportanceChange(importance)}
                className={item.importance === importance ? 'bg-muted' : ''}
                disabled={isUpdating}
              >
                <span className="capitalize">{formatImportance(importance)}</span> {/* Use formatter */}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Placeholder for other potential actions */}
      {/* 
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Due Date</span>
        <Badge
          variant="outline"
          className="flex items-center gap-1 cursor-pointer"
        >
          Set Date
        </Badge>
      </div> 
      */}

      {/* Redesigned Tags Section */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Tags</span>
        <div className="flex items-start gap-1 flex-wrap"> {/* Changed items-center to items-start */}
          {tags.map((tag) => (
            <Badge
              key={tag._id}
              variant="outline" // Add outline variant for border
              className={cn(
                "relative group pr-6", // Add padding for X button, remove explicit height to match other badges
                tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
              )}
            >
              {tag.name}
              {/* Remove Tag Button */}
              {onRemoveTag && (
                <button
                  onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                  className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={`Remove tag ${tag.name}`}
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              )}
            </Badge>
          ))}
          {/* Add Tag Button - Integrated with the tags */}
          <AddTaggableTagPopover 
            taggableId={item._id as Id<'tasks'>} 
            taggableType="task" 
            tagTypeSlug="general-tags"
            triggerLabel="Add Tag"
          />
        </div>
      </div>
    </div>
  );
};

export default TaskActionBar;
