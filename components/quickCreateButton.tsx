'use client';

import React, { useState, useId, useEffect, useRef } from 'react';
import { useToast } from '@/components/hooks/use-toast';
import { useMutation, useQuery, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { 
  Plus, 
  ChevronDown,
  CheckSquare,
  LightbulbIcon,
  Briefcase,
  FileText,
  User,
  UserPlus,
} from 'lucide-react';
import { MentionsInput, Mention, SuggestionDataItem } from 'react-mentions';
import { extractFullMentionsFromText } from '@/convex/utils/mentions';
import { useDebouncedCallback } from 'use-debounce';
import { useUserData } from './user-data-provider';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ReactNode } from 'react';

// Types
/**
 * Interface for items stored in the allPeople state, representing potential mentions
 */
interface SuggestionItem {
  _id: Id<'people'> | Id<'teams'>;
  name: string;
  image?: string;
  type: 'user' | 'person' | 'team';
  user_id?: Id<'users'>; // Only present if type is 'user'
}

/**
 * Extended suggestion data item for react-mentions
 */
interface PersonSuggestion extends SuggestionDataItem {
  image?: string;
  name: string;
  email: string;
}

/**
 * Domain types supported by the QuickCreateButton
 */
export type DomainType = 'task' | 'decision' | 'project' | 'bill';

/**
 * Props for the QuickCreateButton component
 */
export interface QuickCreateButtonProps {
  /**
   * The domain type to create
   */
  domain: DomainType;

  /**
   * Optional CSS class name to apply to the button
   */
  className?: string;

  /**
   * Optional callback function to be called after successful creation
   */
  onCreated?: (domain: DomainType, id: Id<any>) => void;

  /**
   * Optional default assignee ID
   */
  defaultAssignee?: Id<'users'>;
  
  /**
   * Optional width of the button
   */
  width?: string;
  
  /**
   * Whether the button is in collapsed mode (for sidebar)
   */
  isCollapsed?: boolean;
  
  /**
   * Optional callback function called when dropdown or popover state changes
   * Used to keep the sidebar expanded when interacting with dropdown
   */
  onDropdownStateChange?: (isOpen: boolean) => void;
  
  title?: string;
  description?: string;
  icon?: ReactNode;
  footerContent?: ReactNode;
  children?: ReactNode;
}

/**
 * User interface for the assignee dropdown
 */
export interface UserOption {
  _id: Id<'users'>;
  name?: string;
  email: string;
  image?: string;
}

/**
 * Form data for the quick create form
 */
export interface QuickCreateFormData {
  name: string;
  assignee?: (Id<'users'> | Id<'teams'>)[];
}

/**
 * Card-based quick create interface props
 */
export interface QuickCreateCardProps {
  /**
   * The domain type to create
   */
  domain: DomainType;
  
  /**
   * Title for the card
   */
  title: string;
  
  /**
   * Optional description for the card
   */
  description?: string;
  
  /**
   * Optional icon to display in the card header
   */
  icon?: ReactNode;
  
  /**
   * Optional CSS class name to apply to the card
   */
  className?: string;
  
  /**
   * Optional callback function to be called after successful creation
   */
  onCreated?: (domain: DomainType, id: Id<'tasks' | 'decisions' | 'projects' | 'bills'>) => void;
  
  /**
   * Optional default assignee ID
   */
  defaultAssignee?: Id<'users'>;
  
  /**
   * Optional footer content
   */
  footerContent?: ReactNode;
  
  /**
   * Optional additional content to display in the card
   */
  children?: ReactNode;
}

/**
 * Card layout options for quick create UI
 */
export type QuickCreateCardLayout = 'compact' | 'standard' | 'detailed';

/**
 * Style variants for quick create cards
 */
export type QuickCreateCardVariant = 'default' | 'outline' | 'ghost';

// Add custom interface for our suggestion data
interface CustomSuggestionDataItem extends SuggestionDataItem {
  image?: string;
  name?: string;
  type: 'user' | 'person' | 'team';
  user_id?: Id<'users'>;
}

interface TaskInput {
  name: string;
  description?: string;
  status: 'todo';
  driver?: Id<'users'>;
  contributors?: (Id<'users'> | Id<'teams'>)[];
  informed?: (Id<'users'> | Id<'teams'>)[];
}

export function QuickCreate({
  domain = 'task',
  onCreated,
  className = '',
  width = '150px',
  isCollapsed = false,
  onDropdownStateChange,
  title,
  description,
  icon,
  defaultAssignee,
  footerContent,
  children
}: QuickCreateButtonProps) {
  // State
  const { toast } = useToast();
  const uniqueId = useId();
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState<DomainType>(domain);
  const [name, setName] = useState('');
  const [driver, setDriver] = useState<SuggestionItem | undefined>(undefined); 
  const [contributors, setContributors] = useState<(Id<'users'> | Id<'teams'>)[]>([]);
  const [loading, setLoading] = useState(false);
  const [mentionSearch, setMentionSearch] = useState('');
  const [allPeople, setAllPeople] = useState<SuggestionItem[]>([]); // Use defined interface
  const lastSearchRef = useRef('');

  // Get user data from context instead of making our own query
  // Get user and person data from context
const { user: currentUser, person: currentPerson } = useUserData();
// currentPerson?._id is the peopleId for the logged-in user.
// We will always set this as the requestor field in the create payload.

  // Mutations
  const createTask = useMutation(api.tasks.createTasks);
  const createDecision = useMutation(api.decisions.createDecisions);
  const createProject = useMutation(api.projects.createProjects);
  const createBill = useMutation(api.bills.createBill);
  
  // Action for generating task names and descriptions
  const generateTaskNameAndDescription: (args: {
    userInput: string;
    driver: Id<'users'> | null;
    contributors?: Array<Id<'users'> | Id<'teams'>>;
  }) => Promise<any> = useAction(
    // @ts-ignore - This action exists but TypeScript doesn't know about it yet
    api.actions.writeTaskDescription.generateTaskNameAndDescription
  );
  
  // Query that updates as you type - this is fine as it only runs when the dropdown is open
  const peopleResult = useQuery(
    api.directory.directory.searchPeopleAndTeams, 
    isOpen ? {
      search: mentionSearch,
      limit: 20,
      includeNonUsers: true, // Include non-user people for Requestor role
    } : "skip"
  );
  
  // Add this near the top of the component with other queries
  const generalCategory = useQuery(
    api.decisions.getDecisionCategoryBySubtableType, 
    { subtableType: 'general_decisions' }
  );

  // Update people results when query changes
  useEffect(() => {
    if (!isOpen || !peopleResult) return;
    
    // Only update if this is still the latest search
    if (mentionSearch === lastSearchRef.current) {
      // Transform the results to match the SuggestionItem interface
      const transformedResults: SuggestionItem[] = peopleResult.map(result => ({
        _id: result._id,
        name: result.name || '',
        image: result.image,
        type: result.type,
        // Use the actual user_id field from the API result instead of _id
        user_id: result.type === 'user' ? result.user_id : undefined
      }));

      setAllPeople(transformedResults);
      console.log("Updated allPeople with results:", transformedResults);
    }
  }, [peopleResult, isOpen, mentionSearch]);

  // Debounced search function with shorter delay
  const debouncedSetSearch = useDebouncedCallback(
    (search: string) => {
      lastSearchRef.current = search;
      setMentionSearch(search);
    },
    50  // Reduced from 150ms to 50ms for better responsiveness
  );

  // Format people and teams for mentions
  const formatPeopleForMentions = (items: any[]) => {
    return items.map((item) => ({
      id: item._id,
      display: item.name || '',
      image: item.image,
      name: item.name,
      type: item.type,
      user_id: item.user_id
    })) as CustomSuggestionDataItem[];
  };

  // Effects
  // Update initial driver if defaultAssignee is provided and driver isn't set
  useEffect(() => {
    // Find the corresponding SuggestionItem if defaultAssignee is provided
    // This requires allPeople to be potentially loaded, might need adjustment if initial load is slow
    if (defaultAssignee && !driver) {
       const defaultDriverItem = allPeople.find(p => p.type === 'user' && p.user_id === defaultAssignee);
       if (defaultDriverItem) {
         setDriver(defaultDriverItem);
       } 
       // TODO: Consider fetching the user details if not found in allPeople initially
    }
    // Clear contributors if driver is set to default and contributors are empty (initial state)
    // No explicit action needed here, just ensures initial state is clean if defaultAssignee is the driver
  }, [defaultAssignee, driver]); // Removed contributors dependency as it caused potential loops

  // useEffect to notify parent when dropdown or popover states change
  useEffect(() => {
    if (onDropdownStateChange) {
      onDropdownStateChange(isOpen || dropdownOpen);
    }
  }, [isOpen, dropdownOpen, onDropdownStateChange]);

  // Get domain text
  const getDomainText = (d: DomainType): string => ({
    task: 'Add Task',
    decision: 'Add Decision',
    project: 'Add Project',
    bill: 'Add Bill'
  })[d];

  // Get domain icon
  const getDomainIcon = (d: DomainType) => {
    const iconSize = 18;
    const icons = {
      task: <CheckSquare size={iconSize} />,
      decision: <LightbulbIcon size={iconSize} />,
      project: <Briefcase size={iconSize} />,
      bill: <FileText size={iconSize} />
    };
    return icons[d] || <Plus size={iconSize} />;
  };

  // Handle create
  const handleCreate = async () => {
    if (!name.trim()) {
      return;
    }

    try {
      setLoading(true);
      let createdId: any;

      switch (selectedDomain) {
        case 'task': {
          try {
            // For contributors, ensure we use the proper ID (user_id for users, _id for teams)
            const processedContributors = [];
            for (const id of contributors) {
              const item = allPeople.find(p => p._id === id);
              if (item?.type === 'user') {
                // Make sure we have a valid user_id
                if (item.user_id) {
                  processedContributors.push(item.user_id);
                  console.log(`Using user_id for ${item.name}: ${item.user_id}`);
                } else {
                  console.warn(`Missing user_id for ${item.name}`);
                }
              } else if (item?.type === 'team') {
                // For teams, use the _id directly
                processedContributors.push(id);
                console.log(`Using team ID directly: ${id}`);
              }
            }
            
            // Log the processed contributor IDs
            console.log("Using processed contributors:", processedContributors);

            // Extract mentions from text - the mentions in the text are in format @[Name](__type__:id)
            // We need to parse these directly to extract IDs
            const mentionRegex = /@\[(.*?)\]\((.*?):(.*?)\)/g;
            const mentionMatches = [...name.matchAll(mentionRegex)];
            console.log("Detected mentions in text:", mentionMatches);
            
            // Also check for plain @Name format in case we need it
            const plainMentionRegex = /@([a-zA-Z0-9_\s]+)/g;
            const plainMentions = [...name.matchAll(plainMentionRegex)];
            if (plainMentions.length > 0) {
              console.log("Detected plain mentions:", plainMentions);
            }
            
            // Look for the first mention as potential driver
            let driverIdToPass: Id<'users'> | null = null;
            const contributorIdsToPass: Array<Id<'users'> | Id<'teams'>> = [];
            let hasUserMention = false;
            let hasTeamMention = false;
            
            for (const match of mentionMatches) {
              const mentionId = match[3]; // The ID is in the third capture group
              const mentionName = match[1]; // The name is in the first capture group
              console.log(`Found mention: ${mentionName} with ID: ${mentionId}`);
              
              // Find the corresponding person in allPeople
              const person = allPeople.find(p => p._id === mentionId);
              if (person) {
                console.log(`Found person in allPeople:`, person);
                if (person.type === 'user' && person.user_id) {
                  hasUserMention = true;
                  if (!driverIdToPass) {
                    // Critical: Use the user_id from the people table as the driver ID
                    driverIdToPass = person.user_id as Id<'users'>;
                    console.log(`Setting driver to: ${person.name} with user_id: ${driverIdToPass} (ID from users table)`);
                  } else {
                    // For additional users, add their user_id (not _id) to contributors
                    contributorIdsToPass.push(person.user_id as Id<'users'>);
                    console.log(`Adding contributor: ${person.name} with user_id: ${person.user_id} (ID from users table)`);
                  }
                } else if (person.type === 'team') {
                  hasTeamMention = true;
                  // For teams, use the _id directly as it references the teams table
                  contributorIdsToPass.push(person._id as Id<'teams'>);
                  console.log(`Adding team contributor: ${person.name} with id: ${person._id}`);
                } else {
                  console.warn(`Person ${person.name} has type:${person.type} but no user_id! Cannot use as assignee.`);
                }
              } else {
                console.log(`Could not find person with ID: ${mentionId} in allPeople`);
              }
            }

            // Default driver to current user only if no user or team mentions
            if (!hasUserMention && !hasTeamMention && currentUser?._id) {
              driverIdToPass = currentUser._id;
              console.log(`No user or team mentioned, defaulting driver to current user: ${currentUser.name} (${driverIdToPass})`);
            }
            // If there are team mentions but no user mentions, driverIdToPass remains null (no driver assigned)
            
            console.log("Final extracted assignments:", {
              driver: driverIdToPass,
              contributors: contributorIdsToPass
            });

            const aiResult = await generateTaskNameAndDescription({
              userInput: name,
              driver: driverIdToPass, // Pass the ID directly, not wrapped in an object
              contributors: contributorIdsToPass.length > 0 ? contributorIdsToPass : undefined // Pass array of IDs directly
            });
            
            if (!aiResult.success || !aiResult.name) {
              toast({
                title: 'Error',
                description: 'Failed to generate task name and description',
                variant: 'destructive'
              });
              return;
            }
            
            // Log the AI result 
            console.log("AI result for task:", aiResult);

            // Create the task input object with proper typing
            const taskInput: TaskInput = {
              name: aiResult.name || '',
              description: aiResult.description || undefined,
              status: 'todo',
            };

            // Ensure driver is properly set if available
            if (aiResult.driver) {
              console.log("Setting driver to:", aiResult.driver);
              taskInput.driver = aiResult.driver;
            }

            // Ensure contributors are properly set if available
            if (aiResult.contributors && aiResult.contributors.length > 0) {
              console.log("Setting contributors to:", aiResult.contributors);
              taskInput.contributors = aiResult.contributors;
            }

            // Ensure informed are properly set if available
            if (aiResult.informed && aiResult.informed.length > 0) {
              console.log("Setting informed to:", aiResult.informed);
              taskInput.informed = aiResult.informed;
            }

            // Log final task input before creating
            console.log("Creating task with:", taskInput);

            // Create the task
            const taskResult = await createTask({
              input: taskInput
            });
            
            createdId = taskResult.ids[0];
            
            // Reset the name, keep popover open for rapid creation
            setName('');
            // setIsOpen(false); // Keep popover open
            
            toast({
              title: 'Success',
              description: 'Task created with AI-generated name and description',
            });
            
            console.log("Created task with AI-generated name and description:", createdId);
          } catch (error) {
            console.error("Error creating task:", error);
            toast({
              title: "Error",
              description: "Failed to create task",
              variant: "destructive"
            });
          }
          break;
        }

        case 'decision': {
          // Check if we have the general category
          if (!generalCategory?._id) {
            toast({
              title: 'Error',
              description: 'Could not find general decision category',
              variant: 'destructive'
            });
            return;
          }

          // Combine driver and contributors for assigned_to
          // For assignments, deduplication is only relevant for driver/contributors.
          // The requestor field is always independent and can overlap with any other assignment role (driver, contributor, etc).
          // Do NOT remove or filter out the requestor from assignments, and do not apply DCI deduplication logic to requestor.
          // requestor is only active for tasks
          const combinedAssignees = [
            ...(driver?.user_id ? [driver.user_id] : []), // Use driver.user_id
            ...contributors
          ];
          // If you want to deduplicate driver/contributors, you can still use:
          // const uniqueAssignees = [...new Set(combinedAssignees)];
          // But do not apply this logic to the requestor field.

          const decisionResult = await createDecision({
            input: { 
              title: name, 
              decision_category_id: generalCategory._id as Id<'decision_categories'>, 
              status: 'draft', 
              // DCI fields
              driver: driver?.user_id, 
              contributors: contributors.length > 0 ? contributors : undefined,
            }
          });
          createdId = decisionResult.ids[0];
          break;
        }

        case 'project': {
          // Use driver state for owner_id
          const projectResult = await createProject({
            input: { 
              name, 
              status: 'not_started', 
              owner_id: driver?.user_id, // Pass driver.user_id

            }
          });
          createdId = projectResult.ids[0];
          break;
        }

        case 'bill': {
          createdId = await createBill({
            bill: {
              type: 'BILL',
              billNo: name,
              billDate: Date.now(),
              vendor_id: '' as Id<"organizations">,
              amount: 0,

            }
          });
          break;
        }
      }

      if (onCreated && createdId) {
        onCreated(selectedDomain, createdId);
      }
    } catch (error: unknown) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create item',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
};

  // Prevent mouse events from propagating to parent (sidebar) when dropdown or popover is open
  const handleMouseEnter = (e: React.MouseEvent) => {
    if (dropdownOpen || isOpen) {
      e.stopPropagation();
    }
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    if (dropdownOpen || isOpen) {
      e.stopPropagation();
    }
  };

  const handleDropdownOpenChange = (open: boolean) => {
    setDropdownOpen(open);
    // No need to call onDropdownStateChange here as the useEffect will handle it
  };

  const handlePopoverOpenChange = (open: boolean) => {
    setIsOpen(open);
    // No need to call onDropdownStateChange here as the useEffect will handle it
  };

  return (
    <div 
      className={`flex quick-create-container ${isCollapsed ? 'rounded-full' : 'rounded-lg border bg-primary/20'}`} 
      style={{ width: isCollapsed ? 'auto' : width }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Main button that opens the popover */}
      <Popover open={isOpen} onOpenChange={handlePopoverOpenChange}>
        <PopoverTrigger asChild>
          <Button 
            type="button"
            variant="ghost"
            className={`flex items-center justify-center quick-create-trigger ${
              isCollapsed 
                ? 'h-8 w-8 p-0 min-w-0 rounded-full bg-primary text-primary-foreground hover:bg-primary/80' 
                : 'h-7 px-2 bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/70 rounded-l-lg text-xs'
            }`}
            style={isCollapsed ? {} : { width: '130px' }}
            onClick={(e) => e.stopPropagation()}
          >
            <Plus size={isCollapsed ? 18 : 16} className={isCollapsed ? '' : 'mr-1'} />
            {!isCollapsed && (
              <span className="text-xs">
                {getDomainText(selectedDomain)}
              </span>
            )}
          </Button>
        </PopoverTrigger>

        <PopoverContent 
          className="w-[260px] overflow-visible p-2" 
          align={isCollapsed ? "end" : "start"}
          onMouseEnter={(e) => e.stopPropagation()}
          onMouseLeave={(e) => e.stopPropagation()}
        >
          <form
            className="flex flex-col gap-2"
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              void handleCreate();
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div>
              <MentionsInput
                id="title"
                value={name}
                onKeyDown={(event) => {
                  // Submit form on Enter without Shift key
                  if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    // Close the popover and reset the name immediately
                    setName('');
                    setIsOpen(false);
                    // Then handle the creation asynchronously
                    void handleCreate();
                  }
                  // Shift+Enter will still create a new line (default behavior)
                }}
                onChange={(event, newValue, newPlainTextValue, mentions) => {
                  console.log("onChange called", { event, newValue, newPlainTextValue, mentions });
                  setName(newValue);
                  
                  // Extract mentions from the text (utility function mis-types IDs, but we get them)
                  const extractedMentions = extractFullMentionsFromText(newValue);
                  
                  // If there are no mentions, clear driver and contributors
                  if (extractedMentions.length === 0) {
                    setDriver(undefined);
                    setContributors([]);
                    return;
                  }
                  
                  // Build new driver and contributors lists based on current mentions
                  let newDriverItem: SuggestionItem | undefined = undefined; // Store the whole item
                  const newContributors: (Id<'users'> | Id<'teams'>)[] = [];
                  const processedUserIds = new Set<string>(); // Still track processed user IDs
                  const processedTeamIds = new Set<string>();

                  for (const mention of extractedMentions) {
                    // Find the corresponding item in allPeople using the extracted ID
                    // Cast mention.id to string for comparison to avoid TS conflict with union type item._id
                    const mentionedItem = allPeople.find(item => item._id === (mention.id as string)); 
                    
                    if (mentionedItem) {
                      // Handle USER mention (only if they are actual users with user_id)
                      if (mentionedItem.type === 'user' && mentionedItem.user_id) {
                        const userId = mentionedItem.user_id; // Keep for processed check
                        if (!processedUserIds.has(userId)) {
                          if (newDriverItem === undefined) {
                            newDriverItem = mentionedItem; // Store the whole SuggestionItem
                          } else {
                            // Ensure we don't add the driver as a contributor
                            if (mentionedItem.user_id !== newDriverItem.user_id) {
                              newContributors.push(userId as Id<'users'>); 
                            }
                          }
                          processedUserIds.add(userId); // Add user_id to processed set
                        }
                      // Handle TEAM mention
                      } else if (mentionedItem.type === 'team') {
                        const teamId = mentionedItem._id as Id<'teams'>; 
                         if (!processedTeamIds.has(teamId)) {
                          newContributors.push(teamId); // Teams are always contributors
                          processedTeamIds.add(teamId);
                        }
                      }
                      // Ignore people who are not users (no user_id)
                    }
                  }
                  
                  // Update driver and contributors states
                  setDriver(newDriverItem);
                  setContributors(newContributors);
                }}
                allowSuggestionsAboveCursor
                forceSuggestionsAboveCursor
                allowSpaceInQuery
                a11ySuggestionsListLabel="Suggested people"
                placeholder={`${getDomainText(selectedDomain)}`}
                autoFocus
                autoComplete="off"
                className="w-full min-h-[32px] text-xs px-2 py-1 rounded"
                style={{
                  control: {
                    backgroundColor: 'transparent',
                    fontSize: 12,
                    fontWeight: 'normal',
                    minHeight: '32px',
                    maxHeight: '150px',
                    overflow: 'auto',
                  },
                  highlighter: {
                    padding: 8,
                    border: '1px solid transparent',
                    minHeight: '32px',
                  },
                  input: {
                    padding: 8,
                    border: 'none',
                    outline: 'none', 
                    background: 'transparent',
                    minHeight: '32px',
                  },
                  suggestions: {
                    list: {
                      backgroundColor: 'white',
                      border: '1px solid rgba(0,0,0,0.15)',
                      fontSize: 12,
                      borderRadius: '0.375rem',
                    },
                    item: {
                      padding: '5px 12px',
                      borderBottom: '1px solid rgba(0,0,0,0.05)',
                      '&focused': {
                        backgroundColor: 'var(--primary-color-light, #f3f4f6)', // Matches shadcn/ui
                      },
                    },
                  },
                }}
              >
                <Mention
                  trigger="@"
                  // Include type in the markup
                  markup="@[__display__](__type__:__id__)" 
                  data={async (search: string, callback: (data: SuggestionDataItem[]) => void) => {
                    lastSearchRef.current = search;
                    setMentionSearch(search);
                    const currentResults = allPeople ? formatPeopleForMentions(allPeople) : [];
                    callback(currentResults);
                  }}
                  renderSuggestion={(
                    suggestion,
                    search,
                    highlightedDisplay,
                    index,
                    focused
                  ) => {
                    const customSuggestion = suggestion as CustomSuggestionDataItem;
                    return (
                      <div className={`flex items-center p-1 ${focused ? 'bg-primary/10' : ''}`}>
                        {customSuggestion.image ? (
                          <img src={customSuggestion.image} alt={customSuggestion.display || ''} className="w-5 h-5 rounded-full mr-2" />
                        ) : (
                          customSuggestion.type === 'team' ? (
                            <UserPlus size={14} className="mr-2" />
                          ) : (
                            <User size={14} className="mr-2" />
                          )
                        )}
                        <span>{customSuggestion.display}</span>
                        {customSuggestion.type === 'team' && (
                          <Badge variant="outline" className="ml-2 text-[10px] py-0">Team</Badge>
                        )}
                        {customSuggestion.type === 'person' && (
                          <Badge variant="outline" className="ml-2 text-[10px] py-0">Requestor</Badge>
                        )}
                      </div>
                    );
                  }}
                  appendSpaceOnAdd
                  displayTransform={(id, display) => `@${display}`}
                  onAdd={(id: string | number, display: string) => {
                    // Find the selected item in allPeople
                    const mentioned = allPeople.find(item => item._id === (id as string));
                    // console.log("Mention added:", { id, display, mentioned }); // Optional: Keep for debugging if needed
                    // State setting logic removed from here, handled by onChange
                  }}
                />
              </MentionsInput>
              <p className="text-xs text-muted-foreground mt-1">
                Tip: @mention a user to assign the task.
              </p>
            </div>

            {currentUser && (
              <div className="flex items-center justify-between gap-2">
                {/* Assignee Display Area */}
                <div className="flex items-center gap-1 w-[180px] h-7 text-xs py-0 px-2 border rounded-md overflow-hidden">
                  {/* Display Driver */}
                  {driver && driver.user_id && ( // Check driver and driver.user_id exist
                    <div className="flex items-center flex-shrink-0 mr-1 border-r pr-1 border-border" title={`Driver: ${driver.name || currentUser?.name || 'Driver'}`}>
                      <User size={12} className="mr-0.5 text-primary" />
                      <Avatar className="h-5 w-5">
                        <AvatarImage 
                          src={driver.image || currentUser?.image} // Use driver.image
                          alt={driver.name || currentUser?.name || 'Driver'} // Use driver.name
                        />
                        <AvatarFallback className="text-[10px]">
                          {(driver.name || currentUser?.name || '?').charAt(0).toUpperCase()} 
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  )}
                  {/* Display Contributors */}
                  {contributors.length > 0 ? (
                    <div className="flex items-center gap-1 overflow-x-auto">
                      {/* Filter out duplicates and map with unique keys */}
                      {[...new Set(contributors)].map((contributorId, index) => {
                        const item = allPeople.find(p => 
                          (p.type === 'user' && p.user_id === contributorId) || 
                          (p.type === 'team' && p._id === contributorId)
                        );
                        const isCurrentUserContributor = currentUser && contributorId === currentUser._id;
                        const displayData = isCurrentUserContributor ? { ...currentUser, type: 'user' as const } : item;
                        const isTeam = displayData?.type === 'team';
                        // Create a unique key using both the ID and index
                        const uniqueKey = `${contributorId as string}_${index}`;

                        return (
                          <div 
                            key={uniqueKey} 
                            className="flex items-center flex-shrink-0" 
                            title={displayData?.name || (isCurrentUserContributor ? currentUser.email : '')}
                          >
                            {isTeam ? (
                              <div className="h-5 w-5 flex items-center justify-center rounded-full bg-muted text-muted-foreground" title={`Team: ${displayData?.name}`}>
                                <UserPlus size={12} /> 
                              </div>
                            ) : (
                              <Avatar className="h-5 w-5">
                                <AvatarImage 
                                  src={displayData?.image} 
                                  alt={displayData?.name || (isCurrentUserContributor ? currentUser.email : 'User')} 
                                />
                                <AvatarFallback className="text-[10px]">
                                  {(displayData?.name || (isCurrentUserContributor ? currentUser.email : '?')).charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="flex items-center text-muted-foreground">
                      <UserPlus size={14} className="mr-2" />
                      <span>Select assignee</span>
                    </div>
                  )}
                </div>

                <Button
                  type="submit"
                  size="sm"
                  className="h-6 text-xs px-2 py-0"
                  disabled={loading}
                >
                  Create
                </Button>
              </div>
            )}
          </form>
        </PopoverContent>
      </Popover>

      {/* Divider and dropdown menu only shown in expanded mode */}
      {!isCollapsed && (
        <>
          {/* Divider */}
          <div className="w-px h-7 bg-border"></div>

          {/* Dropdown for changing the domain type */}
          <DropdownMenu open={dropdownOpen} onOpenChange={handleDropdownOpenChange}>
            <DropdownMenuTrigger asChild>
              <button 
                className="flex items-center justify-center h-7 w-5 rounded-r-lg bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/70 quick-create-trigger"
                onClick={(e) => e.stopPropagation()}
              >
                <ChevronDown size={15} />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              align="end" 
              className="w-40"
              onMouseEnter={(e) => e.stopPropagation()}
              onMouseLeave={(e) => e.stopPropagation()}
            >
              {(['task', 'decision', 'project', 'bill'] as DomainType[]).map((d) => (
                <DropdownMenuItem 
                  key={d}
                  className="cursor-pointer text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedDomain(d);
                    setDropdownOpen(false);
                  }}
                >
                  {getDomainIcon(d)}
                  <span className="ml-2">{d.charAt(0).toUpperCase() + d.slice(1)}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      )}
    </div>
  );
}
