'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon } from '@/components/icons';
import { format } from 'date-fns';

interface BillsHeaderProps {
  totalAmount: number;
  previousMonthAmount: number;
  unusualPatterns?: {
    type: string;
    message: string;
  }[];
}

export function BillsHeader({
  totalAmount,
  previousMonthAmount,
  unusualPatterns = []
}: BillsHeaderProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [period, setPeriod] = useState('this-month');

  const percentageChange =
    ((totalAmount - previousMonthAmount) / previousMonthAmount) * 100;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Bills Overview</h1>
        <div className="flex space-x-4">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="this-month">This Month</SelectItem>
              <SelectItem value="last-month">Last Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="ytd">Year to Date</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          {period === 'custom' && (
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-[240px] justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, 'PPP') : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <Card className="p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Bills</h3>
          <p className="mt-2 text-3xl font-semibold">
            {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(totalAmount)}
          </p>
          <div
            className={`mt-2 text-sm ${percentageChange >= 0 ? 'text-green-600' : 'text-red-600'}`}
          >
            {percentageChange >= 0 ? '↑' : '↓'}{' '}
            {Math.abs(percentageChange).toFixed(1)}% from last month
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-sm font-medium text-gray-500">
            Month-over-Month
          </h3>
          <p className="mt-2 text-3xl font-semibold">
            {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(previousMonthAmount)}
          </p>
          <p className="mt-2 text-sm text-gray-500">Previous month total</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-sm font-medium text-gray-500">
            Unusual Patterns
          </h3>
          {unusualPatterns.length > 0 ? (
            <ul className="mt-2 space-y-2">
              {unusualPatterns.map((pattern, index) => (
                <li key={index} className="text-sm">
                  <span className="inline-block w-2 h-2 rounded-full bg-yellow-400 mr-2" />
                  {pattern.message}
                </li>
              ))}
            </ul>
          ) : (
            <p className="mt-2 text-sm text-gray-500">
              No unusual patterns detected
            </p>
          )}
        </Card>
      </div>
    </div>
  );
}
