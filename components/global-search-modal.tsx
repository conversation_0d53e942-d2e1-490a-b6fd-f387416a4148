import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Command } from 'cmdk';
import { Dialog, DialogContent, DialogTitle } from './ui/dialog';
import { ScrollArea } from './ui/scroll-area';
import {
  Search,
  FileText,
  Briefcase,
  CheckSquare,
  Sparkles,
  Users,
  Building,
  FileText as Document
} from 'lucide-react';
import { useQuery, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useDebounce } from 'use-debounce';
import { motion } from 'motion/react';
import { useToast } from "@/components/hooks/use-toast";

interface SearchResult {
  id: string;
  title: string;
  type: string;
  description?: string;
  short_description?: string;
  icon?: React.ReactNode;
  url: string;
  score?: number;
  source?: 'keyword' | 'contextual';
}

interface VectorSearchResult {
  id: string;
  title: string;
  type: string;
  short_description?: string;
  score: number;
  item: any;
}

interface GlobalSearchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function GlobalSearchModal({
  open,
  onOpenChange
}: GlobalSearchModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [query, setQuery] = useState('');
  const [debouncedQuery] = useDebounce(query, 300);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>('all');

  // Use the universalSearch query for text search
  const textSearchResults = useQuery(
    api.universalSearch.universalSearch,
    debouncedQuery ? { searchTerm: debouncedQuery, limit: 20 } : 'skip'
  );

  // Use the recentEntities query for default results when no search term is entered
  const recentResults = useQuery(
    api.universalSearch.recentEntities,
    open && !debouncedQuery ? { limit: 15 } : 'skip'
  );

  // Use the vectorSearch action for semantic search
  const vectorSearch = useAction(api.universalSearch.vectorSearch);

  // Get icon based on result type
  const getIconForType = (type: string) => {
    switch (type) {
      case 'project':
        return <Briefcase className="h-4 w-4" />;
      case 'task':
        return <CheckSquare className="h-4 w-4" />;
      case 'decision':
        return <Document className="h-4 w-4" />;
      case 'person':
        return <Users className="h-4 w-4" />;
      case 'organization':
        return <Building className="h-4 w-4" />;
      case 'meeting_notes':
        return <FileText className="h-4 w-4 text-teal-500" />;
      case 'knowledge_base':
        return <FileText className="h-4 w-4 text-indigo-500" />;
      case 'file':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  // Get URL based on result type
  const getUrlForResult = (result: any) => {
    // Check if this is a file that's actually a meeting note or knowledge base
    if (result.type === 'file' && result.item?.meeting_note) {
      return `/documents/meeting-notes/${result.id}`;
    } else if (result.type === 'file' && result.item?.knowledge_base) {
      return `/documents/knowledge-base/${result.id}`;
    }

    switch (result.type) {
      case 'project':
        return `/projects/${result.id}`;
      case 'task':
        return `/tasks/${result.id}`;
      case 'decision':
        return `/decisions/${result.id}`;
      case 'person':
        return `/directory/people/${result.id}`;
      case 'organization':
        return `/directory/organizations/${result.id}`;
      case 'meeting_notes':
        return `/documents/meeting-notes/${result.item.fileId}`;
      case 'knowledge_base':
        return `/documents/knowledge-base/${result.item.fileId}`;
      case 'file':
        return `/documents/files/${result.id}`;
      default:
        return `/${result.type}/${result.id}`;
    }
  };

  // Get the display type for a result
  const getDisplayType = (result: any) => {
    // Check if this is a file that's actually a meeting note or knowledge base
    if (result.type === 'file' && result.item?.meeting_note) {
      return 'meeting_notes';
    } else if (result.type === 'file' && result.item?.knowledge_base) {
      return 'knowledge_base';
    }
    return result.type;
  };

  // Perform vector search when query changes
  useEffect(() => {
    const performVectorSearch = async () => {
      if (debouncedQuery) {
        setIsLoading(true);
        try {
          const vectorResults = await vectorSearch({ searchTerm: debouncedQuery, limit: 10 });
          
          // Process vector search results when typing
          if (vectorResults && Array.isArray(vectorResults)) {
            // Deduplicate vector results: prefer meeting_notes & knowledge_base badges
            const deduped = (() => {
              const seenFileIds = new Set<string>();
              const list: any[] = [];
              for (const r of vectorResults) {
                if (r.type === 'meeting_notes' || r.type === 'knowledge_base') {
                  list.push(r);
                  if (r.item?.fileId) {
                    seenFileIds.add(r.item.fileId);
                  }
                } else if (r.type === 'file') {
                  if (!seenFileIds.has(r.id)) {
                    list.push(r);
                  }
                } else {
                  list.push(r);
                }
              }
              return list;
            })();
            const formattedResults = deduped.map((result: VectorSearchResult) => ({
              id: result.id,
              title: result.title,
              type: result.type,
              short_description: result.short_description || '',
              icon: getIconForType(result.type),
              url: getUrlForResult(result),
              score: result.score,
              source: 'contextual' as const
            }));
            
            setSearchResults(prevResults => {
              const existingIds = new Set(prevResults.map((r: SearchResult) => r.id));
              const newResults = formattedResults.filter((r: SearchResult) => !existingIds.has(r.id));
              return [...prevResults, ...newResults];
            });
          }
        } catch (error) {
          console.error('Vector search error:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    void performVectorSearch();
  }, [debouncedQuery, vectorSearch]);

  // Process text search results
  useEffect(() => {
    if (textSearchResults && textSearchResults.length > 0) {
      // Deduplicate files: prefer meeting_notes and knowledge_base badges
      const deduped = (() => {
        const seenFileIds = new Set<string>();
        const list: any[] = [];
        for (const r of textSearchResults) {
          if (r.type === 'meeting_notes' || r.type === 'knowledge_base') {
            list.push(r);
            if (r.item?.fileId) {
              seenFileIds.add(r.item.fileId);
            }
          } else if (r.type === 'file') {
            if (!seenFileIds.has(r.id)) {
              list.push(r);
            }
          } else {
            list.push(r);
          }
        }
        return list;
      })();
      const formattedResults = deduped.map((result: any) => ({
        id: result.id,
        title: result.title,
        type: result.type,
        // Remove description field as requested
        short_description: result.short_description || '',
        icon: getIconForType(result.type),
        url: getUrlForResult(result),
        source: result.source || 'keyword' as const
      }));
      
      setSearchResults(formattedResults);
    }
  }, [textSearchResults]);

  // Process recent entities when modal is opened with no search query
  useEffect(() => {
    if (recentResults && recentResults.length > 0 && !debouncedQuery) {
      // Deduplicate files: prefer meeting_notes and knowledge_base badges
      const deduped = (() => {
        const seenFileIds = new Set<string>();
        const list: any[] = [];
        for (const r of recentResults) {
          if (r.type === 'meeting_notes' || r.type === 'knowledge_base') {
            list.push(r);
            if (r.item?.fileId) {
              seenFileIds.add(r.item.fileId);
            }
          } else if (r.type === 'file') {
            if (!seenFileIds.has(r.id)) {
              list.push(r);
            }
          } else {
            list.push(r);
          }
        }
        return list;
      })();
      const formattedResults = deduped.map((result: any) => ({
        id: result.id,
        title: result.title,
        type: result.type,
        short_description: result.short_description || '',
        icon: getIconForType(result.type),
        url: getUrlForResult(result),
        updated_at: result.updated_at,
        source: 'keyword' as const
      }));
      
      setSearchResults(formattedResults);
    }
  }, [recentResults, debouncedQuery, open]);

  const handleSelect = (value: string) => {
    if (!recentSearches.includes(value)) {
      setRecentSearches((prev) => [value, ...prev].slice(0, 5));
    }
  };

  // Filter results based on active filter
  const filteredResults = searchResults.filter(result => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'files') return ['meeting_notes', 'knowledge_base', 'file'].includes(result.type);
    if (activeFilter === 'projects') return result.type === 'project';
    if (activeFilter === 'decisions') return result.type === 'decision';
    if (activeFilter === 'tasks') return result.type === 'task';
    if (activeFilter === 'directory') return ['person', 'organization'].includes(result.type);
    return true;
  });

  // Limit people results to three when 'all' filter is active
  const limitedResults = activeFilter === 'all'
    ? (() => {
        let peopleCount = 0;
        return filteredResults.filter(result => {
          if (result.type === 'person') {
            if (peopleCount < 3) {
              peopleCount++;
              return true;
            }
            return false;
          }
          return true;
        });
      })()
    : filteredResults;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="min-w-[300px] max-w-[800px] w-full mx-auto rounded-2xl backdrop-blur-lg bg-gray-50/90 border border-white/20 drop-shadow-lg"
        style={{
          boxShadow: '0 0 15px rgba(255,255,255,0.5), 0 0 30px rgba(120,0,255,0.3), 0 0 45px rgba(255,0,120,0.2)'
        }}
      >
        <DialogTitle className="sr-only">Global Search</DialogTitle>
        <Command
          className="rounded-2xl shadow-none relative bg-transparent"
          filter={(value, search) => 1}
        >
            <div
              className="flex items-center px-3 mb-4"
              cmdk-input-wrapper=""
            >
              <Search className="mr-3 h-5 w-5 shrink-0 opacity-70" />
              <Command.Input
                value={query}
                onValueChange={setQuery}
                placeholder="Search anything or ask AI..."
                className="flex h-16 w-full bg-transparent py-4 text-lg outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 font-medium"
              />
              {query && (
                <button
                  onClick={() => {
                    toast({
                      title: "Coming Soon!",
                      description: "Sorry! This feature doesn't work yet!",
                      variant: "default"
                    });
                  }}
                  className="absolute right-3 top-[12px] px-4 py-2 text-sm text-primary-foreground bg-primary hover:bg-primary/70 rounded-md flex items-center gap-1.5 transition-all shadow-sm hover:shadow font-medium"
                >
                  <Sparkles className="h-3.5 w-3.5" />
                  Ask AI
                </button>
              )}
            </div>
            
            {/* Filter Pills */}
            <div className="px-3 mb-4 flex flex-wrap gap-2">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'all'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                All
              </button>
              <button
                onClick={() => setActiveFilter('files')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'files'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Files
              </button>
              <button
                onClick={() => setActiveFilter('projects')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'projects'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Projects
              </button>
              <button
                onClick={() => setActiveFilter('decisions')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'decisions'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Decisions
              </button>
              <button
                onClick={() => setActiveFilter('tasks')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'tasks'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Tasks
              </button>
              <button
                onClick={() => setActiveFilter('directory')}
                className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === 'directory'
                    ? 'bg-foreground text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Directory
              </button>
            </div>
            
            <div className="px-2">
              <ScrollArea className="h-[400px] px-2 py-3 overflow-y-auto">
                <Command.Group
                  heading={
                    <p className="text-xs font-medium text-muted-foreground">
                      Results
                    </p>
                  }
                  className="pb-2"
                >
                  <div className="grid gap-1 pt-2">
                    {limitedResults.map((result) => (
                      <Command.Item
                        key={result.id}
                        value={result.title}
                        onSelect={() => {
                          handleSelect(result.title);
                          router.push(result.url);
                          onOpenChange(false); // Close modal after selection
                        }}
                        className="flex items-center px-2 py-2 rounded-md text-sm hover:bg-muted cursor-pointer relative z-10"
                      >
                        <div className="flex items-center w-full">
                          {result.icon}
                          <div className="ml-2 w-full">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="font-medium">{result.title}</div>
                                <span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium">
                                  {getDisplayType(result) === 'meeting_notes' ? 'Meeting Notes' :
                                   getDisplayType(result) === 'knowledge_base' ? 'Knowledge Base' :
                                   getDisplayType(result) === 'file' ? 'File' :
                                   getDisplayType(result) === 'task' ? 'Task' :
                                   getDisplayType(result) === 'decision' ? 'Decision' :
                                   getDisplayType(result).charAt(0).toUpperCase() + getDisplayType(result).slice(1)}
                                </span>
                                {result.source && (
                                  <span className="ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                                    {result.source === 'keyword' ? 'Keyword' : 'Contextual Match'}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {result.short_description}
                            </div>
                          </div>
                        </div>
                      </Command.Item>
                    ))}
                  </div>
                </Command.Group>
              </ScrollArea>
            </div>
        </Command>
      </DialogContent>
    </Dialog>
  );
}
