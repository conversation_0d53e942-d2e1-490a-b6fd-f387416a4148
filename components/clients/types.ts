import { Id } from '@/convex/_generated/dataModel';

export type ClientRole = 'Primary' | 'Team';

export interface ClientTeamMember {
  assignmentId: Id<'client_assignments'>;
  userId: Id<'users'>;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  title?: string | null;
  is_primary_assignment: boolean;
}

// This type represents the data structure for a single client card,
// including joined data like assignments and badges.
export type ClientData = {
  _id: Id<'clients'>;
  _creationTime: number;
  client_name: string;
  client_type: 'person' | 'organization';
  client_status: 'active' | 'on_hold' | 'prospective' | 'former';
  client_tier: 'platinum' | 'gold' | 'silver' | 'bronze' | 'none';
  client_communication_frequency: 'weekly' | 'monthly' | 'quarterly' | 'twice_yearly' | 'annually' | 'as_needed';
  client_description?: string | null;
  client_short_description?: string | null;
  client_research?: string | null;
  client_badges?: (Id<'badges'> | null)[] | null;
  client_net_worth?: number | null;
  client_since?: number | null;
  client_last_contact?: number | null;
  updated_at: number;
  assignments: {
    assignment: {
      _id: Id<'client_assignments'>;
      _creationTime: number;
      client_id: Id<'clients'>;
      user_id: Id<'users'>;
      is_primary_assignment: boolean;
      updated_at: number;
    };
    user: {
      _id: Id<'users'>;
      name?: string | null;
      email?: string | null;
      avatar?: string | null;
    } | null;
  }[];
  badges: ({
    _id: Id<'badges'>;
    _creationTime: number;
    name: string;
    color: string;
    description?: string | null;
    icon?: string | null;
    badgeDomain?: 'task' | 'project' | 'projectUpdate' | 'decision' | 'bills' | 'organization' | 'users' | 'people' | 'general' | null;
  } | null)[];
};
