import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ClientStatus, ClientTier, ClientType } from '@/zod/clients-schema';

interface ActiveFiltersProps {
  statusFilter: ClientStatus | 'all';
  setStatusFilter: (value: ClientStatus | 'all') => void;
  tierFilter: ClientTier | 'all';
  setTierFilter: (value: ClientTier | 'all') => void;
  typeFilter: ClientType | 'all';
  setTypeFilter: (value: ClientType | 'all') => void;
  searchText: string;
  setSearchText: (value: string) => void;
  clearAllFilters: () => void;
}

export function ActiveFilters({
  statusFilter,
  setStatusFilter,
  tierFilter,
  setTierFilter,
  typeFilter,
  setTypeFilter,
  searchText,
  setSearchText,
  clearAllFilters,
}: ActiveFiltersProps) {
  const hasActiveFilters = statusFilter !== 'all' || tierFilter !== 'all' || typeFilter !== 'all' || searchText;

  if (!hasActiveFilters) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 flex-wrap">
      <span className="text-sm text-muted-foreground">Active filters:</span>
      {statusFilter !== 'all' && (
        <Badge variant="secondary" className="gap-1">
          Status: {statusFilter}
          <button
            onClick={() => setStatusFilter('all')}
            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
          >
            ×
          </button>
        </Badge>
      )}
      {tierFilter !== 'all' && (
        <Badge variant="secondary" className="gap-1">
          Tier: {tierFilter}
          <button
            onClick={() => setTierFilter('all')}
            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
          >
            ×
          </button>
        </Badge>
      )}
      {typeFilter !== 'all' && (
        <Badge variant="secondary" className="gap-1">
          Type: {typeFilter}
          <button
            onClick={() => setTypeFilter('all')}
            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
          >
            ×
          </button>
        </Badge>
      )}
      {searchText && (
        <Badge variant="secondary" className="gap-1">
          Search: "{searchText}"
          <button
            onClick={() => setSearchText('')}
            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
          >
            ×
          </button>
        </Badge>
      )}
      <Button
        variant="ghost"
        size="sm"
        onClick={clearAllFilters}
        className="text-muted-foreground hover:text-foreground"
      >
        Clear all
      </Button>
    </div>
  );
}
