"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from "@/components/hooks/use-toast";
import { useDebounce } from 'use-debounce';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Loader2 } from 'lucide-react';
import { cn } from "@/lib/utils";
import { ClientRole } from './types';

interface SearchResult {
  _id: Id<'people'> | Id<'teams'>;
  _creationTime: number;
  type: 'user' | 'team' | 'person';
  name: string;
  image?: string;
  email?: string;
  user_id?: Id<'users'>;
}

interface ClientUserAssignmentPopoverProps {
  clientId: Id<'clients'>;
  targetRole: ClientRole;
  triggerButton: React.ReactNode;
  existingMemberIds?: Set<Id<'users'>>;
}

const ClientUserAssignmentPopover = ({
  clientId,
  targetRole,
  triggerButton,
  existingMemberIds = new Set(),
}: ClientUserAssignmentPopoverProps) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 50);
  const [isAssigning, setIsAssigning] = useState<string | null>(null);
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const listRef = React.useRef<HTMLDivElement>(null);

  const searchResultsData = useQuery(
    api.directory.directory.searchPeopleAndTeams,
    isOpen ? { search: debouncedSearchTerm, type: 'user' } : 'skip'
  );

  const assignRoleMutation = useMutation(api.clients.clientMutations.assignClientRole);

  const availableUsers: SearchResult[] = React.useMemo(() => {
    if (!searchResultsData) return [];
    // Ensure we only deal with users and they are not already assigned
    return searchResultsData.filter(
      (entity): entity is SearchResult =>
        entity.type === 'user' &&
        entity.user_id !== undefined &&
        !existingMemberIds.has(entity.user_id as Id<'users'>)
    );
  }, [searchResultsData, existingMemberIds]);

  const handleAssign = async (userId: Id<'users'>) => {
    setIsAssigning(userId.toString());

    try {
      await assignRoleMutation({
        clientId,
        userId: userId,
        is_primary: targetRole === 'Primary',
      });
      
      toast({
        title: "Success",
        description: `Assigned as ${targetRole}.`,
      });
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to assign role:", error);
      toast({
        title: "Assignment Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(null);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setIsAssigning(null);
      setFocusedIndex(-1);
    } else {
      setFocusedIndex(-1);
    }
  }, [isOpen]);

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'ArrowDown' && availableUsers.length > 0) {
      event.preventDefault();
      setFocusedIndex(0);
      listRef.current?.focus();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (!availableUsers.length) return;

    let nextIndex = focusedIndex;

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      nextIndex = (focusedIndex + 1) % availableUsers.length;
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      nextIndex = (focusedIndex - 1 + availableUsers.length) % availableUsers.length;
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (focusedIndex >= 0 && focusedIndex < availableUsers.length) {
        const user = availableUsers[focusedIndex];
        if (!isAssigning && user.user_id) {
          handleAssign(user.user_id as Id<'users'>);
        }
      }
      return;
    } else if (event.key === 'Home') {
       event.preventDefault();
       nextIndex = 0;
    } else if (event.key === 'End') {
       event.preventDefault();
       nextIndex = availableUsers.length - 1;
    } else {
      return;
    }

    setFocusedIndex(nextIndex);
    const focusedElement = listRef.current?.children[nextIndex] as HTMLElement;
    focusedElement?.scrollIntoView({ block: 'nearest' });
  };

  const renderUserItem = (user: SearchResult, index: number) => {
    const userId = user.user_id as Id<'users'>;
    const isLoading = isAssigning === userId.toString();
    const isFocused = index === focusedIndex;

    return (
      <button
        key={userId}
        type="button"
        role="option"
        aria-selected={isFocused}
        onClick={() => !isLoading && handleAssign(userId)}
        onFocus={() => setFocusedIndex(index)}
        className={cn(
          "flex items-center gap-2 p-1.5 rounded cursor-pointer hover:bg-muted w-full text-left",
          isLoading ? 'opacity-70 cursor-not-allowed' : '',
          isFocused ? 'bg-muted ring-1 ring-ring' : ''
        )}
        disabled={isLoading}
      >
        <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
          {isLoading ? (
            <Loader2 className="h-3 w-3 animate-spin text-foreground" />
          ) : (
            <div className="w-3 h-3" />
          )}
        </div>
        <Avatar className="h-5 w-5 flex-shrink-0">
          <AvatarImage src={user.image ?? undefined} alt={user.name ?? 'User'} />
          <AvatarFallback className="text-xs">{user.name?.substring(0, 1).toUpperCase() || '?'}</AvatarFallback>
        </Avatar>
        <span className="text-sm truncate flex-grow">{user.name}</span>
      </button>
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {triggerButton}
      </PopoverTrigger>
       <PopoverContent className="w-80 p-0" side="bottom" align="start">
         <div className="p-2 border-b">
           <Input
             autoFocus
             aria-label="Search users"
             value={searchTerm}
             onChange={(e) => setSearchTerm(e.target.value)}
             onKeyDown={handleInputKeyDown}
            placeholder="Search users..."
            className="h-8 text-sm"
          />
        </div>
        <ScrollArea className="h-[220px]">
          {searchResultsData === undefined && isOpen && (
            <p className="text-xs text-muted-foreground text-center p-2">Loading...</p>
          )}

          {availableUsers.length === 0 && isOpen && searchResultsData !== undefined && (
            <p className="text-xs text-muted-foreground text-center p-2">
              {debouncedSearchTerm ? "No matching users found." : "No users available."}
            </p>
          )}

          <div
            ref={listRef}
             className="space-y-1 p-2 outline-none"
             onKeyDown={handleKeyDown}
              role="listbox"
              tabIndex={-1}
              aria-activedescendant={focusedIndex >= 0 ? availableUsers[focusedIndex]?.user_id?.toString() : undefined}
            >
              {availableUsers.map((user, index) => renderUserItem(user, index))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

export default ClientUserAssignmentPopover;
