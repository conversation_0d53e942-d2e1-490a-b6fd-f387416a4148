"use client";

import React, { useState } from 'react';
import { useClientDetails } from './ClientDetailsContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, CheckCircle, XCircle, AlertCircle, PauseCircle, CircleIcon, ArrowUp, Minus, ArrowDown, AlertOctagon, Slash, Clock, Play, Check, Ban, Repeat, Tag as TagIcon, X as XIcon, Plus, Users, BarChart, TrendingUp } from "lucide-react";
import { useToast } from "@/components/hooks/use-toast";
import { Id } from '@/convex/_generated/dataModel';
import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';
import { useRouter } from 'next/navigation';
import { <PERSON>lient<PERSON>tatus, ClientTier, CommunicationFrequency, ClientStatusSchema, ClientTierSchema, CommunicationFrequencySchema } from '@/zod/clients-schema';
import { Tag } from '@/zod/tags-schema';
import { AddTaggableTagPopover } from '@/components/ui/AddTaggableTagPopover';
import { cn } from '@/lib/utils';

// Color and Icon mappings for Client Status
const CLIENT_STATUS_COLORS: Record<ClientStatus, string> = {
  active: 'bg-green-100 text-green-800 border-green-200',
  on_hold: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  prospective: 'bg-blue-100 text-blue-800 border-blue-200',
  former: 'bg-gray-200 text-gray-700 border-gray-300',
};

const getStatusIcon = (status: ClientStatus) => {
  switch (status) {
    case 'active': return <CheckCircle className="h-3.5 w-3.5" />;
    case 'on_hold': return <PauseCircle className="h-3.5 w-3.5" />;
    case 'prospective': return <TrendingUp className="h-3.5 w-3.5" />;
    case 'former': return <XCircle className="h-3.5 w-3.5" />;
    default: return <CircleIcon className="h-3.5 w-3.5" />;
  }
};

// Color and Icon mappings for Client Tier
const CLIENT_TIER_COLORS: Record<ClientTier, string> = {
  platinum: 'bg-purple-100 text-purple-800 border-purple-200',
  gold: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  silver: 'bg-gray-100 text-gray-800 border-gray-200',
  bronze: 'bg-orange-100 text-orange-800 border-orange-200',
  none: 'bg-gray-200 text-gray-700 border-gray-300',
};

const getTierIcon = (tier: ClientTier) => {
  switch (tier) {
    case 'platinum': return <ArrowUp className="h-3.5 w-3.5" />;
    case 'gold': return <ArrowUp className="h-3.5 w-3.5" />;
    case 'silver': return <Minus className="h-3.5 w-3.5" />;
    case 'bronze': return <ArrowDown className="h-3.5 w-3.5" />;
    default: return <CircleIcon className="h-3.5 w-3.5" />;
  }
};

// Color and Icon mappings for Communication Frequency
const COMM_FREQUENCY_COLORS: Record<CommunicationFrequency, string> = {
  weekly: 'bg-blue-100 text-blue-800 border-blue-200',
  monthly: 'bg-green-100 text-green-800 border-green-200',
  quarterly: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  twice_yearly: 'bg-purple-100 text-purple-800 border-purple-200',
  annually: 'bg-gray-200 text-gray-700 border-gray-300',
  as_needed: 'bg-yellow-200 text-yellow-800 border-yellow-300',
};

const getCommFrequencyIcon = (freq: CommunicationFrequency) => {
  switch (freq) {
    case 'weekly': return <BarChart className="h-3.5 w-3.5" />;
    case 'monthly': return <BarChart className="h-3.5 w-3.5" />;
    case 'quarterly': return <BarChart className="h-3.5 w-3.5" />;
    default: return <Clock className="h-3.5 w-3.5" />;
  }
};

export function ClientActionBar() {
  const { client, tags } = useClientDetails();
  const { toast } = useToast();
  const router = useRouter();
  const [isUpdating, setIsUpdating] = useState(false);
  const updateClientMutation = useMutation(api.clients.clientMutations.updateClient);
  const removeTag = useMutation(api.tags.removeTagFromTaggable);

  const onRemoveTag = async (tagId: Id<'tags'>) => {
    try {
      await removeTag({
        taggable_id: client._id,
        taggable_type: 'client',
        tagId: tagId,
      });
      toast({
        title: 'Tag removed',
        description: 'The tag has been removed from this client.',
      });
    } catch (error) {
      toast({
        title: 'Error removing tag',
        description: (error as Error).message,
        variant: 'destructive',
      });
    }
  };

  const handleStatusChange = async (newStatus: ClientStatus) => {
    if (newStatus === client.client_status) return;
    setIsUpdating(true);
    try {
      await updateClientMutation({ id: client._id, updates: { client_status: newStatus } });
      toast({ title: "Status updated", description: `Client status changed to ${newStatus.replace('_', ' ')}` });
    } catch (error) {
      toast({ title: "Error updating status", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTierChange = async (newTier: ClientTier) => {
    if (newTier === client.client_tier) return;
    setIsUpdating(true);
    try {
      await updateClientMutation({ id: client._id, updates: { client_tier: newTier } });
      toast({ title: "Tier updated", description: `Client tier changed to ${newTier}` });
    } catch (error) {
      toast({ title: "Error updating tier", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleFrequencyChange = async (newFrequency: CommunicationFrequency) => {
    if (newFrequency === client.client_communication_frequency) return;
    setIsUpdating(true);
    try {
      await updateClientMutation({ id: client._id, updates: { client_communication_frequency: newFrequency } });
      toast({ title: "Frequency updated", description: `Communication frequency changed to ${newFrequency.replace('_', ' ')}` });
    } catch (error) {
      toast({ title: "Error updating frequency", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsUpdating(false);
    }
  };

  const currentStatus = client.client_status;
  const currentTier = client.client_tier;
  const currentFrequency = client.client_communication_frequency;

  return (
    <div className="flex items-center gap-4 px-4 pt-1 pb-4 w-full border-b border-gray-200">
      {/* Status Dropdown */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Status</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge variant="outline" className={cn("flex items-center gap-1 cursor-pointer", CLIENT_STATUS_COLORS[currentStatus])}>
              {getStatusIcon(currentStatus)}
              <span className="capitalize">{currentStatus.replace(/_/g, ' ')}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {Object.values(ClientStatusSchema.enum).map((status) => (
              <DropdownMenuItem key={status} onClick={() => handleStatusChange(status)} disabled={isUpdating}>
                <span className="capitalize">{status.replace('_', ' ')}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tier Dropdown */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Tier</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge variant="outline" className={cn("flex items-center gap-1 cursor-pointer", CLIENT_TIER_COLORS[currentTier])}>
              {getTierIcon(currentTier)}
              <span className="capitalize">{currentTier}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {Object.values(ClientTierSchema.enum).map((tier) => (
              <DropdownMenuItem key={tier} onClick={() => handleTierChange(tier)} disabled={isUpdating}>
                <span className="capitalize">{tier}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Communication Frequency Dropdown */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Frequency</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Badge variant="outline" className={cn("flex items-center gap-1 cursor-pointer", COMM_FREQUENCY_COLORS[currentFrequency])}>
              {getCommFrequencyIcon(currentFrequency)}
              <span className="capitalize">{currentFrequency.replace(/_/g, ' ')}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Badge>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {Object.values(CommunicationFrequencySchema.enum).map((freq) => (
              <DropdownMenuItem key={freq} onClick={() => handleFrequencyChange(freq)} disabled={isUpdating}>
                <span className="capitalize">{freq.replace('_', ' ')}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tags Section */}
      <div className="flex flex-col items-start">
        <span className="text-xs text-muted-foreground mb-1">Tags</span>
        <div className="flex items-start gap-1 flex-wrap">
          {tags.map((tag: Tag) => (
            <Badge
              key={tag._id}
              variant="outline"
              className={cn("relative group pr-6", tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80')}
              style={{ backgroundColor: tag.color ? `${tag.color}20` : undefined, borderColor: tag.color ? `${tag.color}40` : undefined, color: tag.color || undefined }}
            >
              {tag.name}
              {onRemoveTag && (
                <button
                  onClick={() => onRemoveTag(tag._id as Id<'tags'>)}
                  className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={`Remove tag ${tag.name}`}
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              )}
            </Badge>
          ))}
          <AddTaggableTagPopover
            taggableId={client._id as Id<'clients'>}
            taggableType="client"
            tagTypeSlug="general-tags"
            triggerLabel="Add Tag"
            onSuccess={() => {
              toast({ title: "Tag added", description: "The tag has been added to this client." });
            }}
          />
        </div>
      </div>
    </div>
  );
}
