"use client";

import { useClientDetails } from './ClientDetailsContext';
import { Badge } from '@/components/ui/badge';
import { MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useState } from 'react';

export function ClientHeader() {
  const { client } = useClientDetails();
  const [isMoreOptionsOpen, setMoreOptionsOpen] = useState(false);

  return (
    <header className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-4">
        <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
          <span className="text-2xl font-bold">
            {client.client_name.charAt(0)}
          </span>
        </div>
        <div>
          <h1 className="text-2xl font-bold">{client.client_name}</h1>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{client.client_type}</span>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <DropdownMenu open={isMoreOptionsOpen} onOpenChange={setMoreOptionsOpen}>
          <DropdownMenuTrigger asChild>
            <button className="p-2 rounded-md hover:bg-muted">
              <MoreVertical className="h-5 w-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem>Archive Client</DropdownMenuItem>
            <DropdownMenuItem>View History</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
