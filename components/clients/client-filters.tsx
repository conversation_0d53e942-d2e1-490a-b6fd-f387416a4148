import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Search, Filter, Grid, List } from 'lucide-react';
import { ClientStatus, ClientTier, ClientType } from '@/zod/clients-schema';

interface ClientFiltersProps {
  searchText: string;
  setSearchText: (value: string) => void;
  statusFilter: ClientStatus | 'all';
  setStatusFilter: (value: ClientStatus | 'all') => void;
  tierFilter: ClientTier | 'all';
  setTierFilter: (value: ClientTier | 'all') => void;
  typeFilter: ClientType | 'all';
  setTypeFilter: (value: ClientType | 'all') => void;
  viewMode: 'grid' | 'list';
  setViewMode: (value: 'grid' | 'list') => void;
}

export function ClientFilters({
  searchText,
  setSearchText,
  statusFilter,
  setStatusFilter,
  tierFilter,
  setTierFilter,
  typeFilter,
  setTypeFilter,
  viewMode,
  setViewMode,
}: ClientFiltersProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      {/* Search */}
      <div className="relative w-full max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search clients..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Filters and View Toggle */}
      <div className="flex items-center gap-2">
        {/* Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Status: {statusFilter === 'all' ? 'All' : statusFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setStatusFilter('all')}>
              All Statuses
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setStatusFilter('active')}>
              Active
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('prospective')}>
              Prospective
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('on_hold')}>
              On Hold
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('former')}>
              Former
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Tier Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Tier: {tierFilter === 'all' ? 'All' : tierFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setTierFilter('all')}>
              All Tiers
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setTierFilter('platinum')}>
              Platinum
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTierFilter('gold')}>
              Gold
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTierFilter('silver')}>
              Silver
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTierFilter('bronze')}>
              Bronze
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTierFilter('none')}>
              Standard
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type: {typeFilter === 'all' ? 'All' : typeFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setTypeFilter('all')}>
              All Types
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setTypeFilter('person')}>
              Person
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTypeFilter('organization')}>
              Organization
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* View Toggle */}
        <div className="flex items-center border rounded-md">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="rounded-r-none"
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="rounded-l-none"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
