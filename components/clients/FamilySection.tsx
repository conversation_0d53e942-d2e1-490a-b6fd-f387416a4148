'use client';

import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { AddRelationshipModal } from './AddRelationshipModal';

// This is a placeholder for the actual PersonCard component
const PersonCard = ({ person, isSelected, onClick }: { person: { _id: Id<'people'>; name: string }, isSelected: boolean, onClick: () => void }) => (
  <div 
    className={`p-2 border rounded-lg cursor-pointer ${isSelected ? 'border-primary' : ''}`}
    onClick={onClick}
  >
    <p>{person.name}</p>
  </div>
);

interface FamilyRelationship {
  source: { _id: Id<'people'>; name: string; imageUrl?: string | null };
  target: { _id: Id<'people'>; name: string; imageUrl?: string | null };
  relationshipId: Id<'relationships'>;
  relationshipName: string;
}

interface FamilySectionProps {
  clientId: Id<'clients'>;
  relationships: FamilyRelationship[];
  onDataChanged?: () => void;
}

export const FamilySection = ({ clientId, relationships, onDataChanged }: FamilySectionProps) => {
  const [selectedPersonId, setSelectedPersonId] = useState<Id<'people'> | null>(null);
  const deleteRelationship = useMutation(api.relationships.deleteRelationshipById);

  const handleDelete = async (id: Id<'relationships'>) => {
    try {
      await deleteRelationship({ id });
      toast.success('Relationship deleted');
      onDataChanged?.();
    } catch (error) {
      toast.error('Failed to delete relationship');
      console.error(error);
    }
  };

  // Extract all unique people from both source and target of family relationships
  const uniquePeople = Array.from(new Map(relationships.flatMap(r => [r.source, r.target]).map(p => [p._id, p])).values());
  
  // Determine button state and text
  const hasAnyPeople = uniquePeople.length > 0;
  const buttonDisabled = hasAnyPeople && !selectedPersonId;
  const buttonText = hasAnyPeople ? 'Add Family Relationship' : 'Add Principal';

  const handleAddClick = () => {
    if (hasAnyPeople) {
      // Logic for adding family relationship between existing people
      console.log('Add family relationship for selected person:', selectedPersonId);
      // TODO: Open popover to select another person and relationship type (is_client_family = true)
    } else {
      // This is handled by the AddPrincipalPopover component now
      console.log('Add principal button clicked - handled by popover');
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Family</CardTitle>
          <div className="text-sm text-muted-foreground">
            {hasAnyPeople ? 'Add more family members or relationships' : 'Add principals to get started'}
          </div>
          <AddRelationshipModal 
            clientId={clientId}
            existingPrincipals={uniquePeople}
            onSuccess={() => {
              onDataChanged?.();
              toast.success('Family member added successfully');
            }}
          >
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {hasAnyPeople ? 'Add Family Member' : 'Add Principal'}
            </Button>
          </AddRelationshipModal>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Family relationships */}
        <div>
          <h3 className="font-semibold mb-3">Family Members & Relationships</h3>
          {relationships.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="mb-2">No family members found.</div>
              <div className="text-sm">
                Click "Add Principal" to add the primary family members (spouse, children, etc.) for this client.
              </div>
            </div>
          ) : (
            <ul className="space-y-2">
              {relationships.map(rel => {
                // Check if this is a principal (source and target are the same person)
                const isPrincipal = rel.source._id === rel.target._id;
                
                return (
                  <li key={rel.relationshipId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center">
                      {isPrincipal ? (
                        // Show principal as a single person with their relationship type
                        <>
                          <span className="font-medium">{rel.source.name}</span>
                          <span className="ml-2 text-sm text-muted-foreground">({rel.relationshipName})</span>
                        </>
                      ) : (
                        // Show actual person-to-person relationship
                        <>
                          <span className="font-medium">{rel.source.name}</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-3 h-4 w-4 text-muted-foreground"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                          <span className="font-medium">{rel.target.name}</span>
                          <span className="ml-2 text-sm text-muted-foreground">({rel.relationshipName})</span>
                        </>
                      )}
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => handleDelete(rel.relationshipId as Id<'relationships'>)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
