"use client";

import React, { useState, useEffect } from 'react';
import { useClientDetails } from './ClientDetailsContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { User, Users, Plus, UserCheck, X } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { useToast } from "@/components/hooks/use-toast";
import CollectionCardModal from '@/components/ui/collectionCardModal';
import ClientUserAssignmentPopover from './ClientUserAssignmentPopover';
import { ClientTeamMember, ClientRole } from './types';

interface MemberToRemove {
  assignmentId: Id<'client_assignments'>;
  name: string;
  role: ClientRole;
}

export function ClientTeamSection() {
  const { client, teamMembers } = useClientDetails();
  const { toast } = useToast();
  
  const [optimisticMembers, setOptimisticMembers] = useState<ClientTeamMember[]>([]);
  const [memberToRemove, setMemberToRemove] = useState<MemberToRemove | null>(null);

  useEffect(() => {
    setOptimisticMembers(teamMembers);
  }, [teamMembers]);

  const assignRoleMutation = useMutation(api.clients.clientMutations.assignClientRole);
  const removeAssignmentMutation = useMutation(api.clients.clientMutations.removeClientAssignment);

  const onDragEnd = (result: DropResult) => {
    const { source, destination, draggableId } = result;

    if (!destination || (source.droppableId === destination.droppableId && source.index === destination.index)) {
      return;
    }

    const isPrimary = destination.droppableId === 'droppable-primary';
    const draggedMember = optimisticMembers.find(m => m.userId === draggableId);

    if (!draggedMember || !client) return;

    // --- Optimistic Update ---
    const previousMembers = [...optimisticMembers];
    const updatedMembers = previousMembers.map(member => {
      if (member.userId === draggableId) {
        return { ...member, is_primary_assignment: isPrimary };
      }
      // If we are setting a new primary, demote the old one
      if (isPrimary && member.is_primary_assignment) {
        return { ...member, is_primary_assignment: false };
      }
      return member;
    });
    setOptimisticMembers(updatedMembers);
    // --- End Optimistic Update ---

    assignRoleMutation({
      clientId: client._id,
      userId: draggedMember.userId,
      is_primary: isPrimary,
    }).catch(error => {
      console.error("Failed to reassign role:", error);
      toast({ title: "Reassignment Failed", description: "Could not update role. Reverting change.", variant: "destructive" });
      setOptimisticMembers(previousMembers); // Revert UI
    });
  };

  const handleConfirmRemove = async () => {
    if (!memberToRemove) return;
    try {
      await removeAssignmentMutation({
        assignmentId: memberToRemove.assignmentId,
      });
      toast({
        title: "Success",
        description: `${memberToRemove.name} removed from ${memberToRemove.role} role.`,
      });
    } catch (error) {
      console.error("Failed to remove assignment:", error);
      toast({
        title: "Removal Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setMemberToRemove(null);
    }
  };

  if (!client) {
    return <Card><CardHeader><CardTitle>Loading Team...</CardTitle></CardHeader></Card>;
  }

  const primaryMember = optimisticMembers.find(m => m.is_primary_assignment);
  const teamMembersList = optimisticMembers.filter(m => !m.is_primary_assignment);

  const existingMemberIds = new Set(optimisticMembers.map(m => m.userId));

  const renderMember = (member: ClientTeamMember, index: number) => (
    <Draggable key={member.userId} draggableId={member.userId} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`flex items-center justify-between rounded-lg border p-1.5 mb-1 bg-card group relative ${snapshot.isDragging ? 'bg-muted shadow-md' : ''} cursor-grab active:cursor-grabbing select-none`}
        >
          <div className="flex items-center gap-1.5 overflow-hidden pl-1.5">
            <Avatar className="h-5 w-5">
              <AvatarImage src={member.image ?? undefined} alt={member.name ?? 'U'} />
              <AvatarFallback className="text-xs">{member.name?.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-grow overflow-hidden">
              <div className="text-sm truncate">{member.name}</div>
            </div>
          </div>
          <div className="flex items-center pr-0.5 flex-shrink-0">
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
              onClick={() => setMemberToRemove({
                assignmentId: member.assignmentId,
                name: member.name ?? 'User',
                role: member.is_primary_assignment ? 'Primary' : 'Team',
              })}
              aria-label={`Remove ${member.name}`}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
    </Draggable>
  );

  const renderAddButton = (role: ClientRole, isEmpty: boolean) => (
    <ClientUserAssignmentPopover
      clientId={client._id}
      targetRole={role}
      existingMemberIds={existingMemberIds}
      triggerButton={
        isEmpty ? (
          <Button
            variant="ghost"
            className="w-full h-7 justify-center items-center border-2 border-dashed border-muted-foreground/30 text-muted-foreground hover:bg-muted/50 hover:text-foreground text-xs"
          >
            <Plus className="h-3.5 w-3.5 mr-1" /> Assign {role}
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-xs h-5 mt-1 text-muted-foreground hover:text-foreground"
          >
            <Plus className="h-3 w-3 mr-1" /> Add
          </Button>
        )
      }
    />
  );

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between px-3 py-1.5">
          <div className="flex items-center gap-1.5">
            <Users className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">Client Team</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="pt-1 pb-1">
          <div className="space-y-2">
            <div>
              <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-0.5 px-0.5 flex items-center gap-1">
                <UserCheck className="h-3 w-3 text-blue-500" />
                Primary
                <span className="text-xs font-normal ml-0.5">
                  {primaryMember ? '(1)' : '(0)'}
                </span>
              </h4>
              <Droppable droppableId="droppable-primary">
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="p-1 rounded-md border border-transparent transition-colors"
                    style={{ minHeight: '40px' }}
                  >
                    {primaryMember && renderMember(primaryMember, 0)}
                    {provided.placeholder}
                    {!primaryMember && !snapshot.isDraggingOver && renderAddButton('Primary', true)}
                  </div>
                )}
              </Droppable>
            </div>
            <div>
              <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-0.5 px-0.5 flex items-center gap-1">
                <Users className="h-3 w-3 text-green-500" />
                Team
                <span className="text-xs font-normal ml-0.5">
                  ({teamMembersList.length})
                </span>
              </h4>
              <Droppable droppableId="droppable-team">
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="p-1 rounded-md border border-transparent transition-colors"
                    style={{ minHeight: '40px' }}
                  >
                    {teamMembersList.map((member, index) => renderMember(member, index))}
                    {provided.placeholder}
                    {!snapshot.isDraggingOver && renderAddButton('Team', teamMembersList.length === 0)}
                  </div>
                )}
              </Droppable>
            </div>
          </div>
        </CardContent>
      </Card>
      {memberToRemove && (
        <CollectionCardModal
          heading="Confirm Removal"
          subheading={`Are you sure you want to remove ${memberToRemove.name} as ${memberToRemove.role}?`}
          onClose={() => setMemberToRemove(null)}
          className="max-w-sm"
          primaryCTA={{
            text: "Remove",
            onClick: handleConfirmRemove,
          }}
          secondaryCTA={{
            text: "Cancel",
            onClick: () => setMemberToRemove(null),
          }}
        >
          <p className="text-sm text-muted-foreground">This will remove the user from the client's team.</p>
        </CollectionCardModal>
      )}
    </DragDropContext>
  );
}
