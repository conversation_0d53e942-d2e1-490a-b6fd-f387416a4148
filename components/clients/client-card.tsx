import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Building2, User, Calendar, Network } from "lucide-react"
import type { ClientData } from "./types"
import Link from "next/link"

interface ClientCardProps {
  client: ClientData
}

export default function ClientCard({ client }: ClientCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatClientAge = (clientSinceTimestamp: number) => {
    const now = new Date()
    const clientSince = new Date(clientSinceTimestamp)
    const diffMs = now.getTime() - clientSince.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) {
      return `${diffDays} days`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} mo`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingDays = diffDays % 365
      const remainingMonths = Math.floor(remainingDays / 30)
      
      if (remainingMonths === 0) {
        return `${years} Yr`
      } else {
        return `${years} Yr ${remainingMonths} Mo`
      }
    }
  }

  const getActivityLevel = () => {
    // Placeholder logic - could be based on recent interactions, projects, etc.
    const activities = ['high', 'medium', 'low'];
    return activities[Math.floor(Math.random() * activities.length)];
  }

  const getActivityColor = (activity: string) => {
    switch (activity) {
      case "high":
        return "bg-green-100 text-green-800 border-green-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getActivityDisplayName = (activity: string) => {
    const displayNames: Record<string, string> = {
      high: 'High',
      medium: 'Medium',
      low: 'Low'
    };
    return displayNames[activity] || 'Unknown';
  };

  const getClientTier = () => {
    // Use the client_tier field directly from the database
    return client.client_tier || 'none';
  };

  const getTierBackground = (tier: string) => {
    switch (tier) {
      case "platinum":
        return "bg-slate-300/30"
      case "gold":
        return "bg-yellow-300/40"
      case "silver":
        return "bg-gray-300/30"
      case "bronze":
        return "bg-orange-300/40"
      case "none":
      default:
        return "bg-gray-50/20"
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "prospective":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "former":
        return "bg-gray-100 text-gray-800 border-gray-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }
  
  const getStatusDisplayName = (status: string) => {
    const displayNames: Record<string, string> = {
      active: 'Active',
      on_hold: 'On Hold',
      prospective: 'Prospective',
      former: 'Former'
    };
    return displayNames[status] || 'Unknown';
  };


  const getBadgeColor = (badgeName: string) => {
    const colors = [
      "bg-blue-100 text-blue-800 border-blue-200",
      "bg-purple-100 text-purple-800 border-purple-200",
      "bg-orange-100 text-orange-800 border-orange-200",
      "bg-pink-100 text-pink-800 border-pink-200",
      "bg-indigo-100 text-indigo-800 border-indigo-200",
    ]
    // Simple hash function to get a consistent color for a badge name
    let hash = 0;
    for (let i = 0; i < badgeName.length; i++) {
      hash = badgeName.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash % colors.length);
    return colors[index];
  }

  const connectionsPlaceholder = 12;

  return (
    <Link href={`/clients/${client._id}/overview`} className="block">
      <Card className="w-full hover:shadow-md transition-shadow duration-200">
        <CardContent className="p-4">
          {/* Header Row */}
          <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {client.client_type === "person" ? (
              <User className="h-4 w-4 text-blue-600 flex-shrink-0" />
            ) : (
              <Building2 className="h-4 w-4 text-purple-600 flex-shrink-0" />
            )}
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm leading-tight truncate">{client.client_name}</h3>
              <p className="text-xs text-muted-foreground truncate">{client.client_short_description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={`text-xs ${getStatusColor(client.client_status)} flex-shrink-0`}>
              {getStatusDisplayName(client.client_status)}
            </Badge>
          </div>
        </div>

        {/* Badges Row */}
        {client.badges && client.badges.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {client.badges.slice(0, 3).map((badge: any, index: number) => (
              badge && <Badge key={index} variant="outline" className={`text-xs px-1.5 py-0.5 ${getBadgeColor(badge.name)}`}>
                {badge.name}
              </Badge>
            ))}
            {client.badges.length > 3 && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600">
                +{client.badges.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Main Info Row */}
        <div className="flex items-center justify-between mb-3 text-xs">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-xs">Net Worth</span>
            {client.client_net_worth && (
              <div className="flex items-center gap-1">
                <span className="font-semibold text-green-600">{formatCurrency(client.client_net_worth)}</span>
              </div>
            )}
          </div>
          <div className="flex flex-col items-end">
            <span className="text-muted-foreground text-xs">Activity</span>
            <Badge variant="outline" className={`text-xs ${getActivityColor(getActivityLevel())}`}>
              {getActivityDisplayName(getActivityLevel())}
            </Badge>
          </div>
        </div>
      </CardContent>

      {/* Bottom Row with Tier Background */}
      <div className={`px-4 pb-4 pt-2 -mt-2 ${getTierBackground(getClientTier())}`}>
        <div className="flex items-center justify-between">
          {/* Team Avatars */}
          <div className="flex items-center gap-1">
            {client.assignments.slice(0, 3).map((assignment: any) => (
              assignment.user && <Avatar key={assignment.user._id} className="h-10 w-10">
                <AvatarImage src={assignment.user.avatar || "/placeholder.svg"} alt={assignment.user.name} />
                <AvatarFallback className="text-xs">
                  {assignment.user.name
                    ?.split(" ")
                    .map((n: string) => n[0])
                    .join("")
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ))}
            {client.assignments.length > 3 && (
              <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center text-xs font-medium text-muted-foreground">
                +{client.assignments.length - 3}
              </div>
            )}
          </div>

          {/* Connections and Client Age */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {/* Connections */}
            <div className="flex items-center gap-1">
              <Network className="h-3 w-3 text-muted-foreground" />
              <span className="font-medium">{connectionsPlaceholder.toLocaleString()}</span>
            </div>

            {/* Client Age */}
            {client.client_since && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatClientAge(client.client_since)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
    </Link>
  )
}
