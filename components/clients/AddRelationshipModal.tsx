'use client';

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogTrigger, DialogTitle } from '@/components/ui/dialog';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Check, User, Users } from 'lucide-react'; 
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AddRelationshipModalProps {
  clientId: Id<'clients'>;
  existingPrincipals?: Array<{
    _id: Id<'people'>;
    name: string;
    imageUrl?: string | null;
  }>;
  onSuccess?: () => void;
  children: React.ReactNode;
}

export function AddRelationshipModal({ clientId, existingPrincipals = [], onSuccess, children }: AddRelationshipModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'principal' | 'family'>(existingPrincipals.length > 0 ? 'family' : 'principal');
  
  // Principal tab state
  const [principalSearchText, setPrincipalSearchText] = useState('');
  const [selectedPrincipalPersonId, setSelectedPrincipalPersonId] = useState<Id<'people'> | null>(null);
  const [selectedPrincipalRelationshipTypeId, setSelectedPrincipalRelationshipTypeId] = useState<Id<'relationship_types'> | null>(null);
  
  // Family tab state
  const [selectedSourcePrincipalId, setSelectedSourcePrincipalId] = useState<Id<'people'> | null>(null);
  const [familySearchText, setFamilySearchText] = useState('');
  const [selectedFamilyPersonId, setSelectedFamilyPersonId] = useState<Id<'people'> | null>(null);
  const [selectedFamilyRelationshipTypeId, setSelectedFamilyRelationshipTypeId] = useState<Id<'relationship_types'> | null>(null);
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Data fetching
  const principalRelationshipTypes = useQuery(api.clients.relationshipQueries.getPrincipalRelationshipTypes);
  const familyRelationshipTypes = useQuery(api.clients.relationshipQueries.getFamilyRelationshipTypes);
  
  // Auto-select "Principal" relationship type when principal types are loaded
  React.useEffect(() => {
    if (principalRelationshipTypes && !selectedPrincipalRelationshipTypeId) {
      const principalType = principalRelationshipTypes.find(type => 
        type.relationship_name.toLowerCase() === 'principal'
      );
      if (principalType) {
        setSelectedPrincipalRelationshipTypeId(principalType._id);
      }
    }
  }, [principalRelationshipTypes, selectedPrincipalRelationshipTypeId]);
  
  const principalSearchResults = useQuery(api.clients.relationshipQueries.searchPeopleForPrincipal, {
    searchText: principalSearchText,
    limit: 10,
  });
  
  const familySearchResults = useQuery(api.clients.relationshipQueries.searchPeopleForPrincipal, {
    searchText: familySearchText,
    limit: 10,
  });
  
  // Mutations
  const createRelationship = useMutation(api.relationships.relationshipMutations.createRelationship);

  const selectedPrincipalPerson = principalSearchResults?.find(person => person._id === selectedPrincipalPersonId);
  const selectedPrincipalRelationshipType = principalRelationshipTypes?.find(type => type._id === selectedPrincipalRelationshipTypeId);
  
  const selectedFamilyPerson = familySearchResults?.find(person => person._id === selectedFamilyPersonId);
  const selectedFamilyRelationshipType = familyRelationshipTypes?.find(type => type._id === selectedFamilyRelationshipTypeId);
  const selectedSourcePrincipal = existingPrincipals.find(p => p._id === selectedSourcePrincipalId);

  const handleClose = () => {
    setIsOpen(false);
    // Reset all state
    setPrincipalSearchText('');
    setSelectedPrincipalPersonId(null);
    setSelectedPrincipalRelationshipTypeId(null);
    setSelectedSourcePrincipalId(null);
    setFamilySearchText('');
    setSelectedFamilyPersonId(null);
    setSelectedFamilyRelationshipTypeId(null);
    setIsSubmitting(false);
  };

  const handleSubmitPrincipal = async () => {
    if (!selectedPrincipalPersonId || !selectedPrincipalRelationshipTypeId) {
      toast.error('Please select both a person and relationship type');
      return;
    }

    setIsSubmitting(true);
    try {
      await createRelationship({
        input: {
          source_type: 'client',
          source_id: clientId,
          target_type: 'person',
          target_id: selectedPrincipalPersonId,
          relationship_type_id: selectedPrincipalRelationshipTypeId,
          is_active: true,
        }
      });

      toast.success(`${selectedPrincipalPerson?.name} added as ${selectedPrincipalRelationshipType?.relationship_name}`);
      handleClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating principal relationship:', error);
      toast.error('Failed to add principal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitFamily = async () => {
    if (!selectedSourcePrincipalId || !selectedFamilyPersonId || !selectedFamilyRelationshipTypeId) {
      toast.error('Please select a principal, person, and relationship type');
      return;
    }

    setIsSubmitting(true);
    try {
      await createRelationship({
        input: {
          source_type: 'person',
          source_id: selectedSourcePrincipalId,
          target_type: 'person',
          target_id: selectedFamilyPersonId,
          relationship_type_id: selectedFamilyRelationshipTypeId,
          is_active: true,
        }
      });

      toast.success(`${selectedFamilyPerson?.name} added as ${selectedSourcePrincipal?.name}'s ${selectedFamilyRelationshipType?.relationship_name}`);
      handleClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating family relationship:', error);
      toast.error('Failed to add family relationship');
    } finally {
      setIsSubmitting(false);
    }
  };

  const canSubmitPrincipal = selectedPrincipalPersonId && selectedPrincipalRelationshipTypeId && !isSubmitting;
  const canSubmitFamily = selectedSourcePrincipalId && selectedFamilyPersonId && selectedFamilyRelationshipTypeId && !isSubmitting;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogTitle>Add Relationship</DialogTitle>
        <div className="space-y-6">
          <div>
            <p className="text-sm text-muted-foreground">
              Add a principal to the client or create relationships between existing family members.
            </p>
          </div>

          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'principal' | 'family')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="principal" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Add Principal
              </TabsTrigger>
              <TabsTrigger value="family" className="flex items-center gap-2" disabled={existingPrincipals.length === 0}>
                <Users className="h-4 w-4" />
                Add Family Relationship
              </TabsTrigger>
            </TabsList>

            <TabsContent value="principal" className="space-y-4 mt-6">
              {/* Relationship Type Selection */}
              <div className="space-y-2">
                <Label>Relationship Type</Label>
                <Select 
                  value={selectedPrincipalRelationshipTypeId || ''} 
                  onValueChange={(value) => setSelectedPrincipalRelationshipTypeId(value as Id<'relationship_types'>)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select relationship type..." />
                  </SelectTrigger>
                  <SelectContent>
                    {principalRelationshipTypes?.map(type => (
                      <SelectItem key={type._id} value={type._id}>
                        <div className="flex flex-col">
                          <span>{type.relationship_name}</span>
                          {type.relationship_description && (
                            <span className="text-xs text-muted-foreground">
                              {type.relationship_description}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Person Selection */}
              <div className="space-y-2">
                <Label>Person</Label>
                <Command className="border rounded-md">
                  <CommandInput
                    placeholder="Search for a person..."
                    value={principalSearchText}
                    onValueChange={setPrincipalSearchText}
                  />
                  <CommandEmpty>
                    {principalSearchText ? 'No people found.' : 'Start typing to search...'}
                  </CommandEmpty>
                  <CommandGroup className="max-h-40 overflow-y-auto">
                    {principalSearchResults?.map(person => (
                      <CommandItem
                        key={person._id}
                        value={person._id}
                        onSelect={() => setSelectedPrincipalPersonId(person._id)}
                        className={cn(
                          "flex items-center gap-3 cursor-pointer",
                          selectedPrincipalPersonId === person._id && "bg-accent"
                        )}
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={person.image || undefined} />
                          <AvatarFallback>
                            {person.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{person.name}</div>
                          {person.title && (
                            <div className="text-sm text-muted-foreground truncate">
                              {person.title}
                            </div>
                          )}
                          {person.email && (
                            <div className="text-xs text-muted-foreground truncate">
                              {person.email}
                            </div>
                          )}
                        </div>
                        {selectedPrincipalPersonId === person._id && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </div>

              {/* Selected Summary */}
              {selectedPrincipalPerson && selectedPrincipalRelationshipType && (
                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-sm font-medium">Ready to add:</div>
                  <div className="text-sm text-muted-foreground">
                    <span className="font-medium">{selectedPrincipalPerson.name}</span>
                    {' '}as{' '}
                    <Badge variant="secondary">{selectedPrincipalRelationshipType.relationship_name}</Badge>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  className="flex-1"
                  onClick={handleSubmitPrincipal}
                  disabled={!canSubmitPrincipal}
                >
                  {isSubmitting ? 'Adding...' : 'Add Principal'}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="family" className="space-y-4 mt-6">
              {existingPrincipals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="mb-2">No principals found</div>
                  <div className="text-sm">Add a principal first before creating family relationships</div>
                </div>
              ) : (
                <>
                  {/* Source Principal Selection */}
                  <div className="space-y-2">
                    <Label>From Principal</Label>
                    <Select 
                      value={selectedSourcePrincipalId || ''} 
                      onValueChange={(value) => setSelectedSourcePrincipalId(value as Id<'people'>)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a principal..." />
                      </SelectTrigger>
                      <SelectContent>
                        {existingPrincipals.map(principal => (
                          <SelectItem key={principal._id} value={principal._id}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={principal.imageUrl || undefined} />
                                <AvatarFallback>
                                  {principal.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              {principal.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Family Relationship Type Selection */}
                  <div className="space-y-2">
                    <Label>Relationship Type</Label>
                    <Select 
                      value={selectedFamilyRelationshipTypeId || ''} 
                      onValueChange={(value) => setSelectedFamilyRelationshipTypeId(value as Id<'relationship_types'>)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select relationship type..." />
                      </SelectTrigger>
                      <SelectContent>
                        {familyRelationshipTypes?.map(type => (
                          <SelectItem key={type._id} value={type._id}>
                            <div className="flex flex-col">
                              <span>{type.relationship_name}</span>
                              {type.relationship_description && (
                                <span className="text-xs text-muted-foreground">
                                  {type.relationship_description}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Target Person Selection */}
                  <div className="space-y-2">
                    <Label>To Person</Label>
                    <Command className="border rounded-md">
                      <CommandInput
                        placeholder="Search for a person..."
                        value={familySearchText}
                        onValueChange={setFamilySearchText}
                      />
                      <CommandEmpty>
                        {familySearchText ? 'No people found.' : 'Start typing to search...'}
                      </CommandEmpty>
                      <CommandGroup className="max-h-40 overflow-y-auto">
                        {familySearchResults?.map(person => (
                          <CommandItem
                            key={person._id}
                            value={person._id}
                            onSelect={() => setSelectedFamilyPersonId(person._id)}
                            className={cn(
                              "flex items-center gap-3 cursor-pointer",
                              selectedFamilyPersonId === person._id && "bg-accent"
                            )}
                          >
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={person.image || undefined} />
                              <AvatarFallback>
                                {person.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">{person.name}</div>
                              {person.title && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {person.title}
                                </div>
                              )}
                              {person.email && (
                                <div className="text-xs text-muted-foreground truncate">
                                  {person.email}
                                </div>
                              )}
                            </div>
                            {selectedFamilyPersonId === person._id && (
                              <Check className="h-4 w-4 text-primary" />
                            )}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </div>

                  {/* Selected Summary */}
                  {selectedSourcePrincipal && selectedFamilyPerson && selectedFamilyRelationshipType && (
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm font-medium">Ready to add:</div>
                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium">{selectedSourcePrincipal.name}</span>
                        {' → '}
                        <span className="font-medium">{selectedFamilyPerson.name}</span>
                        {' '}
                        <Badge variant="secondary">{selectedFamilyRelationshipType.relationship_name}</Badge>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={handleSubmitFamily}
                      disabled={!canSubmitFamily}
                    >
                      {isSubmitting ? 'Adding...' : 'Add Family Relationship'}
                    </Button>
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
