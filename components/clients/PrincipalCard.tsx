"use client";

import { Card, CardContent } from '@/components/ui/card';
import { User } from 'lucide-react';

export function PrincipalCard() {
  return (
    <Card className="bg-primary text-primary-foreground flex-1">
      <CardContent className="flex flex-col items-center justify-center h-full text-center p-6">
        <div className="bg-white/10 rounded-full p-4 mb-4">
          <User className="h-10 w-10" />
        </div>
        <p className="text-lg font-bold tracking-wider uppercase">Principal</p>
        <p className="text-sm opacity-80">Primary Account Holder</p>
      </CardContent>
    </Card>
  );
}
