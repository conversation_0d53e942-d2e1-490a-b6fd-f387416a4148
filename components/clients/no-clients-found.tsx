import { Button } from '@/components/ui/button';
import { Users } from 'lucide-react';

interface NoClientsFoundProps {
  hasActiveFilters: boolean;
  clearAllFilters: () => void;
}

export function NoClientsFound({ hasActiveFilters, clearAllFilters }: NoClientsFoundProps) {
  return (
    <div className="text-center py-12">
      <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2">No clients found</h3>
      <p className="text-muted-foreground mb-4">
        {hasActiveFilters
          ? 'Try adjusting your filters to see more results.'
          : 'Get started by adding your first client.'}
      </p>
      {hasActiveFilters && (
        <Button onClick={clearAllFilters} variant="outline">
          Clear filters
        </Button>
      )}
    </div>
  );
}
