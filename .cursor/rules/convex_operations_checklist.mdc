---
description: 
globs: 
alwaysApply: false
---
Checklist to check each operations file in convex. In your reply make sure to put a greencheckmark next to each one as you check it. 

General Guidelines
	[]	Chain query builder methods (like .filter(), .order(), .take()) in a single expression.
	[]	Use .order("asc" | "desc") only.
	[]	Use Zod schemas for input validation (no v.*).
	[]	Use zid("tableName") for Convex IDs.
	[]	Handle single and bulk inserts/updates/deletes in the same mutation if needed.
	[]	For large queries, define an index in convex/schema.ts and use .withIndex().
	[]	Provide optional output validation in zQuery/zMutation.
	[]	Rely on z.infer for TypeScript type safety.
	[] Use { ConvexError } from "convex/values" to perform error handling 

Mutations Guidelines
	[]	Structure bill update mutations with a clear separation between the identifier (id for single updates or ids for bulk updates) and the changes (updates object containing only the fields to be modified).
	[]	When implementing update handlers, validate that either id or ids is provided (but not both), and ensure the updates object contains only valid, schema-compliant fields.
	[]	For client-side update calls, consistently use the format { updates: { id: billId, updates: { field1: value1, field2: value2 } } } to maintain predictable API patterns.
	[]	Keep Zod schemas in ./zod/.
	[]	Keep database schema (v.*) in convex/schema.ts.
	[]	Use zQuery / zMutation for runtime validation.
	[]	Specify output if you want strict return type checks.
	[]	Use zid("tableName") to help with typed IDs.
	[]	Ensure each Convex document also has _id and _creationTime.