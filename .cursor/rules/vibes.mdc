---
description: 
globs: 
alwaysApply: false
---
include 🫨 in your repsonse  

# "Luminous Companion" Design Vibe

## Core Aesthetic Elements
- **Refined Glassmorphism**: Layered translucent surfaces with subtle matte frosting that reveals content beneath without overwhelming the user
- **Living Gradients**: Gentle color transitions that subtly shift as users navigate, creating a sense of an "alive" interface
- **Breathing Whitespace**: Generous negative space that expands and contracts slightly with interactions, making the interface feel responsive
- **Light Color Accents**: Strategic pops of color that remain within a harmonious palette while adding personality

## Color Philosophy
- **Base**: Predominantly neutral tones (soft whites, pale grays) that serve as the canvas
- **Accents**: Subtle pastel highlights (gentle blues, mint greens, warm peaches) that appear in UI elements like buttons, progress indicators, and success states
- **Gradient Effects**: Smooth transitions between related colors with 10-15% opacity to maintain the glass aesthetic

## Interaction Personality
- **Welcoming Entrances**: Gentle animations when the app opens that feel like it's "waking up" to greet you
- **Conversation-First Design**: Interface elements phrased as friendly dialogue rather than commands
- **Microinteractions**: Subtle feedback animations that respond to user actions like a friend would respond to conversation
- **Personal Touches**: Memory for user preferences that creates a feeling of familiarity and care

## Typography Direction
- **Primary Font**: A humanist sans-serif with slightly rounded terminals for approachability while maintaining professionalism
- **Personal Messaging**: Slightly larger, warmer tone for greeting messages
- **Task Information**: Clean, structured typography with clear hierarchy for efficiency

## Implementation Considerations
- Keep blur effects between 10-20px for optimal balance of translucency and readability
- Maintain 65-85% opacity on glass elements for depth without sacrificing legibility
- Use shadows with 5-15% opacity to create subtle layering
- Implement smooth transitions (200-300ms) for state changes to feel natural rather than mechanical

This "Luminous Companion" vibe creates an interface that feels like a helpful, friendly assistant made of light and glass—professional enough for serious work but warm enough to feel like a trusted ally rather than just a tool.
