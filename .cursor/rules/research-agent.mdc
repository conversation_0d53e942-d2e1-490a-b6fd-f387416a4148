---
description: investigate and document exactly how a specific piece of functionality works in our system.
globs: 
---
# Objective 
**You are an AI developer assistant** with access to a code base. Your task is to investigate and document exactly how a specific piece of functionality (“\[FUNCTIONALITY\]”) works in our system. This includes:

- Locating every relevant file, function, method, or component used.  
- Documenting exactly how data flows from start to finish.  
- Providing complete references (file paths, line numbers, function definitions, important variables, etc.).  
- Showing how each part of the system interacts.  
- Your documentation must allow another agent to replicate the feature exactly as it exists, without introducing new components.
- Do not use fictional or hypothetical files. only list actual files found.

## Instructions
### 0. Pull the file map of the ./SRC directory so you know all the files that exist. 
### 1. Identify Target Functionality
- Locate the specific feature (e.g., a share button) that requires analysis.

### 2. Examine Code Components
- Identify all related files, modules, and components. Do not use fictional or hypothetical files. only list actual files found.
- Record every function, file name, method, query, components, and mutation involved.
- List all proper nouns (e.g., component names, variable names) referenced.

### 3. Document the Flow
- Outline the step-by-step execution path of the feature.
- Describe how each function or method is called and interacts with other parts of the system.
- Detail the order of operations and the way state or data is passed between components.

### 4. Detail Specifics
- For every function or method, specify:
  - The exact file in which it is defined.
  - Its role in the overall functionality.
  - Any parameters, return values, or side effects (mutations) it triggers.
- For components, document:
  - The event handlers involved.
  - The specific functions invoked.
  - The chain of mutations or state updates that follow.

### 2. Requirements for the Response

**Your final output** must be a very long step-by-step guide that allows any developer (or AI) to replicate the exact functionality without creating new components. Be **extremely explicit**. For example:

- List all file paths involved (e.g., `frontend/src/components/ButtonShare.tsx`, `backend/controllers/shareController.js`).  
- List all function names, classes, and important variables (e.g., `function handleShareClick(event)`, `class ShareController`, `const shareMutation`).  
- Describe the flow or sequence of calls (e.g., *“When a user clicks the Share button in `ButtonShare.tsx`, it calls `handleShareClick()`, which then triggers `shareMutation()` from `useShareMutation.js`…”*).  
- Include relevant code snippets or pseudo-code.  
- For each snippet, explain what it does, why it’s needed, and **where** it fits in the flow.  
- If the functionality relies on external services, APIs, or libraries, include references to those and specify how they are configured.  
- Provide any supporting documentation or references (comments, docstrings, wiki links, etc.) if they exist in the code.

 
### 4. Constraints & Guidelines

1. **No High-Level Summaries Alone**: We want deeply technical details—any mention of a function/method should include its location, usage, and relationships to other parts of the code.  
2. **Accuracy is Critical**: If you are unsure or cannot find a detail, clearly state what is missing.  
3. **Consistency of Naming**: The output must preserve all existing function names, file names, class names, variable names, etc.  
4. **No New Components**: Show only how to reuse existing components. If something is missing, note it instead of creating a new component.  
5. Do not use fictional or hypothetical files. only list actual files found.


