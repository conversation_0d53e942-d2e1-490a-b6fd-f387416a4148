---
description: Creating shadcn charts with rechart
globs: *.*
alwaysApply: false
---
<convex_guidelines>
  <charts_guidelines>

    <introduction>
      - The Charts component provides beautiful, composable chart elements built on top of Recharts. 
      - You can copy and paste these charts directly into your applications.
      - Charts come with sensible defaults and can be fully customized.
      - When you upgrade Recharts, you can follow their official documentation without being locked into any extra abstraction.
    </introduction>

    <your_first_chart>
      <data_definition>
        - Define your data in any shape you prefer. The example below uses numeric fields for desktop and mobile visitors:
        ```ts
        const chartData = [
          { month: "January", desktop: 186, mobile: 80 },
          { month: "February", desktop: 305, mobile: 200 },
          { month: "March", desktop: 237, mobile: 120 },
          { month: "April", desktop: 73, mobile: 190 },
          { month: "May", desktop: 209, mobile: 130 },
          { month: "June", desktop: 214, mobile: 140 },
        ];
        ```
      </data_definition>

      <chart_config>
        - The chart config is separate from the data. It defines labels and color tokens for your chart series:
        ```ts
        import { type ChartConfig } from "@/components/ui/chart";

        const chartConfig = {
          desktop: {
            label: "Desktop",
            color: "#2563eb",
          },
          mobile: {
            label: "Mobile",
            color: "#60a5fa",
          },
        } satisfies ChartConfig;
        ```
      </chart_config>

      <build_bar_chart>
        - Create a bar chart with Recharts and wrap it in a responsive <ChartContainer>.
        - Remember to set a minimum height on the container for responsive sizing.
        ```tsx
        "use client";

        import { Bar, BarChart } from "recharts";
        import { ChartContainer, ChartConfig } from "@/components/ui/chart";

        const chartData = [
          { month: "January", desktop: 186, mobile: 80 },
          { month: "February", desktop: 305, mobile: 200 },
          { month: "March", desktop: 237, mobile: 120 },
          { month: "April", desktop: 73, mobile: 190 },
          { month: "May", desktop: 209, mobile: 130 },
          { month: "June", desktop: 214, mobile: 140 },
        ];

        const chartConfig = {
          desktop: {
            label: "Desktop",
            color: "#2563eb",
          },
          mobile: {
            label: "Mobile",
            color: "#60a5fa",
          },
        } satisfies ChartConfig;

        export function Component() {
          return (
            <ChartContainer config={chartConfig} className="min-h-[200px] w-full">
              <BarChart accessibilityLayer data={chartData}>
                <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
                <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4} />
              </BarChart>
            </ChartContainer>
          );
        }
        ```
      </build_bar_chart>
    </your_first_chart>

    <add_grid>
      - To add a grid, import and use the Recharts CartesianGrid component:
      ```tsx
      import { Bar, BarChart, CartesianGrid } from "recharts";

      <ChartContainer config={chartConfig} className="min-h-[200px] w-full">
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
          <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4} />
        </BarChart>
      </ChartContainer>
      ```
    </add_grid>

    <add_axis>
      - Add an X-axis with labels by importing and placing an XAxis component:
      ```tsx
      import { Bar, BarChart, CartesianGrid, XAxis } from "recharts";

      <ChartContainer config={chartConfig} className="h-[200px] w-full">
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="month"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
          <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4} />
        </BarChart>
      </ChartContainer>
      ```
    </add_axis>

    <add_tooltip>
      - Use the custom <ChartTooltip> and <ChartTooltipContent> for a styled tooltip:
      ```tsx
      import {
        ChartTooltip,
        ChartTooltipContent
      } from "@/components/ui/chart";

      <ChartContainer config={chartConfig} className="h-[200px] w-full">
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="month"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
          <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4} />
        </BarChart>
      </ChartContainer>
      ```
    </add_tooltip>

    <add_legend>
      - Use the custom <ChartLegend> and <ChartLegendContent> to add a legend:
      ```tsx
      import {
        ChartLegend,
        ChartLegendContent
      } from "@/components/ui/chart";

      <ChartContainer config={chartConfig} className="h-[200px] w-full">
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="month"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <ChartTooltip content={<ChartTooltipContent />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
          <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4} />
        </BarChart>
      </ChartContainer>
      ```
    </add_legend>

    <theming>
      - Charts support theming via CSS variables or direct color values:
      - Example with CSS variables:
      ```ts
      const chartConfig = {
        desktop: {
          label: "Desktop",
          color: "hsl(var(--chart-1))",
        },
        mobile: {
          label: "Mobile",
          color: "hsl(var(--chart-2))",
        },
      } satisfies ChartConfig;
      ```
      - Then in the chart:
      ```tsx
      <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
      ```
    </theming>

    <tooltip_configuration>
      - Tooltip props:
        - \`labelKey\` and \`nameKey\`: specify which keys from \`chartConfig\` or your data to display.
        - \`indicator\`: controls the indicator style (dot, line, or dashed).
        - \`hideLabel\` or \`hideIndicator\`: hide certain tooltip elements.
      - Colors automatically come from the \`chartConfig\`.
      ```tsx
      <ChartTooltip
        content={<ChartTooltipContent labelKey="visitors" nameKey="browser" />}
      />
      ```
    </tooltip_configuration>

    <legend_configuration>
      - Similar to tooltip, the custom legend uses your chart config for labels and colors.
      - Use \`<ChartLegend content={<ChartLegendContent nameKey="browser" />} />\` for custom legend labels.
    </legend_configuration>

    <accessibility>
      - Enable \`accessibilityLayer\` on any Recharts chart to include keyboard support and better screen reader accessibility:
      ```tsx
      <LineChart accessibilityLayer />
      ```
    </accessibility>

  </charts_guidelines>
</convex_guidelines>
