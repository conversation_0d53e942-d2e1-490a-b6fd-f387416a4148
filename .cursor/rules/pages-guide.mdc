---
description: a guide to each user facing page
globs: 
---
1. Main Navigation

1. Home
	•	Purpose: Provide a quick overview or “dashboard” that gives the user a sense of what’s happening at a glance.
	•	Data/Features:
	•	Recent Bills: Show the most recent invoices or credit card statements, perhaps with a link to view their details.
	•	High-Level Stats: Total spending for the current month, top categories, or an alert if something needs review.
    •	Quick Actions: A button to upload a new bill/statement.
2. Bills Overview Page (/bills)
This page should be all about getting work done with bills:
Quick Actions Section
Prominent "Add New Bill" button
Batch actions for efficient processing
Status-based quick filters
Priority Bills Section
Clear visual hierarchy for bills:
Overdue (Red)
Due This Week (Orange)
Due Next Week (Yellow)
Each bill shows critical info: amount, due date, vendor, status
One-click actions for quick processing
Bill Management Table
Efficient list view of all bills
Essential actions: pay, mark as paid, edit, delete
Basic categorization (just enough for organization)
Key fields: amount, due date, status
Smart sorting/filtering
 

3. Manage Vendors
	•	Purpose: Display and maintain the vendors table.
	•	Data/Features:
	•	List of Vendors: Each vendor’s name and its default spending category.
	•	Create or Edit Vendor: Form to add a new vendor or edit an existing one.
	•	Default Spending Category: The user picks which category new line items should inherit if not overridden.
	•	Search/Filter: Find vendors by name or default category.
	•	Behavior:
	•	Changing the default category here only affects future line items, aligning with your data rules. Past line items remain unchanged.

4. Spending Reports Page (/reports)
This page should be all about understanding spending:
Overview Dashboard
High-level spending metrics
Trend visualizations
Category breakdown
Period-over-period comparisons
Category Analysis
Detailed category breakdowns
Trend analysis by category
Anomaly detection
Category optimization suggestions
Vendor Intelligence
Vendor spending patterns
Top vendor analysis
New/unusual vendor activity
Concentration risk analysis
Smart Insights
AI-generated insights about spending patterns
Predictive analytics
Cost-saving opportunities
Pattern recognition and anomaly detection

5.	Category Management
	•	Purpose: Manage your spending_categories (the hierarchy of categories).
	•	Data/Features:
	•	A list of categories with possible nesting (parent-child relationships).
	•	Ability to add, rename, or remove categories.
	•	If you want to visually represent the hierarchy, you might use a tree view or indentation.



	removed items
	BillsHeader - This component displayed:
Total amount
Previous month amount
Unusual patterns/alerts
PatternRecognition - This component showed:
Insights about spending trends
Vendor spending trends
Trend analysis and anomaly detection
BillsGrouping - This component handled:
Grouped view of bills
Batch processing functionality
Category management/updates