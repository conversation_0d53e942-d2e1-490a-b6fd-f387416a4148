---
description: Fix Args Lint Errors
globs: *.*
alwaysApply: false
---
#### Properly Typing <PERSON>rgs in Handlers
Args is an object with named parameters
There are three approaches for handling types in your handler functions:

1. **Let type inference work automatically** (recommended):
```typescript
export const getDomain = zQuery({
  args: {
    domainId: zid("domains")
  },
  handler: async (ctx, args) => {
    // args.domainId is already correctly typed as Id<"domains">
    const domain = await ctx.db.get(args.domainId);
    return domain;
  }
});
```

2. **Use explicit type annotation** (when needed for clarity):
```typescript
import { QueryCtx } from "./_generated/server";
import { Id } from "./_generated/dataModel";

export const getDomain = zQuery({
  args: {
    domainId: zid("domains")
  },
  handler: async (ctx: QueryCtx, args: { domainId: Id<"domains"> }) => {
    const domain = await ctx.db.get(args.domainId);
    return domain;
  }
});
```

3. **Use type assertion** (only when necessary):
```typescript
export const complexQuery = zQuery({
  args: {
    complexFilter: z.object({/* ... */})
  },
  handler: async (ctx, args) => {
    // If TypeScript has trouble with complex nested types
    const typedFilter = (args.complexFilter as ComplexFilterType);
    // ... use typedFilter
  }
});
```

For most cases, option 1 is sufficient as the zQuery/zMutation helpers handle type inference. Options 2 and 3 are for special cases where you need more explicit typing.
Additional Info:
  - zMutation and zQuery functions from the convex-helpers library expect the args parameter to be a direct object containing Zod validators, not a Zod object itself. When you wrap it in z.object({...}), it creates a Zod validator object instead of a plain object with Zod validators as properties.