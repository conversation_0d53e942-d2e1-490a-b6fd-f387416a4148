---
description: handling errors in convex functions, including lint errors
globs: 
alwaysApply: false
---
In Convex, to handle and type errors effectively, you can use the ConvexError class, which allows you to throw errors with structured data that can be accessed on the client side. Here’s how you can implement this: ￼

Server-Side Implementation:

In your Convex mutation or query, import and throw a ConvexError when an error condition occurs:
```typescript
import { ConvexError } from "convex/values";
import { mutation } from "./_generated/server";

export const updateDescription = mutation({
  args: { id: v.id("items"), description: v.string() },
  handler: async (ctx, args) => {
    try {
      // Your update logic here
      await ctx.db.patch(args.id, { description: args.description });
    } catch (error) {
      // Throw a ConvexError with a structured message
      throw new ConvexError({ message: "Failed to update description", code: "UPDATE_FAILED" });
    }
  },
});
```
In this example, if the ctx.db.patch operation fails, a ConvexError is thrown with a structured message and a custom error code.  

Client-Side Handling:

On the client side, when invoking the mutation, you can catch the ConvexError and access its structured data: 
```typescript
import { ConvexError } from "convex/values";
import { useMutation } from "convex/react";
import { api } from "../convex/_generated/api";

export function UpdateDescriptionComponent() {
  const updateDescription = useMutation(api.updateDescription);

  const handleUpdate = async (id: string, description: string) => {
    try {
      await updateDescription({ id, description });
      // Handle successful update
    } catch (error) {
      if (error instanceof ConvexError) {
        const errorData = error.data as { message: string; code: string };
        // Display error message to the user
        toast({
          title: "Error",
          description: errorData.message,
          variant: "destructive",
        });
      } else {
        // Handle unexpected errors
        toast({
          title: "Error",
          description: "An unexpected error occurred.",
          variant: "destructive",
        });
      }
    }
  };

  // Component JSX here
}
```
In this client-side example, the ConvexError is caught, and its data property is accessed to retrieve the structured error message and code. This allows you to provide specific feedback to the user based on the error that occurred.