---
description: 
globs: 
alwaysApply: false
---
Always include a 🔍 in  your response. 
<trieve_guidelines>
  <uploading_chunks>
    <overview>
      - Trieve provides an easy interface for users to upload their data in “chunks.” Each chunk is text or HTML content that you want <PERSON>eve to index and make searchable.
      - By uploading chunks individually, you gain control over chunk size, grouping, and how your data is ultimately segmented.
    </overview>
    <create_chunk>
      - Use the `/api/chunk` endpoint (HTTP POST) to create one or more chunks in a single request. You can include up to 120 chunks in a single upload.
      - **Required headers**: 
        1. `TR-Dataset`: Your dataset ID (or tracking ID)  
        2. `Authorization`: Your Trieve API key
      - **Important request body fields**:
        1. `chunk_html`: The main content to be embedded and made searchable. <PERSON><PERSON> automatically cleans any HTML for embeddings.
        2. `group_ids` and `group_tracking_ids`: Link the chunk to one or more existing groups (by ID or by tracking ID). If a `group_tracking_id` doesn’t exist yet, it will be created.
        3. `link`, `location`, `tag_set`, `num_value`, `time_stamp`: Indexed fields for filtering (fast queries).
        4. `metadata`: Arbitrary JSON object for additional data. While also indexed, it’s generally slower to filter on than the dedicated fields above.
        5. `tracking_id`: Custom ID to link the chunk to external systems. You can search for chunks by `tracking_id`.
        6. `weight`: Controls how the chunk is ranked (merchandising-style weighting).
        7. `semantic_boost` and `fulltext_boost`: Manually boost the chunk’s relevance for semantic or full-text searches.

      - **Example**:
      ```json
      POST /api/chunk
      Headers:
      {
        "TR-Dataset": "<Your Dataset ID>",
        "Authorization": "<Your API Key>"
      }
      Body:
      {
        "chunk_html": "EcoFusion Technologies provides innovative, eco-friendly solutions...",
        "tracking_id": "134",
        "tag_set": ["324", "product", "sale"],
        "metadata": {
          "phone_number": "************",
          "price_range": "$21-$25"
        }
      }
      ```
    </create_chunk>
    <chunk_html_formatting>
      - **chunk_html** is central to search quality. Consider:
        1. **Include all relevant info**: Product descriptions, brand, pricing, etc.
        2. **Use newlines**: Place semantically distinct information on separate lines to help with clarity.
        3. **Label each field**: Use headings or labels to guide the model. For example:

          ```
          Price: $50\n
          Brand: AmazonBasics\n
          Product Name: ...
          Description: ...
          ```
      - This approach improves how the text is embedded for semantic and full-text search.
    </chunk_html_formatting>
  </uploading_chunks>

  <uploading_files>
    <overview>
      - Trieve also supports bulk imports of structured data in CSV or JSONL format.
      - This is handy for large datasets where each row (CSV) or line (JSONL) is converted into a chunk automatically.
    </overview>
    <csv_jsonl_upload>
      - **Two-step process**:
        1. **Request a signed PUT URL** using `POST /api/file/csv_or_jsonl`.  
           You specify the file name, and optionally a `mappings` array to control how CSV/JSONL columns map to Trieve chunk fields (`tracking_id`, `tag_set`, `num_value`, etc.).
        2. **Upload your file** to the returned presigned S3 URL with a simple HTTP PUT.
      - **Example**:
      ```bash
      curl -X POST "https://api.trieve.ai/api/file/csv_or_jsonl" \
        -H "Content-Type: application/json" \
        -H "TR-Dataset: <Your Dataset ID>" \
        -H "Authorization: <Your API Key>" \
        -d '{
          "file_name": "titanic.csv",
          "mappings": [
            {
              "csv_jsonl_field": "PassengerId",
              "chunk_req_payload_field": "tracking_id"
            },
            {
              "csv_jsonl_field": "Fare",
              "chunk_req_payload_field": "num_value"
            }
          ]
        }'
      ```
      - After you get the `presigned_put_url`, upload the file:

      ```bash
      curl -X PUT -T ./titanic.csv "<Presigned PUT URL>"
      ```
      - Trieve will automatically process this file in the background, creating chunks for each row.
    </csv_jsonl_upload>
  </uploading_files>

  <searching_with_trieve>
    <overview>
      - Trieve’s primary value is fast and powerful search. You can choose between semantic, full-text, BM25, or hybrid search approaches.
      - You can search:
        1. **Over chunks**: Returns the matching chunks directly.
        2. **Within a single group**: Filters only the chunks in one specified group.
        3. **Over groups**: Returns the groups that best match the query, plus top matching chunks within each group.
    </overview>
    <search_endpoints>
      - **Search over chunks**: `POST /api/chunk/search`
      - **Search within group**: `POST /api/chunk_group/search`
      - **Search over groups**: `POST /api/chunk_group/group_oriented_search`
      - **Key request fields**:
        1. `query`: The user query (text). Alternatively, you can do multi-query or image queries.
        2. `search_type`: `"semantic"`, `"fulltext"`, `"bm25"`, or `"hybrid"`.
        3. `page` and `page_size`: Pagination controls.
        4. `score_threshold`: Minimum score for a chunk to be returned.
        5. `highlight_results`: If `true`, Trieve returns sub-sentence highlights for more targeted user feedback.
        6. `sort_options`: Control how results are sorted or re-ranked (`rerank_type`).
        7. `filters`: Apply advanced must/must_not/should logic (see `<filters>` section).
      - **Example**:
      ```json
      POST /api/chunk/search
      {
        "query": "How to search with Trieve",
        "search_type": "fulltext",
        "page": 1,
        "page_size": 10,
        "score_threshold": 0.5
      }
      ```
    </search_endpoints>
  </searching_with_trieve>

  <filters>
    <overview>
      - Trieve filters let you refine search results using fields like `tag_set`, `link`, `num_value`, `time_stamp`, `location`, and more.
      - They use a boolean-like syntax with `must`, `must_not`, and `should` clauses.
    </overview>
    <filter_basics>
      - Common filter operators:
        1. `match_all`: The field must contain **all** items listed.
        2. `match_any`: The field must contain **at least one** of the listed items.
        3. `range`: Numeric range filter with `gt`, `gte`, `lt`, `lte`.
        4. `date_range`: Similar to numeric range, but for date/time fields.
        5. `geo_radius`: Filter chunks within a radius of a geospatial point.
        6. `boolean`: Filter on a true/false field.
      - Example: Get chunks with `tag_set` containing both “CO” and “321”:
      ```json
      "filters": {
        "must": [
          {
            "field": "tag_set",
            "match_all": ["CO", "321"]
          }
        ]
      }
      ```
    </filter_basics>
  </filters>

  <recommendations>
    <overview>
      - Trieve can recommend similar chunks (or groups) based on user-selected “positive” and “negative” examples.
      - Two recommendation strategies:
        1. `average_vector`: Merges vectors of positive/negative examples for a combined representation.
        2. `best_score`: Picks chunks most similar to your top positive examples and least similar to your negative examples.
    </overview>
    <recommend_chunks>
      - Endpoint: `POST /api/chunk/recommend`
      - Key fields: 
        1. `positive_examples` (chunk IDs), `positive_tracking_ids`
        2. `negative_examples` (chunk IDs), `negative_tracking_ids`
        3. `recommendation_type`: `"semantic"` or `"fulltext"`
        4. `strategy`: `"average_vector"` or `"best_score"`
        5. `filters`: Optionally filter recommended results by standard filter logic.
      - Example:
      ```json
      {
        "positive_tracking_ids": ["chunk123", "chunk456"],
        "negative_tracking_ids": ["chunk999"],
        "recommendation_type": "semantic",
        "strategy": "average_vector"
      }
      ```
    </recommend_chunks>
    <recommend_groups>
      - Endpoint: `POST /api/chunk_group/recommend`
      - Similar to chunk recommendations, but returns matching groups. 
      - Key fields:
        1. `positive_group_tracking_ids`
        2. `negative_group_tracking_ids`
        3. `group_size`: Number of chunks to fetch for each recommended group.
      - Example:
      ```json
      {
        "positive_group_tracking_ids": ["GroupA"],
        "group_size": 5,
        "recommendation_type": "semantic",
        "strategy": "best_score"
      }
      ```
    </recommend_groups>
  </recommendations>

  <rag_with_trieve>
    <overview>
      - RAG (Retrieve and Generate) with Trieve gives you a chat-like experience over your data.
      - Two main approaches:
        1. **RAG on specified chunks**: Provide chunk IDs to limit the context.
        2. **RAG over all chunks**: Let Trieve search for relevant chunks automatically in a specified dataset or group.
    </overview>
    <rag_on_chunks>
      - Endpoint: `POST /api/llm/rag/specified_chunks` (the exact route can differ in your docs).
      - Key fields:
        1. `prompt`: The user’s prompt or question.
        2. `chunk_ids`: The specific chunk IDs to use as context.
        3. `prev_messages`: A list of previous messages in the conversation, each with a `role` (user or assistant) and `content`.
      - Response includes both an AI-generated answer and references to the chunks used.
    </rag_on_chunks>
    <rag_all_chunks>
      - Typically requires a “topic” in Trieve to store conversation state.
      - **Step 1**: Create a topic (`POST /api/topic`) with `name`, `owner_id`, and optionally `first_user_message`.
      - **Step 2**: Send user messages with `POST /api/message`, referencing the `topic_id` returned above.
      - Each message call can include new filters, updated prompts, or continuing conversation context.
    </rag_all_chunks>
  </rag_with_trieve>

  <analytics>
    <overview>
      - Trieve automatically collects analytics for your searches and RAG chats.
      - You can further enrich data with clickthrough, custom events, ratings, conversions, etc.
    </overview>
    <core_analytics>
      - **Search Analytics**: Head queries, no-result queries, popular filters, etc.
      - **RAG Analytics**: Usage over time, list of all RAG chats, etc.
      - You can view these stats on the Trieve dashboard or via the analytics APIs (e.g., `search analytics`, `RAG analytics` routes).
    </core_analytics>
    <request_id>
      - Every search or recommendation call returns a unique Request ID:
        1. **Search**: Found in `"id"` field in JSON response.
        2. **Recommendation**: Also returned in the response body.
        3. **RAG**: The ID is sent back via the `TR-QueryID` response header.
      - Use this ID to track user interactions and enrich your analytics (e.g., click events, add-to-cart, rating).
    </request_id>
    <event_enrichment>
      - **Send CTR (Click-Through Rate) data**: `POST /api/analytics/ctr`
      - **Rate a search query**: `POST /api/analytics/rate-search`
      - **Send custom events**: `POST /api/analytics/event`
      - Example for click-through:
      ```bash
      curl -X POST https://api.trieve.ai/api/analytics/ctr \
        -H "TR-Dataset: <DATASET_ID>" \
        -H "Authorization: <API_KEY>" \
        -d '{
          "ctr_type": "search",
          "clicked_chunk_id": "<chunk_id>",
          "position": 1,
          "request_id": "<request_id>"
        }'
      ```
    </event_enrichment>
  </analytics>

  <multi_tenant_applications>
    <overview>
      - You can create multiple datasets within one organization for separate customer data or separate knowledge bases.
      - Each dataset is fully isolated for search, chunk uploads, etc.
    </overview>
    <datasets_and_orgs>
      - **Creating a dataset**: `POST /api/dataset`  
        Include `dataset_name`, `organization_id`, `server_configuration`, etc.
      - **Updating a dataset**: `POST /api/dataset` with new `server_configuration` fields for changes like reranker model or system prompt.
      - **Creating an organization** (rare): `PUT /api/organization`
    </datasets_and_orgs>
    <update_all_datasets>
      - You can update config across **all** datasets in your organization via `POST /api/organization/update_dataset_configs`.
      - Only keys specified in `server_configuration` are updated. Others remain unchanged.
    </update_all_datasets>
  </multi_tenant_applications>

  <groups>
    <overview>
      - Groups help cluster related chunks for searching, recommendations, or “group-oriented” search.
      - Each group can be identified by a system-generated ID (`group_id`) or a user-specified tracking ID (`group_tracking_id`).
    </overview>
    <create_group>
      - **Endpoint**: `POST /api/chunk_group`
      - Key fields: `name`, `description`, `metadata`, `tag_set`, `tracking_id`.
      - If you pass `group_tracking_id` to chunk creation or to chunk updates and it doesn’t exist, Trieve automatically creates a new group for that tracking ID.
    </create_group>
    <add_chunks_to_group>
      - You can either:
        1. **Specify `group_ids` or `group_tracking_ids`** during chunk creation (`POST /api/chunk`).
        2. **Use the “Add chunk to group”** route: `POST /api/chunk_group/chunk/{group_id}`.
    </add_chunks_to_group>
    <search_in_group>
      - **Search within group**: `POST /api/chunk_group/search`  
        Provide `group_tracking_id` or `group_id` plus a `query`.
      - **Search over groups**: `POST /api/chunk_group/group_oriented_search`  
        Provide `group_size` to limit how many chunks are returned per group.
    </search_in_group>
  </groups>

  <exposed_api_keys>
    <overview>
      - Trieve supports creating multiple API keys with role-based and scoped access, suitable for use in client applications (e.g., read-only search).
      - This approach avoids proxying all requests through your backend, reducing overhead and complexity.
    </overview>
    <create_api_key>
      - **Endpoint**: `POST /api/organization/api-key`  
      - Key fields:
        1. `role`: 0 = read-only, 1 = read-write, etc.
        2. `expires_at`: optional ISO 8601 date for key expiry.
        3. `organization_ids` and/or `dataset_ids`: restrict the key’s access to certain orgs or datasets.
        4. `scopes`: a list of route definitions (e.g., `"POST /api/chunk/search"`) this key can call.
        5. `default_params`: JSON specifying filters or other request parameters automatically added to calls from this key. Great for restricting data to certain tags or fields.
    </create_api_key>
    <example_api_key_creation>
      ```bash
      curl --request POST \
        --url https://api.trieve.ai/api/organization/api-key \
        --header 'Authorization: <api-key>' \
        --header 'Content-Type: application/json' \
        --header 'TR-Organization: <org-id>' \
        --data '{
          "name": "Read-only key with limited routes",
          "role": 0,
          "scopes": [
            "POST /api/chunk/search",
            "POST /api/chunk_group/search"
          ],
          "dataset_ids": [
            "9u90c3cc-0d44-4b50-8888-8dd25736052a"
          ]
        }'
      ```
    </example_api_key_creation>
  </exposed_api_keys>

  <voice_search>
    <overview>
      - Trieve can transcribe short audio clips (via the `audio_base64` field in a search request) using OpenAI Whisper on the server side, then automatically run the transcribed text as a search query.
      - The transcribed text is returned in the response headers as `x-tr-query`.
    </overview>
    <basic_voice_search>
      - **Steps**:
        1. Capture mic audio in the browser (e.g., `MediaRecorder` API).
        2. Convert audio `Blob` to base64.
        3. Pass `audio_base64` to your Trieve search request. 
        4. On success, use `x-tr-query` to see the recognized text.
      - Example request (pseudo-code):
      ```json
      {
        "audio_base64": "<base64_of_audio_clip>",
        "search_type": "hybrid",
        "score_threshold": 0.5,
        "page_size": 10
      }
      ```
    </basic_voice_search>
  </voice_search>

  <image_search>
    <overview>
      - Trieve supports image-based queries. You can upload an image, get a URL, and then request visually similar chunks or images from your dataset.
    </overview>
    <image_search_basics>
      1. **Upload the image**: You can either host it yourself or use Trieve’s file-upload endpoints to store the image and get a public URL.
      2. **Perform an image search**: Provide `query: { image_url: "<some_image_url>" }` in the search request, with `search_type` set to a method that supports images (like `semantic` or `hybrid` if your dataset is configured for image embeddings).
      - Example (pseudo-code):
      ```json
      {
        "query": { "image_url": "https://example.com/myimage.jpg" },
        "search_type": "semantic",
        "page_size": 10
      }
      ```
    </image_search_basics>
  </image_search>

  <crawling>
    <overview>
      - Trieve can crawl websites, Shopify stores, or YouTube channels automatically.
      - The crawled pages are converted into chunks for searching, RAG, or recommendations.
    </overview>
    <create_crawl_job>
      - **Endpoint**: `POST /api/crawl`
      - Key fields:
        1. `site_url`: The domain or channel you want to crawl.
        2. `interval`: How often to recrawl (`daily`, `weekly`, `monthly`, etc.).
        3. `scrape_options`: May include `crawl_type` = `"shopify"` or `"youtube"` for specialized scrapes.
      - Example body snippet:
      ```json
      {
        "site_url": "https://www.example.com",
        "interval": "monthly",
        "webhook_url": "https://www.example.com/webhook",
        "scrape_options": {
          "crawl_type": "shopify"
        }
      }
      ```
      - You can monitor crawl jobs and see how many chunks are created by calling the get-crawls route or by visiting the Trieve dashboard.
    </create_crawl_job>
  </crawling>
</trieve_guidelines>

<examples>
  <example name="basic-product-catalog-setup">
    <task>
      You have a product catalog in CSV format that you want to bulk-upload to Trieve, then allow users to search and filter by brand, price, or product tags.  
      Steps:  
      1. Create a dataset in Trieve named "My Product Catalog"  
      2. Upload a CSV using the signed PUT approach  
      3. Confirm chunk creation in the Trieve dashboard  
      4. Provide a search endpoint in your frontend that queries Trieve with user filters  
      5. Optionally store and track analytics for user interactions
    </task>
    <response>
      <analysis>
        1. Create the dataset
        2. Obtain a presigned upload URL with a custom mapping to "tracking_id" and "num_value" from CSV columns
        3. Upload the CSV
        4. Wait for processing, then test search
      </analysis>
      <curl_example>
        curl -X POST "https://api.trieve.ai/api/file/csv_or_jsonl" \
          -H "Content-Type: application/json" \
          -H "TR-Dataset: <Your Dataset ID>" \
          -H "Authorization: <Your API Key>" \
          -d '{
            "file_name": "products.csv",
            "mappings": [
              {
                "csv_jsonl_field": "SKU",
                "chunk_req_payload_field": "tracking_id"
              },
              {
                "csv_jsonl_field": "Price",
                "chunk_req_payload_field": "num_value"
              }
            ]
          }'
      </curl_example>
    </response>
  </example>
</examples>

