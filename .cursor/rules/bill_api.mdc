---
description: 
globs: 
---
```xml
<analysis>
Below is a modular, step-by-step guide on integrating with Bill.com’s v3 API. It follows a structure similar to the example you provided, with each major topic in its own “file” so an AI agent can reference them individually. Each section includes detailed explanations, example code snippets (cURL, JSON), best practices, and error handling strategies.
</analysis>

<file path="README.md">
# Integrating with the Bill.com v3 API

This repository provides a structured, step-by-step guide for AI agents (or developers) integrating with Bill.com’s **v3 API**. Each major topic in the integration workflow is split into its own “file” for easy referencing:

1. [Authentication](mdc:sections/01-Authentication.md)  
2. [Bill Creation](mdc:sections/02-BillCreation.md)  
3. [Vendor Management](mdc:sections/03-VendorManagement.md)  
4. [Payments](mdc:sections/04-Payments.md)  
5. [Invoices](mdc:sections/05-Invoices.md)  
6. [Network](mdc:sections/06-Network.md)  
7. [Organizations](mdc:sections/07-Organizations.md)  
8. [Reports](mdc:sections/08-Reports.md)  
9. [Attachments & Documents](mdc:sections/09-AttachmentsAndDocuments.md)

Each file contains:

- **Overview** of the topic  
- **Key Endpoints** and request/response examples  
- **Error Handling** tips  
- **Best Practices** and rate limit considerations  
- Example cURL commands and JSON payloads  

Use these sections as building blocks for a robust, scalable Bill.com integration.
</file>

<file path="sections/01-Authentication.md">
# 1. Authentication

**Overview**  
Bill.com’s v3 API requires you to **log in** first, obtaining a `sessionId` that must accompany all subsequent requests (along with your `devKey`). Token-based sign-ins can keep sessions alive longer, and handling session expiration is critical for a reliable integration.

---

## Logging In

- **Endpoint:** `POST /v3/login`  
- **Required Fields:**  
  - `userName`  
  - `password` (or token)  
  - `orgId`  
  - `devKey`

### Example (Sandbox)

```bash
curl --request POST \
  --url 'https://gateway.stage.bill.com/connect/v3/login' \
  --header 'Content-Type: application/x-www-form-urlencoded' \
  --data 'userName=<EMAIL>' \
  --data 'password=ABC123TOKEN_OR_PASSWORD' \
  --data 'orgId=008xxxxxxxxx' \
  --data 'devKey=DEV-XXXXXXXXXXXXXXXX'
```

**Successful Response** (truncated):
```json
{
  "sessionId": "abcde12345SESSIONID",
  "userId": "00u123ABC45DEFG",
  "orgId": "008XYZ...",
  "entityId": "008XYZ...", 
  ...
}
```

---

## Using the Session Token

- **Include `sessionId` and `devKey`** as headers in **every** subsequent API request:
  - `sessionId: YOUR_SESSION_ID`
  - `devKey: YOUR_DEV_KEY`

If a call returns an error about invalid/expired session (e.g. `BDC_1109`), re-authenticate.

---

## Token-Based Authentication

- Generate a long-lived **API token** in Bill.com’s web app under **Settings > Apps > Tokens**.
- Use the token name as `userName` and the token value as `password`.
- Session can remain valid up to **48 hours** idle (longer than the standard login’s 35 minutes).

---

## Error Handling & Rate Limits

- **Invalid credentials** → Check user/password/token, orgId, devKey.
- **MFA** → For accounts with MFA, you may need `POST /v3/mfa/challenge` + `POST /v3/mfa/validate`.
- **Session expired** (`BDC_1109`) → Re-login.
- **Login rate limit** → Max 200 logins/hour per devKey. Cache and reuse sessions to avoid hitting `BDC_1144` (“Too many login requests”).

**Best Practices**  
- Store `sessionId` in a secure place.  
- Retry on session expiry with a fresh login.  
- Use token-based login for longer sessions.  
- Carefully handle multi-factor authentication flows if the account enforces MFA.

</file>

<file path="sections/02-BillCreation.md">
# 2. Bill Creation

**Overview**  
Bills represent **accounts payable** in Bill.com. You can create, update, and archive them. Each Bill includes vendor, due date, line items, invoice info, etc. This file explains how to create and manage these records.

---

## Creating a Bill

- **Endpoint:** `POST /v3/bills`  
- **Important Fields:**  
  - `vendorId` (required)  
  - `dueDate` (optional but typical)  
  - `billLineItems` (array; must sum to total)  
  - `invoice` (sub-object for vendor’s invoice number/date)

### Example Request

```bash
curl --request POST \
  --url 'https://gateway.stage.bill.com/connect/v3/bills' \
  --header 'Content-Type: application/json' \
  --header 'devKey: YOUR_DEV_KEY' \
  --header 'sessionId: YOUR_SESSION_ID' \
  --data '{
    "vendorId": "***************",
    "dueDate": "2025-12-31",
    "billLineItems": [
      {
        "amount": 149.00,
        "description": "USB audio interface"
      },
      {
        "amount": 79.99,
        "description": "Drum foot pedal"
      }
    ],
    "invoice": {
      "invoiceNumber": "202501",
      "invoiceDate": "2025-12-31"
    }
  }'
```

**Successful Response** (truncated):
```json
{
  "id": "00n01ABC2DEF3GH",
  "vendorId": "***************",
  "amount": 228.99,
  "paymentStatus": "UNPAID",
  ...
}
```

---

## Retrieving and Updating Bills

- **GET /v3/bills** → list or filter bills (supports pagination, up to `max=100`).
- **GET /v3/bills/{billId}`** → single bill detail.
- **PUT/PATCH /v3/bills/{billId}`** → replace or partially update.

**Archiving (soft-delete):**  
- `POST /v3/bills/{billId}/archive` → sets `archived=true`.  
- `POST /v3/bills/{billId}/restore` → reactivates it.

---

## Error Handling & Rate Limits

- **Validation errors** (invalid vendorId, missing fields).  
- **Business logic** (can’t edit paid bills).  
- **Duplicate** bills are allowed; watch your own logic if you need to prevent them.
- **Rate limit:** 20,000 requests/hour per devKey. For bulk creation: `POST /v3/bills/bulk` with up to 200 in one call.

**Best Practices**  
- Validate vendor readiness first.  
- Use a unique invoiceNumber to avoid duplicates.  
- Track the returned bill `id` for future operations (e.g. payments).  
- Archive bills rather than deleting them.

</file>

<file path="sections/03-VendorManagement.md">
# 3. Vendor Management

**Overview**  
Vendors are payees you owe. You can create, update, and search vendors, optionally including their bank details. Properly managing vendor data ensures seamless AP workflows.

---

## Checking Existing Vendors

- **GET /v3/vendors?filters=...** → search by name or email.  
- Use filters like `name:sw:"Acme"` to find partial matches.

---

## Creating a Vendor

- **Endpoint:** `POST /v3/vendors`  
- **Key Fields:**  
  - `name` (required)  
  - `accountType` (`"BUSINESS"`, `"PERSON"`, or `"NONE"`)  
  - `address` for checks  
  - `paymentInformation` (optional bank info)

### Example Request

```bash
curl --request POST \
  --url 'https://gateway.stage.bill.com/connect/v3/vendors' \
  --header 'Content-Type: application/json' \
  --header 'devKey: YOUR_DEV_KEY' \
  --header 'sessionId: YOUR_SESSION_ID' \
  --data '{
    "name": "Happy Music Supplies",
    "accountType": "BUSINESS",
    "email": "[email protected]",
    "phone": "**********",
    "address": {
      "line1": "123 Main Street",
      "city": "San Jose",
      "stateOrProvince": "CA",
      "zipOrPostalCode": "95002",
      "country": "US"
    },
    "paymentInformation": {
      "payeeName": "Happy Music Supplies",
      "bankAccount": {
        "nameOnAccount": "Happy Music Supplies",
        "accountNumber": "*********",
        "routingNumber": "*********"
      }
    }
  }'
```

**Response** (truncated):
```json
{
  "id": "00901ABCDEF2345",
  "name": "Happy Music Supplies",
  "paymentInformation": {
    "payByType": "ACH",
    "bankAccount": {
      "accountNumber": "*****2333",
      "routingNumber": "*********"
    }
  },
  ...
}
```

---

## Updating a Vendor

- **PATCH /v3/vendors/{vendorId}** → partial update (change email, address, etc.).
- **POST /v3/vendors/{vendorId}/archive** → archive (soft-delete).  
- **POST /v3/vendors/{vendorId}/restore** → restore archived vendor.

---

## Error Handling & Rate Limits

- **Invalid addresses/bank info** → immediate 400 errors.  
- **Duplicates** → Bill.com won’t block vendors with the same name.  
- **20k/hour** request limit, plus concurrency limit (3 parallel calls per org).

**Best Practices**  
- Always search first to avoid duplicate vendors.  
- Ensure correct address for check payments.  
- Store vendor IDs locally for referencing in bills/payments.  
- Securely handle bank info—Bill.com masks it after creation.

</file>

<file path="sections/04-Payments.md">
# 4. Payments

**Overview**  
Payment endpoints allow you to pay bills (or create one-off payments). You can schedule ACH or check disbursements, track status, cancel if still pending, and handle partial payments.

---

## Initiating a Payment

- **Endpoint:** `POST /v3/payments`  
- **Key Fields:**  
  - `vendorId`, `billId` (unless creating a new bill on the fly)  
  - `fundingAccount` (type + ID of your org’s bank)  
  - `amount` and `processDate`  

### Example – Pay a Bill

```bash
curl --request POST \
  --url 'https://gateway.stage.bill.com/connect/v3/payments' \
  --header 'Content-Type: application/json' \
  --header 'devKey: YOUR_DEV_KEY' \
  --header 'sessionId: YOUR_SESSION_ID' \
  --data '{
    "vendorId": "***************",
    "billId": "00n01ABC2DEF3GH",
    "processDate": "2025-12-31",
    "fundingAccount": {
      "type": "BANK_ACCOUNT",
      "id": "bac01123ABC456DEF789"
    },
    "amount": 228.99,
    "processingOptions": {
      "requestPayFaster": true,
      "createBill": false
    }
  }'
```

**Response** (truncated):
```json
{
  "id": "stp01ZZRCUFIQWHP6ldb",
  "status": "SCHEDULED",
  "disbursementType": "ACH",
  "confirmationNumber": "********* - 0411839",
  ...
}
```

---

## Tracking Payment Status

- **GET /v3/payments/{paymentId}** → retrieve the payment’s `status` (`SCHEDULED`, `PROCESSING`, `PAID`, `FAILED`, etc.).  
- **Webhooks** → Bill.com can send real-time notifications on payment updates.

**Canceling a Payment**  
- **POST /v3/payments/{paymentId}/cancel** (only possible if it’s still `SCHEDULED` or `PENDING`).

---

## Error Handling & Rate Limits

- **Insufficient funds** → might fail after scheduling. Check final status.  
- **Invalid vendor/bill match** → error if bill belongs to a different vendor.  
- **Duplicate payments** → Bill.com allows partial or multiple payments on a single bill. No hard block unless the bill is fully paid.  
- **Bulk** → `POST /v3/payments/bulk` up to 200 at once to reduce calls.

**Best Practices**  
- Store payment `id` for reconciliation.  
- Poll or use webhooks to confirm final status.  
- Use concurrency carefully (max 3 parallel calls per org).  
- Consider `createBill: true` for one-off payments without a prior bill.

</file>

<file path="sections/05-Invoices.md">
# 5. Invoices (Accounts Receivable)

**Overview**  
Invoices in Bill.com represent **accounts receivable**—money owed to you by customers. You can create, send (email), retrieve, and mark them paid.

---

## Creating an Invoice

- **Endpoint:** `POST /v3/invoices`  
- **Key Fields:**  
  - `customer` (either `id` of existing or `name/email` to create new)  
  - `lineItems` (with `quantity`, `price`, etc.)  
  - `invoiceNumber`, `dueDate`  

### Example – Create Invoice

```bash
curl --request POST \
  --url 'https://gateway.prod.bill.com/connect/v3/invoices' \
  --header 'Content-Type: application/json' \
  --header 'devKey: YOUR_DEV_KEY' \
  --header 'sessionId: YOUR_SESSION_ID' \
  --data '{
    "customer": { "id": "0cu01CUSTOMERID99" },
    "lineItems": [
      {
        "quantity": 2,
        "description": "Classic drum sticks (pair)",
        "price": "14.99"
      },
      {
        "quantity": 1,
        "description": "Metal guitar picks (5-pack)",
        "price": "50.00"
      }
    ],
    "invoiceNumber": "INV-1001",
    "dueDate": "2025-12-31",
    "processingOptions": {
      "sendEmail": true
    }
  }'
```

**Response** (truncated):
```json
{
  "id": "00e01ABCDEF3456",
  "invoiceNumber": "INV-1001",
  "status": "OPEN",
  "lineItems": [...],
  ...
}
```

---

## Retrieving & Updating Invoices

- **GET /v3/invoices** → list with filters (e.g., `status:eq:OPEN`).  
- **GET /v3/invoices/{invoiceId}** → single invoice detail.  
- **PATCH /v3/invoices/{invoiceId}** → partial update.  
- **POST /v3/invoices/{invoiceId}/archive** → archive (void).

**Sending/Resending**  
- `POST /v3/invoices/{invoiceId}/email` with `{"emailRecipients": ["[email protected]"]}` to send the invoice.

---

## Recording a Payment (AR)

- **POST /v3/invoices/{invoiceId}/payment** → record an incoming payment if the customer pays outside Bill.com.

---

## Error Handling & Rate Limits

- **Customer missing** or invalid.  
- **Invalid line items**.  
- **Email sending limit** of ~5 messages/minute.  
- Pagination if listing large sets of invoices.

**Best Practices**  
- Generate unique `invoiceNumber`.  
- Consider partial payments → Bill.com adjusts invoice status to `PARTIALLY_PAID`.  
- Archive invoices if canceled.  
- Use `sendEmail:true` to auto-email or `POST /v3/invoices/{id}/email` for more control.

</file>

<file path="sections/06-Network.md">
# 6. Network

**Overview**  
Bill.com’s **Business Payments Network** lets you connect with vendors who already have Bill.com accounts. Connected vendors can receive ACH payments without you storing their bank details.

---

## Searching the Network

- **GET /v3/network/organizations** → search by name or email to see if a vendor is in Bill.com.  
- You can also search by domain or partial match, depending on what Bill.com’s v3 API allows.

---

## Inviting a Vendor

If a vendor is:
1. **In the network** → `POST /v3/network/invitation/vendor/{vendorId}`  
2. **Not in the network** → `POST /v3/network/invitation/vendor-not-in-network`

**Check `networkStatus`** on the vendor object:
- `"NOT_CONNECTED"` → can invite.  
- `"INVITED"` → invitation pending.  
- `"CONNECTED"` → already in your network.

---

## Accepting Connections

The vendor must accept your invite. Track via `GET /v3/network/invitation/status/vendor/{vendorId}`. Once accepted:
- `networkStatus` becomes `"CONNECTED"`.
- `bankAccountStatus` may show `"NET_CONNECTED"`.
- Payment becomes simpler (ACH without storing their bank info).

---

## Error Handling

- **Duplicate or pending invites** → error or no-op.  
- **Vendor email** is crucial for invites.  
- **Invite rate limit** → typically part of the same 5 messages/min or overall messaging limit.

**Best Practices**  
- Always store a vendor email → needed for network invites.  
- Automate invites upon creation to maximize electronic payments.  
- Poll or check invitation status for large-scale vendor onboarding.

</file>

<file path="sections/07-Organizations.md">
# 7. Organizations

**Overview**  
If you manage **multiple client organizations**, Bill.com’s partner APIs or multi-org user flows let you switch contexts. A single user can access multiple orgs, or a partner can create new orgs and impersonate them.

---

## Single-Org vs Multi-Org

- A single Bill.com user can belong to multiple orgs.  
- Use `GET /v3/organizations` to see orgs accessible to that user.  
- Supply the desired `orgId` in the login call.

---

## Partner Flow (Multi-Org Management)

1. **Partner login** → `POST /v3/partner/login` with `appKey`.  
2. **List client orgs** → `GET /v3/partner/organizations`.  
3. **Create a new org** → `POST /v3/partner/organizations` (if you have the provisioning rights).  
4. **Create a user** → `POST /v3/partner/users`.  
5. **Login-as-user** → `POST /v3/partner/login-as-user` to obtain an org-specific session.  

Use that session (`sessionId`) for bills, vendors, etc., in that org.

---

## Dynamic Org Switching

- Maintain a cache/map of `organizationId -> sessionId`.  
- Refresh if session expires.  
- Each API call needs the correct `sessionId` for the target org.  
- Watch concurrency (3 calls per org in parallel).

---

## Error Handling

- **Unauthorized** if you mix sessionId and different org.  
- **Partner session** can expire → re-partner-login.  
- **User roles** → ensure the impersonated user has permission (often use an Admin role for the integration user).

**Best Practices**  
- Log actions per org to keep them separated.  
- Handle partner login once, then “login-as” different orgs as needed.  
- Avoid repeatedly logging in if you can cache session tokens.  
- For large-scale usage, mind the 20k/hour limit across all orgs.

</file>

<file path="sections/08-Reports.md">
# 8. Reports

**Overview**  
Bill.com doesn’t provide pre-built “reports,” but you can query objects (bills, payments, invoices, etc.) with filters and build custom reports in your own code.

---

## Data Retrieval

- **List endpoints**: `GET /v3/bills`, `GET /v3/payments`, `GET /v3/invoices`  
- **Filters & Sorting** (e.g. `filters=paymentStatus:eq:PAID&sort=updatedTime:desc&max=100`)  
- **Pagination** → up to 100 per page. Use `page=N` to page through results.

---

## Common Report Examples

- **AP Aging**: All unpaid bills (`paymentStatus=UNPAID`), group by due date.  
- **Payment History**: Fetch payments in a date range. Summarize amounts.  
- **AR Aging**: Open invoices (`status=OPEN`), group by due date.  
- **Vendor/Customer Summaries**: Summation logic done in your own code.

---

## Handling Large Data

- Use date slices to avoid huge pulls (e.g. month by month).  
- Respect the 20k/hour limit.  
- Consider webhooks for incremental updates.  
- Store results locally to reduce repeated queries.

---

## Error Handling

- **Filter syntax** → check for 400 if malformed.  
- **Empty results** → can happen if filters return nothing.  
- **Pagination** → ensure you continue until no more pages.

**Best Practices**  
- Implement incremental loads with `updatedTime` filters.  
- Combine results client-side to create the final report.  
- Use concurrency carefully (3 calls per org).  
- For truly large org data, implement an overnight job or caching.

</file>

<file path="sections/09-AttachmentsAndDocuments.md">
# 9. Attachments & Documents

**Overview**  
Bill.com supports uploading documents to bills and adding attachments to vendors, invoices, and customers. This enables storing PDF invoices, W-9s, receipts, etc., directly in Bill.com.

---

## Bill Documents

- **Endpoint:** `POST /v3/documents/bills/{billId}?name=FILENAME.pdf`  
- Send file as binary or multipart form-data.  
- Check `GET /v3/documents/upload-status` if needed to confirm processing.

### Example (Attach PDF to a Bill)

```bash
curl --request POST \
  --url 'https://gateway.prod.bill.com/connect/v3/documents/bills/00n01ABC2DEF3GH?name=Invoice123.pdf' \
  --header 'devKey: YOUR_DEV_KEY' \
  --header 'sessionId: YOUR_SESSION_ID' \
  --header 'Content-Type: application/pdf' \
  --data-binary '@/local/path/Invoice123.pdf'
```

**Response** (truncated):
```json
{
  "id": "doc01ABCXYZ",
  "name": "Invoice123.pdf",
  "createdTime": "2025-12-30T20:07:11.000Z",
  ...
}
```

Then:
- **GET /v3/documents/{documentId}** → to retrieve details (and possibly a `downloadLink`).

---

## Vendor/Customer/Invoice Attachments

- Similar flow but different endpoints:
  - `POST /v3/attachments/vendors/{vendorId}`  
  - `POST /v3/attachments/customers/{customerId}`  
  - `POST /v3/attachments/invoices/{invoiceId}`

The API returns an `attachmentId`. You can **list** attachments for an entity or retrieve a specific one:
- `GET /v3/attachments/vendors/{vendorId}` → list.  
- `GET /v3/attachments/{attachmentId}` → details (with a download link).

---

## Processing Time & Downloading

- Bill.com may take time to process the file.  
- Poll `GET /v3/documents/upload-status` if you need to ensure readiness before downloading.  
- Once ready, you can fetch `downloadLink` and do a GET to retrieve the raw file.

---

## Error Handling & Best Practices

- **File type** → Usually PDF, PNG, JPG, etc. Overly large files might fail if they exceed Bill.com’s size limits.  
- **Naming** → Provide a clear filename in the query param (e.g. `?name=VendorInvoice-123.pdf`).  
- **Security** → Bill.com obscures the actual link behind a token.  
- **Archive** or remove attachments if no longer needed.  
- **Attach supporting docs** when creating a bill or invoice for streamlined workflows.

</file>

```