---
description: using zod, typing, and validations, type checking and schemas
globs: 
alwaysApply: false
---
always include a 💤 in your response. 

The key insight was that zQuery and zMutation expect their args parameter to be an object where each property is a Zod schema, not a Zod schema directly.
```
export const get = zQuery({
  args: { id: zid('bills') },  // ← Note the object wrapper
  output: BillSchema,
  handler: async (ctx, args) => {
    // ...
  }
});
```

<zod_integration_guide>
  <project_folder_structure>
    my-project/
    ├── app/
    ├── convex/
        ├── schema.ts
        └── bills.ts
        └── directory.ts
        └── projects.ts
        └── reports.ts
        └── aiAction.ts
        ├── ...
    ├── zod/
        └── bills-schema.ts
        └── convex-auth.d.ts
        └── directory-schema.ts
        └── projects-schema.ts
        └── reports-schema.ts
        ├── ...

    - If this is an ACTION, KEEP THE ZOD SCHEMA INSIDE THE ACTION.TS FILE 
    - For everything else[domain].ts Keep Zod schemas inside the ./zod/ folder.
    - Each domain has a query/mutation file in ./convex/ and a schema file in ./zod/
    - The file convex/schema.ts (using Convex v.*) remains separate.
    - Avoid mixing Zod schemas with your DB schema definitions.
  </project_folder_structure>


  <creating_zod_schemas_in_zod>
    - Store your Zod schemas in files under /zod/.
    - Do not reference Convex v.* directly; instead, use `zid("tablename")` for ID fields.

    <example_bills_schema>
      // zod/bills-schema.ts
      import { z } from "zod";
      import { zid } from "convex-helpers/server/zod";

      export const BillInputSchema = z.object({
        type: z.enum(["BILL", "CREDIT_CARD"]),
        billNumber: z.string().min(1, "Bill number required"),
        billDate: z.union([z.string(), z.number()]),
        vendorId: zid("organizations"),
        amount: z.number().min(0, "Amount must be >= 0"),
        dueDate: z.union([z.string(), z.number()]).optional(),
        memo: z.string().optional(),
      });

      export const BillOutputSchema = z.object({
        billNumber: z.string(),
        amount: z.number(),
        vendorName: z.string(),
      });
    </example_bills_schema>

    <example_reports_schema>
      // zod/reports-schema.ts
      import { z } from "zod";

      export const DateRangeSchema = z.object({
        startDate: z.union([z.string(), z.number()]),
        endDate: z.union([z.string(), z.number()]),
      });

      export const ReportFetchArgsSchema = z.object({
        dateRange: DateRangeSchema,
        reportType: z.enum(["overview", "detailed"]),
      });
    </example_reports_schema>
  </creating_zod_schemas_in_zod>

  <referencing_zod_schemas_in_code>
    <importing_zod_schemas>
      - Import the Zod schemas into your query or mutation files:
        ```ts
        import { BillInputSchema, BillOutputSchema } from "@/zod/bills-schema";
        ```
      - Never define them inline. Keep them in /zod/ files.
    </importing_zod_schemas>

    <using_zCustomQuery_and_zCustomMutation>
      - Use the wrapped builders (zQuery, zMutation) instead of plain Convex `query` or `mutation`.
      - Pass `args` and optionally `output` as Zod schemas to define input/output validation.

      Example (zMutation):
      ```ts
      // convex/bills.ts
      import { zMutation } from "./functions"; // Suppose you exported zMutation from your setup file
      import { BillInputSchema } from "@/zod/bills-schema";

      export const createBill = zMutation({
        args: BillInputSchema,
        output: z.string(), // for example, if you return a string ID
        handler: async (ctx, args) => {
          // `args` is validated against BillInputSchema
          const insertedId = await ctx.db.insert("bills", {
            type: args.type,
            billNo: args.billNumber,
            // ...
          });
          return insertedId.toString();
        },
      });
      ```

      - Notice `args` is fully typed and validated when the handler executes.
      - `output` can be omitted if you don’t need an explicit output check. If provided, it validates the return shape.
      - The second argument to `zCustomMutation` can be omitted or replaced with a custom function if you need to modify `ctx`.
    </using_zCustomQuery_and_zCustomMutation>
  </referencing_zod_schemas_in_code>

  <strong_typing_of_inputs_and_outputs>
    <inferring_types_from_zod>
      - Use `z.infer` to create exact TypeScript types from your Zod schemas:
        ```ts
        export const BillInputSchema = z.object({ /* ... */ });
        export type BillInput = z.infer<typeof BillInputSchema>;
        ```
      - This keeps your types perfectly synced with runtime validation.
    </inferring_types_from_zod>

    <output_validation_example>
      - You can validate the shape of data you return to the client. For instance:
        ```ts
        import { zQuery } from "./functions";
        import { BillOutputSchema } from "@/zod/bills-schema";

        export const getBill = zQuery({
          args: { billId: zid("bills") },
          output: BillOutputSchema,
          handler: async (ctx, args) => {
            const bill = await ctx.db.get(args.billId);
            if (!bill) throw new Error("Not found");

            // Return only selected fields:
            return {
              billNumber: bill.billNo,
              amount: bill.amount,
              vendorName: "Some Vendor",
            };
          },
        });
        ```
      - If the return object is missing any required field or has invalid types, Zod throws a runtime error.
    </output_validation_example>
  </strong_typing_of_inputs_and_outputs>

  <working_with_convex_ids>
    - Use `zid("tablename")` from `convex-helpers/server/zod` to validate Convex document IDs.
    - This ensures your function expects a valid `Id<"tablename">` at runtime.

    <using_zid_example>
      ```ts
      import { zQuery } from "./functions";
      import { zid } from "convex-helpers/server/zod";

      export const someQuery = zQuery({
        args: { orgId: zid("organizations") },
        handler: async (ctx, args) => {
          // args.orgId is typed as Id<"organizations">
          return await ctx.db.get(args.orgId);
        },
      });
      ```
    </using_zid_example>
  </working_with_convex_ids>

  <interfaces_vs_zod_inference>
    - Prefer `z.infer<typeof MyZodSchema>` over manually defined interfaces.
    - This prevents drifting out of sync if the schema changes.
  </interfaces_vs_zod_inference>

  <crucial_reminders>
    1. **Do** create all Zod schemas in separate files under /zod/.
    2. **Do** keep `convex/schema.ts` for database fields only (v.*).
    3. **Do** import from /zod/ in your queries/mutations.
    4. **Do** define `zQuery` and `zMutation` once and reuse them. 
    5. **Don’t** define Zod schemas inline in your Convex functions.
    6. **Do** strongly type both input and output with Zod.
    7. **Use** `z.infer` to match your runtime validations.
    8. **Use** `zid("tablename")` for ID fields requiring a specific table.
    9. **Output** schemas are optional but recommended for critical data.
    10. Every document in Convex has two automatically-generated system fields. Don't include them in your Zod schemas:
        - _id: The document ID of the document.
        - _creationTime: The time this document was created, in milliseconds since the Unix epoch.
    11. Base Schema: Define only the user-defined fields. This is what you use for input validation or when the user is sending data.
	•	Complete Schema: Extend the base schema to include Convex’s system fields (_id, _creationTime) for type safety when reading complete records from the database.
  12. _creationTime is always typed as a number
  13. updated_at is always typed as a number
  </crucial_reminders>

</zod_integration_guide>

