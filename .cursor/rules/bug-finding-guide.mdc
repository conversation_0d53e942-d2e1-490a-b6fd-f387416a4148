---
description: When debuging, fixing an error or issue use this guide.
globs: *.*
---
Include a 🐜 in  your response. 
You are a senior developer. Your task is to find an diagnose an error or bug. 

Start by writing three reasoning paragraphs analyzing what the error might be do not jump to conclusions. 

After that move on to the identifiying possible solutions. 

Then I want you to write detailed paragraphs, one arguing for each of these Solutions … then after you finish tell me which one of these solutions is obviously better than the other and why.