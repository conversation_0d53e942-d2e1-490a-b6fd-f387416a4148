---
description: tiptap editor
globs: 
alwaysApply: false
---
# Building with TipTap in a Next.js Environment – Advanced Guide
## Introduction

- Use TipTap's built-in ready state and focus state over creating your own. 
- When initializing your Tiptap editor, add the immediatelyRender: false option to the configuration:
```
const editor = useEditor({
    extensions: [StarterKit],
    content: '<p>Hello World!</p>',
    immediatelyRender: false,
  })
```
- also use dynamic import to load the Tiptap component on the client-side only:
```
const TiptapEditor = dynamic(() => import('../components/TiptapEditor'), {
  ssr: false,
})
```


## TipTap Core Functionalities

### Editor Instance and Content Model

At the heart of TipTap is the `Editor` instance, which manages the document state (built on a ProseMirror schema) and provides an API for manipulating content. When using TipTap in React, you’ll typically create the editor in a React component (e.g. using the `useEditor` hook from `@tiptap/react`) and render it with `<EditorContent>`.

<details>
<summary>Example – Initializing an Editor</summary>

```jsx
// In a Next.js component file (e.g. RichTextEditor.jsx)
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';

const RichTextEditor = () => {
  const editor = useEditor({
    extensions: [StarterKit],
    content: `<p>Hello, <strong>TipTap</strong>!</p>` // initial content
  });

  return editor ? <EditorContent editor={editor} /> : null;
};

export default RichTextEditor;
```

</details>

In a Next.js app, load `RichTextEditor` with `dynamic(() => import('path/to/RichTextEditor'), { ssr: false })` to ensure it only renders client-side <tag reference="REDDIT.COM" />.

**Content Manipulation**  
The `Editor` instance provides methods to interact with the content:

- `editor.getHTML()` – returns the content as an HTML string <tag reference="TIPTAP.DEV" />  
- `editor.getJSON()` – returns the content as a JSON object <tag reference="TIPTAP.DEV" />  
- `editor.getText()` – returns plain text <tag reference="TIPTAP.DEV" />

You can set or insert content programmatically using commands like `editor.commands.setContent()` or `editor.commands.insertContent()`.

**Commands and Chainable Actions**  
TipTap’s API uses commands to alter content or formatting. Each extension can provide its own commands (for example, the **Bold** extension adds a `toggleBold` command). You can invoke commands directly, e.g. `editor.commands.toggleBold()`, or use the chaining API:

```js
editor.chain().focus().toggleBold().setFontFamily('Arial').run();
```

Chain multiple operations and finalize with `.run()`. You can check if a command is valid by using `editor.can().<command>()` before enabling a button or menu item.

**Headless UI Approach**  
Because TipTap is *headless*, you have full control over the UI. That means you’ll typically implement toolbars or menus in React (or use community components) that call TipTap commands on user actions. TipTap includes some helpful UI-oriented extensions like `BubbleMenu` or `FloatingMenu`, but the core remains separate from any default UI <tag reference="TIPTAP.DEV" />.

---

## Schema, Nodes, and Marks

TipTap’s content model is based on ProseMirror’s schema. The schema defines **Nodes** (block or inline elements like paragraphs, images, list items) and **Marks** (inline text formatting like bold, italic).

**Common Node Types** (provided by StarterKit or other built-ins):

- **Document**: The top-level root node  
- **Paragraph**: Standard text block  
- **Heading**: Headings (`<h1>`–`<h6>`)  
- **Text**: Inline text content  
- **BulletList / OrderedList** and **ListItem**: For lists  
- **Blockquote**: Quoted text block  
- **CodeBlock**: Multi-line code (often `<pre><code>`)  
- **HorizontalRule**: Horizontal line (`<hr>`)  
- **Image**: Embeds an image  
- **Table** series: `Table`, `TableRow`, `TableCell`, `TableHeader`  
- **TaskList** / **TaskItem**: For to-do lists with checkboxes  
- **Mention**: Inline mention “@user”  
- **YouTube (Embed)**: Embeds YouTube videos

**Common Mark Types**:

- **Bold**, **Italic**, **Strike**, **Underline**, **Code**
- **Link**: Hyperlink text  
- **Highlight**: Background highlighting  
- **Subscript/Superscript**  
- **TextStyle**: A base mark that allows custom styling  
- **Color**: Built on `TextStyle`, sets text color  
- **FontFamily**: Also built on `TextStyle`, changes font face  

StarterKit bundles many of these so you don’t have to add them manually.

**Schema Customization**  
One of TipTap’s core strengths is schema flexibility. You can add new node/mark types or remove/replace existing ones via extensions. The schema enforces valid content (e.g., lists can only contain list items).

---

## Extension System and Customization

**Extensions** add functionality to TipTap. An extension can be a Node, a Mark, or a general plugin (e.g., undo/redo **History** or **Collaboration**). You can:

- Use built-in extensions  
- Configure them (e.g., limit heading levels)  
- Extend them (override or add features)  
- Create your own

### Built-in Extensions Overview

- **Node Extensions** (Images, Tables, Lists, TaskItems, CodeBlocks, Mentions, etc.)
- **Mark Extensions** (Bold, Italic, Underline, Code, Link, Highlight, Color, etc.)
- **Functional/Plugin Extensions** (History, Placeholder, Collaboration, CharacterCount, BubbleMenu, FloatingMenu, TextAlign, etc.)

### Using and Configuring Extensions

Include an extension by importing it and adding it to the `extensions` array when creating the editor. If you use `StarterKit`, many extensions are included by default, and you can configure or disable pieces of it.

<details>
<summary>Example – Configuring StarterKit</summary>

```js
import StarterKit from '@tiptap/starter-kit';

const editor = new Editor({
  extensions: [
    StarterKit.configure({
      history: false,            // disable the History extension
      heading: { levels: [1, 2] } // only allow <h1> and <h2>
    })
  ],
});
```

</details>

You can also forego `StarterKit` and selectively import only the extensions you need.

### Customizing and Extending Extensions

You can extend existing extensions via `.extend({...})` to tweak behavior. Common use cases:

- Add or change keyboard shortcuts  
- Override default parsing/rendering  
- Add extra attributes  
- Change default options or commands  

<details>
<summary>Example – Adding a Keyboard Shortcut for BulletList</summary>

```js
import BulletList from '@tiptap/extension-bullet-list';

const CustomBulletList = BulletList.extend({
  addKeyboardShortcuts() {
    return {
      'Mod-l': () => this.editor.commands.toggleBulletList(),
    }
  }
});
```

</details>

#### Creating a New Extension

If no built-in extension fits your needs, create your own by extending from `Node`, `Mark`, or `Extension`. You define:

- A unique `name`  
- Schema specs (if it’s a Node/Mark)  
- `parseHTML` / `renderHTML`  
- `addCommands`, `addKeyboardShortcuts`, `addInputRules`, etc.  
- Potentially a custom NodeView for interactive rendering

<details>
<summary>Example – Custom Card Node</summary>

A “Card” node containing an image + caption:

- `group: 'block'`
- `content: 'image caption'`
- A command `insertCard` that inserts the node  
- A custom React NodeView to display an interactive card

</details>

---

## TipTap in Next.js – Integration Notes

Because Next.js does server-side rendering, and TipTap requires DOM APIs, you must ensure the editor only runs on the client. Typically, use `dynamic(..., { ssr: false })`. Also:

- Avoid SSR for the editor content.  
- Consider using a placeholder or a fallback for the server-rendered part.  
- TipTap only mounts once the component is client-side, eliminating SSR errors like “document is not defined”.

---

## Advanced Use Cases

### Real-time Collaboration

TipTap’s **Collaboration** extension integrates with @Y.js for real-time editing <tag reference="TIPTAP.DEV" />. Combine:

1. **Collaboration** extension on the frontend  
2. A Y.js document (`Y.Doc()`)  
3. A provider (WebRTC, Hocuspocus, or another) for broadcasting changes

<details>
<summary>Example – Collaboration Setup with Hocuspocus</summary>

```jsx
import * as Y from 'yjs';
import { HocuspocusProvider } from '@hocuspocus/provider';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import StarterKit from '@tiptap/starter-kit';

const ydoc = new Y.Doc();
const provider = new HocuspocusProvider({
  url: 'wss://localhost:1234',
  name: 'my-document-id',
  document: ydoc
});

const editor = useEditor({
  extensions: [
    StarterKit.configure({ history: false }),
    Collaboration.configure({ document: ydoc }),
    CollaborationCursor.configure({
      provider,
      user: { name: 'You', color: '#58c' }
    }),
  ],
  content: ''
});
```

</details>

This setup allows concurrent editing (like Google Docs). Disable the default History extension to avoid conflicts with Y.js updates. Include `CollaborationCursor` if you want to display other users’ cursors.

### Custom Commands and Actions

You can write your own commands, either by combining existing ones or by defining them in `addCommands()` in an extension.

<details>
<summary>Example – Clear Formatting Command</summary>

```js
editor
  .chain()
  .focus()
  .unsetAllMarks() // built-in to remove all marks
  .clearNodes()    // convert all blocks to paragraphs
  .run();
```

</details>

If you need something more complex (e.g., inserting a specialized node with user input), define a custom command within a new or extended extension.

### Interactive Content and Node Views

**Node Views** let you render node content with a custom React component (fully interactive). This is how you can build, for example, an image node with an “Upload” button, a “remove” icon, etc.

- In your extension’s `addNodeView()` method, return a `ReactNodeViewRenderer(...)`.  
- In your React component, use `<NodeViewWrapper>` and `<NodeViewContent>` from `@tiptap/react`.  

**Suggestions and Slash Commands**  
TipTap has a suggestion utility for triggers like “@” or “/”. You can show a dropdown (like a mention list or slash commands) by configuring `@tiptap/suggestion`. The built-in **Mention** extension uses this approach for “@username” suggestions.

---

## Optimizations and Performance Considerations

1. **Isolate the Editor Component**  
   - TipTap triggers re-renders on editor state changes. Keep your editor in a separate component so it doesn’t cause massive re-renders of the whole page.  
   - Use `shouldRerenderOnTransaction: false` carefully if you want to manage re-renders manually.

2. **Content Size and Complexity**  
   - TipTap can handle large documents, but extremely large content might require optimization.  
   - Avoid deeply nested structures or memory-heavy node views.

3. **Node View Performance**  
   - Each NodeView is a separate React root. If you have many such nodes, performance can degrade.  
   - Use NodeViews sparingly or consider simpler DOM for static items.

4. **Avoid Memory Leaks**  
   - Call `editor.destroy()` when unmounting the editor component.

5. **SSR and Hydration**  
   - Always disable SSR for the editor (use dynamic import with `ssr: false`).  
   - If hydration warnings appear, ensure the editor is not rendered on the server at all.

6. **Editor Updates and External State**  
   - Avoid updating React state on every keystroke. Use TipTap’s internal state and save content only on demand or with debounced autosave.

7. **Use TipTap’s Utilities**  
   - `editor.getHTML()` or `editor.getJSON()` is enough for storage or previews.  
   - For a read-only display elsewhere, consider a read-only `<EditorContent>` or static HTML rendering.

8. **Profiling**  
   - Use React Dev Tools to detect unintentional re-renders.  
   - If needed, wrap your editor component with `React.memo` or separate out complex UI.

---

## Conclusion
 

**Key Takeaways**  
- **Leverage Extensions** to quickly add standard functionality.  
- **Customize or Create Your Own** extensions for unique needs.  
- **Mind Performance**: isolate the editor, manage NodeViews carefully, and avoid SSR.  
- **Real-time**: Combine Collaboration extension with Y.js for multi-user editing.  
 
