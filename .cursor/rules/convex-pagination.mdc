---
description: Pagination Best practices using convex
globs: 
alwaysApply: false
---
We have a shared pagination-schema.ts file [pagination-schema.ts](mdc:zod/pagination-schema.ts)

Paginated Queries
Paginated queries are queries that return a list of results in incremental pages.

This can be used to build components with "Load More" buttons or "infinite scroll" UIs where more results are loaded as the user scrolls.

Example: Paginated Messaging App

Using pagination in Convex is as simple as:

Writing a paginated query function that calls .paginate(paginationOpts).
Using the usePaginatedQuery React hook.
Like other Convex queries, paginated queries are completely reactive.

Writing paginated query functions
Convex uses cursor-based pagination. This means that paginated queries return a string called a Cursor that represents the point in the results that the current page ended. To load more results, you simply call the query function again, passing in the cursor.

To build this in Convex, define a query function that:

Takes in a single arguments object with a paginationOpts property of type PaginationOptions.
PaginationOptions is an object with numItems and cursor fields.
Use paginationOptsValidator exported from "convex/server" to validate this argument
The arguments object may include properties as well.
Calls .paginate(paginationOpts) on a database query, passing in the PaginationOptions and returning its result.
The returned page in the PaginationResult is an array of documents. You may map or filter it before returning it.
convex/messages.ts
TS
import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { paginationOptsValidator } from "convex/server";

export const list = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const foo = await ctx.db
      .query("messages")
      .order("desc")
      .paginate(args.paginationOpts);
    return foo;
  },
});

Additional arguments
You can define paginated query functions that take arguments in addition to paginationOpts:

convex/messages.ts
TS
export const listWithExtraArg = query({
  args: { paginationOpts: paginationOptsValidator, author: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("messages")
      .filter((q) => q.eq(q.field("author"), args.author))
      .order("desc")
      .paginate(args.paginationOpts);
  },
});


Transforming results
You can apply arbitrary transformations to the page property of the object returned by paginate, which contains the array of documents:

convex/messages.ts
TS
export const listWithTransformation = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const results = await ctx.db
      .query("messages")
      .order("desc")
      .paginate(args.paginationOpts);
    return {
      ...results,
      page: results.page.map((message) => ({
        author: message.author.slice(0, 1),
        body: message.body.toUpperCase(),
      })),
    };
  },
});

Paginating within React Components
To paginate within a React component, use the usePaginatedQuery hook. This hook gives you a simple interface for rendering the current items and requesting more. Internally, this hook manages the continuation cursors.

The arguments to this hook are:

The name of the paginated query function.
The arguments object to pass to the query function, excluding the paginationOpts (that's injected by the hook).
An options object with the initialNumItems to load on the first page.
The hook returns an object with:

results: An array of the currently loaded results.
isLoading - Whether the hook is currently loading results.
status: The status of the pagination. The possible statuses are:
"LoadingFirstPage": The hook is loading the first page of results.
"CanLoadMore": This query may have more items to fetch. Call loadMore to fetch another page.
"LoadingMore": We're currently loading another page of results.
"Exhausted": We've paginated to the end of the list.
loadMore(n): A callback to fetch more results. This will only fetch more results if the status is "CanLoadMore".
src/App.tsx
TS
import { usePaginatedQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export function App() {
  const { results, status, loadMore } = usePaginatedQuery(
    api.messages.list,
    {},
    { initialNumItems: 5 },
  );
  return (
    <div>
      {results?.map(({ _id, body }) => <div key={_id}>{body}</div>)}
      <button onClick={() => loadMore(5)} disabled={status !== "CanLoadMore"}>
        Load More
      </button>
    </div>
  );
}


You can also pass additional arguments in the arguments object if your function expects them:

src/App.tsx
TS
import { usePaginatedQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export function App() {
  const { results, status, loadMore } = usePaginatedQuery(
    api.messages.listWithExtraArg,
    { author: "Alex" },
    { initialNumItems: 5 },
  );
  return (
    <div>
      {results?.map(({ _id, body }) => <div key={_id}>{body}</div>)}
      <button onClick={() => loadMore(5)} disabled={status !== "CanLoadMore"}>
        Load More
      </button>
    </div>
  );
}


Reactivity
Like any other Convex query functions, paginated queries are completely reactive. Your React components will automatically rerender if items in your paginated list are added, removed or changed.

One consequence of this is that page sizes in Convex may change! If you request a page of 10 items and then one item is removed, this page may "shrink" to only have 9 items. Similarly if new items are added, a page may "grow" beyond its initial size.

Paginating manually
If you're paginating outside of React, you can manually call your paginated function multiple times to collect the items:

download.ts
TS
import { ConvexHttpClient } from "convex/browser";
import { api } from "../convex/_generated/api";

require("dotenv").config();

const client = new ConvexHttpClient(process.env.VITE_CONVEX_URL!);

/**
 * Logs an array containing all messages from the paginated query "listMessages"
 * by combining pages of results into a single array.
 */
async function getAllMessages() {
  let continueCursor = null;
  let isDone = false;
  let page;

  const results = [];

  while (!isDone) {
    ({ continueCursor, isDone, page } = await client.query(api.messages.list, {
      paginationOpts: { numItems: 5, cursor: continueCursor },
    }));
    console.log("got", page.length);
    results.push(...page);
  }

  console.log(results);
}

getAllMessages();

Take Control of Pagination
 
When you store a lot of data in Convex, you usually want to display it incrementally, using pagination to show one page at a time.

The Convex framework offers .paginate(opts) and usePaginatedQuery() to implement infinite-scroll pagination. These functions are powerful and handle the complex edge cases of stitching the pages together seamlessly. However, there are several scenarios they do not support, or at least don't support out-of-the-box:

Joins like “list all (paginated) messages for each of my (paginated) contacts”
Unions like “list all (paginated) messages for each of my 3 email accounts.”
Virtual infinite scroll view where you jump to a point and then scroll up or down. Think “show me the photos from June 2020 and photos before and after”
Unloading and unsubscribing from unnecessary pages. If you load 100 pages, those 100 pages of documents stick around in the browser’s memory and keep updating when the data changes, even if the documents are far off-screen.
Keeping pages bounded in size. If documents are inserted quickly — e.g. if there are hundreds of inserts per mutation — pages can grow beyond the limits of a Convex function, and throw an error.
These are difficult problems to solve, and a complete solution would require complicating the base interface. For example, if you call .paginate twice in a query function, are you aiming for a join pattern or a union? It becomes unclear whether loadMore should load more of the first or the last db.query. As another example, do you want pagination cursors to be opaque — which is the default with .paginate and useful for security in some cases — or do you want to be able to parse them to allow “jump to June 2020, which may already be a loaded page.”

Convex may eventually solve all of these problems with the built-in interface, but for now we give you the power to solve them yourselves, leveraging the versatility of the Convex runtime running arbitrary TypeScript. Introducing getPage:

import { getPage } from "convex-helpers/server/pagination";

What is getPage?
This function is a new helper, built on top of existing Convex primitives. It supports many arguments, most of them optional with sensible defaults:

a start position: ”give me photos starting at June 2020”
an end position: “give all photos up until August 2020”
an index: “paginate in order of user surname, instead of creation time”
an order for the index: “give me messages from newest to oldest”
a soft limit: “give me 100 messages, but allow more if the page grows”
a hard limit: “give me at most 500 messages, even if the page grows”
See the source code for the full interface and docstrings.

The return value includes a page of documents, whether there are more documents to request, and the index key for each document in the page.

What’s an index key?
The getPage function returns an object:

const { page, indexKeys, hasMore } = await getPage(...);

Each document in page has a corresponding index key, so the document page[3] has index key indexKeys[3]. But what is an index key?

Index keys are locations in the index. For a table like

contacts: defineTable({
	surname: v.string(),
	givenName: v.string()
}).index("name", ["surname", "givenName"])

an index key would be something like ["Smith", "John", 1719412234000, "jh7evzh9wejnwjv88y1a1g9c7h6vpabd"]. Documents in the returned page are sorted by the index key. To avoid duplicates, every index key ends with the creation time and ID of the document.

Usually you don’t need to pay attention to what’s in an index key, because you can pass getPage's response indexKeys directly to its request startIndexKey or endIndexKey fields. However, it can be useful to say “start the page at June 2020” by passing startIndexKey: [Date.parse("2020-06-01")]: the sort order puts this before all dates in June 2020.

When you fetch a page of documents with getPage, you get an index key corresponding to each document, and you can use these to fetch more documents. The last index key indexKeys[indexKeys.length - 1] is particularly useful, because it corresponds to the index location at the end of the fetched page.

Patterns
With getPage giving you complete control of your pagination, you can now solve any of the above problems. Let's look at implementing some common patterns with concrete examples.

All of the following examples will use and build off of this data model:

contacts: defineTable({
	surname: v.string(),
	givenName: v.string(),
	emailAddress: v.string(),
}).index("name", ["surname", "givenName"]),
emails: defineTable({
	address: v.string(),
	body: v.string(),
}).index("address", ["address"]),

Basic Pagination
Let’s start with the most basic query. We list a page of 100 contacts in _creationTime order, starting at the beginning of time:

// In the convex/contacts.ts file
export const firstPageOfContacts = query((ctx) => {
	return getPage(ctx, { table: "contacts" });
});
// Then in React, call the query
const { page } = useQuery(api.contacts.firstPageOfContacts);

To get the next page of contacts, we ask for the page starting at the index key at the end of the first page.

// In convex/contacts.ts
export const pageOfContacts = query((ctx, args) => {
	return getPage(ctx, { table: "contacts", ...args });
});
// In React
const firstPage = useQuery(api.contacts.pageOfContacts);
const secondPage = useQuery(api.contacts.pageOfContacts, firstPage ? {
	startIndexKey: firstPage.indexKeys[firstPage.indexKeys.length - 1],
} : "skip");

Now you have two pages. If you want a dynamic number of pages, instead of useQuery you will want useQueries. At some point you’ll want to wrap all this in a hook, similar to the built-in usePaginatedQuery.

The return value of getPage includes three things:

The page of documents
The index key for each document, allowing you to fetch related pages
A boolean hasMore to tell you if there are more pages to fetch
Use any index
By default, getPage uses the index on _creationTime, but it can use any database index (text-search and vector indexes are not supported). The index determines the format for index keys, and also the order of returned documents. When specifying an index, you have to tell getPage your schema too, so it knows which fields are in the index.

The following query will get the first page of contacts in order of surname, then givenName, because the "name" index is defined as .index("name", ["surname", "givenName"]).

import schema from "./schema";
const { page, indexKeys } = await getPage(ctx, {
	table: "contacts",
	index: "name",
	schema,
});

Pagination with a join
Built-in pagination supports simple joins: if I’m paginating over contacts and each contact has a profilePicId, I can fetch the profile picture for each contact with the pattern described here. However, you can’t do pagination within this join, because the built-in Convex query currently can’t keep track of multiple pagination cursors in one query.

Suppose I want to fetch the first page of contacts, and the first page of emails for each contact. With getPage I can do this:

const {
	page: pageOfContacts,
	indexKeys: contactIndexKeys,
} = await getPage(ctx, { table: "contacts" });
const emails = {};
for (const contact of pageOfContacts) {
	emails[contact.email] = await getPage(ctx, {
		table: "emails",
		index: "address",
		schema,
		startIndexKey: [contact.emailAddress],
		endIndexKey: [contact.emailAddress],
		endInclusive: true,
		absoluteMaxRows: 10,
	});
}
return { pageOfContacts, contactIndexKeys, emails };

You can now fetch subsequent pages of contacts, each with their first page of emails. Or you can fetch subsequent pages of emails for any contact. And this all works because you are tracking the cursors directly.

Jump to a location and scroll up
Infinite scroll is a common interface, but sometimes you want to jump to a later page. If you’re scrolling through your contacts you may want to jump to those with last name starting with “S”. If you’re scrolling through your photos you may want to jump to those from your vacation last year.

Jumping to a location is easy — it’s even supported by built-in Convex pagination via

await ctx.db.query("contacts")
	.withIndex("name", (q)=>q.gte("surname", "S"))
	.paginate(opts);

However, you’re now looking at pages of contacts starting with “S”, and you can’t “scroll up” to see those starting with “R”.

When scrolling up, your query becomes inverted, so it looks like this:

await ctx.db.query("contacts")
	.withIndex("name", (q)=>q.lt("surname", "S"))
	.order("desc")
	.paginate(opts);

You can run this as a separate query, or you can use a single getPage to go in either direction:

const contacts = await getPage(ctx, {
	table: "contacts",
	index: "name",
	schema,
	startIndexKey: ["S"],
	startInclusive: !isScrollingUp,
	order: isScrollingUp ? "asc" : "desc",
});

Stitching the pages together
If you’re fetching multiple pages of data into a reactive client, like a React web page, you’ll want the data to update reactively.

Like any Convex query, pages fetched with getPage and useQueries will automatically re-render when the data updates. However, since pages are initially defined as “the first 100 items” and “the next 100 items after item X”, this can result in gaps or overlaps between pages. This problem is fully described here.

The built-in .paginate() and usePaginatedQuery solve this problem automatically, but getPage does not. Instead, you need to replace the queries after they first load, so “the first 100 items” becomes “the items up to item X”, which then seamlessly joins up with “the next 100 items after item X”. This is the primary purpose of the endIndexKey field passed to getPage.

// Fetch the first page like this:
const {
	indexKeys: indexKeys0,
} = await getPage(ctx, {
	table: "contacts",
});
// Fetch the second page like this:
const {
	page: page1,
	indexKeys: indexKeys1,
} = await getPage(ctx, {
	table: "contacts",
	startIndexKey: indexKeys0[indexKeys0.length - 1],
});
// Re-fetch the first page like this:
const { page: page0 } = await getPage(ctx, {
	table: "contacts",
	endIndexKey: indexKeys0[indexKeys0.length - 1],
});

Suppose initially the 100th contact is John Smith. page1 is defined as the 100 contacts after John Smith. Meanwhile page0 is defined as all contacts up to John Smith, which might grow or shrink as the table changes.

This all sounds complicated to implement, which is why Convex’s built-in pagination handles it for you. This pattern of replacing page queries can also double your function calls and bandwidth usage, which the built-in pagination avoids.

More flexible pagination
With getPage, you take control of your pagination. It’s your data, and your access patterns, so you are best equipped to write optimal queries and hooks for fetching pages.

As you scroll down, you can unsubscribe from pages that are no longer visible.
If you have subscribed to a page and it grows too large, getPage has returned all index keys, so you can use a middle index key to split the page into two.
You can more flexibly filter out a page’s documents, or filter out fields, or join with another table to add fields.
You can choose to either store all index keys, which would tell you if an item at an index position was already loaded, or you can encrypt index keys to hide page boundaries.
Give me the code
Get started writing queries with getPage today, by installing convex-helpers and importing

import { getPage } from "convex-helpers/server/pagination";

```exampleapp.tsx
import { Button } from "@/components/ui/button";
import { useMutation, useQueries, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Doc } from "../convex/_generated/dataModel";
import { ForwardedRef, forwardRef, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { FunctionArgs, FunctionReturnType } from "convex/server";

function App() {
  return (
    <main className="container flex flex-row gap-8">
      <Menu />
      <ContactList />
    </main>
  );
}

function Menu() {
  const makeContact = useMutation(api.contacts.makeRandomContact);
  const count = useQuery(api.contacts.contactCount, {}) ?? 0;
  // Menu bar on the left is a fixed width, buttons positioned in a column.
  return (
    <div className="h-full w-16 flex flex-col gap-4 my-4">
      <p className="text-sm">{count} contacts</p>
      <Button onClick={() => {
            void makeContact({});
          }}>Add</Button>
    </div>
  );
}

function ContactList() {
  // render ContactCard for each contact, in a column layout
  const [clickedLetter, setClickedLetter] = useState<string | undefined>(undefined);

  // Along the right-hand side of the page, have the alphabet in small font, going down next to the list of contacts.
  // The alphabet should be pinned to the right side of the page, and should not scroll with the list of contacts.
  // The alphabet should be on the right side of the page, and the ScrollingContacts should
  // take up the rest of the width.
  return (
    <div className="flex flex-row gap-4 flex-1">
      <ScrollingContacts clickedLetter={clickedLetter} />
      <Alphabet onClick={setClickedLetter} />
    </div>
  );
}

interface PaginatedContacts {
  results: (Doc<"contacts"> | undefined)[];
  scrollDown: () => void;
  scrollUp: () => void;
  scrollToLetter: (letter: string) => void;
}

function usePaginatedContacts(): PaginatedContacts {
  const [queries, setQueries] = useState<[number[], Record<string, { query: typeof api.contacts.contactsPage, args: FunctionArgs<typeof api.contacts.contactsPage> }>]>(
    [
      [0],
      {"0": { query: api.contacts.contactsPage, args: {} }},
    ]
  );
  console.log(`queries: ${JSON.stringify(queries[0])} ${JSON.stringify(queries[1])}`);
  const queryResults = useQueries(queries[1]) as Record<string, FunctionReturnType<typeof api.contacts.contactsPage>>;
  const results = useMemo(() => {
    const results = [];
    for (const queryKey of queries[0]) {
      const queryArg = queries[1]["" + queryKey];
      const result = queryResults["" + queryKey];
      if (!result) {
        results.push(undefined);
      } else if (result instanceof Error) {
        throw result;
      } else {
        if (queryArg.args.endIndexKey === undefined) {
          // Refetch query with a fixed endIndexKey.
          console.log(`refetching query ${queryKey} with fixed endIndexKey`);
          setQueries((prev) => {
            const queryArg = prev[1]["" + queryKey];
            const endIndexKey = result.indexKeys.length > 0 ? result.indexKeys[result.indexKeys.length - 1] : [];
            const startIndexKey = queryArg.args.startIndexKey ?? [];
            const newArgs = queryArg.args.order === "desc" ?
              {
                startIndexKey: endIndexKey,
                endIndexKey: startIndexKey,
                startInclusive: queryArg.args.endInclusive ?? true,
                endInclusive: queryArg.args.startInclusive ?? false,
              } :
              { ...queryArg.args, startIndexKey, endIndexKey };
            console.log(`newArgs: ${JSON.stringify(newArgs)} based on ${JSON.stringify(queryArg.args)} and ${JSON.stringify(result.indexKeys)}`);
            return [prev[0], {...prev[1], [queryKey]: { query: queryArg.query, args: newArgs }}];
          });
        }
        const page = result.page.slice();
        if (queryArg.args.order === "desc") {
          page.reverse();
        }
        results.push(...page);
      }
    }
    return results;
  }, [queryResults, queries]);
  const scrollDown = useCallback(() => {
    setQueries((prev) => {
      console.log("scrolling down");
      const lastQueryKey = prev[0][prev[0].length - 1];
      const lastQuery = prev[1]["" + lastQueryKey];
      const lastArgs = lastQuery.args;
      const lastIndexKey = lastArgs.endIndexKey;
      if (lastIndexKey === undefined) {
        // Last page still loading.
        return prev;
      }
      if (lastIndexKey.length === 0) {
        // At the end.
        return prev;
      }
      const nextQueryKey = prev[0].length;
      return [[...prev[0], nextQueryKey], {
        ...prev[1],
        [nextQueryKey]: { query: lastQuery.query, args: { startIndexKey: lastIndexKey } },
      }];
    });
  }, []);
  const scrollUp = useCallback(() => {
    setQueries((prev) => {
      console.log("scrolling up");
      const firstQueryKey = prev[0][0];
      const firstQuery = prev[1]["" + firstQueryKey];
      const firstArgs = firstQuery.args;
      const firstIndexKey = firstArgs.startIndexKey;
      if (firstIndexKey === undefined || firstIndexKey.length === 0) {
        return prev;
      }
      if (firstArgs.endIndexKey === undefined || firstArgs.order === "desc") {
        // First page still loading or refetching.
        return prev;
      }
      const firstIncluded = firstArgs.startInclusive ?? false;
      const nextQueryKey = prev[0].length;
      return [[nextQueryKey, ...prev[0]], { ...prev[1], [nextQueryKey]: {
        query: firstQuery.query, args: {
          startIndexKey: firstIndexKey,
          startInclusive: !firstIncluded,
          order: "desc",
        } },
      }];
    });
  }, []);
  const scrollToLetter = useCallback((letter: string) => {
    // NOTE this discards previous queries, which is not great, but it's fine for now.
    console.log("scrolling to letter", letter);
    setQueries([
      [0],
      { [0]: { query: api.contacts.contactsPage, args: { startIndexKey: [letter], startInclusive: true } }},
    ]);
  }, []);

  return {
    results,
    scrollDown,
    scrollUp,
    scrollToLetter,
  };
}

function ScrollingContacts({clickedLetter}: {clickedLetter?: string}) {
  const {
    results: contacts,
    scrollDown,
    scrollUp,
    scrollToLetter,
  } = usePaginatedContacts();

  const scrollRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    // Set the initial scrollTop to 10 if it's too low.
    // This ensures that when we "scrollUp", the items are inserted above
    // the current viewport, instead of shifting everything down.
    if (scrollRef.current && scrollRef.current.scrollTop < 60) {
      scrollRef.current.scrollTo(0, 60);
    }
  }, [scrollRef, contacts]);

  const loader = useRef(null);
  // When the last contact is on screen, scrollDown
  const loaderIndex = contacts.length - 1;
  useEffect(() => {
    const handleObserver = (entries: any) => {
      const target = entries[0];
      console.log("handle observer triggered", target.isIntersecting);
      if (target.isIntersecting) {
        scrollDown();
      }
    };
    const observer = new IntersectionObserver(handleObserver);
    if (loader.current) {
      observer.observe(loader.current);
      console.log("intersection observer created at index", loaderIndex);
    }
    return () => observer.disconnect();
  }, [loader, loaderIndex, scrollDown]);
  
  // When the first contact is on screen, scrollUp
  const firstContactId = contacts[0]?._id ?? null;
  const upLoader = useRef(null);
  useEffect(() => {
    const handleObserver = (entries: any) => {
      const target = entries[0];
      console.log("handle scrollUp observer triggered", target.isIntersecting);
      if (target.isIntersecting) {
        scrollUp();
      }
    };
    const observer = new IntersectionObserver(handleObserver);
    if (upLoader.current) {
      observer.observe(upLoader.current);
      console.log("intersection observer for scrolling up created at index 0");
    }
    return () => observer.disconnect();
  }, [upLoader, scrollUp, firstContactId]);

  const [prevActiveLetter, setPrevActiveLetter] = useState<string | undefined>(undefined);
  useEffect(() => {
    if (clickedLetter && prevActiveLetter !== clickedLetter) {
      scrollToLetter(clickedLetter);
      setPrevActiveLetter(clickedLetter);
    }
  }, [clickedLetter, scrollToLetter, prevActiveLetter]);
  return (
    <div
      className="max-h-[100vh] overflow-y-auto flex flex-col flex-grow p-1"
      ref={scrollRef}
    >
      {contacts.map((contact, i) => {
        if (!contact) {
          return <div key={i} className="bg-gray-800 rounded-lg p-4 m-1">Loading...</div>;
        }
        return <ContactCard
          key={contact._id}
          contact={contact}
          ref={i === 0 ? upLoader : i === loaderIndex ? loader : null}
        />;
      })}
    </div>
  );
}

function Alphabet({onClick}: 
  {onClick: (letter: string) => void}
) {
  // The alphabet should be a column of letters, each letter should be a button that, when clicked, scrolls the list of contacts to the first contact whose surname starts with that letter.
  // Instead of normal button styling, the buttons should have blue text and no background.
  // They should also be small and vertically condensed.
  return (
    <div className="flex flex-col gap-1">
      {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map((letter) => (
        <button
          key={letter}
          className="text-blue-500 bg-transparent text-sm"
          onClick={() => {
            onClick(letter);
          }}
        >{letter}</button>
      ))}
    </div>
  );
}

const ContactCard = forwardRef((
  {contact}: {contact: Doc<"contacts">},
  ref: ForwardedRef<HTMLDivElement>,
) => {
  return (
    <div ref={ref} className="bg-gray-800 rounded-lg p-4 m-1">
      <p className="text-lg">{contact.givenName} <span className="font-semibold">{contact.surname}</span></p>
      <p className="text-sm text-gray-400">{contact.phone}</p>
    </div>
  );
});

export default App;
```