---
description: use when you get an error of Type 'OrderedQuery  how to fix convex sorting lints
globs: *.*
alwaysApply: false
---
(Query with Pagination & Filters)

Schemas for filtering and pagination:

```typescript
// zod/domains-schema.ts
export const DomainFilterSchema = z.object({
  searchText: z.string().optional(),
  status: z.enum(["active", "pending", "archived"]).optional(),
  owner_id: zid("users").optional(),
});

export const PaginationSchema = z.object({
  limit: z.number().optional().default(50),
  sortBy: z.enum(["_creationTime", "name", "updated_at"]).optional(),
  sortDirection: z.enum(["asc", "desc"]).optional().default("desc"),
  cursor: z.any().optional()
});
```


in Convex, once you call a method like .order() or .paginate(), the query is finalized and you can't chain more methods on it.
The solution is to build the complete query chain in one go for each specific case, rather than trying to modify a query object incrementally. This approach:
Creates separate query chains for each sorting condition
Executes the pagination directly at the end of each chain
Avoids reassigning the query variable after finalizing operations
   
   // Apply sorting with the correct index
    let results;
    if (sortBy === "name") {
      results = await ctx.db
        .query("tag_types")
        .withIndex("by_name", q => q)
        .order(sortDirection)
        .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
    } else if (sortBy === "_creationTime") {
      results = await ctx.db
        .query("tag_types")
        .order(sortDirection)
        .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
    } else if (sortBy === "updated_at") {
      results = await ctx.db
        .query("tag_types")
        .withIndex("by_updated", q => q)
        .order(sortDirection)
        .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
    } else {
      // Default to _creationTime if sortBy is invalid
      results = await ctx.db
        .query("tag_types")
        .order(sortDirection)
        .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
    }


    Below is an explanation that expands on how you can integrate filtering with pagination in Convex using the new function language. It describes why you must build the full query chain up front and how to incorporate both filtering and sorting so that your query is both efficient and type‐safe.

⸻

Overview

In Convex’s new function language, once you finalize a query by calling methods like .order() or .paginate(), the query is “sealed” and you can no longer add extra methods to modify it. This design means that any filters, sorting conditions, or pagination options must be specified in a single chain before finalizing the query. Doing so helps ensure that:
	•	Filters are applied efficiently.
You should use the built-in .filter() (or even better, combine with indexes) to narrow down the result set, rather than filtering results after calling .collect().
	•	Sorting uses the correct index.
When you sort (for example, by “name” or “updated_at”), you should use .withIndex() if a specific index is available. This guarantees that the query uses the appropriate index for optimal performance.
	•	Pagination is reliable and reactive.
The pagination function finalizes the query so that all pages remain adjacent and do not overlap or have gaps, even if the underlying data changes.

⸻

Building a Complete Query Chain

Since you cannot change a query after calling .order() or .paginate(), you must build the complete chain—starting from filtering, then sorting (with any required indexes), and finally applying pagination. For example, suppose you have a set of filtering criteria (like search text, status, and an owner ID) and a separate pagination object.

You might start with a base query and then conditionally attach filters:

let baseQuery = ctx.db.query("tag_types");

// Apply filtering conditions from DomainFilterSchema
if (filter.searchText) {
  // Use a filtering predicate (e.g., checking if the tag name contains the search text)
  baseQuery = baseQuery.filter(q => q.contains(q.field("name"), filter.searchText));
}
if (filter.status) {
  baseQuery = baseQuery.filter(q => q.eq(q.field("status"), filter.status));
}
if (filter.owner_id) {
  baseQuery = baseQuery.filter(q => q.eq(q.field("owner_id"), filter.owner_id));
}

Then, based on the sorting parameter (from your PaginationSchema), build separate query chains:

let results;
if (sortBy === "name") {
  results = await baseQuery
    .withIndex("by_name", q => q)
    .order(sortDirection)
    .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
} else if (sortBy === "_creationTime") {
  results = await baseQuery
    .order(sortDirection)
    .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
} else if (sortBy === "updated_at") {
  results = await baseQuery
    .withIndex("by_updated", q => q)
    .order(sortDirection)
    .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
} else {
  // Fallback: default sorting by creation time
  results = await baseQuery
    .order(sortDirection)
    .paginate({ cursor: pagination.cursor, numItems: pagination.limit || 50 });
}

Notice that all filtering and sorting (including the proper use of indexes) is done before the .paginate() call. This ensures that when the query is executed, it is already fully constructed. Attempting to modify the query after pagination won’t work because the query has been finalized.

⸻

Best Practices
	•	Plan the Chain Upfront:
Because Convex finalizes the query with .order() or .paginate(), you should decide on all filtering, sorting, and pagination options in one go. This might mean creating separate query chains for different sorting conditions.
	•	Use Indexes for Efficiency:
When filtering or sorting on fields that are indexed (for example, “by_name” or “by_updated”), always use .withIndex() so that Convex can leverage its internal data structures for fast lookups and ordering [ ￼].
	•	Validate Your Inputs:
Using Zod schemas (as seen in your DomainFilterSchema and PaginationSchema) ensures that the parameters passed into your queries are correctly typed and valid. This reduces runtime errors and improves developer experience.
	•	Combine Filters with Pagination:
If you need to filter your data as well as paginate it, make sure the filters are applied in the query chain before calling .paginate(). This way, only the matching documents are scanned for pagination [ ￼].

⸻

Conclusion

To sum up, when working with Convex’s new function language for queries:
	1.	Build the entire query chain in one go—start with any filtering using .filter(), apply the appropriate sorting (using indexes with .withIndex() if needed), and finally call .paginate() to get your results.
	2.	Avoid reassigning or modifying the query after it has been finalized with a terminal operation (like .order() or .paginate()).
	3.	Use Zod schemas to ensure that both filtering and pagination options are validated and type-safe.

This approach guarantees that your queries are efficient, that the correct indexes are used, and that your pagination remains consistent and reactive, even as the underlying data changes.

Feel free to adapt the example above to your specific use case, and let me know if you need further details on any step!