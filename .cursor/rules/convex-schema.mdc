---
description: 
globs: 
alwaysApply: false
---
Using the Id<TableName> type from Convex provides several benefits:
Always Import the Id Type: Import the Id type from the generated data model:
   import { Id } from "../convex/_generated/dataModel";

   Consistent Usage: Use the Id type consistently throughout your application, including in:
Function arguments
Return types
Interface and type definitions
Zod schemas


- do NOT use string types for IDs