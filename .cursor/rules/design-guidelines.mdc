---
description: our design guidelines and principles redesign
globs: 
alwaysApply: false
---
# <PERSON><PERSON><PERSON> Digital Chief of Staff: Design Principles

## Introduction

Fojo is a Digital Chief of Staff platform that operationalizes excellence for service providers. We are creating a new category of software that bridges the critical gap between knowing what excellence looks like and executing it consistently.

**Core Philosophy**: We transform service excellence from an aspiration dependent on individual heroics into a systematic reality. By amplifying human intelligence—providing context, surfacing patterns, and suggesting actions—we make operational excellence the default rather than the exception. Like a trusted advisor, we organize information intuitively and reduce cognitive load, while always leaving final decisions to users.


## Key Principles
## 5-Second Clarity Requirements
- Each page must communicate its primary purpose within 5 seconds
- Overview pages: Users immediately know which items to click for details
- Detail pages: Primary action is instantly obvious (start, approve, question)
- Dashboard pages: Required actions are clearly highlighted
- Navigation: Users always know where they are and how to proceed
- Every step in user journeys is clearly guided, never leaving users lost

### 1. Cognitive Organization
- Groups items based on logical relationships and impact levels
- Maintains intuitive hierarchies that mirror human thought
- Considers magnitude, risk, and complexity in organization
- Continuously refines grouping logic based on user behavior and feedback
- Creates intuitive groupings that mirror human thought processes
- Adapts to domain-specific patterns
- **Ensures primary page purpose is instantly recognizable through visual hierarchy**

### 2. Intelligent Prioritization
- Assesses priority based on impact, time sensitivity, and strategic importance
- Surfaces urgent items needing immediate attention
- Provides clear rationale for prioritization decisions
- **Highlights primary actions with distinctive visual treatment**
- **Positions critical elements where users naturally look first**

### 3. Proactive Insights & Pattern Recognition
- Actively highlights patterns and anomalies users might miss
- Interprets data rather than just displaying it
- Surfaces meaningful correlations across different domains
- Surfaces relevant insights without users having to ask
- Delivers meaningful and actionable notifications
- Anticipates user needs based on role and context
- **Connects insights directly to available actions**

### 4. Temporal Continuity
- Maintains thinking threads across time
- Clearly shows what came before, what's happening now, and what's coming
- Helps users quickly understand changes after absence
- **Provides visual cues to orient users immediately upon page load**

### 5. Guided Agency
- Suggests actions without forcing them
- Provides clear rationale for suggestions
- Makes executing suggested actions simple
- **Ensures primary actions are immediately visible and accessible**
- **Uses directive language for calls-to-action**

### 6. Human Centered Design
- Maintains logical work streams to prevent context switching
- Optimizes cognitive load through thoughtful information presentation
- Respects user decision-making authority at all times
- **Creates intuitive page flows that guide users from overview to action**
- **Eliminates confusion about what to do next at every step**

## Design Implementation Checklist
- [ ] Uses clear visual hierarchy for priority
- [ ] Includes insight cards with context and suggested actions
- [ ] Provides accessible context panels with historical information
- [ ] Offers one-click execution of suggested actions
- [ ] Visualizes connections between related items
- [ ] Shows progress and temporal relationships clearly
- [ ] Balances system suggestions with user autonomy
- [ ] Provides explanations for system-suggested organization
- [ ] **Primary page purpose is communicated through prominent visual elements**
- [ ] **Main actions use distinctive shadcn components for immediate recognition**
- [ ] **Page layout guides the eye to the most important elements first**
- [ ] **Empty states provide clear guidance on first actions**

## Anti-Patterns to Avoid
- [ ] Requiring manual categorization or prioritization
- [ ] Mixing items of vastly different magnitude or impact
- [ ] Presenting raw data without interpretation
- [ ] Overwhelming with too many suggestions
- [ ] Hiding reasoning behind suggestions
- [ ] Forcing workflows without explanation
- [ ] **Pages with no obvious primary action**
- [ ] **Equal visual weight for all page elements**
- [ ] **Requiring users to hunt for what to do next**
- [ ] **Cluttered interfaces that obscure the page's purpose**
- [ ] **Technical jargon in action buttons or navigation**