---
description: when you get a lint error on handler, follow this guide
globs: 
alwaysApply: false
---
This is an schema error, in whatever [domain] we are in, look for the schema definition in '../zod/[domain]-schema';
```
└── 📁zod
    └── auditLog-schema.ts
    └── bills-schema.ts
    └── convex-auth.d.ts
    └── decisions-schema.ts
    └── directory-schema.ts
    └── documents-schema.ts
    └── lineItems-schema.ts
    └── meeting-notes-schema.ts
    └── project-updates-schema.ts
    └── projects-schema.ts
    └── reports-schema.ts
    └── tags-schema.ts
    └── tagTypes-schema.ts
    └── tasks-schema.ts
    └── users-schema.ts
```