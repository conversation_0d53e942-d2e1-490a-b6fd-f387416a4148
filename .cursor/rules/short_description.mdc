---
description: patterns to follow when implementing AI writing short_descriptions 
globs: 
alwaysApply: false
---
# Guide: Ideal Pattern for Triggered Automatic Field Updates (e.g., Short Description Generation)

This guide outlines the recommended, highly reusable pattern for implementing automatic updates to a target field based on changes to a source field within the FOJO Convex backend. It prioritizes scalability, consistency, and minimal code duplication, suitable for scenarios like AI-driven `short_description` generation across many entities.

## Core Principles

1.  **Generality:** Use generic functions (mutations, actions) capable of operating on different tables and fields.
2.  **Configuration:** Define the relationship between source changes and target updates declaratively where possible.
3.  **Reusability:** Create minimal, focused functions for specific tasks (AI call, DB update).
4.  **Consistency:** Apply the exact same flow for every source/target field pair.

## Components of the Ideal Pattern

### 1. Schema Definition (`convex/schema.ts`)
- Both the source and target field will already exist in the schema. The user has already added them. 

2. Generic Field Update Mutation (convex/utils/dbUtils.ts or similar)
Purpose: A single mutation to update any field on any document. Uses zMutation with Zod schemas for args.
Inputs: tableName, documentId, fieldName, value.
Action: Implement this reusable mutation using Zod for args definition.

```typescript 
// convex/utils/dbUtils.ts (or a suitable location)
import { z } from 'zod';
import { zMutation } from '../functions'; // Use the project's zMutation setup
import { Id, TableNames } from '../_generated/dataModel'; // Import necessary types

// Zod schema for the arguments
const UpdateAnyFieldArgsSchema = z.object({
  tableName: z.string().min(1), // Table name must be provided
  documentId: z.string().min(1), // Document ID passed as string
  fieldName: z.string().min(1).refine(name => !name.startsWith('_'), {
    message: "Cannot update internal fields starting with '_'",
  }),
  value: z.any(), // Value type can be refined if needed, z.any() for max flexibility
});

export const updateAnyField = zMutation({
  args: UpdateAnyFieldArgsSchema, // Use the Zod schema here
  // No specific output schema needed unless returning data
  handler: async (ctx, args) => {
    // Args are already validated by Zod by the time the handler runs

    // Validate tableName exists in the schema (runtime check)
    if (!(ctx.db as any)._tables.has(args.tableName)) {
      throw new Error(`Invalid table name: ${args.tableName}`);
    }

    // Cast the string ID to the specific table ID type.
    const typedId = ctx.db.normalizeId(args.tableName as TableNames, args.documentId);
    if (!typedId) {
       throw new Error(`Invalid ID format for table ${args.tableName}: ${args.documentId}`);
    }

    try {
      await ctx.db.patch(typedId, { [args.fieldName]: args.value });
      console.log(`Updated field '${args.fieldName}' for ${args.tableName} ${args.documentId}`);
    } catch (error) {
      console.error(`Error updating field '${args.fieldName}' for ${args.tableName} ${args.documentId}:`, error);
      // Re-throw or handle as needed
      throw error;
    }
  },
});
```

3. Core AI Generation Action (convex/actions/aiUtils.ts or similar)
Purpose: Focused action for AI interaction. Uses zAction with Zod schemas.
Inputs: sourceText, promptSlug.
Outputs: Generated text string.
Error Handling: Throws specific errors (no fallback).

```typescript 
// convex/actions/aiUtils.ts (or a suitable location)
import { z } from 'zod';
import { zAction } from '../functions'; // Use the project's zAction setup
import { api } from '../_generated/api';
import { ConvexError } from 'convex/values';
import { google } from "@ai-sdk/google";
import { generateText } from "ai";

// Zod schema for arguments
const GenerateDescriptionArgsSchema = z.object({
  sourceText: z.string(),
  promptSlug: z.string().min(1),
});

// Zod schema for the output (a non-empty string)
const GeneratedDescriptionOutputSchema = z.string().min(1, "AI returned empty response.");

export const generateDescriptionWithPrompt = zAction({
  args: GenerateDescriptionArgsSchema, // Use Zod schema for args
  output: GeneratedDescriptionOutputSchema, // Use Zod schema for output validation
  handler: async (ctx, args): Promise<string> => {
    // Args validated by Zod

    // 1. Fetch the specified prompt
    const promptRecord = await ctx.runQuery(api.prompts.getPromptBySlug, {
      slug: args.promptSlug,
    });

    if (!promptRecord || !promptRecord.prompt_text) {
      console.error(`Prompt not found for slug: ${args.promptSlug}`);
      throw new ConvexError({
        message: `Required prompt not found: ${args.promptSlug}`,
        code: "PROMPT_NOT_FOUND",
        data: { slug: args.promptSlug },
      });
    }
    const promptText = promptRecord.prompt_text;

    // 2. Initialize AI Model
    const model = google("gemini-2.5-flash-preview-04-17");

    // 3. Craft final prompt and call AI
    const finalPrompt = `${promptText}\n\n${args.sourceText}`;
    console.log(`Generating description with prompt: ${args.promptSlug}`);

    try {
      const { text: aiResponse } = await generateText({
        model,
        prompt: finalPrompt,
      });

      // Output is automatically validated against GeneratedDescriptionOutputSchema by zAction
      return aiResponse.trim();

    } catch (error) {
      console.error(`AI generation failed for prompt ${args.promptSlug}:`, error);
      throw new ConvexError({
        message: error instanceof Error ? error.message : "AI generation failed",
        code: "AI_GENERATION_ERROR",
        data: { promptSlug: args.promptSlug, errorInfo: String(error) },
      });
    }
  },
});
```

4. Generic Orchestrator Action (convex/actions/orchestrators.ts or similar)
Purpose: Coordinates the process. Uses zAction with Zod schemas.
Inputs: tableName, documentId, sourceText, targetFieldName, promptSlug.
Action: Implement using Zod for args and output.

```typescript 
// convex/actions/orchestrators.ts (or a suitable location)
import { z } from 'zod';
import { zAction } from '../functions'; // Use the project's zAction setup
import { api } from '../_generated/api';
import { ConvexError } from 'convex/values';

// Zod schema for arguments
const GenerateAndStoreFieldArgsSchema = z.object({
  tableName: z.string().min(1),
  documentId: z.string().min(1), // Passed as string
  sourceText: z.string(), // Source text can be empty, handled by AI action if needed
  targetFieldName: z.string().min(1).refine(name => !name.startsWith('_'), {
    message: "Target field cannot be an internal field starting with '_'",
  }),
  promptSlug: z.string().min(1),
});

// Zod schema for output
const GenerateAndStoreFieldOutputSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  description: z.string().optional(), // The generated description, if successful
});

export const generateAndStoreField = zAction({
  args: GenerateAndStoreFieldArgsSchema, // Use Zod schema for args
  output: GenerateAndStoreFieldOutputSchema, // Use Zod schema for output
  handler: async (ctx, args): Promise<z.infer<typeof GenerateAndStoreFieldOutputSchema>> => {
    // Args validated by Zod
    console.log(`Orchestrating field update for ${args.tableName} ${args.documentId}, target: ${args.targetFieldName}`);
    try {
      // 1. Generate the description
      const generatedText = await ctx.runAction(api.actions.aiUtils.generateDescriptionWithPrompt, {
        sourceText: args.sourceText,
        promptSlug: args.promptSlug,
      });

      // 2. Store the generated description
      await ctx.runMutation(api.utils.dbUtils.updateAnyField, {
        tableName: args.tableName,
        documentId: args.documentId,
        fieldName: args.targetFieldName,
        value: generatedText,
      });

      console.log(`Successfully generated and stored ${args.targetFieldName} for ${args.tableName} ${args.documentId}`);
      // Output validated by Zod
      return {
        success: true,
        message: `Successfully generated and stored ${args.targetFieldName}`,
        description: generatedText,
      };

    } catch (error) {
      console.error(`Orchestration failed for ${args.tableName} ${args.documentId}:`, error);

      // Handle specific errors gracefully
      if (error instanceof ConvexError && error.data?.code === "PROMPT_NOT_FOUND") {
        return { success: false, message: `Processing skipped: Prompt '${args.promptSlug}' not found.` };
      }
      if (error instanceof ConvexError && error.data?.code === "AI_GENERATION_ERROR") {
        return { success: false, message: `Processing failed: AI generation error for prompt '${args.promptSlug}'.` };
      }

      // Handle generic or database update errors
      return {
        success: false,
        message: error instanceof Error ? error.message : `Failed to generate or store ${args.targetFieldName}`,
      };
    }
  },
});
```

5. Trigger Registration (convex/functions.ts)
Purpose: For each table/field pair, register a trigger.
Action: Trigger detects change, schedules the single generateAndStoreField action with specific parameters. No Zod usage here, as triggers operate directly on DB changes.

```typescript 
// convex/functions.ts
import { Triggers } from 'convex-helpers/server/triggers';
import { DataModel, Id, TableNames } from './_generated/dataModel'; // Ensure TableNames is imported
import { api } from './_generated/api';
import { DatabaseWriter, Scheduler } from './_generated/server';

const triggers = new Triggers<DataModel>();

// --- Reusable Trigger Logic Helper ---
async function scheduleFieldGeneration(
  ctx: { scheduler: Scheduler },
  tableName: TableNames, // Use TableNames type for better safety
  documentId: Id<any>,
  sourceText: string | undefined | null,
  targetFieldName: string,
  promptSlug: string
) {
  if (!sourceText || String(sourceText).trim().length === 0) {
    console.log(`Skipping generation for ${tableName} ${documentId}: Empty source text.`);
    return;
  }
  const sourceTextStr = String(sourceText);

  console.log(`${tableName} source field changed for ${documentId}, scheduling ${targetFieldName} generation with prompt ${promptSlug}`);
  try {
    // Pass documentId as string to the action, as defined in its Zod schema
    await ctx.scheduler.runAfter(0, api.actions.orchestrators.generateAndStoreField, {
      tableName: tableName,
      documentId: documentId.toString(), // Convert Id object to string
      sourceText: sourceTextStr,
      targetFieldName: targetFieldName,
      promptSlug: promptSlug,
    });
    console.log(`Scheduled ${targetFieldName} generation for ${tableName}: ${documentId}`);
  } catch (error) {
    console.error(`Error scheduling ${targetFieldName} generation for ${tableName} ${documentId}:`, error);
  }
}

// --- Trigger Registrations ---

// Example for TableA
triggers.register("tableA", async (ctx, change) => {
  const sourceField = 'source_field_A';
  const targetField = 'target_field_A';
  const prompt = 'prompt-for-A';

  if (
    (change.operation === "insert" && change.newDoc?.[sourceField]) ||
    (change.operation === "update" && change.oldDoc?.[sourceField] !== change.newDoc?.[sourceField] && change.newDoc?.[sourceField])
  ) {
    await scheduleFieldGeneration(
      ctx,
      "tableA", // Pass table name as string literal matching TableNames
      change.id,
      change.newDoc[sourceField],
      targetField,
      prompt
    );
  }
});

// Example for TableB
triggers.register("tableB", async (ctx, change) => {
  const sourceField = 'source_field_B';
  const targetField = 'target_field_B';
  const prompt = 'short-description'; // Generic prompt

  if (
    (change.operation === "insert" && change.newDoc?.[sourceField]) ||
    (change.operation === "update" && change.oldDoc?.[sourceField] !== change.newDoc?.[sourceField] && change.newDoc?.[sourceField])
  ) {
     await scheduleFieldGeneration(
      ctx,
      "tableB", // Pass table name as string literal
      change.id,
      change.newDoc[sourceField],
      targetField,
      prompt
    );
  }
});

// ... Register triggers for all other necessary tables ...

// --- Exports (zMutation, etc.) ---
// Ensure zMutation uses triggers.wrapDB
import { zCustomMutation, zCustomQuery, zCustomAction } from 'convex-helpers/server/zod';
import { customCtx, NoOp } from 'convex-helpers/server/customFunctions';
import { query, mutation, action } from './_generated/server'; // Import standard functions

export const zQuery = zCustomQuery(query, NoOp);
export const zMutation = zCustomMutation(mutation, customCtx(triggers.wrapDB));
export const zAction = zCustomAction(action, NoOp);
```

Flow Summary (Unchanged Logic, Zod Interfaces)
DB Change (source_field_A in tableA).
tableA trigger fires.
Trigger calls scheduleFieldGeneration helper.
Helper schedules generateAndStoreField action (args match Zod schema: tableName, documentId as string, etc.).
generateAndStoreField action runs (input args validated by Zod).
Calls generateDescriptionWithPrompt action (args validated by Zod).
generateDescriptionWithPrompt fetches prompt, calls AI, returns text (output validated by Zod).
generateAndStoreField receives text.
Calls updateAnyField mutation (args validated by Zod).
updateAnyField patches tableA document.
generateAndStoreField returns result (output validated by Zod).
Advantages (Maintained & Enhanced)
High Reusability: Core logic implemented once.
Scalability: Adding new updates involves schema check + trigger registration + prompt (if needed).
Maintainability: Logic changes are localized.
Consistency: All updates follow the same flow.
Type Safety: Zod provides robust runtime validation for function arguments and outputs, catching errors early.
Clear Interfaces: Zod schemas clearly define the expected inputs and outputs for each function.