---
description: 
globs: 
alwaysApply: false
---
```xml
<convex_guidelines>

<best_practices>

<await_all_promises>
<why>
Convex functions use async/await. If you don’t await all promises (e.g. ctx.scheduler.runAfter, ctx.db.patch), you risk missing errors or failing to run the intended operations.
</why>
<how>
Use the "no-floating-promises" ESLint rule and ensure every promise is awaited or otherwise handled.
</how>
</await_all_promises>

<avoid_filter_on_db_queries>
<why>
Filtering large datasets in code (or with .filter) loads all documents first and then filters them, which can be inefficient if the number of documents is large. Using .withIndex or .withSearchIndex is faster and more scalable. For smaller data sets (fewer than ~1000 documents), filtering in plain TypeScript code (after collecting) can be simpler to write and maintain.
</why>
<how>
1. Use .withIndex or .withSearchIndex for performance.
2. If you decide not to use .withIndex, you can collect results first and then filter in code for clarity (only if the dataset is guaranteed to be small).
</how>
<examples>
```typescript
// ❌ Using .filter in a query
const tomsMessages = await ctx.db
  .query("messages")
  .filter((q) => q.eq(q.field("author"), "Tom"))
  .collect();

// ✅ Use an index for larger data sets
const tomsMessages = await ctx.db
  .query("messages")
  .withIndex("by_author", (q) => q.eq("author", "Tom"))
  .collect();

// ✅ Filter in code only if data set is small
const allMessages = await ctx.db.query("messages").collect();
const tomsMessages = allMessages.filter(m => m.author === "Tom");
```
</examples>
</avoid_filter_on_db_queries>

<only_collect_small_results>
<why>
.collect returns all matching documents, which can be costly in bandwidth and cause re-runs if any document changes. For potentially large or unbounded results, use an index to narrow results or use .paginate instead of .collect.
</why>
<how>
1. Use .withIndex to narrow results.
2. Use .paginate for unbounded lists.
3. Consider using a limit, or denormalizing data to avoid collecting large sets.
</how>
<examples>
```typescript
// ❌ Potentially unbounded results
const allMovies = await ctx.db.query("movies").collect();

// ✅ Filter using an index to get fewer documents
const spielbergMovies = await ctx.db
  .query("movies")
  .withIndex("by_director", (q) => q.eq("director", "Steven Spielberg"))
  .collect();

// ✅ Use pagination for large lists
const watchedMovies = await ctx.db
  .query("watchedMovies")
  .withIndex("by_user", (q) => q.eq("user", userId))
  .order("desc")
  .paginate({ limit: 20 });
```
</examples>
</only_collect_small_results>

<check_for_redundant_indexes>
<why>
Indexes that are a prefix of another index are often redundant. Reducing the number of indexes lowers storage overhead and speeds up writes.
</why>
<how>
Review your schema for indexes that are likely supersets. Only keep indexes if you rely on the ordering or multi-property constraints.
</how>
<examples>
```typescript
// ❌ Two indexes when one suffices
.index("by_team", ["team"])
.index("by_team_and_user", ["team", "user"])

// ✅ If sorting by user is never needed independently of team
// just keep "by_team_and_user"
.index("by_team_and_user", ["team", "user"])
```
</examples>
</check_for_redundant_indexes>

<use_argument_validators>
<why>
Public functions can be called by anyone on the Internet. Argument validators ensure you only accept the shape of data you expect, protecting your app from malformed or malicious requests.
</why>
<how>
Always supply args and returns in all query, mutation, and action definitions. Use v.id("tableName"), v.string(), v.object({}), etc. for type-safe arguments.
</how>
<examples>
```typescript
// ❌ No validator; can accidentally patch any table
export const updateMessage = mutation({
  handler: async (ctx, { id, update }) => {
    await ctx.db.patch(id, update);
  },
});

// ✅ With argument validators
export const updateMessage = mutation({
  args: {
    id: v.id("messages"),
    update: v.object({
      body: v.optional(v.string()),
      author: v.optional(v.string()),
    }),
  },
  handler: async (ctx, { id, update }) => {
    await ctx.db.patch(id, update);
  },
});
```
</examples>
</use_argument_validators>

<use_access_control>
<why>
All public functions are callable by anyone. If your logic should only be accessible by certain users, you must enforce checks with ctx.auth.getUserIdentity() or similar logic.
</why>
<how>
1. Verify users with ctx.auth before proceeding.
2. Restrict operations to those that the current user can legitimately perform.
3. Break down large functions into more granular ones if they have different security requirements.
</how>
<examples>
```typescript
// ❌ No checks; any caller can update the team
export const updateTeam = mutation({
  args: { id: v.id("teams"), update: v.object({ name: v.string() }) },
  handler: async (ctx, { id, update }) => {
    await ctx.db.patch(id, update);
  },
});

// ✅ Ensure the caller is authenticated and authorized
export const updateTeam = mutation({
  args: { id: v.id("teams"), update: v.object({ name: v.string() }) },
  handler: async (ctx, { id, update }) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error("Unauthorized");
    const isMember = /* check membership */ true;
    if (!isMember) throw new Error("Unauthorized");
    await ctx.db.patch(id, update);
  },
});
```
</examples>
</use_access_control>

<only_schedule_and_ctx_run_internal_functions>
<why>
Public functions are exposed to the public and require stricter security. Internal functions can skip certain validations because they are only callable from within Convex. Scheduling and function calls (ctx.runQuery, ctx.runMutation, ctx.scheduler.runAfter) should always target internal functions.
</why>
<how>
1. Mark private functions with internalQuery, internalMutation, internalAction.
2. Use internal.YourFileName.yourFunctionName in scheduled or nested calls.
</how>
<examples>
```typescript
// ❌ Scheduling a public mutation
crons.daily("daily reminder", { hourUTC: 17 }, api.messages.sendMessage, {...});

// ✅ Scheduling an internal mutation
crons.daily("daily reminder", { hourUTC: 17 }, internal.messages.sendInternalMessage, {...});
```
</examples>
</only_schedule_and_ctx_run_internal_functions>

<use_helper_functions>
<why>
Most logic can be written as plain TypeScript helpers, keeping query/mutation/action wrappers minimal. This approach eases testing, refactoring, and code sharing between internal and public functions.
</why>
<how>
1. Put core logic in a separate directory (e.g. convex/model/...).
2. Reference those helpers from your public or internal Convex functions.
</how>
<examples>
```typescript
// ✅ Minimal public function, with real logic in helpers
export const listMessages = internalQuery({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, { conversationId }) => {
    return await Conversations.listMessages(ctx, conversationId);
  },
});
```
</examples>
</use_helper_functions>

<use_runaction_only_with_different_runtime>
<why>
runAction is more expensive than a plain function call. Use it only when you need Node.js environment features from a function running in the Convex environment (e.g. using a library that requires Node).
</why>
<how>
1. Prefer plain TypeScript helpers for logic that is within the same runtime.
2. Use runAction only if you truly need code in an action from a query/mutation that can’t run in that environment.
</how>
</use_runaction_only_with_different_runtime>

<avoid_sequential_ctx_runs_in_actions>
<why>
Each ctx.runQuery or ctx.runMutation in an action runs in a separate transaction. If you do multiple calls in a row, your data may become inconsistent between calls. If you want atomicity or consistent reads, combine them into a single function call.
</why>
<how>
1. Combine reads and writes into a single query/mutation if you need consistency.
2. If you have to process a large set in multiple transactions, do so intentionally (e.g., for migrations).
</how>
<examples>
```typescript
// ❌ Might see inconsistent data if the team changes between calls
const team = await ctx.runQuery(internal.teams.getTeam, { teamId });
const owner = await ctx.runQuery(internal.teams.getTeamOwner, { teamId });

// ✅ Combine logic in a single query
const teamAndOwner = await ctx.runQuery(internal.teams.getTeamAndOwner, { teamId });
```
</examples>
</avoid_sequential_ctx_runs_in_actions>

<use_ctx_run_sparingly_in_queries_and_mutations>
<why>
While queries and mutations share a transaction, ctx.runQuery and ctx.runMutation calls still incur overhead. Often, a simple TypeScript helper can replace them.
</why>
<how>
1. Use plain TypeScript helper functions when you don’t need a separate transaction or partial rollback.
2. If you need partial rollback on error, then multiple calls may be justified.
</how>
</use_ctx_run_sparingly_in_queries_and_mutations>

</best_practices>

</convex_guidelines>
```