---
description: Query and Mutation Checklist
globs: 
alwaysApply: false
---
Checklist to check each operations file in convex. In your reply make sure to put a greencheckmark next to each one as you check it. 

General Guidelines
	[]	Chain query builder methods (like .filter(), .order(), .take()) in a single expression.
	[]	Use .order("asc" | "desc") only.
	[]	Use Zod schemas for input validation (no v.*).
	[]	Use zid("tableName") for Convex IDs.
	[]	Handle single and bulk inserts/updates/deletes in the same mutation if needed.
	[]	For large queries, define an index in convex/schema.ts and use .withIndex().
	[]	Provide optional output validation in zQuery/zMutation.
	[]	Rely on z.infer for TypeScript type safety.
	[]	Only use the default convex fields _id and _creationTime. do not create your own
	[]	Nameing convention: Use the action (create, list) + Domain (decision, project, tag). so listDecisions, createTags
	[]	Args are an object with named parameters

Mutations Guidelines
	[]	Structure bill update mutations with a clear separation between the identifier (id for single updates or ids for bulk updates) and the changes (updates object containing only the fields to be modified).
	[]	When implementing update handlers, validate that either id or ids is provided (but not both), and ensure the updates object contains only valid, schema-compliant fields.
	[]	For client-side update calls, consistently use the format { updates: { id: billId, updates: { field1: value1, field2: value2 } } } to maintain predictable API patterns.
	[]	Keep Zod schemas in ./zod/.
	[]	Keep database schema (v.*) in convex/schema.ts.
	[]	Use zQuery / zMutation for runtime validation.
	[]	Specify output if you want strict return type checks.
	[]	Use zid("tableName") to help with typed IDs.
	[]	Don't  each Convex document also has _id and _creationTime.
	[]  Don't use null for optional fields, instead, use undefined
	[]  No manual Casting
	
Typing Schema Interfaces
	Domain types: If your tag (or tag-related data) is used in multiple parts of your app, then  defined centrally (e.g. in your tag-schema.ts file). This way, any changes to the tag’s shape are made in one place and propagated consistently.
	•	Component props: However, a component’s props interface (like CreateTagModalProps) is often considered a view concern. If it’s only used by that modal, it’s best to colocate it in the same file as the component. This keeps the component self-contained and easier to understand.