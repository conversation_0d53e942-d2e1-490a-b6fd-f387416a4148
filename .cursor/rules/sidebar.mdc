---
description: sidebar
globs: 
---
The following guide explains in detail how the sidebar (main menu) is implemented in the Catalyst Vision project. It will walk you through the key files, the UI components that are used, and how the collapsing mechanism and header (with breadcrumb capabilities) are built.

──────────────────────────────
1. OVERVIEW & PROJECT STRUCTURE

• The overall UI is split between “components” (which hold pages and modules) and “ui” components (reusable building blocks).  
• The sidebar (or main navigation) is a central part of the application and is implemented in a dedicated component that controls both the navigation links and the header area (which displays the current page information and search functionality).  
• Many of the UI building blocks—such as Button, Avatar, Input, and Breadcrumb—are located in the “src/components/ui” folder.

──────────────────────────────
2. SIDEBAR IMPLEMENTATION – FILE: src/components/navigation-menu.tsx

This file is the core of the main navigation and header. Key points include:

• Imports & Dependencies  
  – It uses React along with hooks like useState, useEffect, and useRef.  
  – “react-router-dom” is used (via Link and useLocation) to determine the active route and to navigate between pages.  
  – A variety of icons are imported from “lucide-react” (for example, Home, Briefcase, CheckSquare, and more) for visual cues.  
  – Custom UI components from “src/components/ui” are imported (such as Button, Avatar, and Input).

• Main Sidebar Structure  
  – The sidebar appears on the left and is defined using a container that conditionally applies one of two CSS classes depending on expansion:
  ◦ When expanded, it uses a class (e.g., "sidebar-menu") that provides full width with labels for each menu item.
  ◦ When collapsed it applies a narrow class (e.g., "w-16") so only icons are visible.
  – The sidebar includes a logo area at the very top (which displays an icon and the project name “Catalyst”) and a series of navigation links. Each link is rendered using a <Link> component from react-router-dom so that the active route can be highlighted.
  – There are separation elements (using border dividers) to segment the navigation items.

• Collapsing Mechanism  
  – The file defines a state variable “isExpanded” (via useState) that tracks if the sidebar is open (expanded) or collapsed.
  – A toggle button (rendering the “PanelLeftClose” icon) is provided near the top. When a user clicks on it, the onClick handler toggles “isExpanded” (e.g. setIsExpanded(!isExpanded)).  
  – The sidebar’s container uses conditional class names based on “isExpanded” (for example, applying “sidebar-menu” when expanded and “w-16” when collapsed). Additionally, the transition classes (like “transition-all”, “duration-100”, “ease-in-out”) enable smooth animations during state changes.
  – There is also an event listener (via useEffect) that listens for a custom event (“projectPreview”) which automatically adjusts the sidebar’s expansion when a project preview is open.

• Header & Breadcrumb Integration  
  – Below (or to the right of) the sidebar, the main content area renders a top bar. In “navigation-menu.tsx” this header area displays:  
  ◦ A toggle button (which controls the collapse state).  
  ◦ A title that changes depending on the current route (using useLocation to detect the path).  
  ◦ Timing information (for example, “Last updated …”) that uses functions like formatTimeAgo.
  – Although an explicit “Breadcrumb” component is not directly used in this file, the reusable breadcrumb UI is defined separately in “src/components/ui/breadcrumb.tsx” and can be integrated into the header if desired. This file exports components like Breadcrumb, BreadcrumbList, BreadcrumbItem, and BreadcrumbLink that would allow navigation hierarchies to be displayed.

• Additional Sidebar Features  
  – A search button is rendered inside the sidebar. When clicked (or when the user presses the ⌘K shortcut), it opens the global search modal.
  – The component renders the “GlobalSearchModal” (see below) and passes to it the state “isSearchOpen” along with an “onOpenChange” callback so that the search UI is controlled from the same place.
  – There are also links for notifications, integrations, and the user’s profile at the bottom of the sidebar.

──────────────────────────────
3. GLOBAL SEARCH MODAL – FILE: src/components/global-search-modal.tsx

• This component is imported into the navigation-menu and is triggered when the user starts a search (by clicking the search button or using the keyboard shortcut).  
• It uses a dialog component (likely from the UI library) to display a modal over the main content area.  
• The modal uses a “ScrollArea” (from “src/components/ui/scroll-area.tsx”) so that long lists of search results or AI suggestions can be scrolled.  
• Within the modal, the “Command” component (from the “cmdk” library) filters and displays search results, AI suggestions, and recent items dynamically.
• Various icons (such as Search, FileText, and Sparkles) are used within the modal as well.

──────────────────────────────
4. REUSABLE UI COMPONENTS (Inside src/components/ui)

Many components used in the sidebar and header are defined here. A few key examples:

• Button (src/components/ui/button.tsx)  
  – Provides styled buttons that support different variants (default, outline, destructive, etc.), sizes, and states.  
  – Used for the collapse toggle, search action, and various interactive elements in the header and sidebar.

• Avatar (src/components/ui/avatar.tsx)  
  – Contains components for displaying avatar images and fallback text.  
  – Used for profile display at the bottom of the sidebar and in other parts of the UI (for example, in project/team listings).

• Input (src/components/ui/input.tsx)  
  – A styled input field that is used in the search areas and filters.
  
• Breadcrumb (src/components/ui/breadcrumb.tsx)  
  – Exports several components to build breadcrumb navigation (Breadcrumb, BreadcrumbList, BreadcrumbItem, and BreadcrumbLink).  
  – Although not directly used in the provided navigation-menu.tsx file, it is available to build headers that show page hierarchy.

• Separator (src/components/ui/separator.tsx) and Tabs (src/components/ui/tabs.tsx)  
  – These components help organize content on pages (for example, separating groups of links or content and switching between views).

• ScrollArea (src/components/ui/scroll-area.tsx)  
  – Provides a styled container with hidden scrollbars and smooth scrolling behavior.  
  – Used both in the sidebar (for the global search modal) and in various inner content areas.

──────────────────────────────
5. HEADER & BREADCRUMB DETAILS

• The Header Area (part of navigation-menu.tsx)  
  – Is rendered on the top of the main content area after the sidebar.  
  – Displays a dynamic title that reflects the current page (e.g., “Homepage”, “Projects”, or a project’s name).  
  – Shows additional context such as “Last updated” information (using helper functions like formatTimeAgo).
  
• Breadcrumb UI  
  – The dedicated Breadcrumb UI is defined in “src/components/ui/breadcrumb.tsx”.  
  – Its components can be assembled to create navigation trails. For instance, a header could combine BreadcrumbList and BreadcrumbItem to show the hierarchy and allow users to go back to previous pages.
  – Even though the current header in navigation-menu.tsx does not explicitly render a breadcrumb, the components are ready for integration where a multi-level navigation hierarchy is required.

──────────────────────────────
6. COLLAPSE & RESPONSIVE BEHAVIOR

• Collapse Button & State  
  – The “isExpanded” boolean triggers either a full sidebar or a compact version (only icons).  
  – The collapse button (with the “PanelLeftClose” icon) calls setIsExpanded(!isExpanded) on click.
  
• Conditional Styling  
  – The container’s class name is conditionally built (e.g., if isExpanded is true, the full “sidebar-menu” class is applied; otherwise, “w-16” is used).  
  – Transition classes ensure that the width change happens smoothly.

• Event-based Collapse  
  – An additional useEffect in navigation-menu.tsx listens for a custom “projectPreview” event. When a project preview is toggled (often triggered when a user selects a project), the sidebar may automatically collapse or expand based on the detail passed along with the event.
  
• Responsive Design  
  – The sidebar and header are built to respond to changes in browser window size, maintaining usability on different device widths.

──────────────────────────────
7. HOW IT ALL WORKS TOGETHER

When the application starts:  
• The NavigationMenu component (src/components/navigation-menu.tsx) renders the sidebar and header.  
• It reads the current route using useLocation so that the active menu item is highlighted.  
• The sidebar displays a logo, a search button, a list of navigation links (Homepage, Projects, Tasks, Decisions, Documents, Resources, etc.), and bottom controls for notifications and profile access.  
• Clicking the search button or pressing ⌘K opens the GlobalSearchModal (src/components/global-search-modal.tsx), which provides a full-screen search interface complete with suggestions and recent results.  
• The “isExpanded” state controls whether the sidebar shows full labels or only icons. This state is toggled by clicking the collapse button or through an event listener that listens for project preview events.  
• All UI elements (buttons, inputs, avatars, separators) come from the reusable components in “src/components/ui”, ensuring visual consistency and ease of maintenance.

──────────────────────────────
8. SUMMARY OF IMPORTANT FILES

• src/components/navigation-menu.tsx  
 – Main sidebar and header implementation, managing navigation links and collapse behavior.

• src/components/global-search-modal.tsx  
 – Implements the modal search UI that is triggered via the sidebar.

• src/components/ui/button.tsx  
 – Defines the Button component used throughout the sidebar and header.

• src/components/ui/avatar.tsx  
 – Contains the Avatar components for profile images and team member icons.

• src/components/ui/breadcrumb.tsx  
 – Provides the Breadcrumb components for hierarchical navigation in the header (available for use).

• src/components/ui/input.tsx, separator.tsx, tabs.tsx, scroll-area.tsx, etc.  
 – Other core building blocks used within the sidebar, header, and overall layout.

• (Additional files like theme-provider.tsx, and other component files are used to support the overall theming and page structure.)

──────────────────────────────
CONCLUSION

The sidebar (main menu) in Catalyst Vision is a carefully architected component that leverages React Router for routing, custom UI components for consistency, and flexible state management for responsiveness and collapsing behavior. The NavigationMenu component (in src/components/navigation-menu.tsx) ties together the logo, navigation links, search (with the GlobalSearchModal), and header actions—all of which work together to provide an interactive and dynamic user experience. The reusable UI components in the “ui” folder (including Breadcrumb, Button, Avatar, Input, etc.) ensure that the design language is consistent across all parts of the app.

This detailed guide should give you a comprehensive understanding of each aspect of the sidebar’s implementation and the related files involved in providing both the navigation and header functionality.