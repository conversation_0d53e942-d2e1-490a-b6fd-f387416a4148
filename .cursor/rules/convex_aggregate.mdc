---
description: aggregation queries for convex 
globs: 
alwaysApply: false
---
<convex_guidelines>
  <aggregate_guidelines>

    <introduction>
      The Aggregate component provides O(log(n)) operations for counting, summing, and ranking data without naive full scans. It is implemented as a data structure that maintains denormalized counts and sums for efficient lookup.
    </introduction>

    <what_are_aggregates_for>
      - Plain Convex indexes let you paginate and query documents, but for big-picture data (like total counts, sums, high scores), you may want an aggregated view without fetching all documents.
      - Aggregates maintain a sorted key-value structure, letting you perform queries like:
        - Count how many items are above or below a certain key.
        - Find the p95 item (by index).
        - Sum values for quick averages.
        - Compute ranking via indexOf.
      - This provides an alternative to slow table scans or naive counting.
    </what_are_aggregates_for>

    <grouping>
      - You can group data points by choosing appropriate keys:
        - A single numeric key for a leaderboard score.
        - A tuple like \`[username, score]\` to capture both user and score.
        - A prefix bound can then zero in on all scores for a user, or all scores for a game.
      - Sorting carefully matters:
        - If your key is \`[game, username]\`, querying a max will return the lexicographically last username, not the highest numeric score.
        - For highest numeric score in a game, use \`[game, score]\`.
    </grouping>

    <namespacing>
      - If your data is divided into partitions that you never need to combine, define a \`namespace\`.
      - Each namespace holds its own internal structure, eliminating interference between partitions.
      - Example:
        ```ts
        const leaderboardByGame = new TableAggregate<{
          Namespace: Id<"games">;
          Key: number;
          DataModel: DataModel;
          TableName: "scores";
        }>(components.leaderboardByGame, {
          namespace: (doc) => doc.gameId,
          sortKey: (doc) => doc.score,
        });

        const footballHighScore = await leaderboardByGame.max(ctx, {
          namespace: footballId,
        });
        ```
      - This design prevents data from different games from colliding internally.
    </namespacing>

    <defining_multiple_aggregates>
      - You can define multiple \`Aggregate\` instances for different tables or different keys.
      - Each instance is declared with its own name:
        ```ts
        app.use(aggregate, { name: "aggregateScores" });
        app.use(aggregate, { name: "aggregateByGame" });
        ```
      - Then reference them in code:
        ```ts
        const aggregateScores = new TableAggregate<...>(
          components.aggregateScores,
          {...}
        );
        const aggregateByGame = new TableAggregate<...>(
          components.aggregateByGame,
          {...}
        );
        ```
    </defining_multiple_aggregates>

    <usage>
      - You usually attach an aggregate to a table:
        ```ts
        import { TableAggregate } from "@convex-dev/aggregate";

        const aggregate = new TableAggregate<{
          Key: number;
          DataModel: DataModel;
          TableName: "mytable";
        }>(components.aggregate, {
          sortKey: (doc) => doc._creationTime,
          sumValue: (doc) => doc.value,
        });
        ```
      - Whenever you insert, update, or delete a document in the table, update the aggregate:
        ```ts
        // Insert
        const id = await ctx.db.insert("mytable", { foo, bar });
        const doc = await ctx.db.get(id);
        await aggregate.insert(ctx, doc!);

        // Replace
        const oldDoc = await ctx.db.get(id);
        await ctx.db.patch(id, { foo: "newValue" });
        const newDoc = await ctx.db.get(id);
        await aggregate.replace(ctx, oldDoc!, newDoc!);

        // Delete
        const oldDoc = await ctx.db.get(id);
        await ctx.db.delete(id);
        await aggregate.delete(ctx, oldDoc!);
        ```
      - To perform aggregate reads:
        ```ts
        const totalCount = await aggregate.count(ctx);
        const sumValues = await aggregate.sum(ctx);
        const p95Index = Math.floor(totalCount * 0.95);
        const p95Key = (await aggregate.at(ctx, p95Index)).key;
        const rankFor65 = await aggregate.indexOf(ctx, 65);
        ```
    </usage>

    <direct_aggregate>
      - If you want to store and query aggregated data that does not come from a Convex table, use \`DirectAggregate\`.
      ```ts
      import { DirectAggregate } from "@convex-dev/aggregate";

      const direct = new DirectAggregate<{
        Key: number;
        Id: string;
      }>(components.aggregate);

      // Insert new point
      await direct.insert(ctx, { key: 10, id: "random" });

      // Insert with sumValue
      await direct.insert(ctx, { key: 50, id: "another", sumValue: 5 });

      // Delete
      await direct.delete(ctx, { key: 10, id: "random" });

      // Replace
      await direct.replace(ctx, { key: 50, id: "another" }, { key: 60, id: "another" });
      ```
    </direct_aggregate>

    <example_cases>
      - Leaderboard: keep track of scores; find global average, p95, count, and ranks.
      - Offset-based pagination: define a key as \`_creationTime\` to jump directly to a given offset in the sorted data.
      - Random item selection: define a null sortKey so items are effectively unsorted, then pick items by random index.
      - Summing numeric fields: set \`sumValue\` to the field that you need to sum.
      - Grouping by user or game: define a tuple key or a \`namespace\` to isolate partitions.
    </example_cases>

    <operations>
      <attach_existing_table>
        - To add an aggregate to a table already containing data:
          1. Deploy code that uses \`insertIfDoesNotExist\`, \`replaceOrInsert\`, or \`deleteIfExists\` so writes won't break if data isn't in sync.
          2. Run a background job to backfill existing documents into the aggregate with \`insertIfDoesNotExist\`.
          3. Once backfilled, switch to standard \`insert\`, \`replace\`, \`delete\` calls.
      </attach_existing_table>

      <automatic_updates>
        - Always update the aggregate whenever the table changes. Options:
          1. Call aggregate updates manually in your mutations.
          2. Use a shared function that encapsulates table writes and calls the aggregate.
          3. Register a Trigger to automatically call \`aggregate.trigger()\`.
      </automatic_updates>

      <repair_inconsistencies>
        - If the aggregate and table data get out of sync, you can:
          - Clear the aggregate with \`aggregate.clear(ctx)\` or rename the component to start fresh.
          - Or compare the table vs. aggregate data with paginated queries and fix them incrementally.
      </repair_inconsistencies>

      <reactivity_and_transactionality>
        - Aggregates are reactive: any change triggers re-running queries that depend on the aggregate’s data.
        - Aggregates are transactional: updates to the aggregate happen atomically with other writes, ensuring consistency.
        - This sometimes causes conflicts if many writes happen in the same region of the data structure. Use namespacing and bounds to limit the surface area of read/write conflicts.
      </reactivity_and_transactionality>

      <lazy_aggregation>
        - The aggregate root can be lazy, which means \`aggregate.count(ctx)\` might require reading multiple nodes, but writes can have less contention.
        - Setting a large \`maxNodeSize\` or disabling root laziness can improve query speed but may increase collisions among writes.
      </lazy_aggregation>
    </operations>

  </aggregate_guidelines>
</convex_guidelines>
