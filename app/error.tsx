'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // Log the error when the component mounts
  useEffect(() => {
    console.error('Application error:', {
      message: error.message,
      digest: error.digest,
      stack: error.stack,
      url: typeof window !== 'undefined' ? window.location.href : 'server-side',
      timestamp: new Date().toISOString()
    });
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="max-w-md">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
        
        <p className="text-gray-600 mb-8">
          We encountered an error while loading this page. This could be due to a temporary issue.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="default"
            onClick={() => reset()}
          >
            Try again
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => {
              if (typeof window !== 'undefined') {
                window.location.reload();
              }
            }}
          >
            Refresh Page
          </Button>
        </div>
      </div>
    </div>
  );
}
