/* tiptap.scss */
/* Basic editor styles for Tiptap */
.tiptap {
  /* remove or fix the color references so the block is valid in all browsers */
  
  /* Removes default margin on the first child paragraph. */
  :first-child {
    margin-top: 0;
  }

  /* List styles for UL and OL */
  ul, ol {
    list-style-position: outside; // or outside
    /* (Optional) You could do list-style: disc or decimal here if you like:
       list-style: disc inside;
    */
  }

  ul {
    list-style-type: disc;
  }
  
  ol {
    list-style-type: decimal;
  }

  /* Global styling for paragraphs */
  p {
    font-size: 0.85rem; /* Reduced font size */
    /* Use an actual color or a known variable. This is the Tailwind slate-700 HEX (#374151). */
    color: #374151; 
    line-height: 1.4; /* Reduced line height */
    margin: 0.3rem 0; /* Reduced vertical margin */

    /* Markers for bullets/numbers */
    ::marker {
      color: black;
    }
  }

  /* Typography for different text sizes */
  .text-lg {
    font-size: 1.125rem;
  }
  
  .text-sm {
    font-size: 0.875rem;
  }

  /* List container styling */
  ul, ol {
    padding: 0 1rem;
    margin: 0.7rem 1rem 0.7rem 0.4rem; /* Reduced vertical margin */
    font-size: 0.9rem;

    li p {
      margin-top: 0.20em;
      margin-bottom: 0.20em;
    }
  }

  /* Heading styles */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.0;
    margin-top: 0.8rem; /* Reduced top margin */
    /* Removed text-wrap: pretty; */
  }
  
  h1, h2 {
    margin-top: 0.8rem; /* Reduced top margin (consistent with above) */
    margin-bottom: 0.8rem; /* Reduced bottom margin */
  }
  
  h1 {
    font-size: 1.3rem;
  }
  
  h2 {
    font-size: 1.10rem;
  }
  
  h3 {
    font-size: 1.0rem;
  }
  
  h4, h5, h6 {
    font-size: 0.95rem;
  }

  /* Code and preformatted text styles */
  code {
    /* Replace var(--purple-light) with a valid color or define it in :root */
    background-color: #f3e8ff; /* example lavender color */
    border-radius: 0.4rem;
    /* Replace var(--black) with standard black or define --black in :root */
    color: black;
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }
  
  pre {
    /* Replace var(--black) and var(--white) or define them in :root */
    background: #1a1a1a;
    color: white;
    border-radius: 0.5rem;
    font-family: 'JetBrainsMono', monospace;
    margin: 1rem 0; /* Reduced vertical margin */
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  blockquote {
    /* Replace var(--gray-3) or define it in :root. Let's pick a light gray. */
    border-left: 3px solid #cccccc;
    margin: 1rem 0; /* Reduced vertical margin */
    padding-left: 1rem;
  }

  hr {
    border: none;
    /* Replace var(--gray-2) or define it in :root. Let's pick #ddd. */
    border-top: 1px solid #dddddd;
    margin: 1.5rem 0; /* Reduced vertical margin */
  }

  /* Placeholder styling for empty editor paragraphs */
  p.is-editor-empty:first-child::before {
    /* Replace var(--gray-4) or define it in :root. Let's pick #999. */
    color: #999999;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  /* Table styles */
  table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0.5rem 0;
    overflow: hidden;
  }

  table td, table th {
    min-width: 1em;
    border: 2px solid #ced4da;
    padding: 3px 5px;
    vertical-align: top;
    box-sizing: border-box;
    position: relative;
  }

  table th {
    font-weight: bold;
    background-color: #f1f3f5;
  }

  /* Selected cell styling */
  table .selectedCell:after {
    background: rgba(200, 200, 255, 0.4);
    content: "";
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: none;
    position: absolute;
    z-index: 2;
  }

  /* Column resize handle */
  .tableColumnResizer {
    position: absolute;
    right: -2px;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #adf;
    cursor: col-resize;
    user-select: none;
    z-index: 3;
  }

  .resize-cursor {
    cursor: col-resize;
  }
}
