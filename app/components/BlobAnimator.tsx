'use client';

import { useEffect } from 'react';
import { initBlobAnimations } from '../blob-animation';

export default function BlobAnimator() {
  useEffect(() => {
    initBlobAnimations();
  }, []);

  return (
    <>
      <div className="color-blob color-blob-1"></div>
      <div className="color-blob color-blob-2"></div>
      <div className="color-blob color-blob-3"></div>
      <div className="color-blob color-blob-4"></div>
      <div className="color-blob color-blob-5"></div>
    </>
  );
} 