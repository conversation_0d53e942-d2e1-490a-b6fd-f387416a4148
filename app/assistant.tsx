"use client";

import { Thread } from "@/components/assistant-ui/thread";
import { ThreadList } from "@/components/assistant-ui/thread-list";

export const Assistant = () => {
  // The AssistantRuntimeProvider is now likely wrapping this component higher up
  // (e.g., in ConvexClientProvider or layout), so we don't need it here.
  // The Thread and ThreadList components will inherit the runtime context.

  return (
    <div className="grid h-dvh grid-cols-[200px_1fr] gap-x-2 px-4 py-4">
      <ThreadList />
      <Thread />
    </div>
  );
};
