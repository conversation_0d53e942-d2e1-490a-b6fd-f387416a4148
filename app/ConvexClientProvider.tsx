'use client';

import { ConvexAuthNextjsProvider } from '@convex-dev/auth/nextjs';
import { ReactNode, useEffect, createContext, useState, useContext, useMemo } from 'react'; // Added context imports
import { convex } from '@/lib/convex';
import { AssistantRuntimeProvider } from "@assistant-ui/react";
import { useChatRuntime } from "@assistant-ui/react-ai-sdk";
import { CoreMessage } from 'ai'; // Import CoreMessage

// Define the context shape
interface ChatFiltersContextType {
  filters: string[];
  setFilters: (filters: string[]) => void;
}

// Create the context with a default value
const ChatFiltersContext = createContext<ChatFiltersContextType | undefined>(undefined);

// Custom hook to use the context
export const useChatFilters = () => {
  const context = useContext(ChatFiltersContext);
  if (!context) {
    console.warn('useChatFilters used outside of ChatFiltersProvider, returning empty context');
    // Return a default implementation instead of throwing
    return {
      filters: [],
      setFilters: () => console.warn('setFilters called outside provider')
    };
  }
  return context;
};


/**
 * ConvexClientProvider
 *
 * This component provides Convex authentication context to the client-side application.
 * It should be used in the app's client layout to ensure all components have access
 * to authentication state and actions.
 *
 * @param {Object} props - Component props
 * @param {ReactNode} props.children - Child components to be wrapped with the provider
 */
export default function ConvexClientProvider({
  children
}: {
  children: ReactNode;
}) {
  // Get the Convex Site URL for HTTP actions
  const convexSiteUrl = process.env.NEXT_PUBLIC_CONVEX_SITE_URL;

  // Construct the full API endpoint URL for chat
  // Add a fallback in case the site URL is undefined, though it shouldn't be
  const convexChatApi = convexSiteUrl
    ? `${convexSiteUrl}/api/chat`
    : "/api/chat"; // Fallback, though ideally an error should be thrown if URL is missing

  const [filters, setFilters] = useState<string[]>([]); // State to hold filters
  const [isClientReady, setIsClientReady] = useState(false); // Track client-side readiness

  // Memoize context value
  const chatFiltersContextValue = useMemo(() => ({ filters, setFilters }), [filters]);

  // Initialize the chat runtime, customizing the request body
  const runtime = useChatRuntime({
    api: convexChatApi,
    // Create the proper request body structure
    body: (messages: CoreMessage[]) => {
      // Log what we're sending
      console.log('[ConvexClientProvider] Sending request with filters:', {
        filterCount: filters.length,
        messageCount: messages.length,
        hasFilters: filters.length > 0,
        timestamp: new Date().toISOString()
      });

      // Create a request object that includes our filters
      const requestBody = {
        messages,
        filters: filters.length > 0 ?
          filters :
          ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT'],
        // Include any other properties the SDK might be expecting
      };

      console.log('[ConvexClientProvider] Full request body:', JSON.stringify({
        ...requestBody,
        messages: requestBody.messages.length > 0 ? '[messages array]' : '[]'
      }));

      return requestBody;
    },
  });


  // Log the Convex URL for debugging and handle client-side initialization
  useEffect(() => {
    // Mark client as ready after a short delay to ensure hydration is complete
    const readyTimer = setTimeout(() => {
      setIsClientReady(true);
      console.log('[ConvexClientProvider] Client ready');
    }, 100);

    console.log('[ConvexClientProvider] Initialized with URLs:', {
      convexCloudUrl: process.env.NEXT_PUBLIC_CONVEX_URL,
      convexSiteUrl: process.env.NEXT_PUBLIC_CONVEX_SITE_URL,
      timestamp: new Date().toISOString()
    });

    // Add a global event listener to track page navigation
    const handleNavigation = () => {
      console.log('[ConvexClientProvider] Page navigation:', {
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
    };

    window.addEventListener('popstate', handleNavigation);

    // Clean up the event listener and timer
    return () => {
      window.removeEventListener('popstate', handleNavigation);
      clearTimeout(readyTimer);
    };
  }, []);

  return (
    // Provide the chat filters context
    <ChatFiltersContext.Provider value={chatFiltersContextValue}>
      <ConvexAuthNextjsProvider client={convex as any}>
        {/* Wrap children with the AssistantRuntimeProvider */}
        <AssistantRuntimeProvider runtime={runtime}>
          {children}
        </AssistantRuntimeProvider>
      </ConvexAuthNextjsProvider>
    </ChatFiltersContext.Provider>
  );
}
