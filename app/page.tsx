// @ts-nocheck
'use client';

import { useConvexAuth } from 'convex/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { motion, useScroll, useTransform } from 'motion/react';
import { useRef } from 'react';
import { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { GlassmorphicContainer } from '@/components/ui/glassmorphic-container';
import { GlassmorphicHero } from '@/components/ui/glassmorphic-hero';

// Dynamically import ClientLayout with SSR disabled to prevent window-related errors during server rendering
const ClientLayout = dynamic(() => import('./client-layout'), { ssr: false });

export default function Home() {
  const { isAuthenticated, isLoading } = useConvexAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/home');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || isAuthenticated) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent"></div>
      </div>
    );
  }
  // Refs for parallax sections
  const containerRef = useRef(null);
  const heroRef = useRef(null);
  const featuresRef = useRef(null);
  const problemRef = useRef(null);
  const testimonialsRef = useRef(null);
  const ctaRef = useRef(null);

  // Main scroll progress
  const { scrollYProgress } = useScroll();
  const { scrollY } = useScroll();

  // Section-specific parallax effects
  const { scrollYProgress: heroProgress } = useScroll({
    target: heroRef,
    offset: ['start end', 'end start']
  });

  const { scrollYProgress: featuresProgress } = useScroll({
    target: featuresRef,
    offset: ['start end', 'end start']
  });

  const { scrollYProgress: problemProgress } = useScroll({
    target: problemRef,
    offset: ['start end', 'end start']
  });

  // Parallax transformations
  const heroY = useTransform(heroProgress, [0, 1], [0, -100]);
  const featuresY = useTransform(featuresProgress, [0, 1], [0, -100]);
  const problemY = useTransform(problemProgress, [0, 1], [0, -50]);

  // Glass panel sliding effects
  const heroGlassX = useTransform(heroProgress, [0, 0.5], ['-100%', '0%']);
  const featuresGlassX = useTransform(
    featuresProgress,
    [0, 0.5],
    ['100%', '0%']
  );
  const problemSolutionX = useTransform(
    problemProgress,
    [0, 0.5],
    ['-50%', '0%']
  );
  const solutionPanelX = useTransform(problemProgress, [0, 0.5], ['50%', '0%']);

  // Subtle animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  // Staggered animation for component cards
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  // Glass panel slide-in animation
  const slideInGlass = {
    hidden: { opacity: 0, x: -100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 50,
        damping: 20,
        delay: 0.2
      }
    }
  };

  // Feature card mapping
  const featureCards = [
    // ... your feature objects ...
  ].map((feature, index) => (
    <motion.div
      key={index}
      initial={{
        opacity: 0,
        x: feature.slideFrom === 'left' ? -50 : feature.slideFrom === 'right' ? 50 : 0,
        y: feature.slideFrom === 'bottom' ? 50 : 0
      }}
      whileInView={{
        opacity: 1,
        x: 0,
        y: 0
      }}
      viewport={{ once: true, margin: '-100px' }}
      transition={{
        type: 'spring',
        stiffness: 50,
        damping: 20,
        delay: index * 0.1
      }}
      whileHover={{
        y: -10,
        boxShadow: '0 15px 30px rgba(0,0,0,0.1)',
        transition: { duration: 0.2 }
      }}
      className="transform-gpu"
    >
      <Card className="h-full relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <span className="text-2xl mr-3">{feature.icon}</span>
              <CardTitle className="text-lg font-semibold text-gray-800">
                {feature.title}
              </CardTitle>
            </div>
            {feature.isNew && (
              <span className="bg-blue-400/20 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
                New
              </span>
            )}
          </div>
          <CardDescription>{feature.description}</CardDescription>
        </CardContent>
        <CardFooter>
          <span className="text-sm text-gray-500">Compatible with your stack</span>
          <motion.button
            className="text-blue-500 text-sm font-medium flex items-center"
            whileHover={{ x: 3 }}
          >
            Learn more
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </motion.button>
        </CardFooter>
      </Card>
    </motion.div>
  ));

  const homepageContent = (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Hero Section with enhanced glassmorphic effect */}
      <section ref={heroRef} className="relative overflow-hidden">
        <motion.div
          className="absolute inset-0 pointer-events-none"
          style={{ y: heroY }}
        />
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <GlassmorphicHero 
            title="Transform Your First Client Impression into Lasting Trust"
            description="Your clients deserve an exceptional first experience. FOJO eliminates tedious paperwork and delays, delivering a seamless onboarding process that builds immediate trust."
            actions={[
              {
                text: "See It In Action",
                href: "/home",
                variant: "default"
              },
              {
                text: "Learn How It Works",
                href: "/learn-more",
                variant: "outline"
              }
            ]}
            image={{
              src: "/dashboard-preview.svg",
              alt: "Dashboard preview"
            }}
          />
        </motion.div>
      </section>

      {/* Problem & Solution Section with oppositely sliding panels */}
      <section
        ref={problemRef}
        id="capabilities"
        className="relative py-24 z-10 overflow-hidden"
      >
        <div className="container mx-auto px-6">
          <div className="mb-16 text-center">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-6 text-gray-800"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              The Execution Gap: The Hidden Challenge
            </motion.h2>
            <motion.p
              className="text-lg max-w-3xl mx-auto text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              You know what exceptional service looks like. Your team has the
              expertise and intention. But consistently turning that knowledge
              into action remains your greatest challenge.
            </motion.p>
            <motion.p
              className="text-lg max-w-3xl mx-auto text-gray-600 mt-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              This gap—between knowing what excellence is and consistently
              executing it—isn't just frustrating. It's the single greatest
              barrier to transforming your customer experience and scaling your
              business.
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 relative">
            {/* Problem Panel */}
            <motion.div
              initial={{ opacity: 0, x: -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: '-100px' }}
              transition={{
                type: 'spring',
                stiffness: 50,
                damping: 20
              }}
              style={{ x: problemSolutionX }}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
              className="flex justify-center"
            >
              <Card className="h-full p-8 relative overflow-hidden w-full">
                <CardContent>
                  <motion.span className="inline-block mb-5 backdrop-blur-sm bg-blue-100/50 text-blue-600 px-5 py-1.5 rounded-full text-sm font-medium border border-blue-200/20">
                    Without Fojo
                  </motion.span>
                  <CardTitle className="text-2xl font-bold mb-6 text-gray-800">
                    What Happens Without Infrastructure
                  </CardTitle>
                  <ul className="space-y-4 mt-6">
                    {[
                      'Excellence depends on individual heroics',
                      "Knowledge remains trapped in people's minds",
                      'Execution varies widely across your organization',
                      'Scaling service requires proportionally scaling staff',
                      "Customer experience depends on who's available"
                    ].map((item, i) => (
                      <motion.li
                        key={i}
                        className="flex items-start"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: i * 0.1 }}
                      >
                        <span className="text-blue-500 mr-3 text-lg font-bold">
                          ✕
                        </span>
                        <span className="text-gray-600">{item}</span>
                      </motion.li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Solution Panel */}
            <motion.div
              initial={{ opacity: 0, x: 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: '-100px' }}
              transition={{
                type: 'spring',
                stiffness: 50,
                damping: 20
              }}
              style={{ x: solutionPanelX }}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
              className="flex justify-center"
            >
              <Card className="h-full p-8 relative overflow-hidden w-full">
                <CardContent>
                  <motion.span className="inline-block mb-5 backdrop-blur-sm bg-purple-100/50 text-purple-600 px-5 py-1.5 rounded-full text-sm font-medium border border-purple-200/20">
                    With Fojo
                  </motion.span>
                  <CardTitle className="text-2xl font-bold mb-6 text-gray-800">
                    Excellence by Design, Not Exception
                  </CardTitle>
                  <ul className="space-y-4 mt-6">
                    {[
                      'Excellence becomes your operational standard',
                      'Knowledge is embedded in institutional workflows',
                      'Consistent execution across your entire organization',
                      'Service scales without proportionally scaling costs',
                      'Customers enjoy a consistently exceptional experience'
                    ].map((item, i) => (
                      <motion.li
                        key={i}
                        className="flex items-start"
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: i * 0.1 }}
                      >
                        <span className="text-purple-500 mr-3 text-lg font-bold">
                          ✓
                        </span>
                        <span className="text-gray-600">{item}</span>
                      </motion.li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Connecting element - improved transition arrow instead of plus */}
            <motion.div
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 rounded-full backdrop-blur-lg bg-gradient-to-r from-blue-400/30 to-purple-400/30 border border-white/40 shadow-lg flex items-center justify-center z-10 hidden md:flex"
              initial={{ scale: 0, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{
                type: 'spring',
                stiffness: 100,
                damping: 20,
                delay: 0.6
              }}
              whileHover={{
                scale: 1.2,
                boxShadow: '0 0 30px rgba(124, 58, 237, 0.2)',
                transition: { duration: 0.3 }
              }}
              style={{
                left: '50%',
                marginLeft: '-40px'
              }}
            >
              {/* Right arrow icon */}
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10 text-white"
                viewBox="0 0 20 20"
                fill="currentColor"
                animate={{
                  x: [0, 5, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: 'loop'
                }}
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </motion.svg>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="relative py-16 z-10 overflow-hidden">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <GlassmorphicContainer className="p-10 max-w-3xl mx-auto">
              <div className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">
                Introducing the Digital Chief of Staff
              </div>
              <p className="text-gray-600 mb-6">
                We're not just building software. We're creating a new category
                that transforms service excellence from an aspiration dependent
                on individual heroics into a systematic reality.
              </p>

              <h3 className="text-xl font-bold mb-4 text-gray-800">
                How Fojo Bridges the Execution Gap
              </h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="font-bold text-indigo-600 mr-2">1.</span>
                  <div>
                    <p className="font-semibold text-gray-800">
                      Knowledge Becomes Protocol
                    </p>
                    <p className="text-gray-600">
                      Best practices aren't just documented—they're embedded
                      into workflows that guide every interaction and decision.
                    </p>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="font-bold text-indigo-600 mr-2">2.</span>
                  <div>
                    <p className="font-semibold text-gray-800">
                      Individual Brilliance Becomes Institutional Standard
                    </p>
                    <p className="text-gray-600">
                      The exceptional service your top performer provides to
                      their best client becomes the consistent experience for
                      every client.
                    </p>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="font-bold text-indigo-600 mr-2">3.</span>
                  <div>
                    <p className="font-semibold text-gray-800">
                      Intentions Become Outcomes
                    </p>
                    <p className="text-gray-600">
                      The white-glove treatment you intend to provide
                      materializes consistently because the system is designed
                      to deliver it.
                    </p>
                  </div>
                </li>
              </ul>
            </GlassmorphicContainer>
          </motion.div>
        </div>
      </section>

      {/* Features Section with sliding glass panels */}
      <section
        ref={featuresRef}
        id="features"
        className="relative py-16 z-10 overflow-hidden"
      >
        <div className="container mx-auto px-6">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <motion.span className="inline-block backdrop-blur-sm bg-indigo-400/10 text-indigo-600 px-4 py-1 rounded-full text-sm font-medium mb-4 border border-indigo-400/20">
              Core Capabilities
            </motion.span>
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              Excellence By Design, Not Exception
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our components provide the infrastructure needed to transform
              service excellence from occasional heroics to systematic reality.
            </p>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: '-50px' }}
            style={{ y: featuresY }}
          >
            {featureCards}
          </motion.div>

          <div className="mt-16 text-center">
            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            ></motion.div>
          </div>
        </div>
      </section>

      {/* Testimonial Section - with floating glass effect */}
      <section
        ref={testimonialsRef}
        id="testimonials"
        className="relative py-20 z-10"
      >
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{
              // Spring settings for the whileInView animation (default target)
              type: 'spring',
              stiffness: 50,
              damping: 20,
              // Add specific transition settings for the 'y' property in the 'animate' prop
              y: {
                duration: 3, // Duration for one float cycle
                repeat: Infinity, // Loop forever
                repeatType: "loop", // Loop smoothly
                ease: "easeInOut" // Use a smooth easing for the float
              }
            }}
            animate={{
              y: [0, -10, 0]
            }}
          >
            <GlassmorphicContainer className="p-10 max-w-3xl mx-auto relative overflow-hidden">
              <CardContent>
                <motion.span className="inline-block backdrop-blur-sm bg-blue-400/10 text-blue-600 px-4 py-1 rounded-full text-sm font-medium mb-6 border border-blue-400/20">
                  Success Story
                </motion.span>
                <p className="text-xl italic text-gray-600 mb-6 relative z-10">
                  "Fojo has transformed how we deliver service. What used to
                  depend on our top performers is now our standard operating
                  procedure. The execution gap that plagued us for years has
                  finally been closed."
                </p>
                <div className="flex items-center">
                  <motion.div
                    className="rounded-full bg-white/80 h-12 w-12 mr-4 backdrop-blur-sm border border-white/70 shadow-md flex items-center justify-center text-gray-500"
                    whileHover={{
                      boxShadow: '0px 0px 15px rgba(59, 130, 246, 0.3)'
                    }}
                    transition={{
                      duration: 0.3
                    }}
                  >
                    MR
                  </motion.div>
                  <div>
                    <p className="font-semibold text-gray-800">
                      Michael Reynolds
                    </p>
                    <p className="text-sm text-gray-500">
                      Chief Customer Officer, Enterprise Solutions
                    </p>
                  </div>
                </div>
              </CardContent>
            </GlassmorphicContainer>
          </motion.div>
        </div>
      </section>

      {/* CTA Section - with dramatically sliding glassmorphic panel */}
      <section ref={ctaRef} className="relative py-24 z-10 overflow-hidden">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 100 }}
            whileInView={{ opacity: 1, scale: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{
              type: 'spring',
              stiffness: 60,
              damping: 20
            }}
            whileHover={{
              y: -10,
              transition: { duration: 0.3 }
            }}
          >
            <GlassmorphicContainer className="p-12 max-w-4xl mx-auto relative overflow-hidden">
              <CardContent>
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    x: [0, 10, 0],
                    y: [0, 10, 0]
                  }}
                  transition={{
                    duration: 15,
                    repeat: Infinity,
                    repeatType: 'mirror'
                  }}
                />
                <motion.div
                  animate={{
                    scale: [1, 1.5, 1],
                    x: [0, -10, 0],
                    y: [0, -10, 0]
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    repeatType: 'mirror',
                    delay: 5
                  }}
                />
                <div className="relative z-10 text-center">
                  <motion.h2
                    className="text-3xl md:text-4xl font-bold mb-6 text-gray-800"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                  >
                    Ready to Make Excellence Your Operational Standard?
                  </motion.h2>

                  <motion.p
                    className="text-lg mb-8 text-gray-600"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.1 }}
                  >
                    Join the forward-thinking leaders who are redefining what
                    service excellence means and how consistently it can be
                    delivered.
                  </motion.p>

                  <motion.div
                    className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6 mt-8"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                      className="w-full sm:w-auto"
                    >
                      <Link
                        href="/home"
                        className="backdrop-blur-md bg-white/80 border border-white/60 text-gray-800 font-bold py-4 px-10 rounded-lg hover:bg-white/90 transition duration-300 text-center shadow-lg inline-flex items-center justify-center w-full sm:w-auto"
                      >
                        <span>Request a Demo</span>
                        <motion.svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 ml-2 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          animate={{ x: [0, 4, 0] }}
                          transition={{
                            duration: 1.5,
                            ease: 'easeInOut',
                            repeat: Infinity,
                            repeatType: 'loop'
                          }}
                        >
                          <path
                            fillRule="evenodd"
                            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </motion.svg>
                      </Link>
                    </motion.div>

                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                      className="w-full sm:w-auto"
                    >
                      <Link
                        href="/download"
                        className="backdrop-blur-md bg-white/40 border border-white/40 text-gray-700 font-bold py-4 px-10 rounded-lg hover:bg-white/60 transition duration-300 text-center shadow-lg inline-flex items-center justify-center w-full sm:w-auto"
                      >
                        <svg
                          className="h-5 w-5 mr-2 flex-shrink-0"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" />
                          <path d="M11 11h2v6h-2zm0-4h2v2h-2z" />
                        </svg>
                        <span>Download Overview</span>
                      </Link>
                    </motion.div>
                  </motion.div>
                </div>
              </CardContent>
            </GlassmorphicContainer>
          </motion.div>
        </div>
      </section>
    </div>
  );

  // Return the page wrapped in ClientLayout for homepage only
  return <ClientLayout>{homepageContent}</ClientLayout>;
}
