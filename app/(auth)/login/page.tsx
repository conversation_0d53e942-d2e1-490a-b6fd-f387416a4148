'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/hooks/use-toast';
import { useAuthActions } from '@convex-dev/auth/react';
import { useConvexAuth } from 'convex/react';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FcGoogle } from 'react-icons/fc';
import { motion, useReducedMotion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';

/**
 * Glassmorphic Login Page Component
 *
 * A modern, clean login page with frosted glass effects and subtle animations.
 * This component handles authentication redirection on the client side
 * with visual feedback and controlled timing.
 *
 * Design features:
 * - Animated background gradients
 * - Frosted glass card for the login container
 * - Responsive design with subtle animations
 * - Visual feedback during authentication process
 */
export default function LoginPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { signIn } = useAuthActions();
  const { isAuthenticated, isLoading } = useConvexAuth();
  const [attemptedSignIn, setAttemptedSignIn] = React.useState(false);
  const [redirectInProgress, setRedirectInProgress] = React.useState(false);
  const [authSuccessful, setAuthSuccessful] = React.useState(false);

  // Respect user's reduced motion preference
  const shouldReduceMotion = useReducedMotion();

  /**
   * Handle redirection to home page after authentication
   *
   * This effect runs when:
   * 1. Authentication is successful (authSuccessful flag is true)
   * 2. No redirection is currently in progress
   *
   * The middleware will redirect from root to /home for authenticated users.
   */
  React.useEffect(() => {
    // Only proceed if authentication was successful and not already redirecting
    if (authSuccessful && !redirectInProgress) {
      console.log('Authentication successful, redirecting to home');
      setRedirectInProgress(true);

      // Redirect to root path - middleware will handle redirect to /home
      window.location.href = '/home';
    }
  }, [authSuccessful, redirectInProgress]);

  /**
   * Monitor authentication state changes
   */
  React.useEffect(() => {
    // If user attempted sign-in and is now authenticated, mark auth as successful
    if (attemptedSignIn && isAuthenticated && !isLoading) {
      console.log(
        'Authentication state changed to authenticated after sign-in attempt'
      );
      setAuthSuccessful(true);

      // No success toast here anymore
    }
    // If user is already authenticated on page load (without sign-in attempt)
    else if (isAuthenticated && !isLoading && !attemptedSignIn) {
      console.log('User already authenticated on page load');
      setAuthSuccessful(true);
    }
  }, [isAuthenticated, isLoading, attemptedSignIn]);

  /**
   * Handle Google sign-in process
   *
   * This function:
   * 1. Sets the attemptedSignIn flag to true
   * 2. Calls the Google sign-in method
   * 3. The authentication state monitoring effect will handle the rest
   */
  // Check if authentication is disabled via environment variable
  const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === "TRUE";
  
  // If auth is disabled, automatically redirect to home
  useEffect(() => {
    if (isAuthDisabled) {
      console.log('Authentication disabled via DISABLE_AUTH=TRUE, redirecting to home');
      window.location.href = '/home';
    }
  }, [isAuthDisabled]);

  const handleGoogleSignIn = async () => {
    try {
      // If auth is disabled, redirect to home instead of attempting sign-in
      if (isAuthDisabled) {
        console.log('Authentication disabled, redirecting to home');
        window.location.href = '/home';
        return;
      }
      
      setAttemptedSignIn(true);

      // No toast notification here anymore

      await signIn('google');
      // No success toast here - it will be shown by the useEffect after authentication is confirmed
    } catch (error: any) {
      setAttemptedSignIn(false);
      toast({
        title: 'Authentication Error',
        description: 'Failed to sign in with Google. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Reduced animation settings when user prefers reduced motion
  const getAnimationSettings = (originalAnimation: any) => {
    return shouldReduceMotion ? { scale: 1, x: 0, y: 0 } : originalAnimation;
  };

  return (
    <div className="relative min-h-screen w-full overflow-hidden flex items-center justify-center">
      {/* Fixed background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-50 -z-10"></div>

      {/* Animated color splashes with subtle pastel colors */}
      <motion.div
        className="fixed top-[-10%] left-[-5%] w-[40rem] h-[40rem] rounded-full opacity-10 blur-3xl -z-5 transform-gpu"
        animate={getAnimationSettings({
          backgroundColor: ['#d8b4fe', '#e9d5ff', '#c4b5fd'],
          scale: [1, 1.1, 0.95, 1],
          x: [0, 20, -20, 0],
          y: [0, -20, 20, 0]
        })}
        transition={{
          duration: 25,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut'
        }}
        style={{ willChange: 'transform, opacity' }}
      />

      <motion.div
        className="fixed top-[-15%] right-[-10%] w-[35rem] h-[35rem] rounded-full opacity-10 blur-3xl -z-5 transform-gpu"
        animate={getAnimationSettings({
          backgroundColor: ['#bfdbfe', '#dbeafe', '#93c5fd'],
          scale: [1, 0.9, 1.1, 1],
          x: [0, -30, 30, 0],
          y: [0, 30, -30, 0]
        })}
        transition={{
          duration: 30,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut',
          delay: 2
        }}
        style={{ willChange: 'transform, opacity' }}
      />

      <motion.div
        className="fixed bottom-[-15%] right-[-5%] w-[30rem] h-[30rem] rounded-full opacity-8 blur-3xl -z-5 transform-gpu"
        animate={getAnimationSettings({
          backgroundColor: ['#a5f3fc', '#cffafe', '#67e8f9'],
          scale: [1, 0.8, 1.2, 1]
        })}
        transition={{
          duration: 28,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut',
          delay: 7
        }}
        style={{ willChange: 'transform, opacity' }}
      />

      {/* Main content */}
      <div className="relative z-10 w-full max-w-md px-4">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <motion.div
            className="h-12 w-12 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl"
            whileHover={{ rotate: 5, scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            FJ
          </motion.div>
        </div>

        {/* Login Card */}
        <Card className="w-full backdrop-blur-xl bg-white/70 border border-white/40 shadow-lg rounded-xl">
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <motion.h1
                className="text-2xl font-medium text-gray-800"
                initial={{ y: -5, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
              >
                Ready to Elevate Your Day?
              </motion.h1>
              <motion.p
                className="text-gray-600 mt-3"
                initial={{ y: -5, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                Your intelligent workspace is waiting to help you achieve more
              </motion.p>
            </div>

            {redirectInProgress ? (
              // Show redirection progress
              <div className="mb-4 text-center">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                    Everything's set!
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Preparing your personalized workspace...
                  </p>
                  <div className="mt-3 h-1.5 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                    <motion.div
                      className="h-full rounded-full bg-gradient-to-r from-indigo-500 to-purple-600"
                      initial={{ width: '0%' }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 4, ease: 'linear' }}
                    />
                  </div>
                </motion.div>
              </div>
            ) : isLoading ? (
              // Show loading state during Google authentication
              <div className="mb-4 text-center">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                    Connecting with Google
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Almost there—just a few more moments
                  </p>
                  <div className="mt-3 h-1.5 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                    <div
                      className="h-full animate-pulse rounded-full bg-gradient-to-r from-indigo-500 to-purple-600"
                      style={{ width: '60%', animationDuration: '1.5s' }}
                    ></div>
                  </div>
                </motion.div>
              </div>
            ) : (
              // Show sign-in button when not loading or redirecting
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  animate={{
                    boxShadow: [
                      '0 0 0 0 rgba(99, 102, 241, 0)',
                      '0 0 0 4px rgba(99, 102, 241, 0.05)',
                      '0 0 0 0 rgba(99, 102, 241, 0)'
                    ]
                  }}
                  transition={{
                    boxShadow: {
                      repeat: Infinity,
                      duration: 3,
                      ease: 'easeInOut'
                    },
                    scale: {
                      type: 'spring',
                      stiffness: 400,
                      damping: 10
                    }
                  }}
                  className="rounded-lg overflow-hidden"
                >
                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full justify-center gap-3 border border-indigo-200/40 bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm py-6 text-base hover:from-white/50 hover:to-white/30 hover:border-indigo-300/60 transition-all duration-300 shadow-sm hover:shadow-md font-medium text-gray-800 rounded-lg"
                    onClick={handleGoogleSignIn}
                    disabled={isLoading || redirectInProgress}
                  >
                    <FcGoogle className="h-6 w-6" />
                    Continue with Google
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            © 2023 Fojo.vision. All rights reserved.
          </p>
        </div>
      </div>

      {/* Optimized shimmer effect overlay */}
      <motion.div
        className="fixed inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform-gpu pointer-events-none"
        animate={
          !shouldReduceMotion
            ? {
                x: ['-100%', '100%']
              }
            : {}
        }
        transition={{
          repeat: Infinity,
          repeatType: 'loop',
          duration: 8,
          ease: 'linear'
        }}
        style={{
          backgroundSize: '200% 100%',
          willChange: 'transform'
        }}
      />
    </div>
  );
}
