'use client';

import React, { useEffect, useState } from 'react';
import { useReducedMotion, motion } from 'motion/react';
import Link from 'next/link';

import { Card, CardContent } from '@/components/ui/card';
import { GlassmorphicContainer } from '@/components/ui/glassmorphic-container';

export default function ClientLayout({
  children
}: {
  children: React.ReactNode;
}) {
  // State for header background transparency based on scroll
  const [scrolled, setScrolled] = useState(false);
  // Respect user's reduced motion preference
  const shouldReduceMotion = useReducedMotion();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Change header style on scroll
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Header nav items
  const navItems = [
    { label: 'Home', href: '#', scrollToTop: true },
    { label: 'Features', href: '#features' },
    { label: 'Get Started', href: '/home', isButton: true }
  ];

  // Reduced animation settings when user prefers reduced motion
  const getAnimationSettings = (originalAnimation: any) => {
    return shouldReduceMotion ? { scale: 1, x: 0, y: 0 } : originalAnimation;
  };

  return (

      <div className="relative overflow-x-hidden">
        {/* Enhanced background gradient for glassmorphic effect */}
        <div className="fixed inset-0 bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 -z-10"></div>

        {/* Enhanced animated color splashes with more vibrant but still subtle colors */}
        <motion.div
          className="fixed top-[-10%] left-[-5%] w-[45rem] h-[45rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#4c1d95', '#701a75', '#312e81'],
            scale: [1, 1.1, 0.95, 1],
            x: [0, 20, -20, 0],
            y: [0, -20, 20, 0]
          })}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut'
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed top-[-15%] right-[-10%] w-[40rem] h-[40rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#1e3a8a', '#1d4ed8', '#1e40af'],
            scale: [1, 0.9, 1.1, 1],
            x: [0, -30, 30, 0],
            y: [0, 30, -30, 0]
          })}
          transition={{
            duration: 30,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 2
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed top-[40%] left-[-10%] w-[35rem] h-[35rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#ec4899', '#f472b6', '#db2777'],
            scale: [1, 1.15, 0.9, 1]
          })}
          transition={{
            duration: 35,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 5
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed bottom-[-15%] right-[-5%] w-[35rem] h-[35rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#06b6d4', '#22d3ee', '#0891b2'],
            scale: [1, 0.8, 1.2, 1]
          })}
          transition={{
            duration: 28,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 7
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        {/* Removed more animation elements to improve performance */}

        {/* Sticky Header with glassmorphic effect */}
        <header
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'py-1' : 'py-2'}`}
        >
          <GlassmorphicContainer className={`z-50 transition-all duration-300 px-6 ${scrolled ? 'py-1' : 'py-2'} rounded-none`}>
            <div className="container mx-auto flex items-center justify-between">
                {/* Logo */}
                <div className="flex items-center space-x-2">
                  <motion.div
                    className="h-7 w-7 rounded-md bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold"
                    whileHover={{ rotate: 5, scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    FJ
                  </motion.div>
                  <span className="font-bold text-lg text-gray-800 dark:text-white">
                    Fojo.vision
                  </span>
                </div>

                {/* Navigation */}
                <nav className="hidden md:flex items-center space-x-6">
                  {navItems.map((item, index) => (
                    <div key={index} className="relative group">
                      {item.isButton ? (
                        <Link
                          href={item.href}
                          className="relative group backdrop-blur-md bg-white/80 border border-white/40 text-gray-800 px-5 py-2 rounded-lg font-medium shadow-sm hover:bg-white/90 hover:shadow-lg transition-all duration-300"
                        >
                          {item.label}
                        </Link>
                      ) : (
                        <Link
                          href={item.href}
                          onClick={(e) => {
                            if (item.scrollToTop) {
                              e.preventDefault();
                              window.scrollTo({ top: 0, behavior: 'smooth' });
                            }
                          }}
                          className="text-gray-800 font-medium hover:text-blue-600 transition-colors duration-150 py-2 block"
                        >
                          {item.label}
                          <span className="block h-0.5 w-0 group-hover:w-full bg-blue-600 transition-all duration-150 mt-0.5"></span>
                        </Link>
                      )}
                    </div>
                  ))}
                </nav>

                {/* Mobile menu button */}
                <div className="md:hidden">
                  <button
                    onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                    className="p-2 rounded-md text-gray-700 hover:bg-white/60 transition-colors duration-150"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Optimized shimmer effect overlay */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform-gpu"
                animate={
                  !shouldReduceMotion
                    ? {
                        x: ['-100%', '100%']
                      }
                    : {}
                }
                transition={{
                  repeat: Infinity,
                  repeatType: 'loop',
                  duration: 5,
                  ease: 'linear'
                }}
                style={{
                  backgroundSize: '200% 100%',
                  willChange: 'transform'
                }}
              />
          </GlassmorphicContainer>

          {/* Mobile menu with glassmorphic effect */}
          {mobileMenuOpen && (
            <GlassmorphicContainer className="md:hidden absolute top-full left-0 right-0 rounded-b-lg z-50">
              <div className="px-4 py-3 space-y-3">
                {navItems.map((item, index) => (
                  <div key={index}>
                    {item.isButton ? (
                      <Link
                        href={item.href}
                        className="block w-full text-center backdrop-blur-md bg-white/80 border border-white/40 text-gray-800 px-4 py-2 rounded-lg font-medium hover:bg-white/90 hover:shadow-lg transition-all duration-300"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item.label}
                      </Link>
                    ) : (
                      <Link
                        href={item.href}
                        onClick={(e) => {
                          setMobileMenuOpen(false);
                          if (item.scrollToTop) {
                            e.preventDefault();
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                          }
                        }}
                        className="block py-2 text-gray-800 font-medium hover:text-blue-600 transition-colors duration-150"
                      >
                        {item.label}
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            </GlassmorphicContainer>
          )}
        </header>

        {/* Reduced padding to match thinner header */}
        <div className="pt-16"></div>

        {/* Main content area */}
        <main className="relative z-10">{children}</main>

        {/* Glassmorphic footer */}
        <footer className="relative z-10">
          <GlassmorphicContainer className="rounded-none border-t border-white/40">
            <div className="container mx-auto px-6 py-12">
                <div className="grid md:grid-cols-4 gap-8">
                  <div>
                    <h3 className="text-xl font-bold mb-4 text-gray-800">Fojo</h3>
                    <p className="text-gray-600">
                      Transforming service excellence from an aspiration into an
                      operational reality across industries.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4 text-gray-800">Solutions</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          For Financial Services
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          For Healthcare
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          For Hospitality
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4 text-gray-800">Resources</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          Blog
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          Case Studies
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-gray-900 transition-colors duration-150">
                          Excellence Library
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4 text-gray-800">Contact</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li><EMAIL></li>
                      <li>1-800-FOJO-500</li>
                      <li>300 Innovation Drive, Boston, MA</li>
                    </ul>
                  </div>
                </div>
                <div className="border-t border-white/30 mt-8 pt-8 text-center text-gray-500">
                  <p>© 2023 Fojo. All rights reserved.</p>
                </div>
              </div>
          </GlassmorphicContainer>
        </footer>
      </div>

  );
}
