'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { usePathname } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Sparkles } from 'lucide-react';

export default function ReportsLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const currentTab = pathname.includes('allocate') ? 'allocate' : 'overview';

  return (
    <div className="container py-8 max-w-7xl">
      <h1 className="text-3xl font-semibold mb-1">Reports & Analytics</h1>
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-10">
        This page allows you to dynamically report via tags.
      </p>
      <Tabs value={currentTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="overview" asChild>
              <a href="/reports/overview" className="cursor-pointer">
                Tag Reporter
              </a>
            </TabsTrigger>
            <TabsTrigger value="allocate" asChild>
              <a href="/reports/allocate" className="cursor-pointer">
                Allocate Spending
              </a>
            </TabsTrigger>
          </TabsList>

        </div>
      </Tabs>

      {children}
    </div>
  );
}
