import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  BarChart2,
  CreditCard,
  PieChart,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { SpendingStats, DATE_FILTER_OPTIONS } from '@/zod/reports-schema';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Helper functions for trend visualization
const getTrendColor = (trend: number) => {
  return trend > 0 ? 'text-red-500' : 'text-green-500';
};

const getTrendIcon = (trend: number) => {
  return trend > 0 ? (
    <TrendingUp className="h-4 w-4 text-red-500" />
  ) : (
    <TrendingDown className="h-4 w-4 text-green-500" />
  );
};

const getTrendTooltip = (trend: SpendingStats['spendingTrend']) => {
  const changeType = trend.percentage > 0 ? 'increase' : 'decrease';
  return `${Math.abs(trend.percentage).toFixed(1)}% ${changeType} from previous ${trend.periodLabel}
  Previous: ${formatCurrency(trend.previousTotal)}
  Current: ${formatCurrency(trend.currentTotal)}`;
};

// Helper function to get badge color based on trend direction
const getBadgeColorClasses = (
  isPositive: boolean,
  isNeutral: boolean = false
) => {
  if (isNeutral) {
    return 'text-blue-600 border-blue-200 bg-blue-50 dark:text-blue-400 dark:border-blue-800 dark:bg-blue-950/30';
  }

  return isPositive
    ? 'text-red-600 border-red-200 bg-red-50 dark:text-red-400 dark:border-red-800 dark:bg-red-950/30'
    : 'text-green-600 border-green-200 bg-green-50 dark:text-green-400 dark:border-green-800 dark:bg-green-950/30';
};

export function MetricsCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {[1, 2, 3, 4].map((i) => (
        <Card key={i} className="shadow-sm border-none bg-card/50 rounded-xl">
          <CardHeader className="pb-2 flex flex-row items-center justify-between">
            <CardTitle className="text-sm font-medium">
              <Skeleton className="h-4 w-24" />
            </CardTitle>
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardHeader>
          <CardContent>
            <div>
              <Skeleton className="h-8 w-28 mb-2" />
              <Skeleton className="h-5 w-24" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface MetricsCardsProps {
  stats?: SpendingStats;
  loading?: boolean;
}

export function MetricsCards({
  stats: externalStats,
  loading = false
}: MetricsCardsProps) {
  // Provide default values if stats are not provided
  const stats = externalStats || {
    totalSpending: 0,
    avgTransactionSize: 0,
    totalTransactions: 0,
    spendingTrend: {
      percentage: 0,
      previousTotal: 0,
      currentTotal: 0,
      periodLabel: ''
    },
    topCategory: '',
    topCategoryPercentage: 0
  };

  // If loading, show skeleton
  if (loading) {
    return <MetricsCardsSkeleton />;
  }

  // Calculate avg transaction trend (example calculation - replace with actual logic if available)
  const avgTransactionTrend = 0; // Set to 0 to match the screenshot

  // Calculate transaction count trend (example calculation - replace with actual logic if available)
  const transactionCountDiff = 1; // Set to 1 to match the screenshot

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Spending Card */}
      <Card className="shadow-sm border-none bg-card/50 hover:bg-card/80 transition-colors duration-200 rounded-xl">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total Spending
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <DollarSign className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.totalSpending)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={`${getBadgeColorClasses(stats.spendingTrend.percentage > 0)} h-5 px-2 cursor-help inline-flex items-center whitespace-nowrap`}
                    >
                      {stats.spendingTrend.percentage > 0 ? (
                        <ArrowUpRight className="h-3 w-3 mr-1 flex-shrink-0" />
                      ) : (
                        <ArrowDownRight className="h-3 w-3 mr-1 flex-shrink-0" />
                      )}
                      <span className="truncate">
                        {Math.abs(stats.spendingTrend.percentage).toFixed(1)}%
                        from prev
                      </span>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="whitespace-pre-line">
                      {getTrendTooltip(stats.spendingTrend)}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Average Transaction Card */}
      <Card className="shadow-sm border-none bg-card/50 hover:bg-card/80 transition-colors duration-200 rounded-xl">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Avg Transaction
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <BarChart2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.avgTransactionSize)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={`${getBadgeColorClasses(avgTransactionTrend > 0, false)} h-5 px-2 cursor-help inline-flex items-center whitespace-nowrap`}
                    >
                      <ArrowDownRight className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate">0.0% from avg</span>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Average transaction size compared to typical spending
                      patterns
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Transactions Card */}
      <Card className="shadow-sm border-none bg-card/50 hover:bg-card/80 transition-colors duration-200 rounded-xl">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total Transactions
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <CreditCard className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <div className="text-2xl font-bold">
              {stats.totalTransactions.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={`${getBadgeColorClasses(transactionCountDiff > 0, false)} h-5 px-2 cursor-help inline-flex items-center whitespace-nowrap`}
                    >
                      <ArrowUpRight className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate">1 more than usual</span>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Number of transactions compared to your typical activity
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Category Card */}
      <Card className="shadow-sm border-none bg-card/50 hover:bg-card/80 transition-colors duration-200 rounded-xl">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Top Category
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <PieChart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <div className="text-2xl font-bold">
              {stats.topCategory || 'No data'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={`${getBadgeColorClasses(false, true)} h-5 px-2 cursor-help inline-flex items-center whitespace-nowrap`}
                    >
                      <span className="truncate">30% of spending</span>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {stats.topCategoryPercentage
                        ? `${stats.topCategory} accounts for ${stats.topCategoryPercentage}% of your total spending`
                        : 'No category data available'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
