'use client';

import { useCallback, useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { CalendarIcon, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

// Import date utility functions from our shared library
import {
  DateQuickFilterType as LibDateFilterType,
  getDateRangeForQuickFilter as getLibDateRange,
  getDateQuickFilterLabel as getLibFilterLabel,
  toUTC,
  setToStartOfDayUTC,
  setToEndOfDayUTC,
  getCurrentYear,
  getCurrentQuarter,
  createUTCDate,
  formatQuarterLabel
} from '@/lib/date-utils';

// Import schemas from our centralized Zod schema file
import {
  DATE_FILTER_OPTIONS,
  DateQuickFilterType,
  FilterState
} from '@/zod/reports-schema';

// Helper to map our component filter types to library filter types
function mapToLibFilterType(
  filterType: DateQuickFilterType
): LibDateFilterType {
  switch (filterType) {
    case DATE_FILTER_OPTIONS.LAST_30_DAYS:
      return 'last30days';
    case DATE_FILTER_OPTIONS.LAST_60_DAYS:
      return 'last60days';
    case DATE_FILTER_OPTIONS.LAST_90_DAYS:
      return 'last90days';
    case DATE_FILTER_OPTIONS.YEAR_TO_DATE:
      return 'ytd';
    case DATE_FILTER_OPTIONS.Q1:
      return 'q1';
    case DATE_FILTER_OPTIONS.Q2:
      return 'q2';
    case DATE_FILTER_OPTIONS.Q3:
      return 'q3';
    case DATE_FILTER_OPTIONS.Q4:
      return 'q4';
    case DATE_FILTER_OPTIONS.CUSTOM:
      return 'custom';
    default:
      return 'custom';
  }
}

// Helper to build filter parameters
export function buildFilterParams({
  dateFilter,
  startDate,
  endDate
}: {
  dateFilter: DateQuickFilterType;
  startDate?: Date;
  endDate?: Date;
}): URLSearchParams {
  const params = new URLSearchParams();

  // Store dates as UTC timestamps for consistency
  if (startDate) {
    params.set('startDate', startDate.getTime().toString());
  }

  if (endDate) {
    params.set('endDate', endDate.getTime().toString());
  }

  if (dateFilter) {
    params.set('dateFilter', dateFilter);
  }

  return params;
}

// Date picker component
interface DatePickerProps {
  selected: Date | undefined;
  onSelect: (date: Date | undefined) => void;
  placeholder: string;
}

function DatePicker({ selected, onSelect, placeholder }: DatePickerProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = useCallback(
    (date: Date | undefined) => {
      onSelect(date);
      setOpen(false);
    },
    [onSelect]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-[200px] justify-start text-left font-normal text-xs',
            !selected && 'text-muted-foreground'
          )}
        >
          <CalendarIcon className="mr-2 h-3 w-3" />
          {/* Format date for display - always show in local time */}
          {selected ? format(selected, 'PPP') : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={selected}
          onSelect={handleSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

// Get the date range for a quick filter using date-utils library
export function getDateRangeForQuickFilter(filterType: DateQuickFilterType): {
  start?: Date;
  end?: Date;
} {
  // Use the library function directly since we've aligned our filter types
  const libType = mapToLibFilterType(filterType);
  return getLibDateRange(libType);
}

// Get a human-readable label for the quick filter using date-utils library
export function getDateQuickFilterLabel(
  filterType: DateQuickFilterType
): string {
  // Use the library function directly since we've aligned our filter types
  const libType = mapToLibFilterType(filterType);
  return getLibFilterLabel(libType);
}

// Define display modes for the filters
export type FilterDisplayMode = 'badges' | 'dropdown';

export function Filters({
  onFilterChange,
  defaultFilter = DATE_FILTER_OPTIONS.LAST_30_DAYS
}: {
  onFilterChange: (state: FilterState) => void;
  defaultFilter?: DateQuickFilterType;
}) {
  // Initialize with the default filter
  const [activeQuickFilter, setActiveQuickFilter] =
    useState<DateQuickFilterType>(defaultFilter);

  // Initialize date range from the default filter (in UTC)
  const initialDateRange = getDateRangeForQuickFilter(defaultFilter);
  const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>(
    initialDateRange
  );

  // Update parent when filters change
  useEffect(() => {
    const newFilterState: FilterState = {
      dateFilter: activeQuickFilter,
      startDate: dateRange.start,
      endDate: dateRange.end,
      params: buildFilterParams({
        dateFilter: activeQuickFilter,
        startDate: dateRange.start,
        endDate: dateRange.end
      })
    };

    onFilterChange(newFilterState);
  }, [activeQuickFilter, dateRange, onFilterChange]);

  // Handle start date change - convert local date to UTC
  const handleStartDateChange = useCallback((localDate: Date | undefined) => {
    if (!localDate) {
      setDateRange((prev) => ({ ...prev, start: undefined }));
    } else {
      // Convert local date to UTC and set to start of day
      const utcDate = toUTC(localDate);
      setDateRange((prev) => ({ ...prev, start: setToStartOfDayUTC(utcDate) }));
    }

    // Change to custom filter when manually changing dates
    setActiveQuickFilter(DATE_FILTER_OPTIONS.CUSTOM);
  }, []);

  // Handle end date change - convert local date to UTC
  const handleEndDateChange = useCallback((localDate: Date | undefined) => {
    if (!localDate) {
      setDateRange((prev) => ({ ...prev, end: undefined }));
    } else {
      // Convert local date to UTC and set to end of day
      const utcDate = toUTC(localDate);
      setDateRange((prev) => ({ ...prev, end: setToEndOfDayUTC(utcDate) }));
    }

    // Change to custom filter when manually changing dates
    setActiveQuickFilter(DATE_FILTER_OPTIONS.CUSTOM);
  }, []);

  // Handle quick filter selection
  const handleQuickFilterSelect = useCallback(
    (filterType: DateQuickFilterType) => {
      setActiveQuickFilter(filterType);

      // Update the date range based on the quick filter
      if (filterType) {
        const newDateRange = getDateRangeForQuickFilter(filterType);
        setDateRange(newDateRange);
      }
    },
    []
  );

  // Get the current filter label for display
  const currentFilterLabel = getDateQuickFilterLabel(activeQuickFilter);

  return (
    <div className="p-4">
      {/* All filters in a single row */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Quick Filter Dropdown */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground whitespace-nowrap">
            Quick Filter:
          </span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="min-w-[160px] justify-between text-sm"
              >
                {currentFilterLabel}
                <ChevronDown className="ml-2 h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  handleQuickFilterSelect(DATE_FILTER_OPTIONS.LAST_30_DAYS)
                }
              >
                Last 30 Days
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  handleQuickFilterSelect(DATE_FILTER_OPTIONS.LAST_60_DAYS)
                }
              >
                Last 60 Days
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  handleQuickFilterSelect(DATE_FILTER_OPTIONS.LAST_90_DAYS)
                }
              >
                Last 90 Days
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  handleQuickFilterSelect(DATE_FILTER_OPTIONS.YEAR_TO_DATE)
                }
              >
                Year to Date {getCurrentYear()}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleQuickFilterSelect(DATE_FILTER_OPTIONS.Q1)}
              >
                {formatQuarterLabel(1)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleQuickFilterSelect(DATE_FILTER_OPTIONS.Q2)}
              >
                {formatQuarterLabel(2)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleQuickFilterSelect(DATE_FILTER_OPTIONS.Q3)}
              >
                {formatQuarterLabel(3)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleQuickFilterSelect(DATE_FILTER_OPTIONS.Q4)}
              >
                {formatQuarterLabel(4)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  handleQuickFilterSelect(DATE_FILTER_OPTIONS.CUSTOM)
                }
              >
                Custom Date Range
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Start Date Picker */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground whitespace-nowrap">
            Start Date:
          </span>
          <DatePicker
            selected={dateRange.start ? new Date(dateRange.start) : undefined}
            onSelect={handleStartDateChange}
            placeholder="Select start date"
          />
        </div>

        {/* End Date Picker */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground whitespace-nowrap">
            End Date:
          </span>
          <DatePicker
            selected={dateRange.end ? new Date(dateRange.end) : undefined}
            onSelect={handleEndDateChange}
            placeholder="Select end date"
          />
        </div>
      </div>
    </div>
  );
}
