import { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Legend,
  CartesianGrid
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { Info, TrendingUp, BarChart3 } from 'lucide-react';

// Define the data structure coming from our query
interface TimelineDataItem {
  month: string;
  year: number;
  timestamp: number;
  spending: number;
  transactionCount: number;
  projected?: number;
}

interface SpendingTimelineProps {
  data: TimelineDataItem[];
  onTimeRangeChange?: (range: string) => void;
}

// Custom tooltip component for the chart
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const isProjected = !!payload[0]?.payload?.projected;

    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-md shadow-md">
        <p className="font-medium text-sm">
          {payload[0]?.payload?.month} {payload[0]?.payload?.year}
        </p>

        {isProjected ? (
          <div className="mt-1">
            <p className="text-amber-600 dark:text-amber-400 font-medium">
              ${payload[0]?.payload?.projected.toFixed(2)}{' '}
              <span className="text-xs font-normal">(projected)</span>
            </p>
          </div>
        ) : (
          <div className="mt-1">
            <p className="text-blue-600 dark:text-blue-400 font-medium">
              ${payload[0]?.value.toFixed(2)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {payload[0]?.payload?.transactionCount} transactions
            </p>
          </div>
        )}
      </div>
    );
  }

  return null;
};

// Format data for displaying
const formatChartData = (data: TimelineDataItem[]) => {
  return data.map((item) => ({
    ...item,
    // Format as "Month Year" for display
    label: `${item.month} ${item.year}`,
    // If it's a projected month, display the projected value
    value: item.projected || item.spending
  }));
};

export const SpendingTimeline: React.FC<SpendingTimelineProps> = ({
  data,
  onTimeRangeChange
}) => {
  // State for the time range filter
  const [timeRange, setTimeRange] = useState<string>('1y'); // Default to 1 year

  // Format data for display
  const chartData = formatChartData(data);

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    if (onTimeRangeChange) {
      onTimeRangeChange(value);
    }
  };

  // Calculate growth trend
  const calculateGrowth = () => {
    // Need at least 2 months with real spending to calculate growth
    const realMonths = data.filter((m) => m.spending > 0);
    if (realMonths.length < 2) return null;

    const lastMonth = realMonths[realMonths.length - 1];
    const previousMonth = realMonths[realMonths.length - 2];

    if (!lastMonth || !previousMonth) return null;

    const growth =
      ((lastMonth.spending - previousMonth.spending) / previousMonth.spending) *
      100;

    // Return both the growth percentage and month information
    return {
      percentage: growth,
      currentMonth: `${lastMonth.month} ${lastMonth.year}`,
      previousMonth: `${previousMonth.month} ${previousMonth.year}`,
      currentAmount: lastMonth.spending,
      previousAmount: previousMonth.spending
    };
  };

  const growthData = calculateGrowth();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Spending Timeline</CardTitle>
            <CardDescription>
              Monthly spending patterns over time
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            {growthData !== null && (
              <div
                className={`px-3 py-1.5 rounded-md text-xs font-medium ${
                  growthData.percentage >= 0
                    ? 'bg-red-50 text-red-600 dark:bg-red-950/50 dark:text-red-400'
                    : 'bg-green-50 text-green-600 dark:bg-green-950/50 dark:text-green-400'
                }`}
              >
                <div className="flex items-center mb-0.5">
                  {growthData.percentage >= 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingUp className="h-3 w-3 mr-1 transform rotate-180" />
                  )}
                  <span className="font-bold">
                    {Math.abs(growthData.percentage).toFixed(1)}%
                  </span>
                  <span className="ml-1">
                    {growthData.percentage >= 0 ? 'increase' : 'decrease'}
                  </span>
                </div>
                <div className="text-[10px] opacity-90">
                  {growthData.previousMonth} → {growthData.currentMonth}
                </div>
              </div>
            )}

            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-[180px] h-8">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3m">Last 3 months</SelectItem>
                <SelectItem value="6m">Last 6 months</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
                <SelectItem value="ytd">Year to date</SelectItem>
                <SelectItem value="all">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="area">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="area" className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                Area
              </TabsTrigger>
              <TabsTrigger value="bar" className="flex items-center">
                <BarChart3 className="h-4 w-4 mr-2" />
                Bar
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <span className="inline-block w-3 h-3 bg-blue-500 dark:bg-blue-400 rounded-full mr-1"></span>
              <span className="mr-3">Actual</span>

              <span className="inline-block w-3 h-3 bg-amber-500 dark:bg-amber-400 rounded-full mr-1"></span>
              <span>Projected</span>
            </div>
          </div>

          <TabsContent value="area" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 20, left: 10, bottom: 30 }}
              >
                <defs>
                  <linearGradient
                    id="colorSpending"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient
                    id="colorProjected"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="3 3"
                  vertical={false}
                  opacity={0.2}
                />
                <XAxis
                  dataKey="month"
                  tickFormatter={(tick, index) =>
                    `${tick} ${chartData[index]?.year}`
                  }
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tickFormatter={(tick) => `$${tick}`} axisLine={false} />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="spending"
                  stroke="#3b82f6"
                  fillOpacity={1}
                  fill="url(#colorSpending)"
                  name="Actual Spending"
                  activeDot={{ r: 6 }}
                  animationDuration={1000}
                />
                <Area
                  type="monotone"
                  dataKey="projected"
                  stroke="#f59e0b"
                  fillOpacity={0.7}
                  fill="url(#colorProjected)"
                  strokeDasharray="5 5"
                  name="Projected Spending"
                  activeDot={{ r: 6 }}
                  animationDuration={1000}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="bar" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 10, right: 20, left: 10, bottom: 30 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  vertical={false}
                  opacity={0.2}
                />
                <XAxis
                  dataKey="month"
                  tickFormatter={(tick, index) =>
                    `${tick} ${chartData[index]?.year}`
                  }
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tickFormatter={(tick) => `$${tick}`} axisLine={false} />
                <Tooltip content={<CustomTooltip />} />
                <Bar
                  dataKey="spending"
                  fill="#3b82f6"
                  name="Actual Spending"
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                />
                <Bar
                  dataKey="projected"
                  fill="#f59e0b"
                  name="Projected Spending"
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>

        <div className="flex items-center mt-4 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 rounded-md p-2">
          <Info className="h-3.5 w-3.5 mr-2 text-gray-400" />
          <span>
            Projections are based on historical spending patterns and may not
            reflect future expenses
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

// Skeleton loader for the component
export const SpendingTimelineSkeleton: React.FC = () => (
  <Card>
    <CardHeader>
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-6 w-48 mb-1" />
          <Skeleton className="h-4 w-72" />
        </div>
        <Skeleton className="h-8 w-[180px]" />
      </div>
    </CardHeader>
    <CardContent>
      <div className="mb-4">
        <Skeleton className="h-8 w-48" />
      </div>
      <Skeleton className="h-[300px] w-full" />
    </CardContent>
  </Card>
);
