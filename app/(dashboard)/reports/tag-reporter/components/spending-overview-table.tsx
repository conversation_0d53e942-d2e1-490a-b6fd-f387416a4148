'use client';

import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getSortedRowModel,
  useReactTable,
  ExpandedState,
  SortingState,
  Row
} from '@tanstack/react-table';
import {
  ChevronDown,
  ChevronUp,
  TrendingDown,
  TrendingUp,
  Minus,
  Download
} from 'lucide-react';
import {
  useState,
  useMemo,
  useEffect,
  forwardRef,
  useImperativeHandle
} from 'react';
import { SpendingRow, SpendingOverviewTableProps } from '@/zod/reports-schema';
import { useToast } from '@/components/hooks/use-toast';

// The SpendingRow type should match what the backend provides:
// interface SpendingRow {
//   id: string | number;
//   name: string;
//   amount: number;
//   transactionCount: number;
//   trend: "up" | "down" | "stable";
//   parentId: string | number | null;
//   percentage?: number; // Added by backend
//   subCategories?: SpendingRow[];
// }

export interface SpendingOverviewTableRef {
  downloadCSV: () => void;
}

export const SpendingOverviewTable = forwardRef<
  SpendingOverviewTableRef,
  SpendingOverviewTableProps
>(function SpendingOverviewTable({ data }, ref) {
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'amount', desc: true }
  ]);
  const { toast } = useToast();

  // Log the data received from the backend
  useEffect(() => {
    console.log('Data received from backend:', data);
  }, [data]);

  // Memoize the columns definition
  const memoizedColumns = useMemo<ColumnDef<SpendingRow>[]>(
    () => [
      {
        id: 'expander',
        header: () => null,
        cell: ({ row }) => {
          return row.getCanExpand() ? (
            <Button
              className="size-6 shadow-none text-muted-foreground hover:text-foreground"
              onClick={(e) => {
                e.stopPropagation();
                row.getToggleExpandedHandler()();
              }}
              aria-expanded={row.getIsExpanded()}
              aria-label={
                row.getIsExpanded()
                  ? `Collapse details for ${row.original.name}`
                  : `Expand details for ${row.original.name}`
              }
              size="icon"
              variant="ghost"
            >
              {row.getIsExpanded() ? (
                <ChevronUp
                  className="opacity-60"
                  size={14}
                  strokeWidth={2}
                  aria-hidden="true"
                />
              ) : (
                <ChevronDown
                  className="opacity-60"
                  size={14}
                  strokeWidth={2}
                  aria-hidden="true"
                />
              )}
            </Button>
          ) : null;
        }
      },
      {
        header: 'Category',
        accessorKey: 'name',
        cell: ({ row }) => (
          <div
            className={cn(
              'flex items-center gap-2',
              !row.original.parentId && 'font-medium',
              row.original.parentId && 'text-sm text-muted-foreground pl-6'
            )}
          >
            {row.getValue('name')}
            {row.getCanExpand() && (
              <span className="text-xs text-muted-foreground">
                ({row.original.subCategories?.length || 0})
              </span>
            )}
          </div>
        )
      },
      {
        header: 'Transactions',
        accessorKey: 'transactionCount',
        cell: ({ row }) => {
          const count = row.original.transactionCount || 0;

          return (
            <div
              className={cn(
                'text-right',
                !row.original.parentId && 'font-medium',
                row.original.parentId && 'text-sm text-muted-foreground'
              )}
            >
              {count.toLocaleString()}
            </div>
          );
        }
      },
      {
        header: 'Amount',
        accessorKey: 'amount',
        cell: ({ row }) => {
          const amount = row.original.amount || 0;

          const formatted = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
          }).format(amount);

          return (
            <div
              className={cn(
                'text-right',
                !row.original.parentId && 'font-medium',
                row.original.parentId && 'text-sm text-muted-foreground'
              )}
            >
              {formatted}
            </div>
          );
        }
      },
      {
        header: '% of Total',
        accessorKey: 'percentage',
        cell: ({ row }) => {
          // Log the raw value for debugging
          console.log(
            `Percentage for ${row.original.name}:`,
            row.original.percentage
          );

          // Direct passthrough of the backend value with just a '%' sign added
          return (
            <div
              className={cn(
                'text-right',
                !row.original.parentId && 'font-medium',
                row.original.parentId && 'text-sm text-muted-foreground'
              )}
            >
              {row.original.percentage !== undefined
                ? `${row.original.percentage}%`
                : '0%'}
            </div>
          );
        }
      },
      {
        header: 'Trend',
        accessorKey: 'trend',
        cell: ({ row }) => {
          const trend = row.getValue('trend') as 'up' | 'down' | 'stable';
          const isParent = !row.original.parentId;

          return (
            <div className="flex items-center justify-end">
              {trend === 'up' && (
                <Badge
                  variant="outline"
                  className={cn(
                    'text-green-600 border-green-600',
                    !isParent && 'text-xs py-0'
                  )}
                >
                  <TrendingUp
                    className={cn('mr-1', isParent ? 'h-3 w-3' : 'h-2.5 w-2.5')}
                  />
                  Up
                </Badge>
              )}
              {trend === 'down' && (
                <Badge
                  variant="outline"
                  className={cn(
                    'text-red-600 border-red-600',
                    !isParent && 'text-xs py-0'
                  )}
                >
                  <TrendingDown
                    className={cn('mr-1', isParent ? 'h-3 w-3' : 'h-2.5 w-2.5')}
                  />
                  Down
                </Badge>
              )}
              {trend === 'stable' && (
                <Badge
                  variant="outline"
                  className={cn(
                    'text-gray-600 border-gray-600',
                    !isParent && 'text-xs py-0'
                  )}
                >
                  <Minus
                    className={cn('mr-1', isParent ? 'h-3 w-3' : 'h-2.5 w-2.5')}
                  />
                  Stable
                </Badge>
              )}
            </div>
          );
        }
      }
    ],
    []
  );

  // Initialize the TanStack Table with all necessary configurations
  const table = useReactTable({
    data,
    columns: memoizedColumns,
    state: {
      expanded,
      sorting
    },
    onExpandedChange: setExpanded,
    onSortingChange: setSorting,
    getSubRows: (row) => row.subCategories,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableExpanding: true,
    debugTable: process.env.NODE_ENV === 'development'
  });

  // Calculate totals for the footer
  const totals = useMemo(() => {
    // Only count top-level rows to avoid double counting
    const topLevelRows = data.filter((row) => !row.parentId);

    const totalTransactions = topLevelRows.reduce(
      (sum, row) => sum + (row.transactionCount || 0),
      0
    );

    const totalAmount = topLevelRows.reduce(
      (sum, row) => sum + (row.amount || 0),
      0
    );

    const formattedAmount = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(totalAmount);

    return {
      transactions: totalTransactions.toLocaleString(),
      amount: formattedAmount
    };
  }, [data]);

  // Automatically expand all top-level categories if there are only a few
  useEffect(() => {
    if (
      data.length > 0 &&
      data.length <= 5 &&
      Object.keys(expanded).length === 0
    ) {
      const newExpanded: ExpandedState = {};
      data.forEach((row: SpendingRow, index: number) => {
        if (row.subCategories?.length) {
          newExpanded[index] = true;
        }
      });
      setExpanded(newExpanded);
    }
  }, [data, expanded]);

  // Function to convert hierarchical data to CSV format
  const convertToCSV = () => {
    // Define CSV headers
    const headers = [
      'Category',
      'Transactions',
      'Amount',
      '% of Total',
      'Trend'
    ];
    const csvRows = [headers.join(',')];

    // Function to process each row recursively
    const processRow = (row: SpendingRow, isSubcategory = false) => {
      // Get transaction count and amount directly from the row
      const transactionCount = row.transactionCount || 0;
      const amount = row.amount || 0;

      // Direct passthrough of the backend percentage value
      const displayPercentage =
        row.percentage !== undefined ? row.percentage : 0;

      // Format the category name with indentation for subcategories
      const categoryName = isSubcategory ? `  - ${row.name}` : row.name;

      // Format the amount as currency
      const formattedAmount = amount.toFixed(2);

      // Format the trend
      let trendText = 'Stable';
      if (row.trend === 'up') trendText = 'Up';
      if (row.trend === 'down') trendText = 'Down';

      // Create CSV row with proper escaping for fields that might contain commas
      const csvRow = [
        `"${categoryName}"`,
        transactionCount,
        formattedAmount,
        `${displayPercentage}%`,
        trendText
      ].join(',');

      csvRows.push(csvRow);

      // Process subcategories if they exist
      if (row.subCategories && row.subCategories.length > 0) {
        row.subCategories.forEach((subRow) => {
          processRow(subRow, true);
        });
      }
    };

    // Process all top-level rows
    data.forEach((row) => {
      processRow(row);
    });

    // Add totals row
    // Only count top-level rows to avoid double counting
    const topLevelRows = data.filter((row) => !row.parentId);

    const totalTransactions = topLevelRows.reduce(
      (sum, row) => sum + (row.transactionCount || 0),
      0
    );

    const totalAmount = topLevelRows.reduce(
      (sum, row) => sum + (row.amount || 0),
      0
    );

    const totalRow = [
      `"Total"`,
      totalTransactions,
      totalAmount.toFixed(2),
      '100%',
      ''
    ].join(',');

    csvRows.push(totalRow);

    return csvRows.join('\n');
  };

  // Function to download CSV
  const downloadCSV = () => {
    try {
      const csv = convertToCSV();
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      // Create a link element to trigger download
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'spending_overview.csv');
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Download started',
        description: 'Your spending overview data is being downloaded as CSV.'
      });
    } catch (error) {
      console.error('Error downloading CSV:', error);
      toast({
        title: 'Download failed',
        description: 'There was an error downloading the CSV file.',
        variant: 'destructive'
      });
    }
  };

  // Expose the downloadCSV method to parent components
  useImperativeHandle(ref, () => ({
    downloadCSV
  }));

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={cn(
                      header.column.getCanSort() &&
                        'cursor-pointer select-none',
                      header.id === 'amount' && 'text-right',
                      header.id === 'percentage' && 'text-right',
                      header.id === 'transactionCount' && 'text-right'
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <div className="flex items-center justify-between">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {header.column.getCanSort() && (
                        <span className="ml-1">
                          {{
                            asc: <ChevronUp className="h-4 w-4" />,
                            desc: <ChevronDown className="h-4 w-4" />
                          }[header.column.getIsSorted() as string] ?? null}
                        </span>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className={cn(
                    'transition-colors',
                    row.original.parentId && 'h-9 hover:bg-muted/30',
                    !row.original.parentId &&
                      'h-10 hover:bg-muted/50 font-medium',
                    row.getCanExpand() && 'cursor-pointer'
                  )}
                  onClick={() => {
                    if (row.getCanExpand()) {
                      row.toggleExpanded();
                    }
                  }}
                  aria-expanded={row.getIsExpanded()}
                  data-state={row.getIsExpanded() ? 'expanded' : 'collapsed'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={memoizedColumns.length}
                  className="h-24 text-center"
                >
                  No spending data available.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <tfoot className="border-t">
            <TableRow className="bg-muted/50">
              <TableCell>{/* Expander column - empty */}</TableCell>
              <TableCell className="font-medium">Total</TableCell>
              <TableCell className="text-right font-medium">
                {totals.transactions}
              </TableCell>
              <TableCell className="text-right font-medium">
                {totals.amount}
              </TableCell>
              <TableCell className="text-right font-medium">100%</TableCell>
              <TableCell>{/* Trend column - empty */}</TableCell>
            </TableRow>
          </tfoot>
        </Table>
      </div>
    </div>
  );
});
