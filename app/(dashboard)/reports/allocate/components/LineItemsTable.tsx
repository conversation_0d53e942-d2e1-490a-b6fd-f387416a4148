'use client';

import { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import { api } from '@/convex/_generated/api';
import { useQuery, useMutation } from 'convex/react';
import { Id } from '@/convex/_generated/dataModel';
import { format } from 'date-fns';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  SortingState,
  useReactTable
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ArrowUpDown } from 'lucide-react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { useToast } from '@/components/hooks/use-toast';
import { Spinner } from '@/components/icons';
import { CategoryDropdown } from '@/components/category-dropdown';
import { SelectionType } from './AllocateFilter';
import { TableSkeleton } from './TableSkeleton';

// Interface for line items based on LineItemSchema
interface LineItem {
  _id: Id<'lineItems'>;
  _creationTime: number;
  bill_id: Id<'bills'>;
  amount: number;
  post_date: number;
  merchant_name: string;
  description: string;
  spending_category?: Id<'tags'>;
  vendor_id: Id<'organizations'>;
  updated_at: number;
}

// Interface for category used in CategoryDropdown
interface ExpenseCategory {
  _id: Id<'tags'>;
  name: string;
  tag_type: Id<'tag_types'>;
  children?: Array<{
    _id: Id<'tags'>;
    name: string;
    tag_type: Id<'tag_types'>;
  }>;
}

// Interface for paginated results
interface PaginatedLineItems {
  page: LineItem[];
  isDone: boolean;
  continueCursor: string | null;
}

/**
 * LineItemsTable Component
 *
 * This component displays a table of all line items using TanStack Table.
 * It fetches data directly using server-side pagination with Convex.
 */
export function LineItemsTable({
  categories,
  dateRange,
  searchQuery,
  selectedCategory,
  categorySelectionType = 'all_categories',
  onVisibleItemsChange
}: {
  categories?: ExpenseCategory[];
  dateRange?: { start?: Date; end?: Date };
  searchQuery?: string;
  selectedCategory?: Id<'tags'> | undefined;
  categorySelectionType?: SelectionType;
  onVisibleItemsChange?: (items: LineItem[]) => void;
} = {}) {
  const { toast } = useToast();

  // State for sorting
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'post_date', desc: true }
  ]);

  // State for pagination
  const [continueCursor, setContinueCursor] = useState<string | null>(null);
  const [pageIndex, setPageIndex] = useState<number>(0);
  const [pageSize] = useState<number>(25);

  // State for tracking pagination history for "back" button
  const [cursorHistory, setCursorHistory] = useState<(string | null)[]>([null]);

  // State for error tracking
  const [hasError, setHasError] = useState(false);

  // Add mutation for updating line items
  const updateLineItem = useMutation(api.lineItems.updateLineItem);

  // Add optimistic updates state
  const [optimisticUpdates, setOptimisticUpdates] = useState<Record<string, Id<'tags'> | undefined>>({});

  // Query line items with pagination
  const queryResults = useQuery(api.lineItems.listLineItems, {
    filter: {
      // Sort by date descending
      sortBy: 'date',
      sortDirection: 'desc',

      // Filters
      ...(dateRange?.start && { startDate: dateRange.start.getTime() }),
      ...(dateRange?.end && { endDate: dateRange.end.getTime() }),
      ...(searchQuery && { searchText: searchQuery }),

      // Category filtering - handling different selection types
      // - 'category' - filter for a specific category
      // - 'uncategorized' - filter for items with no category (undefined)
      // - 'all_categories' - no category filtering (show all items)
      ...(categorySelectionType === 'category' &&
        selectedCategory && {
          category: selectedCategory
        }),
      ...(categorySelectionType === 'uncategorized' && {
        onlyUncategorized: true
      })
      // For 'all_categories', we don't add any category filter
    },
    paginationOpts: {
      numItems: pageSize,
      cursor: continueCursor
    }
  }) as PaginatedLineItems | undefined;

  // Add loading state tracking
  const isLoading = queryResults === undefined;

  // Handle errors in an effect
  useEffect(() => {
    if (queryResults === undefined && !isLoading) {
      console.error('Error fetching line items: query returned undefined');
      setHasError(true);
    }
  }, [queryResults, isLoading]);

  // Use paginated query results - handle possible undefined
  const lineItems = queryResults?.page || [];

  // Call onVisibleItemsChange when lineItems change, but only after initial load and when not loading
  useEffect(() => {
    if (!isLoading && queryResults) {
      onVisibleItemsChange?.(lineItems);
    }
  }, [queryResults, isLoading, onVisibleItemsChange]);

  // Track if we've reached the end of data
  const isDone = queryResults?.isDone || false;

  // IMPROVED: Navigation functions for server-side pagination
  const handleNextPage = () => {
    if (!isDone && queryResults?.continueCursor) {
      console.log(
        'Moving to next page with cursor:',
        queryResults.continueCursor
      );
      // Save the current cursor to history before changing
      setCursorHistory((prev) => [...prev, queryResults.continueCursor]);
      // Update the cursor to the next page
      setContinueCursor(queryResults.continueCursor);
      setPageIndex((prev) => prev + 1);
    } else {
      toast({
        title: 'End of Results',
        description: "You've reached the end of the available line items.",
        variant: 'default'
      });
    }
  };

  const handlePreviousPage = () => {
    if (pageIndex > 0) {
      // Go back to the previous cursor in history
      const newHistory = [...cursorHistory];
      newHistory.pop(); // Remove the current cursor
      const previousCursor = newHistory[newHistory.length - 1]; // Get the previous cursor

      console.log('Moving to previous page with cursor:', previousCursor);
      setCursorHistory(newHistory);
      setContinueCursor(previousCursor);
      setPageIndex((prev) => prev - 1);
    } else {
      setContinueCursor(null);
      setCursorHistory([null]);
      setPageIndex(0);
    }
  };

  // Format functions
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Define columns for the table
  const columns = useMemo<ColumnDef<LineItem>[]>(
    () => [
      {
        accessorKey: 'post_date',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="whitespace-nowrap h-7 px-2 text-xs"
          >
            Date
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        ),
        cell: ({ row }) => formatDate(row.getValue('post_date'))
      },
      {
        accessorKey: 'merchant_name',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-7 px-2 text-xs"
          >
            Merchant
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        ),
        cell: ({ row }) => (
          <div
            className="truncate max-w-[180px]"
            title={row.getValue('merchant_name')}
          >
            {row.getValue('merchant_name')}
          </div>
        )
      },
      {
        accessorKey: 'description',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-7 px-2 text-xs"
          >
            Description
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        ),
        cell: ({ row }) => (
          <div
            className="truncate max-w-[130px]"
            title={row.getValue('description')}
          >
            {row.getValue('description')}
          </div>
        )
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="whitespace-nowrap h-7 px-2 text-xs"
          >
            Amount
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        ),
        cell: ({ row }) => {
          const amount = parseFloat(row.getValue('amount'));
          return <div className="text-right">{formatCurrency(amount)}</div>;
        }
      }
    ],
    [categories]
  );

  // Use React.memo or useMemo to prevent unnecessary re-renders
  const memoizedColumns = useMemo(() => columns, [columns]);

  // Create a properly typed table
  const table = useReactTable<LineItem>({
    data: lineItems,
    columns: memoizedColumns,
    state: {
      sorting
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // Use manual pagination for server-side
    manualPagination: true,
    pageCount: -1 // -1 tells TanStack not to worry about the page count
  });

  // Table UI
  return (
    <div className="space-y-2">
      {isLoading ? (
        <TableSkeleton />
      ) : (
        <>
          <div>
            <Table className="w-full">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow
                    key={headerGroup.id}
                    className="h-8 hover:bg-transparent border-b"
                  >
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id} className="h-8 py-1 px-2">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                    {/* Add static header for category column */}
                    <TableHead className="h-8 py-1 px-2">
                      <div className="text-xs">Category</div>
                    </TableHead>
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {hasError ? (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length + 1}
                      className="h-16 text-center text-red-500 border-b border-gray-100"
                    >
                      Error loading line items. Please try refreshing the page.
                    </TableCell>
                  </TableRow>
                ) : lineItems.length > 0 ? (
                  lineItems.map((item: LineItem) => (
                    <TableRow
                      key={item._id.toString()}
                      className="h-6 border-b border-gray-100"
                    >
                      {/* Date column */}
                      <TableCell key={`${item._id}-date`} className="py-0 px-2">
                        {formatDate(item.post_date)}
                      </TableCell>
                      
                      {/* Merchant column */}
                      <TableCell key={`${item._id}-merchant`} className="py-0 px-2">
                        <div
                          className="truncate max-w-[180px]"
                          title={item.merchant_name}
                        >
                          {item.merchant_name}
                        </div>
                      </TableCell>
                      
                      {/* Description column */}
                      <TableCell key={`${item._id}-description`} className="py-0 px-2">
                        <div
                          className="truncate max-w-[130px]"
                          title={item.description}
                        >
                          {item.description}
                        </div>
                      </TableCell>
                      
                      {/* Amount column */}
                      <TableCell key={`${item._id}-amount`} className="text-right py-1 px-2">
                        {formatCurrency(item.amount)}
                      </TableCell>
                      
                      {/* Category column */}
                      <TableCell key={`${item._id}-category`} className="py-1 px-2">
                        <CategoryDropdown
                          lineItem={{
                            _id: item._id,
                            spending_category: optimisticUpdates[item._id.toString()] ?? item.spending_category
                          }}
                          categories={categories || []}
                          onCategoryChangeAction={(categoryId, selectionType) => {
                            // Apply optimistic update immediately
                            setOptimisticUpdates(prev => ({
                              ...prev,
                              [item._id.toString()]: categoryId || undefined
                            }));

                            // Handle async operation separately
                            updateLineItem({
                              id: item._id.toString(),
                              updates: { spending_category: categoryId }
                            })
                              .then(result => {
                                if (result.success) {
                                  toast({
                                    title: 'Category Updated',
                                    description: `Updated category for ${item.merchant_name}`,
                                    duration: 2000
                                  });
                                } else {
                                  throw new Error('Update failed');
                                }
                              })
                              .catch(error => {
                                setOptimisticUpdates(prev => {
                                  const { [item._id.toString()]: _, ...rest } = prev;
                                  return rest;
                                });
                                
                                toast({
                                  variant: 'destructive',
                                  title: 'Failed to Update',
                                  description: 'Could not update category. Please try again.',
                                  duration: 3000
                                });
                              });
                          }}
                          triggerClassName="w-[150px]"
                        />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length + 1}
                      className="h-16 text-center border-b border-gray-100"
                    >
                      <div className="flex flex-col items-center justify-center">
                        <div>No line items found</div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {!table.getCanNextPage() && !table.getCanPreviousPage() ? null : table.getPageCount() !== -1 ? (
            /* Client-side pagination section */
            <Pagination className="mt-1">
              <PaginationContent className="h-7">
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => table.previousPage()}
                    isDisabled={!table.getCanPreviousPage()}
                    className="h-7"
                  />
                </PaginationItem>
                {Array.from({ length: Math.min(5, table.getPageCount()) }).map(
                  (_, i) => (
                    <PaginationItem key={i}>
                      <PaginationLink
                        onClick={() => table.setPageIndex(i)}
                        isActive={table.getState().pagination.pageIndex === i}
                        className="h-7"
                      >
                        {i + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )
                )}
                {table.getPageCount() > 5 && (
                  <PaginationItem>
                    <PaginationEllipsis className="h-7" />
                  </PaginationItem>
                )}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => table.nextPage()}
                    isDisabled={!table.getCanNextPage()}
                    className="h-7"
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          ) : (
            /* Server-side pagination section */
            <div className="flex items-center justify-end mt-1">
              <div className="text-xs text-muted-foreground mr-2">
                Page {pageIndex + 1} {!isDone && '...'}
              </div>
              <Pagination>
                <PaginationContent className="h-7">
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={handlePreviousPage}
                      isDisabled={pageIndex === 0}
                      className="h-7"
                    />
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => {}}
                      isActive={true}
                      className="h-7"
                    >
                      {pageIndex + 1}
                    </PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationNext
                      onClick={handleNextPage}
                      isDisabled={isDone}
                      className="h-7"
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
}
