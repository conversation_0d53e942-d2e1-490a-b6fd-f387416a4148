import { AICategorizeButton } from '@/components/auto-categorize-button';
import { Id } from '@/convex/_generated/dataModel';

// Interface for line items
interface LineItem {
  _id: Id<'lineItems'>;
  amount: number;
  description: string;
  merchant_name: string;
  spending_category?: Id<'tags'>;
}

interface FilteredAICategorizeButtonProps {
  visibleLineItems: LineItem[];
  onSuccess?: (result: { success: boolean; categorized: number; mappings: Record<string, { category: string }> }) => void;
}

export function FilteredAICategorizeButton({ visibleLineItems, onSuccess }: FilteredAICategorizeButtonProps) {
  // Filter to only uncategorized items
  const uncategorizedItems = visibleLineItems.filter(item => !item.spending_category);

  return (
    <AICategorizeButton 
      lineItems={uncategorizedItems}
      onSuccess={onSuccess}
    />
  );
} 