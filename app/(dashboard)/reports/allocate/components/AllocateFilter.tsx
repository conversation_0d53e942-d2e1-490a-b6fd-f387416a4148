'use client';

import {
  memo,
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect,
  ChangeEvent
} from 'react';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CalendarIcon, ChevronDown, Search, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Id } from '@/convex/_generated/dataModel';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
  SelectSeparator
} from '@/components/ui/select';
import {
  DateQuickFilterType,
  getDateQuickFilterLabel,
  toUTC,
  setToStartOfDayUTC
} from '@/lib/date-utils';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

// CSS class constants for reuse
const RESPONSIVE_FLEX_CONTAINER =
  'flex flex-col space-y-2 md:space-y-0 md:flex-row md:items-center md:space-x-2';

// Special value constants for filter options
export const UNCATEGORIZED_VALUE = 'uncategorized' as const;
export const ALL_CATEGORIES_VALUE = 'all' as const;

// Selection type for better context on what was selected
export type SelectionType = 'category' | 'uncategorized' | 'all_categories';

// Updated to include undefined and match database representation
export type CategoryFilterValue =
  | Id<'tags'>
  | undefined
  | 'uncategorized'
  | 'all';

// Utility functions for category value conversion
export const categoryValueToSelectValue = (
  catValue: CategoryFilterValue
): string => {
  if (catValue === undefined) {
    return ALL_CATEGORIES_VALUE; // Default to ALL when undefined
  }
  if (catValue === 'uncategorized') {
    return UNCATEGORIZED_VALUE;
  }
  if (catValue === 'all') {
    return ALL_CATEGORIES_VALUE;
  }
  return catValue.toString();
};

export const selectionTypeToFilterValue = (
  category: Id<'tags'> | undefined,
  selectionType: SelectionType
): CategoryFilterValue => {
  if (selectionType === 'uncategorized') return 'uncategorized';
  if (selectionType === 'all_categories' || !category) return 'all';
  return category;
};

export const filterValueToCategory = (
  value: CategoryFilterValue
): { category: Id<'tags'> | undefined; selectionType: SelectionType } => {
  if (value === 'uncategorized')
    return { category: undefined, selectionType: 'uncategorized' };
  if (value === 'all' || value === undefined)
    return { category: undefined, selectionType: 'all_categories' };
  return { category: value, selectionType: 'category' };
};

// Types
export interface AllocateFilterProps {
  dateRange: {
    start?: Date;
    end?: Date;
  };
  onDateRangeChange: (dateRange: { start?: Date; end?: Date }) => void;
  activeDateQuickFilter: DateQuickFilterType;
  onDateQuickFilterChange: (filterType: DateQuickFilterType) => void;
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  selectedCategory?: Id<'tags'> | undefined;
  onCategoryChange?: (
    category: Id<'tags'> | undefined,
    selectionType: SelectionType
  ) => void;
  categories: any[];
  categorySelectionType?: SelectionType;
}

// Date picker component
const DatePickerWithPopover = memo(
  ({
    selected,
    onSelect,
    placeholder
  }: {
    selected?: Date;
    onSelect: (date: Date | undefined) => void;
    placeholder: string;
  }) => {
    const [open, setOpen] = useState(false);

    // Log when selected prop changes
    useEffect(() => {
      console.log(
        `DatePickerWithPopover (${placeholder}): selected prop changed to:`,
        selected
      );
    }, [selected, placeholder]);

    const handleSelect = useCallback(
      (date: Date | undefined) => {
        // If date is undefined, pass undefined up
        if (!date) {
          console.log(
            `DatePickerWithPopover (${placeholder}): Selected date is undefined`
          );
          onSelect(undefined);
          setOpen(false);
          return;
        }

        // For date objects, we need to preserve the date exactly as selected
        // without timezone shifting. We'll use setToStartOfDayUTC to ensure
        // the date is set to midnight UTC on the selected date.
        const utcDate = setToStartOfDayUTC(date);

        // Log for debugging
        console.log(
          `DatePickerWithPopover (${placeholder}): Selected date:`,
          date
        );
        console.log(
          `DatePickerWithPopover (${placeholder}): UTC date:`,
          utcDate
        );
        console.log(
          `DatePickerWithPopover (${placeholder}): Date components:`,
          {
            year: date.getFullYear(),
            month: date.getMonth(),
            day: date.getDate()
          }
        );

        onSelect(utcDate);
        setOpen(false);
      },
      [onSelect, placeholder]
    );

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-[160px] justify-start text-left font-normal text-xs',
              !selected && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="mr-1 h-3.5 w-3.5" />
            {selected ? (
              format(selected, 'MMM d, yyyy')
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selected}
            onSelect={handleSelect}
            initialFocus
            formatters={{
              formatMonthCaption: (date) => format(date, 'MMM yyyy')
            }}
            classNames={{
              caption_label: 'text-xs font-medium',
              day: 'text-xs h-7 w-7'
            }}
          />
        </PopoverContent>
      </Popover>
    );
  }
);

// Integrated CategoryFilter component
const CategoryFilter = memo(function CategoryFilter({
  categories,
  value,
  onChange,
  className,
  contentClassName,
  placeholder = 'Filter by category',
  showFilterIcon = true,
  showUncategorized = true,
  showAllCategories = true
}: {
  categories: Array<{
    _id: Id<'tags'>;
    name: string;
    tag_type: Id<'tag_types'>;
    children?: Array<{
      _id: Id<'tags'>;
      name: string;
      tag_type: Id<'tag_types'>;
    }>;
  }>;
  value: CategoryFilterValue;
  onChange: (value: CategoryFilterValue, selectionType: SelectionType) => void;
  className?: string;
  contentClassName?: string;
  placeholder?: string;
  showFilterIcon?: boolean;
  showUncategorized?: boolean;
  showAllCategories?: boolean;
}) {
  // Track the previous value to prevent unnecessary updates
  const previousValueRef = useRef<string | null>(
    categoryValueToSelectValue(value)
  );

  // Map our internal value to what the Select component expects
  const currentValue = categoryValueToSelectValue(value);

  const handleCategoryChange = (selectValue: string) => {
    // Prevent unnecessary updates by checking if the value hasn't changed
    if (selectValue === previousValueRef.current) {
      return;
    }

    // Update the previous value ref
    previousValueRef.current = selectValue;

    // Handle uncategorized case - return undefined to indicate no category
    if (selectValue === UNCATEGORIZED_VALUE) {
      onChange('uncategorized', 'uncategorized');
      return;
    }

    // Handle all categories case
    if (selectValue === ALL_CATEGORIES_VALUE) {
      onChange('all', 'all_categories');
      return;
    }

    // Otherwise convert to a tag ID and return
    const categoryId = selectValue as Id<'tags'>;
    onChange(categoryId, 'category');
  };

  const getCategoryDisplayName = (value: string): string => {
    if (value === ALL_CATEGORIES_VALUE) return 'All Categories';
    if (value === UNCATEGORIZED_VALUE) return 'Uncategorized';

    // First try to find it as a parent category
    const parentCategory = categories.find(
      cat => cat._id.toString() === value
    );
    if (parentCategory) return parentCategory.name;

    // Then look for it in children
    for (const parent of categories) {
      const child = parent.children?.find(
        sub => sub._id.toString() === value
      );
      if (child) {
        return `${parent.name}: ${child.name}`;
      }
    }

    return placeholder;
  };

  return (
    <div className="relative">
      <Select value={currentValue} onValueChange={handleCategoryChange}>
        <SelectTrigger
          className={cn(
            'h-9 w-[180px] rounded-md border text-xs',
            'bg-background text-foreground',
            'border-input hover:border-muted-foreground/30',
            className
          )}
        >
          {showFilterIcon && (
            <Filter className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
          )}
          <SelectValue>{getCategoryDisplayName(currentValue)}</SelectValue>
        </SelectTrigger>

        <SelectContent
          className={cn(
            'rounded-md border bg-popover p-1',
            'shadow-md',
            contentClassName
          )}
          position="popper"
          sideOffset={4}
        >
          <SelectGroup>
            {showAllCategories && (
              <SelectItem value={ALL_CATEGORIES_VALUE} className="text-xs">
                All Categories
              </SelectItem>
            )}

            {showUncategorized && (
              <SelectItem value={UNCATEGORIZED_VALUE} className="text-xs">
                Uncategorized
              </SelectItem>
            )}
          </SelectGroup>

          <SelectSeparator className="my-1" />

          {/* Parent categories with their children */}
          {categories.map((category) => (
            <SelectGroup key={`parent-${category._id}`}>
              {/* Parent category as label */}
              <SelectLabel className="text-xs font-semibold uppercase tracking-wider text-muted-foreground/70 px-2 py-1.5">
                {category.name}
              </SelectLabel>

              {/* Child categories as selectable items */}
              {category.children?.map((sub) => (
                <SelectItem
                  key={`child-${sub._id}`}
                  value={sub._id.toString()}
                  className="text-xs pl-4"
                >
                  {sub.name}
                </SelectItem>
              ))}
            </SelectGroup>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
});

// Date Range Filter component
const DateRangeFilter = ({
  dateRange,
  onDateRangeChange,
  activeQuickFilter,
  onQuickFilterChange
}: {
  dateRange: { start?: Date; end?: Date };
  onDateRangeChange: (range: { start?: Date; end?: Date }) => void;
  activeQuickFilter: DateQuickFilterType;
  onQuickFilterChange: (filter: DateQuickFilterType) => void;
}) => {
  // Log when activeQuickFilter changes
  useEffect(() => {
    console.log(
      'DateRangeFilter: activeQuickFilter changed to:',
      activeQuickFilter
    );
  }, [activeQuickFilter]);

  // Consolidated date change handler that works for both start and end dates
  const handleDateChange = useCallback(
    (field: 'start' | 'end', date: Date | undefined) => {
      // Log for debugging
      console.log(`DateRangeFilter: ${field} date changed to:`, date);

      // Create a new date range object with the updated field
      const newDateRange = { ...dateRange, [field]: date };
      console.log('New date range:', newDateRange);

      // Pass the updated date range to the parent component
      onDateRangeChange(newDateRange);

      // Set the filter type to custom since the user manually selected a date
      console.log('DateRangeFilter: Setting filter type to custom');
      onQuickFilterChange('custom');
    },
    [dateRange, onDateRangeChange, onQuickFilterChange]
  );

  // Log current date range for debugging
  useEffect(() => {
    console.log('DateRangeFilter: Current date range:', dateRange);
  }, [dateRange]);

  return (
    <div className="flex items-center gap-1.5 flex-wrap">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1 text-xs w-[120px] justify-between"
          >
            {getDateQuickFilterLabel(activeQuickFilter)}
            <ChevronDown className="h-3.5 w-3.5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[160px]">
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('last30days')}
            className="text-xs"
          >
            Last 30 Days
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('last60days')}
            className="text-xs"
          >
            Last 60 Days
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('last90days')}
            className="text-xs"
          >
            Last 90 Days
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('ytd')}
            className="text-xs"
          >
            Year to Date
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('custom')}
            className="text-xs"
          >
            Custom
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('q1')}
            className="text-xs"
          >
            Q1
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('q2')}
            className="text-xs"
          >
            Q2
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('q3')}
            className="text-xs"
          >
            Q3
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => onQuickFilterChange('q4')}
            className="text-xs"
          >
            Q4
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DatePickerWithPopover
        selected={dateRange.start}
        onSelect={(date) => handleDateChange('start', date)}
        placeholder="Start Date"
      />
      <span className="text-xs text-muted-foreground">to</span>
      <DatePickerWithPopover
        selected={dateRange.end}
        onSelect={(date) => handleDateChange('end', date)}
        placeholder="End Date"
      />
    </div>
  );
};

// Main AllocateFilter component
export function AllocateFilter({
  dateRange,
  onDateRangeChange,
  activeDateQuickFilter,
  onDateQuickFilterChange,
  searchQuery,
  onSearchQueryChange,
  selectedCategory,
  onCategoryChange,
  categories,
  categorySelectionType = 'all_categories'
}: AllocateFilterProps) {
  // Log date range changes for debugging
  useEffect(() => {
    console.log('AllocateFilter: dateRange prop changed:', dateRange);
  }, [dateRange]);

  // Wrap onDateRangeChange with debugging
  const handleDateRangeChange = useCallback(
    (newDateRange: { start?: Date; end?: Date }) => {
      console.log(
        'AllocateFilter: handleDateRangeChange called with:',
        newDateRange
      );
      onDateRangeChange(newDateRange);
    },
    [onDateRangeChange]
  );

  // Handle search query changes
  const handleSearchChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onSearchQueryChange(e.target.value);
    },
    [onSearchQueryChange]
  );

  // Memoize the search input component
  const searchInput = useMemo(
    () => (
      <div className="relative w-full md:w-auto">
        <Input
          type="search"
          placeholder="Search..."
          value={searchQuery}
          onChange={handleSearchChange}
          className="h-9 w-full md:w-[200px] rounded-md border border-input pl-8 text-xs"
        />
        <Search className="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-muted-foreground" />
      </div>
    ),
    [searchQuery, handleSearchChange]
  );

  // Memoize the category filter component
  const categoryFilter = useMemo(() => {
    if (!onCategoryChange) return null;

    // Convert selectedCategory to the format CategoryFilter expects
    const categoryValue = selectionTypeToFilterValue(
      selectedCategory,
      categorySelectionType
    );

    // Create wrapper function to adapt the onChange callback
    const handleCategoryChange = (
      value: CategoryFilterValue,
      selectionType: SelectionType
    ) => {
      // Convert the internal value format back to what's expected
      const { category } = filterValueToCategory(value);
      onCategoryChange(category, selectionType);
    };

    return (
      <CategoryFilter
        value={categoryValue}
        categories={categories}
        onChange={handleCategoryChange}
        showAllCategories={true}
      />
    );
  }, [onCategoryChange, selectedCategory, categories, categorySelectionType]);

  // Memoize date range filter component
  const dateRangeFilterComponent = useMemo(
    () => (
      <DateRangeFilter
        dateRange={dateRange}
        onDateRangeChange={handleDateRangeChange}
        activeQuickFilter={activeDateQuickFilter}
        onQuickFilterChange={onDateQuickFilterChange}
      />
    ),
    [
      dateRange,
      handleDateRangeChange,
      activeDateQuickFilter,
      onDateQuickFilterChange
    ]
  );

  return (
    <div className="flex flex-wrap items-center gap-2 w-full mb-4">
      {dateRangeFilterComponent}
      <div className="h-6 border-l border-border mx-1"></div>
      {categoryFilter}
      {searchInput}
    </div>
  );
}
