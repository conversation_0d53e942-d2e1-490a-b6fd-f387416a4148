'use client';

import React, { useMemo } from 'react';
import { <PERSON>, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Cell } from 'recharts';
import { TrendingUp, TrendingDown, Minus, X, Maximize2, Minimize2 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig
} from '@/components/ui/chart';

// Define a color palette for the chart
const CHART_COLORS = {
  blue: "hsl(var(--chart-1))",     // Primary
  green: "hsl(var(--chart-2))",    // Success/Positive
  red: "hsl(var(--chart-3))",      // Destructive/Negative
  yellow: "hsl(var(--chart-4))",   // Warning
  purple: "hsl(var(--chart-5))",   // Special
  orange: "hsl(var(--chart-6))",   // Alert
  teal: "hsl(var(--chart-7))",     // Info
  pink: "hsl(var(--chart-8))",     // Highlight
  indigo: "hsl(var(--chart-9))",   // Secondary
  gray: "hsl(var(--chart-10))"     // Neutral/Uncategorized
} as const;

// Create an array of colors for easy cycling (excluding gray which is reserved for uncategorized)
const colorArray = [
  CHART_COLORS.blue,
  CHART_COLORS.green,
  CHART_COLORS.purple,
  CHART_COLORS.orange,
  CHART_COLORS.teal,
  CHART_COLORS.pink,
  CHART_COLORS.indigo,
  CHART_COLORS.yellow,
  CHART_COLORS.red,
];

// Define the props interface
export interface SpendingGraphProps {
  spendingData: Array<{
    id: string;
    name: string;
    value: number;
    formattedAmount: string;
    percentage: number;
    count?: number;
    color?: string;
  }>;
  totalSpent: number;
  onSelectCategory: (categoryId: string | null) => void;
  selectedCategoryId: string | null;
}

// Define the category type
interface Category {
  id: string;
  name: string;
  value: number;
  formattedAmount: string;
  percentage: number;
  count?: number;
  color?: string;
  smallCategories?: Category[];
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: {
      category: string;
      amount: number;
      count: number;
    };
  }>;
}

interface ChartDataItem extends Category {
  fill: string;
}

export function SpendingGraph({
  spendingData = [],
  totalSpent = 0,
  onSelectCategory,
  selectedCategoryId
}: SpendingGraphProps) {
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const [showOtherCategories, setShowOtherCategories] = React.useState(false);

  // Ensure spendingData is an array to prevent map errors
  const safeSpendingData = Array.isArray(spendingData) ? spendingData : [];

  // Group small categories into "Other"
  const processedSpendingData = useMemo(() => {
    const threshold = 0.05; // 5%
    const mainCategories: Category[] = [];
    const smallCategories: Category[] = [];
    let otherTotal = 0;
    let otherCount = 0;
    
    // First pass: separate into main and other
    safeSpendingData.forEach(category => {
      const percentage = category.value / totalSpent;
      if (percentage >= threshold) {
        mainCategories.push(category);
      } else {
        smallCategories.push(category);
        otherTotal += category.value;
        otherCount += category.count || 0;
      }
    });

    // Sort small categories by value descending
    smallCategories.sort((a, b) => b.value - a.value);

    // Only add Other category if there are small categories to group
    if (otherTotal > 0) {
      mainCategories.push({
        id: "other",
        name: "Other",
        value: otherTotal,
        formattedAmount: new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(otherTotal),
        percentage: (otherTotal / totalSpent) * 100,
        count: otherCount,
        color: CHART_COLORS.gray,
        smallCategories // Keep track of constituent categories
      });
    }

    // Sort by value descending
    return mainCategories.sort((a, b) => b.value - a.value);
  }, [safeSpendingData, totalSpent]);

  // Create chart config from spending data with consistent colors
  const chartConfig = useMemo(() => {
    const config: ChartConfig = {};
    processedSpendingData.forEach((category, index) => {
      // Special cases for system categories
      if (category.id === "uncategorized" || category.id === "other") {
        config[category.id] = {
          label: category.name,
          color: CHART_COLORS.gray
        };
      } else {
        // For all other categories, cycle through colors
        config[category.id] = {
          label: category.name,
          color: colorArray[index % colorArray.length]
        };
      }
    });
    return config;
  }, [processedSpendingData]);

  // Format the data for the chart with fill colors
  const chartData = useMemo(() => {
    return processedSpendingData.map(category => ({
      ...category,
      fill: chartConfig[category.id]?.color || CHART_COLORS.gray
    }));
  }, [processedSpendingData, chartConfig]);

  // Get the data to display in the chart
  const displayData = useMemo(() => {
    if (showOtherCategories) {
      const otherCategory = processedSpendingData.find(cat => cat.id === "other");
      return otherCategory?.smallCategories?.map((category: Category, index: number) => ({
        ...category,
        fill: colorArray[index % colorArray.length]
      })) || [];
    }
    return chartData;
  }, [showOtherCategories, processedSpendingData, chartData, colorArray]);

  // Calculate the current view's total (only for display purposes)
  const currentTotal = useMemo(() => {
    if (showOtherCategories) {
      const otherCategory = processedSpendingData.find(cat => cat.id === "other");
      return otherCategory?.value || 0;
    }
    return totalSpent;
  }, [showOtherCategories, processedSpendingData, totalSpent]);

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Clear selected category
  const clearSelection = () => {
    onSelectCategory(null);
  };

  // Handle pie section click
  const handlePieClick = (entry: { id: string }) => {
    if (entry.id === "other") {
      setShowOtherCategories(!showOtherCategories);
      onSelectCategory(null);
    } else if (selectedCategoryId === entry.id) {
      onSelectCategory(null);
    } else {
      setShowOtherCategories(false);
      onSelectCategory(entry.id);
    }
  };

  // Format currency for display
  const formattedTotal = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  }).format(currentTotal);

  // Show empty state if no data
  if (processedSpendingData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">
            Spending by Category
          </CardTitle>
          <CardDescription>
            No spending data available for the selected filters
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px] text-muted-foreground">
          No data to display
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={isFullscreen ? 'fixed inset-4 z-50 overflow-auto' : undefined}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-base font-medium">
            Spending by Category
          </CardTitle>
          <CardDescription>
            {showOtherCategories
              ? 'Viewing categories under 5%'
              : selectedCategoryId
              ? 'Viewing selected category'
              : 'Tap a category to filter transactions'}
          </CardDescription>
        </div>
        <div className="flex gap-1">
          {(selectedCategoryId || showOtherCategories) && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                clearSelection();
                setShowOtherCategories(false);
              }}
              title="Clear selection"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={toggleFullscreen}
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <div className="flex flex-col md:flex-row gap-4">
        <CardContent className="flex-1 pb-0 min-w-0">
          <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[400px]">
            <PieChart>
              <ChartTooltip 
                content={({ active, payload }) => {
                  if (!active || !payload?.length) return null;
                  const data = payload[0].payload;
                  return (
                    <div className="rounded-lg bg-popover p-2 shadow-md">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-2 h-2 rounded-full" 
                          style={{ backgroundColor: data.fill }}
                        />
                        <span className="font-medium">{data.name}</span>
                      </div>
                      <div className="mt-1 text-muted-foreground text-sm">
                        {((data.value / totalSpent) * 100).toFixed(1)}%
                        <span className="ml-1 opacity-75">
                          ({data.formattedAmount})
                        </span>
                      </div>
                      {data.count !== undefined && (
                        <div className="text-muted-foreground text-sm">
                          {data.count} transactions
                        </div>
                      )}
                    </div>
                  );
                }}
              />
              <Pie
                data={displayData}
                dataKey="value"
                nameKey="name"
                innerRadius={80}
                outerRadius={120}
                paddingAngle={2}
                onClick={handlePieClick}
                strokeWidth={selectedCategoryId ? 1 : 0}
                stroke="var(--border)"
              >
                {displayData.map((entry: ChartDataItem) => (
                  <Cell
                    key={entry.id}
                    fill={entry.fill}
                    className="transition-opacity hover:opacity-80"
                    stroke={selectedCategoryId === entry.id ? "var(--ring)" : "var(--border)"}
                    strokeWidth={selectedCategoryId === entry.id ? 2 : 1}
                  />
                ))}
                <Label
                  content={({ viewBox }) => {
                    if (!viewBox || !("cx" in viewBox) || !("cy" in viewBox)) return null;
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-2xl font-bold"
                        >
                          {formattedTotal}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 20}
                          className="fill-muted-foreground text-sm"
                        >
                          {showOtherCategories ? "Under 5%" : "Total Spent"}
                        </tspan>
                      </text>
                    );
                  }}
                />
              </Pie>
            </PieChart>
          </ChartContainer>
        </CardContent>
        
        <div className="p-4 md:w-80 flex flex-col gap-2 border-t md:border-t-0">
          <h4 className="text-sm font-medium">
            {showOtherCategories ? "Categories Under 5%" : "Categories"}
          </h4>
          <div className="flex flex-wrap gap-2 content-start">
            {displayData.map((category: ChartDataItem) => (
              <Button
                key={category.id}
                variant="outline"
                size="xs"
                className={`flex items-center gap-2 ${selectedCategoryId === category.id ? 'bg-muted' : ''}`}
                onClick={() => handlePieClick(category)}
              >
                <div
                  className="w-2 h-2 rounded-full shrink-0"
                  style={{ backgroundColor: category.fill }}
                />
                <span>{category.name}</span>
                <span className="text-muted-foreground">
                  {((category.value / totalSpent) * 100).toFixed(1)}%
                </span>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}
