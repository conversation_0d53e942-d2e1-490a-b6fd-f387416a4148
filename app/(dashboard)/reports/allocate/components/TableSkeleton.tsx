import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';

export function TableSkeleton() {
  // Create an array of 10 items for the skeleton rows
  const skeletonRows = Array.from({ length: 10 }, (_, i) => i);

  return (
    <div className="space-y-2">
      <Table className="w-full">
        <TableHeader>
          <TableRow className="h-8 hover:bg-transparent border-b">
            <TableHead className="h-8 py-1 px-2">
              <Skeleton className="h-4 w-16" />
            </TableHead>
            <TableHead className="h-8 py-1 px-2">
              <Skeleton className="h-4 w-24" />
            </TableHead>
            <TableHead className="h-8 py-1 px-2">
              <Skeleton className="h-4 w-32" />
            </TableHead>
            <TableHead className="h-8 py-1 px-2">
              <Skeleton className="h-4 w-20" />
            </TableHead>
            <TableHead className="h-8 py-1 px-2">
              <Skeleton className="h-4 w-24" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {skeletonRows.map((index) => (
            <TableRow key={index} className="h-6 border-b border-gray-100">
              <TableCell className="py-0 px-2">
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell className="py-0 px-2">
                <Skeleton className="h-4 w-32" />
              </TableCell>
              <TableCell className="py-0 px-2">
                <Skeleton className="h-4 w-48" />
              </TableCell>
              <TableCell className="py-0 px-2">
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell className="py-0 px-2">
                <Skeleton className="h-4 w-[200px]" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 