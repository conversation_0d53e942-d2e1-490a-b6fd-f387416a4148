'use client';

import { useState, useMemo, useCallback } from 'react';
import { LineItemsTable } from './components/LineItemsTable';
import { AllocateFilter, SelectionType } from './components/AllocateFilter';
import { AutosaveIndicator } from '@/components/ui/autosave-indicator';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useToast } from '@/components/hooks/use-toast';
import { Id } from '@/convex/_generated/dataModel';
import { FilteredAICategorizeButton } from './components/FilteredAICategorizeButton';
import { SpendingGraph } from './components/allocate-spending-graph';
import {
  DateQuickFilterType,
  getDateRangeForQuickFilter
} from '@/lib/date-utils';
import { FilterSkeleton } from './components/FilterSkeleton';
import { z } from 'zod';
import { SpendingCategorySchema } from '@/zod/lineItems-schema';

// Define the type from our schema
type SpendingCategory = z.infer<typeof SpendingCategorySchema>;

// Interface for line items
interface LineItem {
  _id: Id<'lineItems'>;
  amount: number;
  description: string;
  merchant_name: string;
  spending_category?: Id<'tags'>;
}

export default function AllocatePage() {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isAICategorizing, setIsAICategorizing] = useState(false);
  const [selectedGraphCategory, setSelectedGraphCategory] = useState<string | null>(null);

  // Filter state
  const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>(
    getDateRangeForQuickFilter('last30days')
  );
  const [activeDateQuickFilter, setActiveDateQuickFilter] =
    useState<DateQuickFilterType>('last30days');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<
    Id<'tags'> | undefined
  >(undefined);
  const [categorySelectionType, setCategorySelectionType] =
    useState<SelectionType>('all_categories');

  // State to track visible line items
  const [visibleLineItems, setVisibleLineItems] = useState<LineItem[]>([]);

  // First fetch the expense categories tag type
  const expenseCategoriesTagType = useQuery(api.tags.getTagTypeBySlug, {
    slug: 'expense-categories'
  });

  // Loading state
  const isLoading = expenseCategoriesTagType === undefined;

  // Then fetch the category hierarchy using the tag type ID
  const categoryHierarchy = useQuery(api.tags.getHierarchy, {
    params: {
      tag_type: expenseCategoriesTagType?._id
    }
  });

  // Transform the hierarchy into the format expected by CategoryDropdown
  // The hierarchy can be either a single tag with children or an array of tags
  const categories = useMemo(() => {
    if (!categoryHierarchy) return [];

    const hierarchyArray = Array.isArray(categoryHierarchy) 
      ? categoryHierarchy 
      : [categoryHierarchy];

    return hierarchyArray.map(parent => ({
      _id: parent._id,
      name: parent.name,
      tag_type: parent.tag_type,
      children: parent.children?.map(child => ({
        _id: child._id,
        name: child.name,
        tag_type: child.tag_type
      }))
    }));
  }, [categoryHierarchy]);

  // Filter handlers
  const handleDateRangeChange = (newRange: { start?: Date; end?: Date }) => {
    setDateRange(newRange);
  };

  const handleDateQuickFilterChange = (filterType: DateQuickFilterType) => {
    setActiveDateQuickFilter(filterType);

    // Only update the date range if the filter type is not 'custom'
    // For 'custom', we want to keep the user's manually selected dates
    if (filterType !== 'custom') {
      setDateRange(getDateRangeForQuickFilter(filterType));
    }
  };

  const handleSearchQueryChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryChange = (
    category: Id<'tags'> | undefined,
    selectionType: SelectionType
  ) => {
    setSelectedCategory(category);
    setCategorySelectionType(selectionType);
  };

  // Handler to update visible items
  const handleVisibleItemsChange = useCallback((items: LineItem[]) => {
    setVisibleLineItems(items);
  }, []);

  // Query to get spending data
  const spendingData = useQuery(api.lineItems.getSpendingByCategory, {
    filter: {
      category: selectedCategory,
      startDate: dateRange.start?.getTime(),
      endDate: dateRange.end?.getTime()
    }
  });

  // Transform spending data for the graph
  const transformedSpendingData = useMemo(() => {
    if (!spendingData) return [];
    return spendingData.categories.map((category: SpendingCategory) => ({
      id: category.id,
      name: category.name,
      value: category.amount,
      formattedAmount: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(category.amount),
      percentage: (category.amount / spendingData.totalSpent) * 100,
      count: category.count,
      color: category.color
    }));
  }, [spendingData]);

  // Handle graph category selection
  const handleGraphCategorySelect = useCallback((categoryId: string | null) => {
    setSelectedGraphCategory(categoryId);
    if (categoryId) {
      // Find the category in the hierarchy and select it
      const category = categories.find(c => 
        c._id === categoryId || c.children?.some(child => child._id === categoryId)
      );
      if (category) {
        setSelectedCategory(categoryId as Id<'tags'>);
        setCategorySelectionType('all_categories');
      }
    } else {
      setSelectedCategory(undefined);
    }
  }, [categories]);

  return (
    <div className="container pl-0">
      {isLoading ? (
        <FilterSkeleton />
      ) : (
        <>
          <div className="flex justify-between items-center mb-2">
            <h1 className="text-xl font-semibold">Line Items</h1>
            <AutosaveIndicator saving={isSaving} />
          </div>

          {/* Spending Graph */}
          <div className="mb-6">
            <SpendingGraph
              spendingData={transformedSpendingData}
              totalSpent={spendingData?.totalSpent || 0}
              onSelectCategory={handleGraphCategorySelect}
              selectedCategoryId={selectedGraphCategory}
            />
          </div>

          {/* Auto Categorize Button */}
          <div className="mb-4">
            <FilteredAICategorizeButton 
              visibleLineItems={visibleLineItems}
              onSuccess={() => {
                toast({
                  title: "Success",
                  description: "Line items have been categorized"
                });
              }}
            />
          </div>

          {/* Allocate Filters */}
          <div className="mb-4">
            <AllocateFilter
              dateRange={dateRange}
              onDateRangeChange={handleDateRangeChange}
              activeDateQuickFilter={activeDateQuickFilter}
              onDateQuickFilterChange={handleDateQuickFilterChange}
              searchQuery={searchQuery}
              onSearchQueryChange={handleSearchQueryChange}
              selectedCategory={selectedCategory}
              onCategoryChange={handleCategoryChange}
              categories={categories}
              categorySelectionType={categorySelectionType}
            />
          </div>

          {/* Line items table - uses server-side pagination */}
          <LineItemsTable
            categories={categories}
            dateRange={dateRange}
            searchQuery={searchQuery}
            selectedCategory={selectedCategory}
            categorySelectionType={categorySelectionType}
            onVisibleItemsChange={handleVisibleItemsChange}
          />
        </>
      )}
    </div>
  );
}
