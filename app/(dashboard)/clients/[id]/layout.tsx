"use client";

import { useQuery } from 'convex/react';
import { useParams } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { ClientDetailsProvider } from '@/components/clients/ClientDetailsContext';
import { ReactNode } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ClientHeader } from '@/components/clients/ClientHeader';
import { ClientActionBar } from '@/components/clients/ClientActionBar';
import { ClientSubmenu } from '@/components/clients/ClientSubmenu';

export default function ClientDetailsLayout({ children }: { children: ReactNode }) {
  const params = useParams();
  const clientId = params.id as Id<'clients'>;

  // Fetch the unified data for the entire page
  const pageData = useQuery(api.clients.clientQueries.getClientDetailsPageData, {
    clientId: clientId,
  });

  // Show a loading skeleton while data is being fetched
  if (!pageData) {
    return (
      <div className="p-6">
        <Skeleton className="h-12 w-1/4 mb-4" />
        <Skeleton className="h-8 w-full mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  // Provide the fetched data to all children via context
  return (
    <ClientDetailsProvider value={pageData}>
      <div className="flex flex-col h-full">
        <ClientHeader />
        <ClientActionBar />
        <ClientSubmenu />
        <main className="flex-1 overflow-y-auto p-6 bg-muted/20">
          {children}
        </main>
      </div>
    </ClientDetailsProvider>
  );
}
