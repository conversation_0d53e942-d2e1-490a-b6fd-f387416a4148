"use client";

import { PrincipalCard } from '@/components/clients/PrincipalCard';
import { ClientDetailsCard } from '@/components/clients/ClientDetailsCard';
import { ClientTeamSection } from '@/components/clients/ClientTeamSection';
import { ActiveItemsComponent } from '@/components/clients/ActiveItemsComponent';
import { ContactInformationComponent } from '@/components/clients/ContactInformationComponent';

export default function ClientOverviewPage() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <div className="flex flex-col lg:flex-row gap-6">
          <PrincipalCard />
          <ClientDetailsCard />
        </div>
        <ActiveItemsComponent />
      </div>
      <div className="space-y-6">
        <ClientTeamSection />
        <ContactInformationComponent />
      </div>
    </div>
  );
}
