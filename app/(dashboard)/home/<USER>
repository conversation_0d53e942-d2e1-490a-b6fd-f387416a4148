'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';
import Link from 'next/link';
import {
  CalendarClock,
  FileCheck,
  ListChecks,
  AlertTriangle,
  Zap,
  Award,
  PiggyBank,
  TrendingUp,
  FileText,
  FolderGit2,
  Clock,
  CheckSquare,
  Gavel,
  Info,
  User,
  Users,
  Building,
  Receipt,
  FileIcon
} from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';
import { CollectionCard } from '@/components/ui/collection-card';
import { useRouter } from 'next/navigation';
import { formatRelativeTime } from '@/lib/date-utils';

// Import entity-specific card components
import { ProjectCard } from '@/app/(dashboard)/projects/components/project-card';
import TasksCard from '@/app/(dashboard)/tasks/components/TasksCard';
import DecisionsCard from '@/app/(dashboard)/decisions/components/decisionsCard';
import { RecentlyModified } from '@/components/documents/recently-modified';
import { PeopleCard } from '@/components/cards/peopleCard';
import { OrganizationCard } from '@/components/cards/organizationCard';

// Helper functions for file cards
const getDocTypeColor = (docType: string) => {
  const docTypes = [
    { id: 'KNOWLEDGE_BASE', color: 'bg-blue-500' },
    { id: 'MEETING_NOTES', color: 'bg-amber-500' },
    { id: 'CONTRACT', color: 'bg-emerald-500' },
    { id: 'DOCUMENT', color: 'bg-purple-500' },
    { id: 'BILL', color: 'bg-red-500' }
  ];
  const foundDocType = docTypes.find(dt => dt.id === docType);
  return foundDocType ? foundDocType.color : 'bg-gray-500';
};

const getDocTypeIcon = (docType: string) => {
  // Use the imported icons
  const iconMap: { [key: string]: React.ReactElement } = {
    'KNOWLEDGE_BASE': <FileText className="w-6 h-6" />,
    'MEETING_NOTES': <CalendarClock className="w-6 h-6" />,
    'CONTRACT': <FileText className="w-6 h-6" />,
    'DOCUMENT': <FileIcon className="w-6 h-6" />,
    'BILL': <Receipt className="w-6 h-6" />
  };
  return iconMap[docType] || <FileIcon className="w-6 h-6" />; // Default to File icon
};

// Helper function to get the URL path segment based on docType
const getDocTypePathSegment = (docType: string): string => {
  const pathMap: { [key: string]: string } = {
    'KNOWLEDGE_BASE': 'knowledge-base',
    'MEETING_NOTES': 'meeting-notes',
    'CONTRACT': 'contracts',
    'DOCUMENT': 'general-docs',
    'BILL': 'bills'
  };
  return pathMap[docType] || 'general-docs'; // Default to general-docs if type not found
};

// Helper function to render the appropriate card component based on entity type
const renderEntityCard = (entity: any, onClick: () => void) => {
  switch (entity.entityType) {
    case 'project':
      return (
        <ProjectCard
          id={entity._id}
          name={entity.name || 'Untitled Project'}
          short_description={entity.short_description}
          status={entity.status}
          priority={entity.priority}
          lastUpdated={entity.updated_at}
          onClick={onClick}
        />
      );
    case 'task':
      return (
        <TasksCard
          task={entity}
          onClick={onClick}
        />
      );
    case 'decision':
      return (
        <DecisionsCard
          decision={entity}
          onClick={onClick}
        />
      );
    case 'file':
      // Use the same card as in the documents page
      return (
        <Card className="cursor-pointer hover:shadow-md transition-all" onClick={onClick}>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className={`${getDocTypeColor(entity.docType)} w-10 h-10 rounded-md flex items-center justify-center text-white mr-4`}>
                {getDocTypeIcon(entity.docType)}
              </div>
              <div>
                <h3 className="font-medium">{entity.title || 'Untitled'}</h3>
                <div className="flex items-center text-sm text-gray-500">
                  <p>Modified {entity.updated_at ? formatRelativeTime(entity.updated_at) : 'Unknown'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    case 'person':
      return (
        <PeopleCard
          person={entity}
          onClick={onClick}
        />
      );
    case 'organization':
      return (
        <OrganizationCard
          organization={entity}
          onClick={onClick}
        />
      );
    case 'bill':
      // Generic bill card
      return (
        <Card className="cursor-pointer hover:shadow-md transition-all" onClick={onClick}>
          <CardHeader>
            <CardTitle className="text-base">{entity.name || 'Untitled Bill'}</CardTitle>
            <CardDescription>Updated {formatRelativeTime(entity.updated_at)}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Receipt className="w-5 h-5 mr-2" />
              <span>{entity.amount ? `$${entity.amount.toFixed(2)}` : 'No amount'}</span>
            </div>
          </CardContent>
        </Card>
      );
    default:
      // Generic card for other entity types
      return (
        <Card className="cursor-pointer hover:shadow-md transition-all" onClick={onClick}>
          <CardHeader>
            <CardTitle className="text-base">{entity.name || entity.title || 'Untitled'}</CardTitle>
            <CardDescription>Updated {formatRelativeTime(entity.updated_at)}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              <span>{entity.short_description || 'No description'}</span>
            </div>
          </CardContent>
        </Card>
      );
  }
};

function Dashboard() {
  const router = useRouter();
  const user = useQuery(api.users.currentUser);
  const firstName = user?.name?.split(' ')[0] || 'User';

  // Fetch recently updated entities
  const recentlyUpdatedResult = useQuery(api.dashboard.getRecentlyUpdatedEntities, {
    limit: 5
  });

  // Fetch user's tasks
  const userTasksResult = useQuery(api.dashboard.getUserTasks, {
    limit: 5
  });

  // Fetch user's decisions
  const userDecisionsResult = useQuery(api.dashboard.getUserDecisions, {
    limit: 5
  });

  // Fetch items where user is informed
  const informedItemsResult = useQuery(api.dashboard.getUserInformedItems, {
    limit: 5
  });

  return (
    <div className="glass-content p-6">
      {/* Welcome Header */}
      <section className="flex flex-col items-start justify-between gap-4 md:flex-row mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {firstName}
          </h1>
          <p>
            Your personal dashboard — here's what needs your attention today.
          </p>
        </div>
      </section>

      {/* Main content */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Recently Updated */}
        <CollectionCard
          heading="Recently Updated"
          subheading="Recently updated items across your workspace"
        >
          {recentlyUpdatedResult ? (
            recentlyUpdatedResult.entities && recentlyUpdatedResult.entities.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {recentlyUpdatedResult.entities.map((entity: any) => (
                  <div key={entity._id}>
                    {renderEntityCard(entity, () => {
                      switch (entity.entityType) {
                        case 'project':
                          router.push(`/projects/${entity._id}`);
                          break;
                        case 'task':
                          router.push(`/tasks/${entity._id}`);
                          break;
                        case 'decision':
                          router.push(`/decisions/${entity._id}`);
                          break;
                        case 'file':
                          // Use the correct path segment based on docType
                          const pathSegment = getDocTypePathSegment(entity.docType);
                          router.push(`/documents/${pathSegment}/${entity._id}`);
                          break;
                        case 'person':
                          router.push(`/directory/people/${entity._id}`);
                          break;
                        case 'organization':
                          router.push(`/directory/organizations/${entity._id}`);
                          break;
                        case 'bill':
                          router.push(`/bills/${entity._id}`);
                          break;
                        default:
                          break;
                      }
                    })}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-white/60">
                <Clock className="w-12 h-12 mb-4 opacity-40" />
                <p>No recently updated items</p>
              </div>
            )
          ) : (
            <div className="flex h-32 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-white border-t-transparent" />
            </div>
          )}
        </CollectionCard>

        {/* Your Tasks */}
        <CollectionCard
          heading="Your Tasks"
          subheading="Tasks where you are driver or contributor"
          buttonText="View All"
          onButtonClick={() => router.push('/tasks')}
        >
          {userTasksResult ? (
            userTasksResult.tasks &&
            userTasksResult.tasks.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {userTasksResult.tasks.map((task: any) => (
                  <div key={task._id}>
                    <TasksCard
                      task={task}
                      onClick={() => router.push(`/tasks/${task._id}`)}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-white/60">
                <CheckSquare className="w-12 h-12 mb-4 opacity-40" />
                <p>No tasks assigned to you</p>
              </div>
            )
          ) : (
            <div className="flex h-32 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-white border-t-transparent" />
            </div>
          )}
        </CollectionCard>

        {/* Your Decisions */}
        <CollectionCard
          heading="Your Decisions"
          subheading="Decisions where you are driver or contributor"
          buttonText="View All"
          onButtonClick={() => router.push('/decisions')}
        >
          {userDecisionsResult ? (
            userDecisionsResult.decisions && userDecisionsResult.decisions.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {userDecisionsResult.decisions.map((decision: any) => (
                  <div key={decision._id}>
                    <DecisionsCard
                      decision={decision}
                      onClick={() => router.push(`/decisions/${decision._id}`)}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-white/60">
                <Gavel className="w-12 h-12 mb-4 opacity-40" />
                <p>No decisions assigned to you</p>
              </div>
            )
          ) : (
            <div className="flex h-32 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-white border-t-transparent" />
            </div>
          )}
        </CollectionCard>

        {/* Informed Items */}
        <CollectionCard
          heading="Informed Items"
          subheading="Tasks and decisions where you are informed"
        >
          {informedItemsResult ? (
            informedItemsResult.items && informedItemsResult.items.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {informedItemsResult.items.map((item: any) => (
                  <div key={item._id}>
                    {item.itemType === 'task' ? (
                      <TasksCard
                        task={item}
                        onClick={() => router.push(`/tasks/${item._id}`)}
                      />
                    ) : (
                      <DecisionsCard
                        decision={item}
                        onClick={() => router.push(`/decisions/${item._id}`)}
                      />
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-white/60">
                <Info className="w-12 h-12 mb-4 opacity-40" />
                <p>No items where you are informed</p>
              </div>
            )
          ) : (
            <div className="flex h-32 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-white border-t-transparent" />
            </div>
          )}
        </CollectionCard>
      </div>
    </div>
  );
}

export default function HomePage() {
  return <Dashboard />;
}