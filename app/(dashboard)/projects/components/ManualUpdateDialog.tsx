import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Chip } from "@/components/ui/chip";
import { X, Plus, Check, Paperclip } from "lucide-react";
import { cn } from "@/lib/utils";
import { ManualTimelineItem } from "@/zod/projects-schema";

interface ManualUpdateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (timelineItem: ManualTimelineItem) => void;
}

export default function ManualUpdateDialog({
  isOpen, 
  onClose, 
  onSubmit 
}: ManualUpdateDialogProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState("");
  const [hasRisk, setHasRisk] = useState(false);
  const [attachments, setAttachments] = useState<Array<{
    name: string;
    url: string;
    type: string;
  }>>([]);
  const [isDragging, setIsDragging] = useState(false);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setTitle("");
      setDescription("");
      setTags([]);
      setCurrentTag("");
      setHasRisk(false);
      setAttachments([]);
    }
  }, [isOpen]);

  const handleAddTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      console.log("Adding tag:", currentTag.trim());
      setTags([...tags, currentTag.trim()]);
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    console.log("Removing tag:", tagToRemove);
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleFiles = (files: FileList) => {
    // In a real app, you would upload these files to a server/storage
    // For now, we'll create object URLs as a demo
    const newAttachments = Array.from(files).map(file => ({
      name: file.name,
      url: URL.createObjectURL(file),
      type: file.type
    }));

    setAttachments([...attachments, ...newAttachments]);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    handleFiles(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (!files) return;
    handleFiles(files);
  };

  const handleRemoveAttachment = (index: number) => {
    const newAttachments = [...attachments];
    URL.revokeObjectURL(newAttachments[index].url); // Clean up object URL
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };

  const handleSubmit = () => {
    if (!title.trim()) return;

    // Ensure we have the 'test' tag for consistency with existing items
    const finalTags = [...tags];
    if (!finalTags.includes("test")) {
      finalTags.push("test");
    }

    // Construct object with proper typing
    const timelineItem: ManualTimelineItem = {
      title: title.trim(),
      description: description.trim() || undefined,
      status: "completed", // Assuming default status
      tags: finalTags,
      hasRisk,
      type: "update",
      updateDetails: "test", // Add default update details for consistency
      attachments: attachments.length > 0 ? attachments : undefined,
    };

    onSubmit(timelineItem);
    onClose();
    
    // Reset form
    setTitle("");
    setDescription("");
    setTags([]);
    setCurrentTag("");
    setHasRisk(false);
    setAttachments([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] animate-scale-in">
        <DialogHeader>
          <DialogTitle>Add Manual Update</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="What's the update?"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Add more details about this update..."
              rows={3}
            />
          </div>

          {/* File Attachments */}
          <div className="grid gap-2">
            <Label htmlFor="attachments">Attachments</Label>
            <div className="relative">
              <Input
                id="attachments"
                type="file"
                multiple
                onChange={handleFileChange}
                className="hidden"
              />
              <div 
                onClick={() => document.getElementById('attachments')?.click()}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                className={cn(
                  "flex flex-col items-center justify-center p-6 gap-2 border-2 border-dashed rounded-lg cursor-pointer transition-colors",
                  isDragging ? "border-primary bg-primary/5" : "hover:bg-muted/50"
                )}
              >
                <div className={cn(
                  "p-2 rounded-full transition-colors",
                  isDragging ? "bg-primary/20" : "bg-primary/10"
                )}>
                  <Paperclip className="w-5 h-5 text-primary" />
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium">Click to upload files</p>
                  <p className="text-xs text-muted-foreground">or drag and drop them here</p>
                </div>
              </div>
            </div>
            {attachments.length > 0 && (
              <div className="flex flex-col gap-2 mt-2">
                {attachments.map((file, index) => (
                  <div 
                    key={index}
                    className="flex items-center gap-2 p-2 rounded-md border bg-muted/30"
                  >
                    <Paperclip className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm flex-1 truncate">{file.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:text-destructive"
                      onClick={() => handleRemoveAttachment(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="grid gap-2">
            <Label htmlFor="tags">Tags</Label>
            <div className="flex items-center gap-2">
              <Input
                id="tags"
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                placeholder="Add tags"
                onKeyDown={handleKeyDown}
              />
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={handleAddTag}
                disabled={!currentTag.trim()}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Chip 
                    key={tag} 
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <X 
                      className="w-3 h-3 cursor-pointer hover:text-destructive" 
                      onClick={() => handleRemoveTag(tag)}
                    />
                  </Chip>
                ))}
              </div>
            )}
          </div>

          {/* Risk Checkbox */}
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="hasRisk"
                checked={hasRisk}
                onChange={(e) => setHasRisk(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300"
              />
              <Label htmlFor="hasRisk" className="text-sm font-normal">
                This update includes risks or blockers
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!title.trim()}
          >
            <Check className="w-4 h-4 mr-2" /> Add Update
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
