import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/hooks/use-toast';
import {
  Project,
  ProjectStatus,
  ProjectPriority,
  ProjectStatusEnum,
  ProjectPriorityEnum
} from '@/zod/projects-schema';
import ProjectForm from './ProjectForm';

// Helper function to format dates
const formatDate = (dateValue: Date | number | string): string => {
  if (!dateValue) return '';
  const dateObj =
    typeof dateValue === 'string' || typeof dateValue === 'number'
      ? new Date(dateValue)
      : dateValue;
  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  };
  return dateObj.toLocaleDateString('en-US', options);
};

// Status colors mapping
const PROJECT_STATUS_COLORS: Record<ProjectStatus, string> = {
  in_progress: 'bg-blue-100 text-blue-800',
  perpetual: 'bg-purple-100 text-purple-800',
  not_started: 'bg-gray-100 text-gray-800',
  completed: 'bg-green-100 text-green-800',
  paused: 'bg-yellow-100 text-yellow-800',
  cancelled: 'bg-red-100 text-red-800'
};

// Priority colors mapping
const PROJECT_PRIORITY_COLORS: Record<ProjectPriority, string> = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800'
};

// Status badge component with appropriate colors
export const StatusBadge = ({ status }: { status: ProjectStatus | undefined }) => {
  if (!status) return null;

  return (
    <Badge className={`${PROJECT_STATUS_COLORS[status]} rounded-full`}>
      {status.replace(/_/g, ' ')}
    </Badge>
  );
};

// Priority badge component with appropriate colors
export const PriorityBadge = ({
  priority
}: {
  priority: ProjectPriority | undefined;
}) => {
  if (!priority) return null;

  return (
    <Badge className={`${PROJECT_PRIORITY_COLORS[priority]} rounded-full`}>
      {priority}
    </Badge>
  );
};

type ProjectDetailsProps = {
  project: Project;
  onEdit?: () => void;
  onDelete?: () => void;
  onProjectUpdated?: () => void;
};

/**
 * ProjectDetails component that directly shows the ProjectForm for editing
 * It now uses autosave functionality - no save button needed
 *
 * @param project - The project object to display
 * @param onEdit - Optional callback function when Edit button is clicked (not used anymore)
 * @param onDelete - Optional callback function when Delete button is clicked
 * @param onProjectUpdated - Optional callback function when project is updated
 */
export default function ProjectDetails({
  project,
  onEdit,
  onDelete,
  onProjectUpdated
}: ProjectDetailsProps) {
  const { toast } = useToast();

  // Handle successful save
  const handleSaved = () => {
    if (onProjectUpdated) {
      onProjectUpdated();
    }
  };

  // Always show the form in edit mode
  return <ProjectForm project={project} onSaved={handleSaved} />;
}
