'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChevronUp, ChevronDown, ArrowUpDown } from 'lucide-react';
import { z } from 'zod';
import { ProjectStatusEnum, ProjectPriorityEnum, ProjectStatus, ProjectPriority } from '@/zod/projects-schema';
import { Person } from '@/zod/directory-schema';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  ColumnDef,
  SortingState
} from '@tanstack/react-table';

// Define types for the component based on the schema and current implementation
interface ProjectTableItem {
  id: string | number;
  name: string;
  description?: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  progress: number;
  tasks: number;
  completedTasks: number;
  decisions: number;
  completedDecisions: number;
  team: Person[];
  lastUpdated?: string;
}

interface ProjectsTableProps {
  projects: ProjectTableItem[];
  selectedProjects: string[];
  onProjectCheckboxChange: (checked: boolean, id: string | number) => void;
  onSelectAll: (checked: boolean) => void;
}

/**
 * ProjectsTable - A reusable table component for displaying projects
 *
 * This component displays projects in a table format with columns for:
 * - Project name and description
 * - Status
 * - Priority
 * - Progress
 * - Team members
 * - Tasks completion
 * - Decisions completion
 *
 * Uses TanStack Table for advanced table functionality
 */
const ProjectsTable = ({
  projects,
  selectedProjects,
  onProjectCheckboxChange,
  onSelectAll
}: ProjectsTableProps) => {
  // State for sorting
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Helper function to get status color based on project status
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-200 text-yellow-800';
      case 'not_started':
        return 'bg-gray-200 text-gray-700';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'perpetual':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  // Helper function to get priority color
  const getPriorityColor = (priority: ProjectTableItem['priority']) => {
    switch (priority) {
      case 'high':
      case 'urgent':
        return 'text-orange-600';
      case 'medium':
        return 'text-blue-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  // Component to display priority with chevrons
  const PriorityChevrons = ({
    priority
  }: {
    priority: ProjectTableItem['priority'];
  }) => {
    const color = getPriorityColor(priority);
    const className = `h-4 w-4 ${color} stroke-[3]`;

    // Different approach for positioning chevrons
    switch (priority) {
      case 'high':
      case 'urgent':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-[-2px]">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[4px]">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[10px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      case 'medium':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-0">
              <ChevronUp className={className} />
            </div>
            <div className="absolute top-[6px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      case 'low':
        return (
          <div className="relative flex flex-col items-center justify-center w-6 h-6">
            <div className="absolute top-[4px]">
              <ChevronUp className={className} />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Format status for display
  const formatStatus = (status: ProjectTableItem['status']) => {
    // Handle both schema enum values and current implementation values
    const displayStatus = String(status).replace('_', ' ');
    return displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1);
  };

  // Define columns
  const columns = useMemo<ColumnDef<ProjectTableItem>[]>(
    () => [
      // Selection column
      {
        id: 'select',
        header: ({ table }) => (
          <div className="flex items-center">
            <Checkbox
              checked={
                selectedProjects.length === projects.length &&
                projects.length > 0
              }
              onCheckedChange={(checked: boolean) => {
                onSelectAll(checked);
              }}
              className="border-muted-foreground"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center">
            <Checkbox
              checked={selectedProjects.includes(row.original.id.toString())}
              onCheckedChange={(checked: boolean) =>
                onProjectCheckboxChange(checked, row.original.id)
              }
              className="border-muted-foreground"
            />
          </div>
        ),
        enableSorting: false,
        size: 50
      },

      // Project name and description
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Project
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex flex-col">
            <Link
              href={`/projects/${row.original.id}`}
              className="hover:underline"
            >
              <div className="font-medium truncate">{row.original.name}</div>
            </Link>
            {row.original.description && (
              <div className="text-sm text-muted-foreground line-clamp-1 mt-1">
                {row.original.description}
              </div>
            )}
          </div>
        ),
        size: 400
      },

      // Status
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </div>
        ),
        cell: ({ row }) => (
          <span
            className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(row.original.status)}`}
          >
            {formatStatus(row.original.status)}
          </span>
        )
      },

      // Priority
      {
        accessorKey: 'priority',
        header: ({ column }) => (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Priority
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </div>
        ),
        cell: ({ row }) => (
          <PriorityChevrons priority={row.original.priority} />
        ),
        sortingFn: (rowA, rowB, columnId) => {
          // Custom sorting function for priority
          const priorityOrder = { low: 0, medium: 1, high: 2, urgent: 3 };
          const priorityA = rowA.original
            .priority as keyof typeof priorityOrder;
          const priorityB = rowB.original
            .priority as keyof typeof priorityOrder;

          return priorityOrder[priorityA] - priorityOrder[priorityB];
        }
      },

      // Progress
      {
        accessorKey: 'progress',
        header: ({ column }) => (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Progress
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Progress
              value={row.original.progress}
              className={`w-24 h-3 ${
                row.original.status === 'paused'
                  ? '[&>div]:bg-gray-400'
                  : '[&>div]:bg-blue-600'
              }`}
            />
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              {row.original.progress}%
            </span>
          </div>
        )
      },

      // Team
      {
        id: 'team',
        header: 'Team',
        cell: ({ row }) => (
          <div className="flex -space-x-2">
            {row.original.team.map((member) => (
              <Avatar
                key={member._id}
                className="border-2 border-background w-8 h-8"
              >
                <AvatarImage src={member.image} alt={member.name} />
                <AvatarFallback>
                  {member.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
        ),
        enableSorting: false
      },

      // Tasks
      {
        id: 'tasks',
        header: 'Tasks',
        cell: ({ row }) => (
          <div className="flex items-center">
            <span className="text-sm text-muted-foreground font-normal">
              {row.original.completedTasks}/{row.original.tasks}
            </span>
          </div>
        ),
        accessorFn: (row) => `${row.completedTasks}/${row.tasks}`
      },

      // Decisions
      {
        id: 'decisions',
        header: 'Decisions',
        cell: ({ row }) => (
          <div className="flex items-center">
            <span className="text-sm text-muted-foreground font-normal">
              {row.original.completedDecisions}/{row.original.decisions}
            </span>
          </div>
        ),
        accessorFn: (row) => `${row.completedDecisions}/${row.decisions}`
      }
    ],
    [projects, selectedProjects, onProjectCheckboxChange, onSelectAll]
  );

  // Initialize TanStack table
  const table = useReactTable({
    data: projects,
    columns,
    state: {
      sorting
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel()
  });

  return (
    <div className="border rounded-lg">
      <table className="w-full">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id} className="border-b bg-muted/50">
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className={`text-left p-3 text-sm font-medium ${
                    header.id === 'select' ? 'w-[50px] px-4' : ''
                  } ${header.id === 'name' ? 'pl-2 pr-3 w-[400px]' : ''} ${
                    header.id === 'tasks' || header.id === 'decisions'
                      ? 'whitespace-nowrap'
                      : ''
                  }`}
                  style={{
                    width:
                      header.getSize() !== 150 ? header.getSize() : undefined
                  }}
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : ''
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </div>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.id}
              className="border-b last:border-b-0 hover:bg-muted/50"
            >
              {row.getVisibleCells().map((cell) => (
                <td
                  key={cell.id}
                  className={`p-3 ${
                    cell.column.id === 'select' ? 'w-[50px] px-4' : ''
                  } ${
                    cell.column.id === 'name' ? 'pl-2 pr-3 max-w-[400px]' : ''
                  } ${cell.column.id === 'progress' ? 'pl-3 pr-1' : ''}`}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProjectsTable;
