import { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Tag } from '@/zod/tags-schema';
import { Check, X } from 'lucide-react';
import { useDebounce } from 'use-debounce';

import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

interface MultiSelectTagFilterProps {
  selectedTags: Id<'tags'>[];
  onChange: (tagIds: Id<'tags'>[]) => void;
}

export function MultiSelectTagFilter({
  selectedTags,
  onChange
}: MultiSelectTagFilterProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchQuery, 300);
  const [tempSelectedTags, setTempSelectedTags] = useState<Id<'tags'>[]>([]);

  // Fetch the general-tags tag type
  const generalTagsType = useQuery(api.tags.getTagTypeBySlug, { slug: 'general-tags' });
  const generalTagsTypeId = generalTagsType?._id;

  // Fetch only tags of the general-tags type
  const allTagsData = useQuery(
    api.tags.fetchTags,
    open && generalTagsTypeId ? { filter: { tag_type: generalTagsTypeId } } : "skip"
  );
  const allTags = allTagsData ?? [];

  // Filter tags based on search query
  const filteredTags = allTags.filter(tag => {
    // Filter by search term
    return !debouncedSearchTerm ||
      tag.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase());
  });

  // Set of selected tag IDs for quick lookup
  const selectedTagIds = new Set(tempSelectedTags.map(id => id.toString()));

  // Handle selecting a tag - automatically apply filter when tag is selected/deselected
  const handleSelectTag = (tagId: Id<'tags'>) => {
    const tagIdStr = tagId.toString();

    let newSelectedTags: Id<'tags'>[];

    if (selectedTagIds.has(tagIdStr)) {
      // Remove tag if already selected
      newSelectedTags = tempSelectedTags.filter(id => id.toString() !== tagIdStr);
    } else {
      // Add tag if not selected
      newSelectedTags = [...tempSelectedTags, tagId];
    }

    // Update local state
    setTempSelectedTags(newSelectedTags);

    // Apply filter immediately
    onChange(newSelectedTags);
  };

  // Fetch all tags (not just the general-tags type)
  const allTagsInSystem = useQuery(api.tags.fetchTags, { filter: {} }) || [];

  // Get the names of selected tags for display
  const getSelectedTagNames = () => {
    if (selectedTags.length === 0) return [];

    // Create a map of tag IDs to names for quick lookup
    const tagMap = new Map<string, string>();

    // Add tags from allTags to the map (tags of the general-tags type)
    allTags.forEach(tag => {
      tagMap.set(tag._id.toString(), tag.name);
    });

    // Add all tags from the system to the map
    allTagsInSystem.forEach(tag => {
      tagMap.set(tag._id.toString(), tag.name);
    });

    // Map selected tag IDs to names
    return selectedTags.map(tagId => {
      const tagIdStr = tagId.toString();
      return tagMap.get(tagIdStr) || `Tag ${tagIdStr.substring(0, 4)}...`;
    });
  };

  // Get tag names for display
  const tagNames = getSelectedTagNames();

  // Initialize temp selected tags when popover opens
  useEffect(() => {
    if (open) {
      setTempSelectedTags([...selectedTags]);
    }
  }, [open, selectedTags]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between"
          aria-expanded={open}
        >
          <div className="flex flex-wrap gap-1 max-w-[90%] overflow-hidden">
            {selectedTags.length > 0 ? (
              <>
                <span className="text-xs font-medium mr-1">Tags:</span>
                {tagNames.map((name, i) => (
                  <Badge key={i} variant="secondary" className="mr-1 truncate max-w-[100px]">
                    {name}
                  </Badge>
                ))}
              </>
            ) : (
              <span className="text-muted-foreground">Filter by Tags</span>
            )}
          </div>
          {selectedTags.length > 0 && (
            <span
              className="inline-flex h-4 w-4 items-center justify-center rounded-sm hover:bg-muted cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onChange([]);
              }}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Clear</span>
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <div className="p-2 border-b">
          <Input
            autoFocus
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tags..."
            className="h-8 text-sm"
          />
        </div>

        <ScrollArea className="h-[180px] p-2">
          {allTagsData === undefined && open && (
            <p className="text-xs text-muted-foreground text-center py-2">Loading...</p>
          )}

          {filteredTags.length === 0 && open && debouncedSearchTerm && (
            <p className="text-xs text-muted-foreground text-center py-2">No matching tags found.</p>
          )}

          {filteredTags.length === 0 && open && !debouncedSearchTerm && allTagsData !== undefined && (
            <p className="text-xs text-muted-foreground text-center py-2">No tags available.</p>
          )}

          <div className="space-y-1">
            {filteredTags.map((tag) => {
              const isSelected = selectedTagIds.has(tag._id.toString());
              return (
                <div
                  key={tag._id.toString()}
                  onClick={() => handleSelectTag(tag._id)}
                  className={`flex items-center gap-2 p-1.5 rounded cursor-pointer hover:bg-muted ${isSelected ? 'bg-muted' : ''}`}
                >
                  {/* Selection Indicator */}
                  <div className="w-4 h-4 flex items-center justify-center">
                    {isSelected && <Check className="h-3 w-3 text-foreground" />}
                  </div>

                  {/* Tag Color Indicator */}
                  <div
                    className="w-2 h-2 rounded-full flex-shrink-0"
                    style={{
                      backgroundColor: tag.color || '#9ca3af' // Use gray-400 as fallback
                    }}
                  />

                  {/* Tag Name */}
                  <span className="text-sm truncate flex-grow">{tag.name}</span>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
