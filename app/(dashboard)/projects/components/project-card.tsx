'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronUp,
  MoreHorizontal,
  Flag,
  Clock
} from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';
import { Person } from '@/zod/directory-schema';

/**
 * Type definitions for the project card component
 */
interface ProjectUpdate {
  title: string;
  author: string;
  timestamp: Date;
}

// Interface for the project data
export interface ProjectCardProps {
  id: Id<'projects'> | string;
  name: string;
  description?: string;
  short_description?: string; // Added short_description
  status?:
    | 'in_progress'
    | 'perpetual'
    | 'not_started'
    | 'completed'
    | 'paused'
    | 'cancelled'
    | 'active'
    | 'draft';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  progress?: number;
  lastUpdated?: string | number;
  team?: Person[];
  tasks?: { total: number; completed: number };
  decisions?: { total: number; completed: number };
  updates?: {
    total: number;
    unread: number;
    latest?: ProjectUpdate;
  };
  onClick?: () => void;
}

/**
 * Helper function to get the appropriate color for the status badge
 */
const getStatusColor = (status?: string) => {
  if (!status) return 'bg-gray-200 text-gray-700';

  switch (status) {
    case 'active':
    case 'in_progress':
      return 'bg-blue-100 text-blue-800';
    case 'paused':
      return 'bg-yellow-200 text-yellow-800';
    case 'draft':
    case 'not_started':
      return 'bg-gray-200 text-gray-700';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    case 'perpetual':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-200 text-gray-700';
  }
};

/**
 * Helper function to get the appropriate color for the priority indicator
 */
const getPriorityColor = (priority?: string) => {
  if (!priority) return 'text-gray-600';

  switch (priority) {
    case 'high':
    case 'urgent':
      return 'text-orange-600';
    case 'medium':
      return 'text-blue-600';
    case 'low':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};

/**
 * Component to display priority level using chevron icons
 */
const PriorityChevrons = ({ priority }: { priority?: string }) => {
  if (!priority) return null;

  const color = getPriorityColor(priority);
  const className = `h-4 w-4 ${color} stroke-[3]`;

  switch (priority) {
    case 'high':
    case 'urgent':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[-2px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[10px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'medium':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-0">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[6px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'low':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    default:
      return null;
  }
};

/**
 * Helper function to format a timestamp as a relative time string
 */
function formatTimeAgo(timestamp?: string | number) {
  if (!timestamp) return 'Unknown';

  const date = new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  }
}

/**
 * ProjectCard component
 *
 * Displays a card with project information including name, description, status,
 * priority, progress, team members, and recent updates.
 */
export function ProjectCard({
  id,
  name,
  description, // Keep original description prop for now, might be used elsewhere
  short_description, // Added short_description
  status,
  priority,
  progress = 0,
  lastUpdated,
  team = [],
  tasks = { total: 0, completed: 0 },
  decisions = { total: 0, completed: 0 },
  updates,
  onClick
}: ProjectCardProps) {
  // Format the status text for display
  const statusText = status
    ? status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
    : '';

  return (
    <Card className="p-6 hover:shadow-md transition-all" onClick={onClick}>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-grow">
            <Link href={`/projects/${id}`} className="hover:underline">
              <CardTitle className="text-lg">{name}</CardTitle>
            </Link>
            {status && (
              <span
                className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}
              >
                {statusText}
              </span>
            )}
            {priority && (
              <div className="flex items-center">
                <PriorityChevrons priority={priority} />
              </div>
            )}
          </div>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>

        {/* Only display short_description if available */}
        {short_description && (
          <div className="text-sm text-muted-foreground line-clamp-2 mt-2">
            {short_description}
          </div>
        )}

        <Progress
          value={progress}
          className={`h-3 mt-2 ${status === 'paused' ? '[&>div]:bg-gray-400' : '[&>div]:bg-blue-600'}`}
        />
      </CardHeader>

      <CardContent className="p-0 pt-4">
        <div className="flex items-center">
          {team.length > 0 && (
            <div className="flex -space-x-2">
              {team.map((member) => (
                <Avatar
                  key={member._id}
                  className="border-2 border-background w-8 h-8"
                >
                  <AvatarImage src={member.image} alt={member.name} />
                  <AvatarFallback>
                    {member.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
          )}

          <div className="ml-auto flex items-center space-x-6">
            {lastUpdated && (
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {formatTimeAgo(lastUpdated)}
                </span>
              </div>
            )}

            <div className="flex items-center space-x-4">
              {tasks.total > 0 && (
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground mr-2">
                    Tasks:
                  </span>
                  <span className="text-sm font-medium">
                    {tasks.completed}/{tasks.total}
                  </span>
                </div>
              )}

              {decisions.total > 0 && (
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground mr-2">
                    Decisions:
                  </span>
                  <span className="text-sm font-medium">
                    {decisions.completed}/{decisions.total}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {updates?.latest && status !== 'draft' && status !== 'not_started' ? (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm max-w-[80%]">
                <span className="text-muted-foreground shrink-0">
                  Recent update:
                </span>
                <span className="ml-1.5 truncate">{updates.latest.title}</span>
              </div>
              {updates?.unread > 0 && (
                <div className="bg-red-100 text-red-700 rounded text-xs px-2 py-0.5 shrink-0 font-medium">
                  {updates.unread} new
                </div>
              )}
            </div>
          </div>
        ) : (
          (status === 'draft' || status === 'not_started') && (
            <div className="mt-3 pt-3 border-t">
              <div className="flex items-center text-sm">
                <span className="text-muted-foreground shrink-0">
                  Recent update:
                </span>
                <span className="ml-1.5 text-muted-foreground">
                  No changes yet
                </span>
              </div>
            </div>
          )
        )}
      </CardContent>
    </Card>
  );
}
