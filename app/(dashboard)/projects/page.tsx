'use client';
import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
  LayoutGrid,
  LayoutList,
  ChevronUp,
  Search,
} from 'lucide-react';
import Link from 'next/link';
import ProjectsTable from './components/projects-table';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Tag } from '@/zod/tags-schema';
import { useToast } from '@/components/hooks/use-toast';
import { StatusBadge, PriorityBadge } from './components/ProjectDetails';
import { Project, ProjectStatus, ProjectPriority } from '@/zod/projects-schema';
import { Person } from '@/zod/directory-schema';
import { MultiSelectTagFilter } from './components/MultiSelectTagFilter';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ProjectCard } from './components/project-card';

interface ProjectUpdate {
  title: string;
  author: string;
  timestamp: Date;
}

interface ProjectTableItem {
  id: string | number;
  name: string;
  description?: string;
  short_description?: string; // Added short_description
  status: ProjectStatus;
  priority: ProjectPriority;
  progress: number;
  tasks: number;
  completedTasks: number;
  decisions: number;
  completedDecisions: number;
  team: Person[];
  lastUpdated?: string;
  updates?: {
    total: number;
    unread: number;
    latest: ProjectUpdate;
  };
}

// No mock data needed anymore

// Helper function to map Convex project data to our UI format
const mapProjectData = (project: Project): ProjectTableItem => {
  return {
    id: project._id,
    name: project.name || 'Project Name',
    description: project.description,
    short_description: project.short_description,
    status: project.status || 'not_started',
    priority: project.priority || 'medium',
    progress: 0, // Default progress
    tasks: 0, // Default tasks
    completedTasks: 0, // Default completed tasks
    decisions: 0, // Default decisions
    completedDecisions: 0, // Default completed decisions
    team: [], // Empty team by default
    lastUpdated: new Date(
      project.updated_at || project._creationTime
    ).toISOString()
  };
};

const getStatusColor = (status: ProjectStatus) => {
  switch (status) {
    case 'in_progress':
      return 'bg-blue-100 text-blue-800';
    case 'paused':
      return 'bg-yellow-200 text-yellow-800';
    case 'not_started':
      return 'bg-gray-200 text-gray-700';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    case 'perpetual':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-200 text-gray-700';
  }
};

const getPriorityColor = (priority: ProjectPriority) => {
  switch (priority) {
    case 'high':
    case 'urgent':
      return 'text-orange-600';
    case 'medium':
      return 'text-blue-600';
    case 'low':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};

const PriorityChevrons = ({
  priority
}: {
  priority: ProjectPriority;
}) => {
  const color = getPriorityColor(priority);
  const className = `h-4 w-4 ${color} stroke-[3]`;

  switch (priority) {
    case 'high':
    case 'urgent':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[-2px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[10px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'medium':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-0">
            <ChevronUp className={className} />
          </div>
          <div className="absolute top-[6px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    case 'low':
      return (
        <div className="relative flex flex-col items-center justify-center w-6 h-6">
          <div className="absolute top-[4px]">
            <ChevronUp className={className} />
          </div>
        </div>
      );
    default:
      return null;
  }
};

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  }
}

export default function ProjectsPage() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>('all');
  const [priorityFilter, setPriorityFilter] = useState<string | null>('all');
  const [selectedTagIds, setSelectedTagIds] = useState<Id<'tags'>[]>([]);
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);

  // Fetch all projects with filters
  const projects = useQuery(api.projects.listProjects, {
    filter: {
      ...(selectedTagIds.length > 0 ? { tag_ids: selectedTagIds } : {})
    },
    sortBy: 'updated_at',
    sortDirection: 'desc'
  });

  // Handle loading state
  if (!projects) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Loading projects...</p>
      </div>
    );
  }

  // Filter projects based on search term and filters
  // Note: Tag filtering is already handled at the database level in the query
  const filteredProjects = projects.page.filter((project: Project) => {
    // Text search in name and description
    const matchesSearch =
      !searchTerm ||
      (project.name &&
        project.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (project.description &&
        project.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Status filter
    const matchesStatus =
      !statusFilter ||
      statusFilter === 'all' ||
      project.status === statusFilter;

    // Priority filter
    const matchesPriority =
      !priorityFilter ||
      priorityFilter === 'all' ||
      project.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Convert filtered projects to UI format for both views
  const mappedProjects = filteredProjects.map((project: Project) =>
    mapProjectData(project)
  );

  // Handle project checkbox change for table view
  const handleProjectCheckboxChange = (
    checked: boolean,
    id: string | number
  ) => {
    if (checked) {
      setSelectedProjects((prev) => [...prev, id.toString()]);
    } else {
      setSelectedProjects((prev) =>
        prev.filter((projectId) => projectId !== id.toString())
      );
    }
  };

  // Handle select all checkbox for table view
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProjects(
        mappedProjects.map((project: ProjectTableItem) => project.id.toString())
      );
    } else {
      setSelectedProjects([]);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Projects</h1>
          <p className="text-muted-foreground">
            Manage and track your projects
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setViewMode('card')}
              className={
                viewMode === 'card'
                  ? 'bg-border hover:bg-border/90'
                  : 'bg-background hover:bg-background/90'
              }
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setViewMode('list')}
              className={
                viewMode === 'list'
                  ? 'bg-border hover:bg-border/90'
                  : 'bg-background hover:bg-background/90'
              }
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-xl">Filters</CardTitle>
          <CardDescription>
            Filter projects by name, status, priority, or tag
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search field */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Status filter */}
            <div className="w-full md:w-48">
              <Select
                value={statusFilter || 'all'}
                onValueChange={(value) =>
                  setStatusFilter(value === 'all' ? null : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="perpetual">Perpetual</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Priority filter */}
            <div className="w-full md:w-48">
              <Select
                value={priorityFilter || 'all'}
                onValueChange={(value) =>
                  setPriorityFilter(value === 'all' ? null : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tag filter */}
            <div className="w-full md:w-[300px]">
              <MultiSelectTagFilter
                selectedTags={selectedTagIds}
                onChange={setSelectedTagIds}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card or Table View based on viewMode */}
      {viewMode === 'card' ? (
        /* Card View */
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredProjects.length === 0 ? (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              No projects found. Try adjusting your filters or create a new
              project.
            </div>
          ) : (
            mappedProjects.map((project: ProjectTableItem) => (
              <ProjectCard
                key={project.id}
                id={project.id as string}
                name={project.name}
                description={project.description}
                short_description={project.short_description}
                status={project.status}
                priority={project.priority}
                progress={project.progress}
                lastUpdated={project.lastUpdated}
                team={project.team}
                tasks={{
                  total: project.tasks,
                  completed: project.completedTasks
                }}
                decisions={{
                  total: project.decisions,
                  completed: project.completedDecisions
                }}
                updates={project.updates}
              />
            ))
          )}
        </div>
      ) : (
        /* Table View */
        <Card>
          <CardContent className="p-0">
            <ProjectsTable
              projects={mappedProjects}
              selectedProjects={selectedProjects}
              onProjectCheckboxChange={handleProjectCheckboxChange}
              onSelectAll={handleSelectAll}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
