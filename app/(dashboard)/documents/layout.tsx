'use client';

import React from 'react';
import SecondaryNavMenu from '@/components/blocks/secondary-nav-menu';
import { usePathname } from 'next/navigation';

// Define the navigation items here, as they are part of the shared layout
const navItems = [
  { label: 'Home', href: '/documents' }, 
  { label: 'Collections', href: '/documents/collections' },
  { label: 'Knowledge Base', href: '/documents/knowledge-base' }, 
  { label: 'Meeting Notes', href: '/documents/meeting-notes' },
  { label: 'Contracts', href: '/documents/contracts' },
  { label: 'Documents', href: '/documents/general-docs' } 
];

export default function DocumentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the active item based on the current path
  // Find the item whose href is the longest prefix of the current pathname
  let activeItemHref = '/documents'; // Default to home
  let maxMatchLength = '/documents'.length;

  for (const item of navItems) {
    if (pathname.startsWith(item.href) && item.href.length > maxMatchLength) {
      activeItemHref = item.href;
      maxMatchLength = item.href.length;
    }
  }
  
  // Handle the case where the exact path is /documents
  if (pathname === '/documents') {
     activeItemHref = '/documents';
  }

  // Get the heading text based on the active item
  const getHeadingText = () => {
    // Show "Fojo Documents" specifically for the home page
    if (pathname === '/documents') {
      return 'Fojo Documents';
    }
    const activeItem = navItems.find(item => item.href === activeItemHref);
    return activeItem ? activeItem.label : 'Fojo Documents';
  };

  return (
    <div className="flex flex-col min-h-full max-w-full overflow-x-hidden">
      {/* Header with Secondary Navigation */}
      <header className="border-b px-2 sm:px-4 md:px-6 py-3 sm:py-4 w-full">
         {/* Add title above the nav menu */}
         <h1 className="text-2xl sm:text-3xl font-bold truncate">{getHeadingText()}</h1>
         <div className="mt-2 sm:mt-4 overflow-x-auto -mx-2 sm:mx-0 px-2 sm:px-0"> 
            <SecondaryNavMenu
              items={navItems}
              activeItem={activeItemHref}
            />
         </div>
      </header>

      {/* Page Content */}
      <main className="flex-1 w-full max-w-full overflow-x-hidden"> 
        {/* The specific page component (e.g., KnowledgeBaseListPage) will be rendered here */}
        {children}
      </main>
    </div>
  );
}
