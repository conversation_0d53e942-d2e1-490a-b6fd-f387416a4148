"use client";

import type React from "react";

import { useState } from "react";
import { FileUp, FileText, Calendar, File, Plus, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/components/lib/utils";
import { CollectionCard } from "@/components/ui/collection-card";
import { ItemCard } from "@/components/ui/item-card";

export default function NewDocumentPage() {
  const router = useRouter();
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [selectedDocType, setSelectedDocType] = useState<string | null>(null);

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file: File) => {
    setIsUploading(true);
    // Simulate upload process
    setTimeout(() => {
      setUploadedFile(file);
      setIsUploading(false);
    }, 1500);
  };

  const handleCreateFromScratch = (type: string) => {
    router.push(`/editor/${type}`);
  };

  const handleContinueWithUpload = () => {
    if (!selectedDocType || !uploadedFile) return;

    if (
      selectedDocType === "knowledge-base" ||
      selectedDocType === "meeting-notes"
    ) {
      router.push(`/editor/${selectedDocType}?file=${uploadedFile.name}`);
    } else {
      console.log(`Storing contract: ${uploadedFile.name}`);
      setIsUploadDialogOpen(false);
      setUploadedFile(null);
      router.push("/documents");
    }
  };

  const openUploadDialog = (type: string) => {
    setSelectedDocType(type);
    setIsUploadDialogOpen(true);
  };

    const documentTypes = [
        {
            id: "knowledge-base",
            name: "Knowledge Base Article",
            description: "Create searchable, editable knowledge base articles",
            icon: FileText,
            canCreateFromScratch: true,
        },
        {
            id: "meeting-notes",
            name: "Meeting Notes",
            description: "Document meeting discussions, decisions, and action items",
            icon: Calendar,
            canCreateFromScratch: true,
        },
        {
            id: "contract",
            name: "Contract",
            description: "Store contracts securely without editing",
            icon: File,
            canCreateFromScratch: false,
        },
        {
            id: "document",
            name: "Document",
            description: "Store documents securely without editing",
            icon: File,
            canCreateFromScratch: false,
        }
    ];

    return (
        <div className="container mx-auto py-6 max-w-3xl">
            <div className="flex items-center mb-6">
                <Button variant="ghost" size="icon" asChild className="mr-2">
                    <Link href="/documents">
                        <ArrowLeft className="h-5 w-5" />
                        <span className="sr-only">Back</span>
                    </Link>
                </Button>
                <h1 className="text-2xl font-bold">Add New Document</h1>
            </div>

            <CollectionCard heading="Select document type" subheading="Choose a document type to get started.">
                <div className="divide-y">
                    {documentTypes.map((type) => (
                        <div key={type.id}>
              <ItemCard
                icon={type.icon}
                title={type.name}
                description={type.description}
                onCreateClick={() => handleCreateFromScratch(type.id)}
                onUploadClick={() => openUploadDialog(type.id)}
                canCreateFromScratch={type.canCreateFromScratch}
              />
            </div>
          ))}
        </div>
      </CollectionCard>

      {/* Upload Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {selectedDocType === "knowledge-base" && "Upload Knowledge Base Article"}
              {selectedDocType === "meeting-notes" && "Upload Meeting Notes"}
              {selectedDocType === "contract" && "Upload Contract"}
              {selectedDocType === "document" && "Upload Document"}
            </DialogTitle>
            <DialogDescription>
              {selectedDocType === "knowledge-base" || selectedDocType === "meeting-notes"
                ? "Your document will be processed and converted to an editable format."
                : "Your document will be stored as-is without editing capabilities."}
            </DialogDescription>
          </DialogHeader>

          {!uploadedFile ? (
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-6 text-center",
                dragActive ? "border-muted-foreground/75 bg-muted-foreground/5" : "border-muted-foreground/25"
              )}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center justify-center space-y-3">
                <div className="rounded-full bg-muted-foreground/5 p-3">
                  <FileUp className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-base font-medium">Drag and drop your document here</h3>
                <p className="text-sm text-muted-foreground">Supported formats: PDF, DOCX, TXT, MD</p>

                <div className="mt-2">
                  <label htmlFor="file-upload">
                    <Button size="sm" asChild>
                      <span>Select File</span>
                      <input
                        id="file-upload"
                        type="file"
                        className="sr-only"
                        onChange={handleFileChange}
                        accept=".pdf,.docx,.txt,.md"
                      />
                    </Button>
                  </label>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="border rounded-md p-3">
                <p className="text-sm text-muted-foreground mb-1">Uploaded file:</p>
                <div className="flex items-center gap-2 font-medium text-sm">
                  <File className="h-4 w-4" />
                  {uploadedFile.name}
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" size="sm" onClick={() => setUploadedFile(null)}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleContinueWithUpload}>
                  {selectedDocType === "knowledge-base" || selectedDocType === "meeting-notes"
                    ? "Continue to Editor"
                    : "Upload Document"}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Upload Processing Dialog */}
      <Dialog open={isUploading}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Processing Document</DialogTitle>
            <DialogDescription>Please wait while we process your document...</DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-4">
            <div className="h-8 w-8 rounded-full border-4 border-muted-foreground border-t-transparent animate-spin"></div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
