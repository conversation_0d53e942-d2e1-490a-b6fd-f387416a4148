"use client";

import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button"; // Keep Button import if needed elsewhere
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
// Removed Popover imports
// Removed SecondaryNavMenu import
import { FeaturedCollections } from "@/components/documents/featured-collections";
import { useQuery } from 'convex/react'; // Keep useQuery for currentUser and document counts
import { api } from '@/convex/_generated/api'; // Keep api import
import { useRouter } from 'next/navigation'; // Keep useRouter if needed elsewhere
import { useToast } from '@/components/hooks/use-toast'; // Keep useToast if needed elsewhere
// Removed PlusCircle, MeetingIcon imports from lucide-react
import { DocumentTypes } from "@/components/documents/document-types";
import { RecentlyModified } from "@/components/documents/recently-modified";
import { Badge } from "@/components/ui/badge"; // Keep Badge if used
// Removed MessageSquare, Grid3X3, ArrowRight, Send imports from lucide-react
import PrimaryChatCTA from "@/components/documents/primary-chat-cta";
// Removed Plus, Book, CalendarClock, FileText, File imports from icons
import { PanelLeft, CalendarClock as CalendarClockIcon, FileText as FileTextIcon, File as FileIcon } from "@/components/icons"; // Keep necessary icons for docTypes

const DocumentDashboard = () => {
  // Removed activeTab state
  const [chatQuery, setChatQuery] = useState("");
  // Removed Popover state
  const router = useRouter(); // Keep router if needed
  const { toast } = useToast(); // Keep toast if needed
  // Removed createKBArticle, createMeetingNote mutations (handled in HeaderBar)
  // Fetch user identity using currentUser query - Keep if needed for other logic
  const currentUser = useQuery(api.users.currentUser);
  
  // Fetch document counts from Convex
  const documentCounts = useQuery(api.files.files.getDocumentTypeCounts) || {
    KNOWLEDGE_BASE: 0,
    MEETING_NOTES: 0,
    CONTRACT: 0,
    DOCUMENT: 0
  };

  // Removed navItems definition (moved to layout)

  // Use renamed icon imports for clarity and add routes
  const docTypes = [
    { 
      id: 'KNOWLEDGE_BASE', 
      name: 'Knowledge Base', 
      icon: <PanelLeft size={16} />, 
      count: documentCounts?.KNOWLEDGE_BASE || 0, 
      color: 'text-blue-500',
      route: '/documents/knowledge-base'
    },
    { 
      id: 'MEETING_NOTES', 
      name: 'Meeting Notes', 
      icon: <CalendarClockIcon size={16} />, 
      count: documentCounts?.MEETING_NOTES || 0, 
      color: 'text-amber-500',
      route: '/documents/meeting-notes'
    },
    { 
      id: 'CONTRACT', 
      name: 'Contracts', 
      icon: <FileTextIcon size={16} />, 
      count: documentCounts?.CONTRACT || 0, 
      color: 'text-emerald-500',
      route: '/documents/contracts'
    },
    { 
      id: 'DOCUMENT', 
      name: 'Documents', 
      icon: <FileIcon size={16} />, 
      count: documentCounts?.DOCUMENT || 0, 
      color: 'text-purple-500',
      route: '/documents/general-docs'
    }
  ];

  // Recent documents are now fetched directly in the RecentlyModified component

  // More comprehensive document collections
  const collections = [
    {
      id: "1",
      title: 'Client Onboarding',
      description: 'Documents related to new client setup',
      docCount: 12,
      docTypes: ['KNOWLEDGE_BASE', 'CONTRACT', 'DOCUMENT'],
      lastUpdated: '2 days ago'
    },
    {
      id: "2",
      title: 'Project Alpha',
      description: 'Strategic initiative documentation',
      docCount: 8,
      docTypes: ['MEETING_NOTES', 'DOCUMENT'],
      lastUpdated: '5 days ago'
    },
    {
      id: "3",
      title: 'Service Delivery',
      description: 'Service implementation and delivery',
      docCount: 15,
      docTypes: ['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT'],
      lastUpdated: '1 week ago'
    },
  ];

  const getDocTypeColor = (type: string) => {
    const docType = docTypes.find(dt => dt.id === type);
    return docType ? docType.color : 'text-gray-500';
  };

  const getDocTypeIcon = (type: string) => {
    const docType = docTypes.find(dt => dt.id === type);
    return docType ? docType.icon : <FileIcon size={16} />; // Use FileIcon here for the default
  };

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle chat submission
    console.log("Chat query:", chatQuery);
    setChatQuery("");
  };


  return (
    <div className="flex flex-col">
        {/* Main Content - Render directly without activeTab check */}
      <main className="flex-1 min-h-screen overflow-auto p-6">
        <>
          {/* Primary Chat CTA */}
          <PrimaryChatCTA />

          {/* Recent Documents - Moved directly below PrimaryChatCTA */}
          <RecentlyModified />

          {/* Featured Collections */}
          <FeaturedCollections collections={collections} />

          {/* Document Types (Small Cards) */}
          <DocumentTypes docTypes={docTypes} />
          </>
      </main>
    </div>
  );
};

export default DocumentDashboard;
