'use client';

import { <PERSON>ert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/hooks/use-toast';
import { AlertCircle } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { DuplicateGroup } from '@/zod/directory-schema';
import { Id } from '@/convex/_generated/dataModel';

interface Props {
  type: 'people' | 'organizations';
}

export function DuplicateEntitiesAlert({ type }: Props) {
  const { toast } = useToast();
  const [duplicates, setDuplicates] = useState<DuplicateGroup[]>([]);

  // Query potential duplicates based on type
  const potentialDuplicates = useQuery(
    type === 'people'
      ? api.directory.directoryPeople.findDuplicatePeople
      : api.directory.directoryOrganizations.findDuplicateOrganizations,
    { limit: 100, threshold: 0.7 }
  );

  // Mutation to merge entities - using the correct merge functions
  const mergePeopleMutation = useMutation(api.directory.directoryPeople.mergePeople);
  const mergeOrganizationsMutation = useMutation(api.directory.directoryOrganizations.mergeOrganizations);

  useEffect(() => {
    if (potentialDuplicates) {
      // Handle different return types from the two API endpoints
      if (Array.isArray(potentialDuplicates)) {
        // For organizations (returns array directly)
        setDuplicates(potentialDuplicates);
      } else if (potentialDuplicates.duplicates) {
        // For people (returns object with duplicates array)
        setDuplicates(potentialDuplicates.duplicates);
      }
    }
  }, [potentialDuplicates]);

  const handleMerge = useCallback(
    async (group: DuplicateGroup) => {
      try {
        if (type === 'people') {
          // For people, use the first ID as primary and the rest as secondary
          const primaryId = group.ids[0] as Id<"people">;
          const secondaryIds = group.ids.slice(1) as Id<"people">[];
          
          await mergePeopleMutation({
            primaryId,
            secondaryIds
          });
        } else {
          // For organizations, use the first ID as target and the rest as sources
          const targetId = group.ids[0] as Id<"organizations">;
          const sourceIds = group.ids.slice(1) as Id<"organizations">[];
          
          await mergeOrganizationsMutation({
            targetId,
            sourceIds
          });
        }
        
        toast({
          title: 'Success',
          description: `Successfully merged ${type === 'people' ? 'people' : 'organizations'} with name "${group.name}"`
        });
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        toast({
          title: 'Error',
          description: `Failed to merge: ${errorMessage}`,
          variant: 'destructive'
        });
      }
    },
    [mergePeopleMutation, mergeOrganizationsMutation, toast, type]
  );

  if (!duplicates.length) return null;

  return (
    <div className="space-y-4 mb-6">
      {duplicates.map((group, index) => (
        <Alert key={index} className="bg-yellow-50/50">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle className="ml-2">
            Potential Duplicate {type === 'people' ? 'People' : 'Organizations'}{' '}
            Found
          </AlertTitle>
          <AlertDescription className="ml-2">
            <div className="mt-2">
              <p>
                Found {group.ids.length} {type} with similar names:
                <span className="font-semibold"> "{group.name}"</span>
                {group.similarity < 1 &&
                  ` (${Math.round(group.similarity * 100)}% similar)`}
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={async () => handleMerge(group)}
              >
                Merge {type}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
}
