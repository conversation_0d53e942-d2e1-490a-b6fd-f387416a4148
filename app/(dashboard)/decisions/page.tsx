'use client';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { useQuery, useConvexAuth, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel'; // Import Doc
import TemplateFilters, { FilterOption } from '../../../components/blocks/template-filters';
import ViewSwitcher from '../../../components/blocks/view-switcher';
import { useToast } from '@/components/hooks/use-toast';
import TemplateTable from '../../../components/blocks/template-table';
import CollectionCard from '@/components/ui/collection-card';
import DecisionsCard from '@/app/(dashboard)/decisions/components/decisionsCard';
import KanbanView from './components/KanbanView';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox'; // Re-add Checkbox import
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { HelpCircle, Info } from '@/components/icons';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { subDays, subMonths, subYears, startOfDay } from 'date-fns';

import { Spinner } from '@/components/ui/spinner';
import EditKanbanModal from './components/EditKanbanModal'; // Import the new component
// Import string validator instead of enum
import { DecisionStatusEnum, LEGACY_STATUS_VALUES } from '@/zod/decisions-schema';

/**
 * Decisions Dashboard Page
 *
 * Displays a list of decisions with filtering and pagination.
 * Supports three view modes:
 * - Kanban: Drag-and-drop board with columns for different decision statuses
 * - Card: Grid layout of decision cards
 * - Table: Tabular data presentation with sorting and filtering
 *
 * The Kanban view is the default and allows users to visually manage decision
 * workflow by dragging cards between status columns.
 */
// Define the time filter options
const TIME_FILTER_OPTIONS = [
  { value: 'today', label: 'Today', days: 0 },
  { value: '1w', label: 'Last 1 Week', days: 7 },
  { value: '2w', label: 'Last 2 Weeks', days: 14 },
  { value: '1m', label: 'Last 1 Month', days: 30 },
  { value: '2m', label: 'Last 2 Months', days: 60 },
  { value: '3m', label: 'Last 3 Months', days: 90 },
  { value: '6m', label: 'Last 6 Months', days: 180 },
  { value: '1y', label: 'Last 1 Year', days: 365 }
];

export default function DecisionsPage() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>('all');
  const [viewMode, setViewMode] = useState<'card' | 'list' | 'kanban'>('kanban'); // Default to kanban view
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [cursor, setCursor] = useState<any>(null);
  const [allDecisions, setAllDecisions] = useState<Doc<'decisions'>[]>([]); // Use Doc type


  // Category filter state
  const [categoryFilter, setCategoryFilter] = useState<'All' | 'General' | 'Investment'>(() => {
    if (typeof window !== 'undefined') {
      return (localStorage.getItem('decisionsCategoryFilter') as 'All' | 'General' | 'Investment') || 'All';
    }
    return 'All';
  });

  // Edit Kanban modal state
  const [editKanbanModalOpen, setEditKanbanModalOpen] = useState(false);

  // Type alias for Decision Category Document for clarity
  type DecisionCategoryDoc = Doc<'decision_categories'>;

  // State for time filter (stored in localStorage)
  const [timeFilter, setTimeFilter] = useState<string>(() => {
    // Initialize from localStorage or default to '2m' (2 months)
    if (typeof window !== 'undefined') {
      return localStorage.getItem('decisionsTimeFilter') || '2m';
    }
    return '2m';
  });

  // State for the "My Decisions" filter - default to unchecked
  const [showMyDecisionsOnly, setShowMyDecisionsOnly] = useState(() => {
    // Initialize from localStorage or default to false
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('decisionsMyDecisionsFilter');
      return stored === 'true' ? true : false;
    }
    return false;
  });

  // Get authenticated user identity
  const { isLoading: authLoading, isAuthenticated } = useConvexAuth();
  const currentUser = useQuery(api.users.currentUser);

  // Fetch all decision categories to get their statuses and configurations
  const categoriesResult = useQuery(api.decisions.listDecisionCategories);
  const categories = categoriesResult; // Keep using 'categories' variable name for consistency below

  // Create a Map of all category configurations keyed by their ID
  const allCategoryConfigsMap = React.useMemo(() => {
    const map = new Map<Id<'decision_categories'>, DecisionCategoryDoc>();
    categories?.forEach(cat => {
      // Ensure cat._id is correctly typed as Id<'decision_categories'> if necessary
      map.set(cat._id as Id<'decision_categories'>, cat as DecisionCategoryDoc);
    });
    return map;
  }, [categories]);


  // Find the specific category configurations based on the current filter
  const allKanbanCategory = categories?.find(
    (cat: DecisionCategoryDoc) => cat.immutable_slug === "all-decisions-kanban"
  );
  const generalCategory = categories?.find(
    (cat: DecisionCategoryDoc) => cat.name?.toLowerCase() === "general"
  );
  const investmentCategory = categories?.find(
    (cat: DecisionCategoryDoc) => cat.name?.toLowerCase() === "investment"
  );

  // Fetch decisions from Convex with pagination
  const decisionsResult = useQuery(api.decisions.listDecisions, {
    pagination: {
      limit: 25,
      cursor: cursor,
      sortBy: '_creationTime',
      sortDirection: 'desc'
    }
  });

  // Save time filter to localStorage when it changes and sync with KanbanView
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('decisionsTimeFilter', timeFilter);
    }
  }, [timeFilter]);

  // Load time filter from localStorage when it changes externally (from KanbanView)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleStorageChange = () => {
        const storedFilter = localStorage.getItem('decisionsTimeFilter');
        if (storedFilter && storedFilter !== timeFilter) {
          setTimeFilter(storedFilter);
        }

        const storedMyDecisions = localStorage.getItem('decisionsMyDecisionsFilter');
        const myDecisionsValue = storedMyDecisions === 'true';
        if (storedMyDecisions !== null && myDecisionsValue !== showMyDecisionsOnly) {
          setShowMyDecisionsOnly(myDecisionsValue);
        }
        // Sync category filter from localStorage
        const storedCategory = localStorage.getItem('decisionsCategoryFilter');
        if (storedCategory && storedCategory !== categoryFilter) {
          setCategoryFilter(storedCategory as 'All' | 'General' | 'Investment');
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }
  }, [timeFilter, showMyDecisionsOnly, categoryFilter]);

  // Save My Decisions filter to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('decisionsMyDecisionsFilter', showMyDecisionsOnly.toString());
      // Dispatch a storage event to notify other components
      window.dispatchEvent(new Event('storage'));
    }
  }, [showMyDecisionsOnly]);

  // Persist category filter to localStorage and notify other components
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('decisionsCategoryFilter', categoryFilter);
      window.dispatchEvent(new Event('storage'));
    }
  }, [categoryFilter]);

  // Update allDecisions when new data is fetched
  React.useEffect(() => {
    // Ensure the fetched decisions conform to the expected type
    const newDecisions = decisionsResult?.decisions as Doc<'decisions'>[] | undefined;
    if (newDecisions) {
      if (cursor) {
        // Append new decisions to existing ones
        setAllDecisions(prev => [...prev, ...newDecisions]);
      } else {
        // Initial load
        setAllDecisions(newDecisions);
      }
    }
  }, [decisionsResult, cursor]);

  // Generate status options from all categories' statuses
  const statusOptions: FilterOption[] = React.useMemo(() => {
    // Start with an "All" option
    const options: FilterOption[] = [{ value: 'all', label: 'All Statuses' }];

    // If categories are loaded, extract all unique statuses
    if (categories) {
      const statusMap = new Map<string, string>(); // Map to track unique statuses (value -> label)

      // Collect all statuses from all categories
      categories.forEach(category => {
        if (category.statuses && Array.isArray(category.statuses)) {
          category.statuses.forEach(status => {
            // Use label if available, otherwise use value
            statusMap.set(status.value, status.label || status.value);
          });
        }
      });

      // Convert the map to array of FilterOptions
      statusMap.forEach((label, value) => {
        options.push({ value, label });
      });
    } else {
      // Fallback to legacy statuses if categories aren't loaded yet
      LEGACY_STATUS_VALUES.forEach(value => {
        // Convert snake_case to Title Case for the label
        const label = value
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        options.push({ value, label });
      });
    }

    return options;
  }, [categories]);

  // ===========================
  // FILTERING LOGIC
  // ===========================

  // Calculate the cutoff timestamp based on the time filter
  const getTimeFilterCutoff = () => {
    // 'all' option removed; always apply a time filter
    if (timeFilter === 'all') {
      return 0; // No time filter
    }

    const timeOption = TIME_FILTER_OPTIONS.find(option => option.value === timeFilter);
    if (!timeOption || timeOption.days === undefined) return 0;

    // For 'today' option, use the past 24 hours (not just since midnight)
    if (timeOption.value === 'today') {
      // Cutoff is 24 hours ago from now
      return Date.now() - 24 * 60 * 60 * 1000;
    }

    // For other options with days > 0
    const now = new Date();
    let cutoffDate: Date;

    if (timeOption.days <= 30) {
      cutoffDate = subDays(now, timeOption.days);
    } else if (timeOption.days <= 180) {
      cutoffDate = subMonths(now, Math.floor(timeOption.days / 30));
    } else {
      cutoffDate = subYears(now, Math.floor(timeOption.days / 365));
    }
    return startOfDay(cutoffDate).getTime();
  };

  // Filter decisions based on search term, status filter, time filter, and my decisions filter
  // Ensure the filter operation preserves the Doc<'decisions'> type
  const filteredDecisions = React.useMemo(() => allDecisions?.filter((decision: Doc<'decisions'>): decision is Doc<'decisions'> => {
    // Text search in name, short_description, description, and amount
    const matchesSearch =
      !searchTerm ||
      (decision.title &&
        decision.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (decision.short_description &&
        decision.short_description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (decision.description &&
        decision.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (typeof decision.amount === "number" &&
        !isNaN(decision.amount) &&
        decision.amount
          .toLocaleString("en-US", { style: "currency", currency: "USD", maximumFractionDigits: 0 })
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      (typeof decision.amount === "number" &&
        !isNaN(decision.amount) &&
        decision.amount
          .toString()
          .toLowerCase()
          .includes(searchTerm.toLowerCase()));

    // Status filter
    const matchesStatus =
      !statusFilter ||
      statusFilter === 'all' ||
      decision.status === statusFilter;

    // Time filter
    const cutoffTimestamp = getTimeFilterCutoff();
    const updateTime = decision.updated_at || decision._creationTime;
    const matchesTimeFilter = timeFilter === 'all' || updateTime >= cutoffTimestamp;

    // My Decisions filter
    let matchesMyDecisions = true;
    if (showMyDecisionsOnly && currentUser) {
      const userId = currentUser._id;
      const userTeamIds = currentUser.teams || [];
      const isDriver = decision.driver === userId;
      const isContributor = decision.contributors?.some((contributorId: Id<'users'> | Id<'teams'>) =>
        contributorId === userId || userTeamIds.includes(contributorId as Id<'teams'>)
      );
      matchesMyDecisions = isDriver || isContributor;
    }

    // Category filter (NEW)
    let matchesCategory = true;
    if (categoryFilter === 'General' && generalCategory) {
      matchesCategory = decision.decision_category_id === generalCategory._id;
    } else if (categoryFilter === 'Investment' && investmentCategory) {
      matchesCategory = decision.decision_category_id === investmentCategory._id;
    } // 'All' shows all categories

    return matchesSearch && matchesStatus && matchesTimeFilter && matchesMyDecisions && matchesCategory;
  }) || [], [allDecisions, searchTerm, statusFilter, timeFilter, showMyDecisionsOnly, currentUser, categoryFilter, generalCategory, investmentCategory]);

  // --- START: Fetch Badges and Tags based on filteredDecisions (Moved Here) ---

  // Extract decision IDs from the *filtered* list
  const filteredDecisionIds = React.useMemo(() => filteredDecisions.map(d => d._id), [filteredDecisions]);

  // Extract *all unique* badge IDs from the filtered decisions
  const uniqueBadgeIds = React.useMemo(() => {
    const ids = new Set<Id<'badges'>>();
    filteredDecisions.forEach(decision => {
      // Ensure decision.badges is treated as an array of IDs
      (decision.badges as Id<'badges'>[] | undefined)?.forEach(badgeId => ids.add(badgeId));
    });
    return Array.from(ids);
  }, [filteredDecisions]);

  // Fetch details for all unique badges needed
  const badgeDetailsResult = useQuery(
    api.badges.getBadgesByIds, // Use the existing function
    uniqueBadgeIds.length > 0 ? { badgeIds: uniqueBadgeIds } : 'skip'
  );

  // Create a map for easy lookup of badge details by ID
  const badgeDetailsMap = React.useMemo(() => {
    const map = new Map<Id<'badges'>, Doc<'badges'>>();
    badgeDetailsResult?.forEach(badge => {
      if (badge) {
        map.set(badge._id, badge);
      }
    });
    return map;
  }, [badgeDetailsResult]);

  // Fetch tags for the filtered decisions using the correct batch query
  const tagMapResult = useQuery(
    api.tags.getTagsForMultipleDecisions, // Use the function you created
    // Ensure decisionIds are correctly typed for the query
    filteredDecisionIds.length > 0 ? { decisionIds: filteredDecisionIds as Id<'decisions'>[] } : 'skip'
  );
  // Use the raw result object from the query
  const tagData = tagMapResult; // No need for useMemo conversion here if we use the object directly

  // --- END: Fetch Badges and Tags ---


  // ===========================
  // EVENT HANDLERS
  // ===========================

  // Handle item checkbox change for table view
  const handleItemCheckboxChange = (checked: boolean, id: string) => {
    if (checked) {
      setSelectedItems((prev) => [...prev, id]);
    } else {
      setSelectedItems((prev) => prev.filter((itemId) => itemId !== id));
    }
  };

  // Handle select all checkbox for table view
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredDecisions.map((item) => item._id));
    } else {
      setSelectedItems([]);
    }
  };

  // Handle decision click
  const handleDecisionClick = (id: Id<'decisions'>) => {
    // Navigate to decision details or perform other actions
    console.log(`Decision clicked: ${id}`);
    // Could add navigation here, e.g.:
    // router.push(`/decisions/${id}`);
  };

  // Handle load more button click
  const handleLoadMore = () => {
    if (decisionsResult?.continuation) {
      setCursor(decisionsResult.continuation);
    }
  };

  // Convert decisions to table items format
  const tableItems = filteredDecisions.map(decision => ({
    id: decision._id,
    title: decision.name,
    shortDescription: decision.shortDescription || '',
    status: decision.status || 'pending',
    lastUpdated: decision.updated_at || decision._creationTime,
    priority: decision.priority,
    assignedTo: decision.assignedTo,
    // Add any other fields needed for the table
  }));

  // Memoize kanbanColumns and statuses to avoid hook order issues
  const kanbanColumnsMemo = React.useMemo(() => (
    categoryFilter === "All"
      ? allKanbanCategory?.kanban_columns || []
      : categoryFilter === "General"
        ? generalCategory?.kanban_columns || []
        : investmentCategory?.kanban_columns || []
  ), [categoryFilter, allKanbanCategory, generalCategory, investmentCategory]);
  const statusesMemo = React.useMemo(() => (
    categoryFilter === "All"
      ? allKanbanCategory?.statuses || []
      : categoryFilter === "General"
        ? generalCategory?.statuses || []
        : investmentCategory?.statuses || []
  ), [categoryFilter, allKanbanCategory, generalCategory, investmentCategory]);

  // Memoize decisionCategoriesForCards at the top level to avoid breaking the Rules of Hooks
  const decisionCategoriesForCards = React.useMemo(() => {
    const map = new Map();
    filteredDecisions.forEach(decision => {
      const cat = allCategoryConfigsMap.get(decision.decision_category_id as Id<'decision_categories'>);
      if (cat) map.set(decision._id, cat);
    });
    return map;
  }, [filteredDecisions, allCategoryConfigsMap]);

  return (
    <div className="w-full mx-auto py-6">
      {/* Header section with title, subtitle, and view switcher */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Decisions</h1>
          <p className="text-muted-foreground">
            Manage and track all decisions across your organization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <ViewSwitcher viewMode={viewMode} setViewMode={setViewMode} />
        </div>
      </div>



      {/* Loading state */}
      {!decisionsResult && (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
        </div>
      )}

       {/* Table View */}
      {viewMode === "list" && decisionsResult && (
        <div>
          {filteredDecisions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No decisions found. Try adjusting your filters or create a new decision.
            </div>
          ) : (
            <TemplateTable
              items={tableItems}
              paginationEnabled={true}
              selectedItems={selectedItems}
              onItemCheckboxChange={handleItemCheckboxChange}
              onSelectAll={handleSelectAll}
              baseRoute="/decisions"
            />
          )}
        </div>
      )}

      {/* Kanban View */}
      {viewMode === 'kanban' && decisionsResult && (
        <>
          {/* Responsive Filters section for Kanban */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Filters group: wraps on small screens */}
            <div className="flex flex-wrap items-center gap-4">
              {/* Search bar */}
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  placeholder="Search decisions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-[250px]"
                />
              </div>
              {/* Time filter (no label) */}
              <div className="flex items-center gap-2">
                <Select value={timeFilter} onValueChange={setTimeFilter}>
                  <SelectTrigger id="kanban-time-filter" className="w-[180px]">
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_FILTER_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {/* "My Decisions" Switch with info popover */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="kanban-my-decisions-filter"
                  checked={showMyDecisionsOnly}
                  onCheckedChange={setShowMyDecisionsOnly}
                  className="border"
                  style={{ borderColor: 'var(--foreground)' }}
                />
                <Label htmlFor="kanban-my-decisions-filter" className="text-sm font-medium">
                  My decisions
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <button
                      type="button"
                      className="p-0 m-0 bg-transparent border-none outline-none"
                      tabIndex={0}
                      aria-label="Info about My decisions"
                    >
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent align="start" side="bottom" className="max-w-xs text-sm">
                    Shows decisions where you are driver, contributor, or informed (including teams you are on).
                  </PopoverContent>
                </Popover>
              </div>
              {/* Category Dropdown (no label) */}
              {/*
                Category selector: prevent wrapping and enable truncation.
                - min-w-0: allow truncation inside flex
                - truncate, overflow-hidden, text-ellipsis: show ellipsis for long text
                - max-w-xs: limit width for modern, clean look
              */}
              {/*
              Filter bar row: horizontally scrollable, prevents wrapping, allows truncation.
              - flex-nowrap: prevents wrapping
              - min-w-0: enables children to truncate
              - overflow-x-auto: allows horizontal scroll if needed
            */}
            <div className="flex flex-nowrap items-center gap-2 min-w-0 overflow-x-auto">
                <Select value={categoryFilter} onValueChange={v => setCategoryFilter(v as any)}>
                  {/*
                    Category selector: prevent wrapping and enable truncation.
                    - whitespace-nowrap: never wraps
                    - min-w-0: enables truncation
                    - max-w-[200px]: limit width for modern look
                    - truncate, overflow-hidden, text-ellipsis: ellipsis for long text
                  */}
                  <SelectTrigger id="kanban-category-filter" className="whitespace-nowrap min-w-0 max-w-[200px] truncate overflow-hidden text-ellipsis rounded-lg">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All" className="truncate">All Decisions</SelectItem>
                    <SelectItem value="General" className="truncate">General Decisions</SelectItem>
                    <SelectItem value="Investment" className="truncate">Investment Decisions</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Edit Kanban View Button: moves below filters on small screens */}
            <div className="flex items-center mt-2 md:mt-0">
              <Button
                variant="secondary"
                className="ml-2"
                onClick={() => setEditKanbanModalOpen(true)}
                type="button"
              >
                Edit Kanban View
              </Button>
            </div>
          </div>
          {/* KanbanView with memoized props */}
          <KanbanView
            kanbanColumns={kanbanColumnsMemo}
            statuses={statusesMemo}
            decisions={filteredDecisions}
            categoryFilter={categoryFilter}
            badgeDetailsMap={badgeDetailsMap}
            tagData={tagData}
            allCategoryConfigs={allCategoryConfigsMap}
            decisionCategoriesForCards={decisionCategoriesForCards}
          />
          {/* Edit Kanban Modal */}
          {editKanbanModalOpen && categories && ( // Ensure categories are loaded before rendering modal
            <EditKanbanModal
              open={editKanbanModalOpen}
              onClose={() => setEditKanbanModalOpen(false)}
              categories={categories || []}
              categoryFilter={categoryFilter}
            />
          )}
        </>
      )}

      {/* Card View */}
      {viewMode === 'card' && decisionsResult && (
        <>
          {/* Filters section */}
          <div className="mb-6">
            <div className="flex flex-wrap items-center gap-4 mb-4">
              {/* Time filter */}
              <div className="flex items-center gap-2">
                <Label htmlFor="card-time-filter" className="text-sm font-medium">Time Filter:</Label>
                <Select value={timeFilter} onValueChange={setTimeFilter}>
                  <SelectTrigger id="card-time-filter" className="w-[180px]">
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_FILTER_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* "My Decisions" Filter Checkbox */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="card-my-decisions-filter"
                  checked={showMyDecisionsOnly}
                  onCheckedChange={(checked: boolean | 'indeterminate') => setShowMyDecisionsOnly(checked === true)} // Add type annotation
                />
                <Label htmlFor="card-my-decisions-filter" className="text-sm font-medium">
                  Show only my decisions (Driver/Contributor)
                </Label>
              </div>
            </div>

            <TemplateFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              primaryFilter={statusFilter}
              onPrimaryFilterChange={setStatusFilter}
              primaryFilterOptions={statusOptions}
              primaryFilterLabel="Status"
              secondaryFilter={null}
              onSecondaryFilterChange={() => {}}
              secondaryFilterOptions={[]}
              secondaryFilterLabel="Priority"
              title="Filters"
              description="Filter decisions by name or status"
            />
          </div>


            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDecisions.length === 0 ? (
                <div className="col-span-full text-center py-8 text-muted-foreground">
                  No decisions found. Try adjusting your filters or create a new decision.
                </div>
              ) : (
                filteredDecisions.map((decision) => {
                  // Look up tags from the tagData object using string ID
                  const decisionTags = tagData?.[decision._id];
                  // Look up badge *details* using the IDs from the decision and the details map
                  const decisionBadges = (decision.badges as Id<'badges'>[] | undefined)?.map(badgeId => badgeDetailsMap.get(badgeId)).filter(Boolean) as Doc<'badges'>[] | undefined;

                  return (
                    <DecisionsCard
                      key={decision._id}
                      decision={decision} // Decision object still contains badge IDs
                      badges={decisionBadges} // Pass the array of full badge documents
                      tags={decisionTags} // Pass the array of full tag documents
                      onClick={() => handleDecisionClick(decision._id as Id<'decisions'>)} // Ensure ID type match
                    />
                  );
                })
              )}
            </div>

            {/* Load More Button */}
            {filteredDecisions.length > 0 && decisionsResult.continuation && (
              <div className="flex justify-center mt-8">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                >
                  Load More
                </Button>
              </div>
            )}
         </>
      )}
    </div>
  );
}
