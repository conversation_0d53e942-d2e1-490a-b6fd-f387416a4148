'use client';

// Import necessary hooks and components
import React, { useState, useEffect, ReactNode } from 'react';
import { usePara<PERSON>, useRouter, usePathname } from 'next/navigation'; // Added usePathname
import { useQuery, useMutation } from 'convex/react';
import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useToast } from '@/components/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu imports
import SecondaryNavMenu from "@/components/blocks/secondary-nav-menu";
import DecisionActionBar from '@/components/decisions/ActionBar'; // Use DecisionActionBar
import { ArrowLeft, Trash2, AlertCircle, MoreVertical } from 'lucide-react'; // Added MoreVertical
import CollectionCardModal from "@/components/ui/collectionCardModal"; // For delete modal
import { Badge } from "@/components/ui/badge"; // For delete modal
import { getStatusStyle, formatStatus } from '@/lib/utils/status-styles'; // For delete modal
import { formatRelativeTime } from "@/lib/date-utils"; // For delete modal

// Define the props for the layout component
interface DecisionDetailLayoutProps {
  children: ReactNode;
}

export default function DecisionDetailLayout({ children }: DecisionDetailLayoutProps) {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname(); // Get current path
  const decisionId = params.id as string;
  const { toast } = useToast();

  // State for title editing
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [titleValue, setTitleValue] = useState('');

  // State for saving status (used for title saves and delete action button)
  const [isSaving, setIsSaving] = useState(false);

  // State for delete confirmation modal (keep for CollectionCardModal)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Fetch decision details using the Convex API
  const decision = useQuery(api.decisions.getDecision, {
    id: decisionId as Id<'decisions'>
  });

  // Fetch decision category - moved outside conditional
  const decisionCategory = useQuery(api.decisions.getDecisionDetails, 
    decision ? { id: decisionId as Id<'decisions'> } : 'skip'
  );

  // Extract tag_type_id array from the category (default to empty array if missing)
  const tagTypeIds = decisionCategory?.category?.tag_type_id ?? [];

  // Fetch all tag type objects for the IDs in the array
  const tagTypes = useQuery(
    api.tags.getTagTypesByIds,
    tagTypeIds.length > 0 ? { ids: tagTypeIds } : 'skip'
  );

  // Fetch the general tag type (always show this section in ActionBar)
  const generalTagType = useQuery(api.tags.getTagTypeBySlug, { slug: "general-tags" });

  const updateDecision = useMutation(api.decisions.updateDecisions);
  const deleteDecision = useMutation(api.decisions.deleteDecisions);
  const removeTag = useMutation(api.tags.removeTagFromTaggable);

  // Fetch tags for the decision
  const tags = useQuery(api.tags.getTagsForTaggable, {
    taggable_id: decisionId as Id<'decisions'>,
    taggable_type: 'decision'
  });

  // Update title state when decision loads or changes
  useEffect(() => {
    if (decision) {
      setTitleValue(decision.title || '');
    }
  }, [decision]);

  // Handle title save on blur
  const handleTitleSave = async () => {
    if (titleValue.trim() !== '' && titleValue !== decision?.title) {
      try {
        setIsSaving(true);
        await updateDecision({
          updates: {
            id: decisionId as Id<'decisions'>,
            updates: {
              title: titleValue.trim()
            }
          }
        });
        toast({
          title: "Title updated",
          description: "The title has been updated successfully."
        });
      } catch (error: unknown) {
        toast({
          title: "Error updating title",
          description: error instanceof Error
            ? error.message
            : "There was an error updating the title.",
          variant: "destructive"
        });
        setTitleValue(decision?.title || '');
      } finally {
        setIsSaving(false);
      }
    } else if (titleValue.trim() === '') {
      setTitleValue(decision?.title || '');
    }
    setIsEditingTitle(false);
  };

   // Handle removing a tag
  const handleRemoveTag = async (tagId: Id<'tags'>) => {
    try {
      await removeTag({
        taggable_id: decisionId as Id<'decisions'>,
        taggable_type: 'decision',
        tagId: tagId
      });
      toast({
        title: "Tag removed",
        description: "The tag has been removed from this decision."
      });
      // Note: Convex should handle re-fetching automatically
    } catch (error) {
       toast({
        title: "Error removing tag",
        description: error instanceof Error ? error.message : "Failed to remove tag",
        variant: "destructive"
      });
    }
  };

  // Function to open the delete confirmation modal
  const openDeleteModal = () => {
    setIsDeleteModalOpen(true);
  };

  // Function to close the delete confirmation modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  // Handle delete action with optimistic navigation and delay
  const handleDelete = async () => {
    // Add explicit check
    if (!decision) return;
    // Capture title before navigation
    const decisionTitle = decision.title || 'Untitled Decision';

    // Close the modal first if it's open (though triggering from menu item now)
    setIsDeleteModalOpen(false);
    // Set saving state to disable buttons
    setIsSaving(true);

    // Optimistically navigate away first
    router.push('/decisions');

    // Show initial toast
    toast({
      title: "Deleting decision...",
      description: `"${decisionTitle}" is being deleted.`
    });

    // Add a delay to allow navigation/unmount before mutation
    await new Promise(resolve => setTimeout(resolve, 500)); // Updated delay to 500ms

    // Perform the delete mutation in the background
    try {
      const result = await deleteDecision({
        decisionIds: [decisionId as Id<"decisions">]
      });

      // Handle potential partial failures from the mutation if needed
      if (result.failedIds && result.failedIds.length > 0) {
         toast({
           title: "Error deleting decision",
           description: `Failed to delete "${decisionTitle}". ${result.failedIds[0].error}`,
           variant: "destructive",
           duration: 10000
         });
      }
      // No success toast needed as user is navigated away
    } catch (error: unknown) {
      // Show error toast if background deletion fails
      toast({
        title: "Error deleting decision",
        description: `Failed to delete "${decisionTitle}". ${error instanceof Error ? error.message : ''}`,
        variant: "destructive",
        duration: 10000 // Show error for longer
      });
    }
    // No need to reset isSaving as the component should unmount
  };

  // Determine active tab based on pathname
  const getCurrentActiveTab = () => {
    // Add more checks if more tabs are added
    if (pathname?.endsWith('/activity')) return 'activity';
    return 'overview'; // Default to overview
  };
  const activeTab = getCurrentActiveTab();

  // Define nav items with correct hrefs
  const navItems = [
    { label: "Overview", href: `/decisions/${decisionId}/overview` },
    { label: "Activity", href: `/decisions/${decisionId}/activity` }
    // Add more items like "Details" if needed
  ];

  // Handle loading state
  if (decision === undefined) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Loading decision details...</p>
      </div>
    );
  }

  // Handle not found state
  if (decision === null) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <AlertCircle className="h-12 w-12 text-destructive" />
        <p className="mt-4 text-lg font-semibold">Decision not found</p>
        <p className="text-muted-foreground">The requested decision could not be loaded.</p>
        <Button variant="outline" className="mt-4" asChild>
          <Link href="/decisions">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Decisions
          </Link>
        </Button>
      </div>
    );
  }

  // Format short description
  const shortDescription = decision.short_description || 'No description available';

  // Extract isActive status for investment decisions, checking subtableData
  const isActive = decisionCategory?.category?.subtable_type === 'investment_decisions' && decisionCategory.subtableData
    ? (decisionCategory.subtableData as any)?.is_active // Use type assertion cautiously or refine type from getDecisionDetails
    : undefined;

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="px-3 pt-3 pb-1"> {/* Reduced bottom padding from py-3 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" asChild>
              <Link href="/decisions">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            {/* Make this container flexible to allow title input to grow */}
            <div className="flex-1 min-w-0"> {/* Added flex-1 and min-w-0 */}
              {isEditingTitle ? (
                <Input
                  type="text"
                  value={titleValue}
                  onChange={(e) => setTitleValue(e.target.value)}
                  onBlur={handleTitleSave}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') e.currentTarget.blur();
                    else if (e.key === 'Escape') {
                      setTitleValue(decision.title || '');
                      setIsEditingTitle(false);
                    }
                  }}
                  autoFocus
                  className="text-2xl font-bold text-gray-900 bg-[hsl(var(--input))] border-none focus:outline-none focus:ring-2 focus:ring-primary/20 rounded px-1 w-full h-auto" // Added h-auto for consistency
                  disabled={isSaving}
                />
              ) : (
                <h1
                  className="text-2xl font-bold text-gray-900 cursor-pointer hover:bg-gray-50 rounded px-1"
                  onClick={() => {
                    setTitleValue(decision.title || '');
                    setIsEditingTitle(true);
                  }}
                >
                  {decision.title || 'Untitled Decision'}
                </h1>
              )}
              <p className="text-sm text-muted-foreground">{shortDescription}</p>
            </div>
          </div>
          <div className="flex gap-2">
            {/* More Options Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" disabled={isSaving}>
                  <MoreVertical className="h-4 w-4" />
                  <span className="sr-only">More options</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {/* Trigger for the existing custom modal */}
                <DropdownMenuItem
                  onClick={openDeleteModal} // Still use openDeleteModal to show the custom modal
                  className="text-red-500 focus:bg-red-50 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Decision
                </DropdownMenuItem>
                {/* Add other options here if needed */}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Tabs navigation using SecondaryNavMenu */}
        <div className="mt-5"> {/* Reduced margin from mt-6 */}
          <SecondaryNavMenu
            items={navItems}
            activeItem={activeTab}
             // Rely on component's internal logic for active state based on full hrefs
          />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Action Bar */}
        <div className="max-w-screen-lg w-full">
          <DecisionActionBar
            item={decision}
            updateDecision={updateDecision}
            tags={tags}
            onRemoveTag={handleRemoveTag}
            decisionCategory={decisionCategory?.category}
            tagTypes={tagTypes}
            generalTagType={generalTagType} // Always show General Tags section
            isActive={isActive}
          />
        </div>
        {/* Render the specific tab's content */}
        <div className="p-4"> {/* Add padding around children */}
          {children}
        </div>
      </main>
       {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
         <CollectionCardModal
          heading="Confirm Decision Deletion"
          subheading="This action cannot be undone"
          primaryCTA={{
            text: isSaving ? "Deleting..." : "Delete Decision", // Update button text while saving
            onClick: handleDelete,
            // disabled: isSaving, // Removed disabled prop
          }}
          secondaryCTA={{
            text: "Cancel",
            // disabled: isSaving, // Removed disabled prop
            onClick: closeDeleteModal,
          }}
          onClose={closeDeleteModal}
          showFooter={true}
          className="max-w-sm"
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              You are about to permanently delete the following decision:
            </p>

            <div className="bg-gray-100/50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900">{decision.title}</h3>
              {decision.short_description && (
                <p className="text-sm text-gray-600 mt-1">
                  {decision.short_description}
                </p>
              )}
              <div className="mt-2 flex items-center">
                <Badge
                  variant="outline"
                  className={`${getStatusStyle(decision?.status)}`}
                >
                  {formatStatus(decision?.status)}
                </Badge>
                <span className="text-xs text-gray-500 ml-2">
                  Created {formatRelativeTime(decision._creationTime)}
                </span>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              <p>
                This will permanently remove this decision and all associated
                data from the system.
              </p>
              <p className="mt-2 font-medium text-red-600">
                This action cannot be reversed.
              </p>
            </div>
          </div>
        </CollectionCardModal>
      )}
    </div>
  );
}
