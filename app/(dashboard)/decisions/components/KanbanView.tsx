'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { useQuery, useMutation, useConvexAuth } from 'convex/react';
import confetti from "canvas-confetti";
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel';
import { useToast } from '@/components/hooks/use-toast';
import DecisionsCard from '@/app/(dashboard)/decisions/components/decisionsCard';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { MorphingPopover, MorphingPopoverContent } from '@/components/ui/morphing-popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DecisionStatusEnum, LEGACY_STATUS_VALUES } from '@/zod/decisions-schema';
import { z } from 'zod';

// Define the time filter options
const TIME_FILTER_OPTIONS = [
  { value: 'today', label: 'Today', days: 0 },
  { value: '1w', label: '1 week', days: 7 },
  { value: '2w', label: '2 weeks', days: 14 },
  { value: '1m', label: '1 month', days: 30 },
  { value: '2m', label: '2 months', days: 60 },
  { value: '3m', label: '3 months', days: 90 },
  { value: '6m', label: '6 months', days: 180 },
  { value: '1y', label: '1 year', days: 365 },
  { value: 'all', label: 'All time', days: 0 }
];

// Define the column display names
const COLUMN_DISPLAY_NAMES: Record<string, string> = {
  draft: 'Draft',
  inProgress: 'In Progress',
  concluded: 'Concluded'
};

// Define the confetti function type
type ConfettiFn = (options?: confetti.Options) => Promise<any>;

/**
 * KanbanView Component
 *
 * Displays decisions in a Kanban board with three columns:
 * - Draft: Decisions with status "draft"
 * - In Progress: Decisions with status "in_review", "escalated", or "on_hold"
 * - Concluded: Decisions with status "approved", "rejected", or "cancelled"
 *
 * Supports drag and drop between columns to update decision status.
 */
interface KanbanViewProps {
  kanbanColumns: {
    name: string;
    order: number;
    statuses: string[];
  }[];
  statuses: {
    value: string;
    label?: string;
    description?: string;
  }[];
  decisions: Doc<'decisions'>[];
  categoryFilter: "All" | "General" | "Investment";
  badgeDetailsMap?: Map<Id<'badges'>, Doc<'badges'>>;
  tagData?: Record<string, Doc<'tags'>[]>;
  allCategoryConfigs: Map<Id<'decision_categories'>, Doc<'decision_categories'>>;
  decisionCategoriesForCards?: Map<string, Doc<'decision_categories'>>;
}

type DecisionCategoryDoc = Doc<'decision_categories'>;

// Active decision type for drag operations
interface ActiveDecision {
  id: Id<'decisions'>;
  destinationColumn: string;
  destinationStatuses: string[];
  position?: { top: number; left: number };
}

const KanbanView: React.FC<KanbanViewProps> = ({
  kanbanColumns,
  statuses,
  decisions,
  categoryFilter,
  badgeDetailsMap,
  tagData,
  allCategoryConfigs,
  decisionCategoriesForCards,
}) => {
  const { toast } = useToast();
  const router = useRouter();

  // Persistent refs instead of state where possible
  const popoverOpenRef = useRef(false);
  const activeDecisionRef = useRef<ActiveDecision | null>(null);
  const draggableRefs = useRef<Record<string, HTMLElement | null>>({});
  const concludedColumnRef = useRef<HTMLDivElement>(null);
  
  // Required UI state (minimized)
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [timeFilter, setTimeFilter] = useState(() => 
    typeof window !== 'undefined' ? localStorage.getItem('decisionsTimeFilter') || '2m' : '2m'
  );
  const [showMyDecisionsOnly, setShowMyDecisionsOnly] = useState(() => 
    typeof window !== 'undefined' ? localStorage.getItem('decisionsMyDecisionsFilter') === 'true' : false
  );
  const [columnStatusFilters, setColumnStatusFilters] = useState<Record<number, string[]>>({});
  
  // Sort columns once and memoize
  const sortedColumns = React.useMemo(() => 
    [...(kanbanColumns || [])].sort((a, b) => a.order - b.order),
    [kanbanColumns]
  );

  // Build status display names map once
  const STATUS_DISPLAY_NAMES = React.useMemo(() => {
    const map: Record<string, string> = {};
    statuses?.forEach((s) => {
      map[s.value] = s.label || s.value;
    });
    return map;
  }, [statuses]);

  // Update decision status mutation
  const updateDecision = useMutation(api.decisions.updateDecisions);

  // --- Optimistic state for drag-and-drop ---
  const [optimisticDecisions, setOptimisticDecisions] = useState<Doc<'decisions'>[]>(decisions);
  const [pendingStatusUpdate, setPendingStatusUpdate] = useState<{
    id: Id<'decisions'>;
    originalStatus: string;
  } | null>(null);

  // Sync optimisticDecisions with server decisions when they change (unless a pending update is in progress)
  useEffect(() => {
    if (!pendingStatusUpdate) {
      setOptimisticDecisions(decisions);
    }
  }, [decisions, pendingStatusUpdate]);

  // Local storage management
  useEffect(() => {
    // Initialize column filters
    if (kanbanColumns) {
      const initial: Record<number, string[]> = {};
      kanbanColumns.forEach((col, idx) => {
        initial[idx] = col.statuses.slice();
      });
      setColumnStatusFilters(initial);
    }
    
    // Save filters to localStorage and handle external changes
    if (typeof window !== 'undefined') {
      localStorage.setItem('decisionsTimeFilter', timeFilter);
      localStorage.setItem('decisionsMyDecisionsFilter', showMyDecisionsOnly.toString());
      
      const handleStorageChange = () => {
        const storedFilter = localStorage.getItem('decisionsTimeFilter');
        if (storedFilter && storedFilter !== timeFilter) {
          setTimeFilter(storedFilter);
        }

        const storedMyDecisions = localStorage.getItem('decisionsMyDecisionsFilter');
        const myDecisionsValue = storedMyDecisions === 'true';
        if (storedMyDecisions !== null && myDecisionsValue !== showMyDecisionsOnly) {
          setShowMyDecisionsOnly(myDecisionsValue);
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }
  }, [kanbanColumns, timeFilter, showMyDecisionsOnly]);

  /**
   * Create and trigger confetti effect
   */
  const triggerConfetti = useCallback((decisionId: Id<'decisions'>) => {
    // Create canvas for confetti
    const canvas = document.createElement('canvas');
    canvas.className = 'confetti-canvas';
    document.body.appendChild(canvas);

    const myConfetti = confetti.create(canvas, {
      resize: true,
      useWorker: true
    });

    const confettiInstance = (myConfetti || (() => Promise.resolve())) as ConfettiFn;
    
    // Trigger with small delay to allow UI to update
    setTimeout(() => {
      const decisionCard = document.querySelector(`[data-id="${decisionId}"]`);
      if (!decisionCard) {
        canvas.remove();
        return;
      }
      
      const rect = decisionCard.getBoundingClientRect();
      const originX = (rect.left + rect.width / 2) / window.innerWidth;
      const originY = rect.top / window.innerHeight;
      
      void confettiInstance({
        origin: { x: originX, y: originY },
        particleCount: 150,
        spread: 70
      }).then(() => {
        setTimeout(() => canvas.remove(), 3000);
      });
    }, 100);
  }, []);

  /**
   * Handle status update when a card is dropped or status is selected from popover
   */
  const handleStatusUpdate = useCallback(async (
    decisionId: Id<'decisions'>, 
    newStatus: string
  ) => {
    try {
      // Update UI optimistically
      setOptimisticDecisions(prev =>
        prev.map(d =>
          d._id === decisionId ? { ...d, status: newStatus } : d
        )
      );
      
      // Perform the actual update
      await updateDecision({
        updates: {
          id: decisionId,
          updates: {
            status: newStatus as z.infer<typeof DecisionStatusEnum>,
          },
        },
      });
      
      // Show success toast and trigger confetti if approved
      if (newStatus === 'approved') {
        triggerConfetti(decisionId);
      }
      
      toast({
        title: 'Status updated',
        description: `Decision status changed to ${formatStatusDisplay(newStatus)}`,
      });
    } catch (error) {
      console.error('Error updating decision status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update decision status',
        variant: 'destructive',
      });
    } finally {
      setPopoverOpen(false);
      activeDecisionRef.current = null;
      popoverOpenRef.current = false;
    }
  }, [updateDecision, toast, triggerConfetti]);
  
  /**
   * Format status text for display
   */
  const formatStatusDisplay = (status: string): string => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
  };

  /**
   * Calculate position for the status selection popover
   */
  const calculatePopoverPosition = (draggableId: string): { top: number; left: number } => {
    const draggedElement = draggableRefs.current[draggableId];
    let position = { top: 100, left: 100 };
    
    if (draggedElement) {
      const rect = draggedElement.getBoundingClientRect();
      position = {
        top: rect.bottom + 10,
        left: rect.left + (rect.width / 2) - 100,
      };
      
      // Ensure popover stays in viewport
      if (position.left < 10) position.left = 10;
      if (position.left + 200 > window.innerWidth) position.left = window.innerWidth - 210;
      if (position.top < 10) position.top = 10;
      if (position.top + 300 > window.innerHeight) position.top = window.innerHeight - 310;
    }
    
    return position;
  };

  /**
   * Handle drag end event
   */
  const handleDragEnd = useCallback(async (result: DropResult) => {
    const { source, destination, draggableId } = result;

    // No change or invalid drop
    if (!destination || source.droppableId === destination.droppableId) {
      return;
    }

    // Extra debug: log all decisions and the draggableId
    console.log('Drag/drop debug: draggableId:', draggableId, 'decisions:', decisions);

    const decisionId = draggableId as Id<'decisions'>;
    const destColIdx = sortedColumns.findIndex(col => col.name === destination.droppableId);
    const destCol = sortedColumns[destColIdx];
    const columnStatusOptions = destCol ? destCol.statuses : [];

    // Try to get the decision from the arrays first
    let draggedDecision = optimisticDecisions.find(d => d._id === decisionId) || decisions.find(d => d._id === decisionId);
    
    // If decision not found in arrays, try to get category ID from data attribute
    let categoryId: Id<'decision_categories'> | undefined;
    if (!draggedDecision) {
      console.log('  Decision not found in arrays - trying to get category ID from data attribute');
      // Find the draggable element by ID
      const draggableElement = document.querySelector(`[data-id="${decisionId}"]`);
      if (draggableElement) {
        const dataCategoryId = draggableElement.getAttribute('data-category-id');
        console.log('  Found data-category-id:', dataCategoryId);
        if (dataCategoryId) {
          categoryId = dataCategoryId as Id<'decision_categories'>;
        }
      } else {
        console.error('  Draggable element not found in DOM');
      }
    } else if (draggedDecision.decision_category_id) {
      categoryId = draggedDecision.decision_category_id as Id<'decision_categories'>;
    }
    
    // Get the decision category for this decision
    let decisionCategoryStatuses: string[] = [];
    let categoryDoc: Doc<'decision_categories'> | undefined;
    
    // If we have a category ID, try to get the category from allCategoryConfigs
    if (categoryId && allCategoryConfigs) {
      categoryDoc = allCategoryConfigs.get(categoryId);
      console.log('  Looking up category by ID:', categoryId, 'Found:', !!categoryDoc);
    } else if (draggedDecision) {
      // Try to get from decisionCategoriesForCards map
      if (decisionCategoriesForCards) {
        categoryDoc = decisionCategoriesForCards.get(draggedDecision._id);
      }
    }
    
    // If we found a category, use its statuses
    if (categoryDoc && Array.isArray(categoryDoc.statuses)) {
      decisionCategoryStatuses = categoryDoc.statuses.map(s => s.value);
    }

    // Debug logs
     
    console.log('Drag/drop debug:');
     
    console.log('  Dragged decision:', draggedDecision);
     
    console.log('  Kanban column statuses:', columnStatusOptions);
     
    // Get the category doc for logging
    const categoryDocForLogging = draggedDecision && draggedDecision.decision_category_id ? 
      allCategoryConfigs?.get(draggedDecision.decision_category_id as Id<'decision_categories'>) : undefined;
    
    console.log('  Decision category:', categoryDocForLogging);
     
    console.log('  Decision category statuses:', decisionCategoryStatuses);

    // Filter to get the intersection of both arrays
    const validStatusOptions = columnStatusOptions.filter(
      status => decisionCategoryStatuses.includes(status)
    );
     
    console.log('  Valid status options (intersection):', validStatusOptions);

    // If only one valid status option, update directly
    if (destination.droppableId === 'draft' || validStatusOptions.length === 1) {
      const newStatus = destination.droppableId === 'draft' ? 'draft' : validStatusOptions[0];
      handleStatusUpdate(decisionId, newStatus);
    } else {
      // Multiple valid status options - show popover at drop location and optimistically move card
      // Find the original status
      const originalStatus = (optimisticDecisions.find(d => d._id === decisionId)?.status) ||
        (decisions.find(d => d._id === decisionId)?.status) || '';
      // Optimistically move the card to the destination column (using the first status as a placeholder)
      const tempStatus = validStatusOptions[0] || destination.droppableId;
      setOptimisticDecisions(prev =>
        prev.map(d =>
          d._id === decisionId ? { ...d, status: tempStatus } : d
        )
      );
      setPendingStatusUpdate({ id: decisionId, originalStatus });

      // Try to find the destination column DOM node
      let position = { top: 100, left: 100 };
      const destColumnNode = document.querySelector(`[data-kanban-column-id="${destination.droppableId}"]`);
      if (destColumnNode) {
        const rect = destColumnNode.getBoundingClientRect();
        // Place popover at the vertical center of the column, horizontally centered
        position = {
          top: rect.top + rect.height / 2 - 100,
          left: rect.left + rect.width / 2 - 100,
        };
        // Clamp to viewport
        if (position.left < 10) position.left = 10;
        if (position.left + 200 > window.innerWidth) position.left = window.innerWidth - 210;
        if (position.top < 10) position.top = 10;
        if (position.top + 300 > window.innerHeight) position.top = window.innerHeight - 310;
      }
      const destinationStatuses = validStatusOptions;
      activeDecisionRef.current = {
        id: decisionId,
        destinationColumn: destination.droppableId,
        destinationStatuses,
        position
      };
      popoverOpenRef.current = true;
      setPopoverOpen(true);
    }
  }, [sortedColumns, handleStatusUpdate, statuses]);

  /**
   * Store refs to draggable items
   */
  const setDraggableRef = useCallback((id: string, element: HTMLElement | null) => {
    draggableRefs.current[id] = element;
  }, []);

  /**
   * Handle column status filter change
   */
  const handleColumnStatusFilterChange = useCallback((colIdx: number, status: string, checked: boolean) => {
    setColumnStatusFilters(prev => {
      const current = prev[colIdx] || sortedColumns[colIdx].statuses;
      const next = checked 
        ? [...current, status]
        : current.filter(s => s !== status);
      return { ...prev, [colIdx]: next };
    });
  }, [sortedColumns]);

  /**
   * Get decisions for a specific column with applied filters
   */
  const getDecisionsForColumn = useCallback((col: { statuses: string[] }, colIdx: number) => {
    const allowedStatuses = columnStatusFilters[colIdx] || col.statuses;
    return optimisticDecisions
      .filter((d) => allowedStatuses.includes(d.status))
      .sort((a, b) => {
        const aTime = a.updated_at ?? a._creationTime ?? 0;
        const bTime = b.updated_at ?? b._creationTime ?? 0;
        return bTime - aTime;
      });
  }, [optimisticDecisions, columnStatusFilters]);

  return (
    // Main Kanban container: take full viewport height minus 250px, prevent clipping
    <div className="container mx-auto py-1 min-h-[calc(100vh-250px)] h-[calc(100vh-250px)] overflow-visible">
      <DragDropContext onDragEnd={handleDragEnd}>
        {/*
          Kanban grid:
          - Fill all available vertical space
          - Prevent horizontal and vertical clipping
          - Columns will also fill height and not clip overlays/popovers
        */}
        <div className="grid grid-flow-col auto-cols-[minmax(250px,1fr)] gap-6 overflow-visible h-full">
          {sortedColumns.map((col, colIdx) => (
            <div key={col.name} data-kanban-column-id={col.name} className="h-full">
              <KanbanColumn
                id={col.name}
                title={col.name}
                decisions={getDecisionsForColumn(col, colIdx)}
                isDropDisabled={popoverOpen}
                statusFilter={columnStatusFilters[colIdx] || col.statuses}
                onStatusFilterChange={(status, checked) =>
                  handleColumnStatusFilterChange(colIdx, status, checked)
                }
                statusOptions={col.statuses}
                setDraggableRef={setDraggableRef}
                statusDisplayNames={STATUS_DISPLAY_NAMES}
                badgeDetailsMap={badgeDetailsMap}
                tagData={tagData}
              />
            </div>
          ))}
        </div>
      </DragDropContext>
      
      {/* Status selection popover */}
      <MorphingPopover open={popoverOpen} onOpenChange={(open) => {
        if (!open && pendingStatusUpdate) {
          // User cancelled: revert the optimistic update
          setOptimisticDecisions(prev =>
            prev.map(d =>
              d._id === pendingStatusUpdate.id ? { ...d, status: pendingStatusUpdate.originalStatus } : d
            )
          );
          setPendingStatusUpdate(null);
        }
        setPopoverOpen(open);
      }}>
        {popoverOpen && activeDecisionRef.current?.position && (
          <MorphingPopoverContent
            className="w-64"
            top={activeDecisionRef.current.position.top}
            left={activeDecisionRef.current.position.left}
          >
            <div className="space-y-4">
              <h3 className="font-medium">Select Status</h3>
              <div className="flex flex-col gap-2">
                {(() => {
                  if (!activeDecisionRef.current) return null;
                  
                  const statusOptions = activeDecisionRef.current.destinationStatuses;
                  const validStatuses = statusOptions.filter((status): status is string => 
                    Boolean(status && status.trim() !== '')
                  );
                  
                  return validStatuses.map(status => (
                    <Button
                      key={status}
                      variant="outline"
                      className="justify-start"
                      onClick={() => {
                        activeDecisionRef.current && handleStatusUpdate(activeDecisionRef.current.id, status);
                        setPendingStatusUpdate(null);
                      }}
                    >
                      {STATUS_DISPLAY_NAMES[status] || formatStatusDisplay(status)}
                    </Button>
                  ));
                })()}
              </div>
              <Button
                variant="ghost"
                className="w-full"
                onClick={() => {
                  setPopoverOpen(false);
                  activeDecisionRef.current = null;
                  // Revert optimistic update on cancel
                  if (pendingStatusUpdate) {
                    setOptimisticDecisions(prev =>
                      prev.map(d =>
                        d._id === pendingStatusUpdate.id ? { ...d, status: pendingStatusUpdate.originalStatus } : d
                      )
                    );
                    setPendingStatusUpdate(null);
                  }
                }}
              >
                Cancel
              </Button>
            </div>
          </MorphingPopoverContent>
        )}
      </MorphingPopover>
    </div>
  );
};

interface KanbanColumnProps {
  id: string;
  title: string;
  decisions: Doc<'decisions'>[]; 
  isDropDisabled: boolean;
  statusFilter?: string[];
  onStatusFilterChange?: (status: string, checked: boolean) => void;
  statusOptions?: string[];
  setDraggableRef: (id: string, element: HTMLElement | null) => void;
  statusDisplayNames: Record<string, string>;
  badgeDetailsMap?: Map<Id<'badges'>, Doc<'badges'>>;
  tagData?: Record<string, Doc<'tags'>[]>;
}

/**
 * KanbanColumn Component
 *
 * Represents a column in the Kanban board.
 * Displays a list of decisions and allows for filtering by status.
 */
const KanbanColumn: React.FC<KanbanColumnProps> = ({
  id,
  title,
  decisions,
  isDropDisabled,
  statusFilter,
  onStatusFilterChange,
  statusOptions,
  setDraggableRef,
  statusDisplayNames,
  badgeDetailsMap,
  tagData,
}) => {
  const router = useRouter();
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Format status for display
  const formatStatus = (status: string): string => {
    return statusDisplayNames[status] || status.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
  };

  return (
    <Card className="flex flex-col min-w-0 w-full h-full overflow-visible">
      <div className="p-4 pb-2 border-b min-w-0 w-full">
        <div className="flex items-center justify-between min-w-0 w-full">
          <h3 className="font-medium min-w-0 truncate">{title}</h3>
          <div className="flex items-center space-x-2">
          <Badge variant="secondary">
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                maximumFractionDigits: 0,
              }).format(
                decisions.reduce((sum, d) => sum + (d.amount ?? 0), 0)
              )}
            </Badge>
            <div className="text-sm text-muted-foreground">{decisions.length}</div>
       
          </div>
        </div>
        
        {/* Status filter (only shown when statusOptions exist) */}
        {statusOptions && statusFilter && onStatusFilterChange && (
          <div className="mt-1">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs h-7"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              Filter Status
            </Button>
            
            {isFilterOpen && (
              <div className="mt-2 p-2 border rounded-md bg-background">
                <div className="space-y-2">
                  {statusOptions
                    .filter(status => Boolean(status && status.trim() !== ''))
                    .map(status => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${id}-${status}`}
                          checked={statusFilter.includes(status)}
                          onCheckedChange={(checked) =>
                            onStatusFilterChange(status, checked === true)
                          }
                        />
                        <Label htmlFor={`${id}-${status}`} className="text-xs">
                          {formatStatus(status)}
                        </Label>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      <Droppable
        droppableId={id}
        isDropDisabled={isDropDisabled}
        renderClone={(provided, snapshot, rubric) => {
          // Store the draggable ID for debugging
          const draggableId = rubric.draggableId as Id<'decisions'>;
          console.log('renderClone called with draggableId:', draggableId);
          
          // Try to find the decision in the decisions array
          const decision = decisions[rubric.source.index];
          
          // If decision is still undefined, create a placeholder
          if (!decision) {
            console.error('Decision not found in renderClone for ID:', draggableId);
            // Return a simple placeholder div
            return (
              <div
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                style={{
                  ...provided.draggableProps.style,
                  zIndex: 50,
                  padding: '1rem',
                  background: 'white',
                  border: '1px solid #ccc',
                  borderRadius: '0.5rem',
                }}
              >
                Loading decision...
              </div>
            );
          }
          
          // Normal rendering with found decision
          return (
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              style={{
                ...provided.draggableProps.style,
                zIndex: 50,
              }}
              data-id={decision._id}
              data-category-id={decision.decision_category_id || ''}
            >
              <DecisionsCard
                decision={decision}
                badges={
                  (decision.badges as Id<'badges'>[] | undefined)
                    ?.map(badgeId => badgeDetailsMap?.get(badgeId))
                    .filter(Boolean) as Doc<'badges'>[] | undefined
                }
                tags={tagData?.[decision._id]}
                onClick={() => router.push(`/decisions/${decision._id}`)}
              />
            </div>
          );
        }}
      >
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`flex-1 p-2 overflow-visible flex flex-col min-w-0 w-full h-0 ${
              snapshot.isDraggingOver ? 'bg-muted/50' : ''
            }`}
            style={{ minHeight: 0 }}
          >
            {/*
              Use overflow-visible here to allow tooltips, popovers, and overlays from DecisionsCard
              to render outside the Kanban column. If you need vertical scrolling, use a nested scroll container.
             */}
            {decisions.length === 0 ? (
              <div className="h-full flex items-center justify-center text-sm text-muted-foreground">
                No decisions
              </div>
            ) : (
              <div className="space-y-3 min-w-0 w-full">
                {decisions.map((decision, index) => (
                  <Draggable
                    key={decision._id}
                    draggableId={decision._id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={(el) => {
                          setDraggableRef(decision._id, el);
                          provided.innerRef(el);
                        }}
                        data-id={decision._id}
                        data-category-id={decision.decision_category_id || ''}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={`${snapshot.isDragging ? 'opacity-70' : ''} min-w-0 w-full`}
                        onClick={(e) => {
                          if (!snapshot.isDragging) {
                            e.preventDefault();
                            e.stopPropagation();
                            router.push(`/decisions/${decision._id}`);
                          }
                        }}
                      >
                        <DecisionsCard
                          decision={decision}
                          badges={
                            (decision.badges as Id<'badges'>[] | undefined)
                              ?.map(badgeId => badgeDetailsMap?.get(badgeId))
                              .filter(Boolean) as Doc<'badges'>[] | undefined
                          }
                          tags={tagData?.[decision._id]}
                          onClick={() => router.push(`/decisions/${decision._id}`)}
                        />
                      </div>
                    )}
                  </Draggable>
                ))}
              </div>
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </Card>
  );
};

export default KanbanView;
