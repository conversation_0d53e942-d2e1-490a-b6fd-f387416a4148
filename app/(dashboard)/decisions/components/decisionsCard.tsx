'use client';

import React, { useState } from 'react';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
// Radix UI tooltips are portalled by default - no need for manual Portal
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { Doc, Id } from '@/convex/_generated/dataModel';
import * as Icons from '@/components/icons';
import { motion, useTransform, AnimatePresence, useMotionValue, useSpring } from 'motion/react';
import { cn } from '@/lib/utils';

// Define status color mapping
const statusColorMap: Record<string, string> = {
  draft: 'bg-gray-100 text-gray-800 border-gray-300',
  in_review: 'bg-yellow-100 text-yellow-800 border-yellow-300',
  approved: 'bg-green-100 text-green-800 border-green-300',
  rejected: 'bg-red-100 text-red-800 border-red-300',
  escalated: 'bg-orange-100 text-orange-800 border-orange-300',
  cancelled: 'bg-purple-100 text-purple-800 border-purple-300',
  on_hold: 'bg-blue-100 text-blue-800 border-blue-300'
};

// Define status display names
const statusDisplayNames: Record<string, string> = {
  draft: 'Draft',
  in_review: 'In Review',
  approved: 'Approved',
  rejected: 'Rejected',
  escalated: 'Escalated',
  cancelled: 'Cancelled',
  on_hold: 'On Hold'
};

interface DecisionsCardProps {
  decision: Doc<'decisions'>;
  badges?: Doc<'badges'>[];
  tags?: Doc<'tags'>[];
  onClick?: () => void;
}

// Icon map to associate string names with Lucide icon components
const iconMap: Record<string, React.ComponentType<any>> = {
  thumbsUp: Icons.ThumbsUp,
  checkCircle: Icons.CheckCircle,
  xCircle: Icons.XCircle,
  clock: Icons.Clock,
  userIcon: Icons.UsersIcon,
  // Add more icons as needed
};

/**
 * DecisionsCard Component
 *
 * Compact card displaying decision information.
 * Optimized for vertical space efficiency.
 * Shows title, status, short description, badges, tags, assignee, and last update time.
 */
const DecisionsCard: React.FC<DecisionsCardProps> = ({ decision, badges, tags, onClick }) => {
  // Spring animation config
  const springConfig = { stiffness: 100, damping: 5 };
  const x = useMotionValue(0);
  const rotate = useSpring(
    useTransform(x, [-100, 100], [-45, 45]),
    springConfig
  );
  const translateX = useSpring(
    useTransform(x, [-100, 100], [-50, 50]),
    springConfig
  );

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const halfWidth = event.currentTarget.offsetWidth / 2;
    x.set(event.nativeEvent.offsetX - halfWidth);
  };

  // Format dates for display
  const formattedDueDate = decision.due_date
    ? formatDistanceToNow(new Date(decision.due_date), { addSuffix: true })
    : 'No due date';

  // Format updated time
  const formattedUpdateTime = decision.updated_at
    ? formatDistanceToNow(new Date(decision.updated_at), { addSuffix: true, includeSeconds: false }).replace('about ', '')
    : formatDistanceToNow(new Date(decision._creationTime), { addSuffix: true, includeSeconds: false }).replace('about ', '');

  // Get status color class
  const statusColorClass = decision.status
    ? statusColorMap[decision.status]
    : 'bg-gray-100 text-gray-800 border-gray-300';

  const router = useRouter();

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.push(`/decisions/${decision._id}`);
    }
  };

  // Define accent colors based on status, using CSS variables from globals.css
  const getAccentColor = (status: string | undefined) => {
    switch (status) {
      case 'draft':
        return 'hsl(var(--chart-10) / 0.4)';
      case 'in_review':
        return 'hsl(var(--chart-4) / 0.4)';
      case 'approved':
        return 'hsl(var(--chart-9) / 0.5)';
      case 'rejected':
        return 'hsl(var(--chart-3) / 0.4)';
      case 'escalated':
        return 'hsl(var(--chart-6) / 0.4)';
      case 'cancelled':
        return 'hsl(var(--chart-5) / 0.4)';
      case 'on_hold':
        return 'hsl(var(--chart-2) / 0.4)';
      default:
        return 'hsl(var(--chart-10) / 0.4)';
    }
  };

  // Define accent colors based on status for dark mode, using CSS variables from globals.css
  const getAccentColorDark = (status: string | undefined) => {
    switch (status) {
      case 'draft':
        return 'hsl(var(--chart-10) / 0.7)';
      case 'in_review':
        return 'hsl(var(--chart-4) / 0.7)';
      case 'approved':
        return 'hsl(var(--chart-9) / 0.8)';
      case 'rejected':
        return 'hsl(var(--chart-3) / 0.7)';
      case 'escalated':
        return 'hsl(var(--chart-6) / 0.7)';
      case 'cancelled':
        return 'hsl(var(--chart-5) / 0.7)';
      case 'on_hold':
        return 'hsl(var(--chart-2) / 0.7)';
      default:
        return 'hsl(var(--chart-10) / 0.7)';
    }
  };

  const accentColor = getAccentColor(decision.status);
  const accentColorDark = getAccentColorDark(decision.status);

  // Get status display name
  const statusName = decision.status
    ? statusDisplayNames[decision.status]
    : 'Unknown Status';

  // Card root previously used overflow-hidden, which can obscure tooltips/popovers.
  // We use overflow-visible to ensure tooltips and popovers are not clipped by the card.
  // This preserves rounded corners and modern look, but allows interactive overlays to display correctly.
  return (
    <Card
      className="transition-all hover:shadow-md cursor-pointer rounded-lg overflow-hidden border border-gray-200 relative min-w-0 w-full" // overflow-hidden ensures accent bar and content are clipped to card's rounded corners
      onClick={handleCardClick}
      onMouseMove={handleMouseMove}
      style={{ width: '100%', minWidth: 0, maxWidth: '100%' }}
    >
      {/* Accent bar with tooltip showing status */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className="absolute left-0 top-0 bottom-0 w-2 hover:bg-opacity-80 cursor-pointer"
              style={{
                backgroundColor: `var(--tw-content-bg, ${accentColor})`
              }}
              aria-label={`Status: ${statusName}`}
            />
          </TooltipTrigger>
          {/*
             Radix UI tooltips are portalled by default - no need for manual Portal wrapping.
             The tooltip content will automatically render outside any overflow containers.
           */}
          <TooltipContent side="right" className="select-none z-[9999]">
            <span className="font-semibold">{statusName}</span>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* 
        * CardContent previously used overflow-hidden, which can obscure tooltips/popovers.
        * We remove overflow-hidden here to allow tooltips to render outside the card.
        * If you need to scope overflow for internal elements, do it on a nested container, not the CardContent root.
        */}
      <CardContent className="p-3 pl-4 min-w-0 w-full">

        <div className="flex flex-col space-y-1 min-w-0 w-full">
          {/* Header: Full width with smaller font */}
          <div className="w-full pb-0.5 flex items-start justify-between min-w-0">
            {/* Title with tooltip for full display on hover */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <h3 className="text-sm font-medium flex-1 min-w-0 truncate cursor-pointer">
                    {decision.title || decision.name}
                  </h3>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs break-words z-[9999]">
                  <span className="font-semibold">
                    {decision.title || decision.name}
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {/* Priority Chip */}
            {decision.priority && (
              <span
                className={
                  "ml-2 px-2 py-0.5 rounded-full text-xs font-semibold " +
                  (decision.priority === "high"
                    ? "bg-red-100 text-red-700"
                    : decision.priority === "medium"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-gray-100 text-gray-700")
                }
                title={`Priority: ${decision.priority.charAt(0).toUpperCase() + decision.priority.slice(1)}`}
              >
                {decision.priority.charAt(0).toUpperCase() + decision.priority.slice(1)}
              </span>
            )}
          </div>

          {/* Short description directly below heading */}
          {decision.short_description && (
            <p className="text-xs text-muted-foreground line-clamp-1 py-0 mt-0.5">
              {decision.short_description}
            </p>
          )}

          {/* Amount + Badges + Tags section */}
          {((badges && badges.length > 0) || (tags && tags.length > 0)) && (
            <div className="flex flex-row flex-wrap items-center gap-2 py-1 w-full min-w-0">
              {/* Amount Chip */}
              {typeof decision.amount === "number" && !isNaN(decision.amount) && (
                <span
                  className={
                    "px-2 py-0.5 rounded-full text-xs font-semibold flex-shrink-0 bg-primary/30"
                  }
                  title={`Amount: $${Math.round(decision.amount).toLocaleString()}`}
                >
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "USD",
                    maximumFractionDigits: 0,
                  }).format(decision.amount)}
                </span>
              )}
              {/* Badges */}
              {badges && badges.length > 0 && badges.map((badge) => (
                    <Badge
                      key={badge._id}
                      variant="secondary"
                      className="text-xs font-light py-0 h-5"
                      style={{
                        backgroundColor: badge.color ? `${badge.color}0D` : undefined,
                        color: badge.color,
                        borderColor: badge.color ? `${badge.color}0D` : undefined
                      }}
                    >
                      {badge.icon ? (
                        iconMap[badge.icon] ? (
                          React.createElement(iconMap[badge.icon], {
                            className: 'mr-1 h-3 w-3'
                          })
                        ) : (
                          <Icons.HelpCircle className="mr-1 h-3 w-3" />
                        )
                      ) : null}
                      {badge.name}
                    </Badge>
                  ))
              }
              {/* Tags */}
              {tags && tags.length > 0 && tags.map((tag) => {
                const tagName = tag.name;
                const tagColor = tag.color || '#cccccc';

                return (
                  <Badge
                    key={tag._id}
                    variant="secondary"
                    className="text-xs font-light py-0 h-5"
                    style={{
                      backgroundColor: `${tagColor}0D`,
                      color: tagColor,
                      borderColor: `${tagColor}33`
                    }}
                  >
                    {tag.icon && iconMap[tag.icon] ? (
                      React.createElement(iconMap[tag.icon], { className: 'mr-1 h-3 w-3' })
                    ) : null}
                    {tagName}
                  </Badge>
                );
              })}
            </div>
          )}

          {/* Bottom row: Icons for assignee, due date, and last updated */}
          <div className="flex justify-between items-center text-xs pt-1">
            <div className="flex items-center gap-3">
              {/* Assignee - just icon */}
              <div
                className="flex items-center"
                title={decision.assigned_to && decision.assigned_to.length > 0 ? 'Assigned' : 'Unassigned'}
              >
                <Icons.UsersIcon
                  className={cn(
                    'h-4 w-4',
                    decision.assigned_to && decision.assigned_to.length > 0 ? 'text-blue-500' : 'text-gray-400'
                  )}
                />
              </div>

              {/* Due date - more compact, prevent wrapping */}
              <div className="flex items-center max-w-[90px] truncate" title={`Due: ${formattedDueDate}`}> 
                <span className="text-muted-foreground mr-1">Due</span>
                <span className="font-medium truncate">{formattedDueDate.replace(' ago', '').replace('in ', '')}</span>
              </div>
            </div>

            {/* Last updated - just icon and time, prevent wrapping */}
            <div
              className="flex items-center text-muted-foreground max-w-[90px] truncate"
              title={`Updated: ${formattedUpdateTime}`}
            >
              <Icons.Clock className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="truncate">{formattedUpdateTime.replace(' ago', '').replace('in ', '')}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DecisionsCard;
