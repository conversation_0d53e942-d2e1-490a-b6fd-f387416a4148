'use client';
import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import CollectionCardModal from "@/components/ui/collectionCardModal";
import { useToast } from '@/components/hooks/use-toast';
import { Spinner } from '@/components/ui/spinner';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';

// Define the structure for a Kanban column
interface KanbanColumn {
  name: string;
  order: number;
  statuses: string[];
}

// Define the structure for a Status
interface Status {
  value: string;
  label?: string;
}

// Define the structure for a Decision Category
interface DecisionCategory {
  _id: Id<string>; // Use Id<string> for broader compatibility
  name?: string;
  immutable_slug?: string;
  kanban_columns?: KanbanColumn[];
  statuses?: Status[];
  // Add _creationTime if needed for sorting or keys, though likely not needed here
}

interface EditKanbanModalProps {
  open: boolean;
  onClose: () => void;
  categories: DecisionCategory[];
  categoryFilter: "All" | "General" | "Investment"; // Add categoryFilter prop
}

// Helper function to get all unique statuses from categories
const getAllStatuses = (categories: DecisionCategory[]): Status[] => {
  const statusMap = new Map<string, Status>();
  categories.forEach(category => {
    if (category.statuses && Array.isArray(category.statuses)) {
      category.statuses.forEach(status => {
        if (!statusMap.has(status.value)) {
          statusMap.set(status.value, status);
        }
      });
    }
  });
  return Array.from(statusMap.values());
};

// Color palette for category badges (glassmorphic, pastel, semi-transparent)
const CATEGORY_COLORS = [
  // You can adjust or expand this palette as needed
  'bg-gradient-to-r from-blue-200/60 to-blue-100/60',
  'bg-gradient-to-r from-pink-200/60 to-pink-100/60',
  'bg-gradient-to-r from-green-200/60 to-green-100/60',
  'bg-gradient-to-r from-yellow-200/60 to-yellow-100/60',
  'bg-gradient-to-r from-purple-200/60 to-purple-100/60',
  'bg-gradient-to-r from-orange-200/60 to-orange-100/60',
  'bg-gradient-to-r from-teal-200/60 to-teal-100/60',
  'bg-gradient-to-r from-indigo-200/60 to-indigo-100/60',
];

/**
 * Returns a color class for a given category name or id.
 * Uses the index in the categories array for deterministic mapping.
 */
function getCategoryColorClass(catName: string, categories: DecisionCategory[]): string {
  const idx = categories.findIndex(c => c.name === catName);
  // Fallback to gray if not found
  if (idx === -1) return 'bg-gray-200/60';
  return CATEGORY_COLORS[idx % CATEGORY_COLORS.length];
}

export default function EditKanbanModal({ open, onClose, categories, categoryFilter }: EditKanbanModalProps) {
  const { toast } = useToast();
  const updateKanbanColumnsMutation = useMutation(api.decisions.updateKanbanColumns);

  // Find the appropriate category based on the filter
  const categoryToEdit = categoryFilter === "All"
    ? categories?.find((cat) => cat.immutable_slug === "all-decisions-kanban")
    : categoryFilter === "General"
      ? categories?.find((cat) => cat.name?.toLowerCase() === "general")
      : categories?.find((cat) => cat.name?.toLowerCase() === "investment");

  // State for columns and available statuses
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [availableStatuses, setAvailableStatuses] = useState<Status[]>([]);
  const [editingColumnId, setEditingColumnId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  // State for which column's 3-dot menu is open
  const [columnMenuOpenId, setColumnMenuOpenId] = useState<number | null>(null);

  // Initialize columns and available statuses
  useEffect(() => {
    const allStatuses = getAllStatuses(categories);
    let initialColumns: KanbanColumn[] = [];

    if (categoryToEdit?.kanban_columns && Array.isArray(categoryToEdit.kanban_columns)) {
      initialColumns = [...categoryToEdit.kanban_columns].sort((a, b) => a.order - b.order);
    } else {
      // Default columns if none exist
      initialColumns = [
        { name: "Draft", order: 0, statuses: [] },
        { name: "In Progress", order: 1, statuses: [] },
        { name: "Concluded", order: 2, statuses: [] }
      ];
    }
    setColumns(initialColumns);

    // Determine available statuses
    const assignedStatusValues = new Set(initialColumns.flatMap(col => col.statuses));
    const unassigned = allStatuses.filter(status => !assignedStatusValues.has(status.value));
    setAvailableStatuses(unassigned);

  }, [categoryToEdit, categories]);

  // Editable column title logic
  const handleEditColumn = (index: number) => setEditingColumnId(index);

  const handleColumnTitleChange = (index: number, value: string) => {
    setColumns(cols => cols.map((col, idx) =>
      idx === index ? { ...col, name: value } : col
    ));
  };

  const handleColumnTitleBlur = async () => {
    if (editingColumnId !== null && categoryToEdit) {
      try {
        setIsLoading(true);
        await updateKanbanColumnsMutation({
          categoryId: categoryToEdit._id as Id<'decision_categories'>, // Cast to correct type
          kanbanColumns: columns
        });
        toast({
          title: "Column updated",
          description: "Kanban column name has been updated successfully."
        });
      } catch (error) {
        console.error("Error updating column name:", error);
        toast({
          title: "Error",
          description: "Failed to update column name.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
        setEditingColumnId(null); // Ensure editing mode is exited
      }
    }
  };

  // Add new column
  const handleAddColumn = async () => {
    if (!categoryToEdit) return;

    // Create new column with next order value
    const newColumn: KanbanColumn = {
      name: "New Column",
      order: columns.length,
      statuses: []
    };

    const updatedColumns = [...columns, newColumn];

    try {
      setIsLoading(true);
      setColumns(updatedColumns); // Optimistic update

      await updateKanbanColumnsMutation({
        categoryId: categoryToEdit._id as Id<'decision_categories'>, // Cast to correct type
        kanbanColumns: updatedColumns
      });

      toast({
        title: "Column added",
        description: "New kanban column has been added successfully."
      });
    } catch (error) {
      console.error("Error adding column:", error);
      // Revert optimistic update
      setColumns(columns);
      toast({
        title: "Error",
        description: "Failed to add new column.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete column
  const handleDeleteColumn = async (indexToDelete: number) => {
    if (!categoryToEdit) return;

    const columnToDelete = columns[indexToDelete];
    const statusesToReturn = columnToDelete.statuses;

    // Remove column and reorder remaining columns
    const updatedColumns = columns
      .filter((_, idx) => idx !== indexToDelete)
      .map((col, idx) => ({ ...col, order: idx }));

    // Add statuses from deleted column back to available pool
    const statusesToAddBack = getAllStatuses(categories).filter(s => statusesToReturn.includes(s.value));
    const updatedAvailable = [...availableStatuses, ...statusesToAddBack];

    try {
      setIsLoading(true);
      setColumns(updatedColumns); // Optimistic update for columns
      setAvailableStatuses(updatedAvailable); // Optimistic update for available

      await updateKanbanColumnsMutation({
        categoryId: categoryToEdit._id as Id<'decision_categories'>, // Cast to correct type
        kanbanColumns: updatedColumns
      });

      toast({
        title: "Column deleted",
        description: "Kanban column has been deleted successfully."
      });
    } catch (error) {
      console.error("Error deleting column:", error);
      // Revert optimistic updates
      setColumns(columns);
      setAvailableStatuses(availableStatuses);
      toast({
        title: "Error",
        description: "Failed to delete column.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Drag and Drop Handler
  const onDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId, type } = result;

    // Dropped outside any droppable area
    if (!destination) return;

    // Dropped in the same place
    if (source.droppableId === destination.droppableId && source.index === destination.index) return;

    // COLUMN REORDER
    if (type === "COLUMN") {
      let newColumns = Array.from(columns);
      const [removed] = newColumns.splice(source.index, 1);
      newColumns.splice(destination.index, 0, removed);

      // Update order property
      newColumns = newColumns.map((col, idx) => ({ ...col, order: idx }));

      console.log("New kanban column order:", newColumns.map(c => c.name));

      setColumns(newColumns);
      setIsLoading(true);

      try {
        await updateKanbanColumnsMutation({
          categoryId: categoryToEdit!._id as Id<'decision_categories'>,
          kanbanColumns: newColumns
        });
      } catch (error) {
        console.error("Error updating kanban column order:", error);
        toast({
          title: "Save Error",
          description: "Failed to save column order.",
          variant: "destructive"
        });
        setColumns(columns);
      } finally {
        setIsLoading(false);
      }
      return;
    }

    // STATUS REORDER/MOVE
    const sourceDroppableId = source.droppableId;
    const destinationDroppableId = destination.droppableId;
    const statusValue = draggableId; // draggableId is the status value

    let newColumns = [...columns];
    let newAvailableStatuses = [...availableStatuses];
    const allStatusesMap = new Map(getAllStatuses(categories).map(s => [s.value, s]));
    const draggedStatus = allStatusesMap.get(statusValue);

    if (!draggedStatus) return; // Should not happen

    // --- Logic for moving status ---

    // Case 1: Moving from Available Pool
    if (sourceDroppableId === 'available-statuses') {
      // Remove from available
      newAvailableStatuses.splice(source.index, 1);

      // Add to destination column
      const destColIndex = newColumns.findIndex(col => `column-${col.order}` === destinationDroppableId);
      if (destColIndex !== -1) {
        const destCol = newColumns[destColIndex];
        const newStatuses = Array.from(destCol.statuses);
        newStatuses.splice(destination.index, 0, statusValue);
        newColumns[destColIndex] = { ...destCol, statuses: newStatuses };
      }
    }
    // Case 2: Moving to Available Pool
    else if (destinationDroppableId === 'available-statuses') {
      // Remove from source column
      const sourceColIndex = newColumns.findIndex(col => `column-${col.order}` === sourceDroppableId);
      if (sourceColIndex !== -1) {
        const sourceCol = newColumns[sourceColIndex];
        const newStatuses = Array.from(sourceCol.statuses);
        newStatuses.splice(source.index, 1);
        newColumns[sourceColIndex] = { ...sourceCol, statuses: newStatuses };
      }
      // Add to available (maintain some order, e.g., alphabetical by label)
      newAvailableStatuses.splice(destination.index, 0, draggedStatus);
      newAvailableStatuses.sort((a, b) => (a.label || a.value).localeCompare(b.label || b.value));
    }
    // Case 3: Moving between columns or reordering within a column
    else {
      const sourceColIndex = newColumns.findIndex(col => `column-${col.order}` === sourceDroppableId);
      const destColIndex = newColumns.findIndex(col => `column-${col.order}` === destinationDroppableId);

      if (sourceColIndex !== -1 && destColIndex !== -1) {
        const sourceCol = newColumns[sourceColIndex];
        const destCol = newColumns[destColIndex];

        // Remove from source column
        const sourceStatuses = Array.from(sourceCol.statuses);
        sourceStatuses.splice(source.index, 1);
        newColumns[sourceColIndex] = { ...sourceCol, statuses: sourceStatuses };

        // Add to destination column (if different column)
        if (sourceDroppableId === destinationDroppableId) {
           // Reordering within the same column
           sourceStatuses.splice(destination.index, 0, statusValue);
           newColumns[sourceColIndex] = { ...sourceCol, statuses: sourceStatuses }; // Update again with inserted item
        } else {
          // Moving to a different column
          const destStatuses = Array.from(destCol.statuses);
          destStatuses.splice(destination.index, 0, statusValue);
          newColumns[destColIndex] = { ...destCol, statuses: destStatuses };
        }
      }
    }

    // --- Update state optimistically and trigger mutation ---
    setColumns(newColumns);
    setAvailableStatuses(newAvailableStatuses);
    setIsLoading(true); // Indicate loading during save

    try {
      await updateKanbanColumnsMutation({
        categoryId: categoryToEdit!._id as Id<'decision_categories'>, // Use non-null assertion as it should exist
        kanbanColumns: newColumns
      });
      // No success toast needed as it's autosave
    } catch (error) {
      console.error("Error updating kanban columns after drag:", error);
      toast({
        title: "Save Error",
        description: "Failed to save changes after drag.",
        variant: "destructive"
      });
      // Revert state on error
      setColumns(columns);
      setAvailableStatuses(availableStatuses);
    } finally {
      setIsLoading(false);
    }
  };


  // Modal content
  return (
    <CollectionCardModal
      heading="Edit Kanban Board"
      subheading="Customize your kanban board layout"
      onClose={onClose}
      showFooter={true}
      // Lower z-index than DnD drag preview (9999)
      className="max-w-5xl max-h-[90vh] rounded-3xl z-[1050]"
      primaryCTA={{
        text: "View Kanban",
        onClick: onClose
      }}
    >
      <DragDropContext onDragEnd={onDragEnd}>

        {/* Top: Available Statuses Pool, grouped by category */}
        <Droppable
          droppableId="available-statuses"
          direction="horizontal"
          type="STATUS"
          renderClone={(provided, snapshot, rubric) => {
            const status = availableStatuses[rubric.source.index];
            return (
              <div
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                className="px-3 py-1 rounded-full text-xs font-medium cursor-grab bg-blue-200 text-blue-800 shadow-lg"
                style={{
                  ...provided.draggableProps.style,
                  minWidth: 60
                }}
              >
                {status.label || status.value}
              </div>
            );
          }}
        >
          {(provided, snapshot) => {
            // Group available statuses by category
            const categoryMap: Record<string, Status[]> = {};
            availableStatuses.forEach(status => {
              const cat = categories.find(cat =>
                cat.statuses?.some(s => s.value === status.value)
              );
              const catName = cat?.name || 'Uncategorized';
              if (!categoryMap[catName]) categoryMap[catName] = [];
              categoryMap[catName].push(status);
            });
            const categoryNames = Object.keys(categoryMap);

            return (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`mb-6 p-4 border rounded-lg ${snapshot.isDraggingOver ? 'bg-green-50' : 'bg-gray-50'}`}
              >
                <div className="font-medium mb-2">Available Statuses (Drag to columns below)</div>
                <div className="flex flex-col gap-4 min-h-[40px]">
                  {categoryNames.length === 0 && (
                    <div className="text-gray-400 text-xs italic">All statuses assigned</div>
                  )}
                  {categoryNames.map((catName) => (
                    <div key={catName}>
                      <div className="text-xs font-semibold mb-1 text-gray-700">{catName}</div>
                      <div className="flex flex-wrap gap-2">
                        {categoryMap[catName].map((status, index) => {
                          // Find the global index for Draggable
                          const globalIndex = availableStatuses.findIndex(s => s.value === status.value);
                          // Find the color for this status's category
                          const colorClass = getCategoryColorClass(catName, categories);
                          return (
                            <Draggable key={status.value} draggableId={status.value} index={globalIndex}>
                              {(provided, snapshot) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  className={`px-3 py-1 rounded-full text-xs font-medium cursor-grab shadow-lg ${colorClass} text-gray-700`}
                                  style={provided.draggableProps.style}
                                >
                                  {status.label || status.value}
                                </div>
                              )}
                            </Draggable>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                  {provided.placeholder}
                </div>
              </div>
            );
          }}
        </Droppable>

      {/* Kanban Columns Section */}
      <div className="mb-2">
        <div className="font-medium mb-2">Kanban Board Columns</div>
        <p className="text-sm text-gray-500 mb-4">
          Drag statuses between columns or back to the 'Available Statuses' pool. Changes are saved automatically.
        </p>
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-center mb-4">
          <Spinner size="sm" />
        </div>
      )}

      {/* Kanban Columns */}
      <Droppable
        droppableId="kanban-columns"
        direction="horizontal"
        type="COLUMN"
      >
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="flex gap-4 overflow-x-auto pb-4"
          >
            {columns.map((col, colIndex) => (
              <Draggable
                key={`column-${col.order}`}
                draggableId={`column-${col.order}`}
                index={colIndex}
              >
                {(dragProvided, dragSnapshot) => (
                  <div
                    ref={dragProvided.innerRef}
                    {...dragProvided.draggableProps}
                    style={{
                      ...dragProvided.draggableProps.style,
                      minWidth: 280,
                      maxWidth: 340,
                      opacity: dragSnapshot.isDragging ? 0.85 : 1,
                    }}
                  >
                    <div
                      className={`flex flex-col h-full bg-white rounded-lg border border-gray-200 shadow-sm`}
                    >
                      {/* Column Header */}
                      <div
                        className="p-4 border-b flex items-center justify-between cursor-grab"
                        {...dragProvided.dragHandleProps}
                      >
                        {editingColumnId === colIndex ? (
                          <input
                            className="text-base font-semibold bg-white border rounded px-2 py-1 w-full mr-2"
                            value={col.name}
                            onChange={e => handleColumnTitleChange(colIndex, e.target.value)}
                            onBlur={handleColumnTitleBlur}
                            autoFocus
                          />
                        ) : (
                          <span
                            className="text-base font-semibold mr-2 truncate text-left hover:underline focus:outline-none cursor-pointer"
                            onClick={() => handleEditColumn(colIndex)}
                            style={{ minWidth: 0, flex: 1 }}
                          >
                            {col.name}
                          </span>
                        )}
                        <div className="flex items-center flex-shrink-0">
                          {/* 3-dot menu for column actions */}
                          <div className="relative">
                            <button
                              className="p-1 rounded hover:bg-gray-200"
                              type="button"
                              title="Column actions"
                              disabled={isLoading}
                              onClick={() => setColumnMenuOpenId(columnMenuOpenId === colIndex ? null : colIndex)}
                            >
                              <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <circle cx="10" cy="4" r="1.5" fill="currentColor" />
                                <circle cx="10" cy="10" r="1.5" fill="currentColor" />
                                <circle cx="10" cy="16" r="1.5" fill="currentColor" />
                              </svg>
                            </button>
                            {/* Dropdown menu for delete */}
                            {columnMenuOpenId === colIndex && (
                              <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded shadow-lg z-50">
                                <button
                                  className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                  onClick={() => {
                                    setColumnMenuOpenId(null);
                                    handleDeleteColumn(colIndex);
                                  }}
                                  disabled={isLoading}
                                >
                                  Delete column
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      {/* Column Content (Draggable Statuses) */}
                      <Droppable
                        droppableId={`column-${col.order}`}
                        type="STATUS"
                        renderClone={(provided, snapshot, rubric) => {
                          const statusValue = columns[colIndex].statuses[rubric.source.index];
                          const status = getAllStatuses(categories).find(s => s.value === statusValue);
                          if (!status) return null;
                          return (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className="flex items-center justify-between rounded px-2 py-1 cursor-grab bg-blue-200 text-blue-800 shadow-md"
                              style={{
                                ...provided.draggableProps.style,
                                minWidth: 60
                              }}
                            >
                              <span className="text-xs font-medium">
                                {status.label || status.value}
                              </span>
                            </div>
                          );
                        }}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            // Highlight the column when a status is dragged over it
                            className={`flex flex-col gap-2 px-3 py-2 flex-grow overflow-y-auto min-h-[120px] transition-colors duration-150
                              ${snapshot.isDraggingOver ? 'bg-blue-50/60 border-2 border-blue-300 shadow-lg' : 'bg-white border'}
                              rounded-b-lg`}
                          >
                            {col.statuses.map((statusValue, statusIndex) => {
                              const status = getAllStatuses(categories).find(s => s.value === statusValue);
                              if (!status) return null;
                              // Find the category for this status
                              const cat = categories.find(cat =>
                                cat.statuses?.some(s => s.value === status.value)
                              );
                              return (
                                <Draggable key={status.value} draggableId={status.value} index={statusIndex}>
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`flex items-center gap-2 justify-between rounded px-2 py-1 cursor-grab ${
                                        snapshot.isDragging ? 'bg-blue-200 text-blue-800 shadow-md' : 'bg-white border'
                                      }`}
                                      style={provided.draggableProps.style}
                                    >
                                      <span className="text-xs font-medium">
                                        {status.label || status.value}
                                      </span>
                                      {/* Chip for category only */}
                                      {cat && (
                                        <span
                                          className={`inline-block px-2 py-0.5 rounded-full text-gray-700 text-[10px] font-semibold ml-1 backdrop-blur-md border border-white/30 shadow-sm ${getCategoryColorClass(cat.name || '', categories)}`}
                                          style={{
                                            // Optional: add more glassmorphic effect
                                            // background: 'rgba(255,255,255,0.3)',
                                            // boxShadow: '0 4px 30px rgba(0,0,0,0.05)',
                                          }}
                                        >
                                          {cat.name}
                                        </span>
                                      )}
                                    </div>
                                  )}
                                </Draggable>
                              );
                            })}
                            {provided.placeholder}
                            {col.statuses.length === 0 && !snapshot.isDraggingOver && (
                              <div className="text-gray-400 text-xs text-center py-6 italic">
                                Drag statuses here
                              </div>
                            )}
                          </div>
                        )}
                      </Droppable>
                    </div>
                  </div>
                )}
              </Draggable>
            ))}
            {/* Add Column Button */}
            <button
              className="flex flex-col items-center justify-center min-w-[60px] h-[200px] border-2 border-dashed border-gray-300 rounded-lg text-gray-400 hover:bg-gray-50 flex-shrink-0"
              onClick={handleAddColumn}
              type="button"
              title="Add column"
              disabled={isLoading}
            >
              <span className="text-2xl font-bold">+</span>
            </button>
            {provided.placeholder}
          </div>
        )}
      </Droppable>
      </DragDropContext>
    </CollectionCardModal>
  );
}
