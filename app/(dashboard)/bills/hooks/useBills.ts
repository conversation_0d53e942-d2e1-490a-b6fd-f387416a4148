'use client';

import { useMemo, useCallback } from 'react';
import { Id } from '@/convex/_generated/dataModel';
import { BillStatus, BillType, UIBill, PriorityBill } from '@/zod/bills-schema';
import { useToast } from '@/components/hooks/use-toast';
import { useMutation, useQuery, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';

export function useBills() {
  const { toast } = useToast();

  // =============================================
  // Data Fetching (consolidated in one place)
  // =============================================
  const billList =
    useQuery(api.bills.listBills, {
      filters: { sortBy: 'billDate', query: 'desc' },
    }) || [];

  const lineItems =
    useQuery(api.lineItems.listLineItems, {
      filter: {
        sortBy: 'date',
        sortDirection: 'desc',
      },
    }) || [];

  const organizations =
    useQuery(api.directory.directoryOrganizations.listOrganizations, {
      pagination: {
        limit: 100,
        sortBy: 'name',
        sortDirection: 'asc',
      },
      filter: {},
    })?.organizations || [];

  // =============================================
  // Mutations & Actions
  // =============================================
  const deleteBillsMutation = useMutation(api.bills.deleteBills);
  const updateBillMutation = useMutation(api.bills.update);
  const createBillsFromBillCom = useMutation(api.bills.createBillsFromBillCom);
  const getBillComConfigData = useQuery(api.integrations.billCom.getBillComConfig);
  const billComLogin = useAction(api.integrations.billComActions.billComLogin);
  const getBills = useAction(api.integrations.billComActions.getBills);

  // =============================================
  // Utility Functions
  // =============================================
  // Helper function for Bill status
  const determineBillStatus = useCallback((bill: {
    dueDate?: number;
    billDate?: number;
    type: BillType;
    billStatus?: BillStatus;
  }): BillStatus => {
    const dueDateMs = bill.dueDate || bill.billDate;
    const isOverdue =
      !!dueDateMs && dueDateMs < Date.now() && bill.type === 'BILL';

    if (bill.billStatus) {
      return bill.billStatus;
    } else {
      // No billStatus field:
      if (isOverdue) return 'UNPAID'; // Use UNPAID for overdue bills
      if (bill.type === 'CREDIT_CARD') return 'PAID';
      return 'UNDEFINED'; // Use UNDEFINED as default
    }
  }, []);

  // =============================================
  // Data Transformations
  // =============================================
  // Transform bills with proper typing
  const processedBills = useMemo(() => {
    // Handle both paginated and non-paginated responses
    const billsArray = Array.isArray(billList) 
      ? billList 
      : billList?.page || [];

    return billsArray.map((dbBill) => {
      // Get line items for this bill
      const items = Array.isArray(lineItems) ? lineItems : lineItems.page;
      const billLineItems = items.filter(
        (item: any) => item?.bill_id && item.bill_id === dbBill._id
      );

      // Determine vendor
      const vendor = organizations.find(org => org._id === dbBill.vendor_id)?.name || 'Unknown Vendor';
      
      // Determine category
      const uniqueCategories = new Set(
        billLineItems.map((item: any) =>
          item?.spending_category ? 'Categorized' : 'Uncategorized'
        )
      );

      let category: string;
      if (dbBill.type === 'CREDIT_CARD') {
        category = 'Credit Card';
      } else if (!uniqueCategories.size) {
        category = 'Uncategorized';
      } else if (uniqueCategories.size === 1) {
        category = [...uniqueCategories][0] as string;
      } else {
        category = 'Multiple Categories';
      }

      // Determine bill status
      const billStatus = determineBillStatus({
        dueDate: dbBill.dueDate,
        billDate: dbBill.billDate,
        type: dbBill.type as BillType,
        billStatus: dbBill.billStatus
      });

      // Determine if high value
      const isHighValue = dbBill.amount > 2000;

      // Return formatted bill object with correct typing
      const uiBill: UIBill = {
        ...dbBill,
        billStatus,
        vendor,
        category,
        percentageChange: undefined, // Future implementation
        previousAmount: undefined,   // Future implementation
      };
      
      // Store isHighValue for later access
      const billWithHighValue = {
        ...uiBill,
        _isHighValue: isHighValue // Using a different name to prevent type errors
      };
      
      return billWithHighValue;
    });
  }, [billList, lineItems, organizations, determineBillStatus]);

  // Filter priority bills
  const priorityBills = useMemo(() => 
    processedBills
      .filter((bill: any) => { // Use any type to access custom properties
        // Convert to PriorityBill type with needed calculations
        const dueDateMs = bill.dueDate || bill.billDate;
        const dueDate = dueDateMs ? new Date(dueDateMs) : undefined;
        const billDate = bill.billDate ? new Date(bill.billDate) : undefined;
        
        // Priority criteria:
        
        // 1. High value bills
        if (bill._isHighValue) return true;
        
        // 2. Overdue bills (due date is in the past and not paid)
        if (dueDate && dueDate < new Date() && bill.billStatus !== 'PAID') return true;
        
        // 3. Bills due within next 7 days
        if (!dueDate) return false;
        const diffDays = (dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
        return diffDays <= 7 && diffDays >= 0 && bill.billStatus !== 'PAID';
      })
      .map(bill => ({
        _id: bill._id,
        dueDate: bill.dueDate ? new Date(bill.dueDate) : undefined,
        billDate: bill.billDate ? new Date(bill.billDate) : undefined,
        amount: bill.amount,
        vendor: bill.vendor,
        billStatus: bill.billStatus,
        category: bill.category,
        type: bill.type,
        isHighValue: (bill as any)._isHighValue,
        isUnusual: false, // Future implementation
        billNo: bill.billNo,
        memo: bill.memo
      } as PriorityBill)),
    [processedBills]
  );

  // =============================================
  // Operations
  // =============================================
  // Delete bills
  const handleDeleteBills = async (billIds: Id<'bills'>[]) => {
    try {
      await deleteBillsMutation({ delete: { ids: billIds } });
      toast({
        title: 'Success',
        description: `Successfully deleted ${
          billIds.length
        } bill${billIds.length > 1 ? 's' : ''}.`,
      });
      return true;
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to delete bill(s). Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Update bill status
  const handleUpdateBillStatus = async (
    billId: Id<'bills'>,
    status: BillStatus
  ) => {
    try {
      await updateBillMutation({
        updates: {
          id: billId,
          updates: {
            billStatus: status,
          },
        },
      });

      toast({
        title: 'Status Updated',
        description: `Bill status has been updated to ${status}`,
      });
      return true;
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update bill status. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Sync bills from Bill.com
  const syncBills = async () => {
    try {
      // 1. Get Bill.com Config and check if integration is active
      const config = getBillComConfigData;
      
      // Skip sync if config is not found or integration is not active
      if (!config) {
        toast({
          title: 'Configuration Error',
          description: 'Bill.com integration is not properly configured.',
          variant: 'destructive',
        });
        return false;
      }

      // 2. Login to Bill.com
      const loginResponse = await billComLogin({
        devKey: config.devKey,
        username: config.apiUserName,
        password: config.apiPasswordOrToken!,
        organizationId: config.orgId,
        rememberMeId: config.rememberMeId,
        device: config.device,
      });

      if (!loginResponse.success || !loginResponse.data) {
        toast({
          title: 'Authentication Failed',
          description: 'Could not authenticate with Bill.com. Please check your credentials.',
          variant: 'destructive',
        });
        return false;
      }

      // 3. Get Bills
      const billsResponse = await getBills({
        sessionId: loginResponse.data.sessionId,
        devKey: config.devKey,
      });

      if (!billsResponse.success || !billsResponse.data) {
        toast({
          title: 'Failed to Fetch Bills',
          description: 'Could not retrieve bills from Bill.com. Please try again later.',
          variant: 'destructive',
        });
        return false;
      }

      // 4. Get all existing vendors to build a mapping
      // We'll let the server handle the vendor mapping in the createBillsFromBillCom action
      // This avoids the React hook error by moving the query to the server side
      const vendorMap: Record<string, Id<'organizations'>> = {};

      // 5. Save the bills to the database
      const result = await createBillsFromBillCom({
        billComBills: billsResponse.data.results,
        organizationMap: vendorMap
      });

      // 6. Show success only if bills were actually processed
      if (result.createdCount > 0 || result.updatedCount > 0) {
        toast({
          title: 'Bills Synced',
          description: `Created: ${result.createdCount}, Updated: ${result.updatedCount}, Skipped: ${result.skippedCount}`,
        });
      } else if (result.skippedCount > 0) {
        toast({
          title: 'No New Bills',
          description: 'All bills are up to date.',
        });
      }
      
      return true;
    } catch (error) {
      console.error('Error syncing bills:', error);
      toast({
        title: 'Sync Failed',
        description: error instanceof Error ? error.message : 'An unknown error occurred during sync',
        variant: 'destructive',
      });
      return false;
    }
  };

  // =============================================
  // Return
  // =============================================
  return {
    // Data
    bills: processedBills,
    priorityBills,
    isLoading: !billList || !lineItems || !organizations,
    
    // Operations
    operations: {
      deleteBills: handleDeleteBills,
      updateStatus: handleUpdateBillStatus,
      syncBills
    },
    
    // Tab filtering helper
    getFilteredBills: (filter: (bill: UIBill) => boolean) => {
      return processedBills.filter(filter);
    }
  };
}
