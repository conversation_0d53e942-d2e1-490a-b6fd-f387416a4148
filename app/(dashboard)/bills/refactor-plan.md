# Bills Page Refactoring Plan

## Current Issues
- Over-fragmented component structure (8+ files just for the table)
- Complex data transformation logic spread across files
- Heavy main page component handling too many responsibilities
- Duplicate data processing logic
- Over-engineered UI patterns

## Proposed Architecture

### 1. Move Data Logic to Custom Hooks

Create a single `useBills.ts` hook that handles:
- Data fetching (bills, line items, organizations)
- Data transformations and filtering
- Bills operations (status updates, delete, sync)

```tsx
// useBills.ts
export function useBills() {
  // Data fetching
  const bills = useQuery(...)
  const lineItems = useQuery(...)
  const organizations = useQuery(...)
  
  // Data transformations (moved from page component)
  const processedBills = useMemo(...)
  const priorityBills = useMemo(...)
  
  // Operations
  const handleUpdateStatus = async (id, status) => {...}
  const handleDeleteBills = async (ids) => {...}
  const syncBills = async () => {...}
  
  return {
    bills: processedBills,
    priorityBills,
    isLoading: !bills || !lineItems || !organizations,
    operations: {
      updateStatus: handleUpdateStatus,
      deleteBills: handleDeleteBills,
      syncBills
    }
  }
}
```

### 2. Simplify the Table Component

Consolidate the table into 1-2 files instead of 8+ files:

```tsx
// BillsTable.tsx (single file)
export function BillsTable({ bills, onSelectionChange, onEditBill, ...props }) {
  // Table state
  const [sorting, setSorting] = useState(...)
  const [pagination, setPagination] = useState(...)
  
  // Column definitions
  const columns = [...]
  
  // Table instance
  const table = useReactTable({...})
  
  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex items-center justify-between">...</div>
      
      {/* Table */}
      <Table>
        <TableHeader>...</TableHeader>
        <TableBody>...</TableBody>
        <TableFooter>...</TableFooter>
      </Table>
      
      {/* Pagination */}
      <div className="flex items-center justify-between">...</div>
    </div>
  )
}
```

### 3. Simplified Page Component

```tsx
// page.tsx
export default function BillsPage() {
  const { bills, priorityBills, isLoading, operations } = useBills()
  const [selectedBills, setSelectedBills] = useState([])
  const router = useRouter()
  
  if (isLoading) return <LoadingSpinner />
  
  return (
    <div className="space-y-8">
      {/* Priority Bills */}
      {priorityBills.length > 0 && (
        <PriorityBills
          bills={priorityBills}
          onViewDetails={(id) => router.push(`/bills/${id}`)}
          onUpdateStatus={operations.updateStatus}
        />
      )}
      
      {/* Bills Table */}
      <BillsTable
        bills={bills}
        selectedBills={selectedBills}
        onSelectionChange={setSelectedBills}
        onSyncBills={operations.syncBills}
        onDeleteBills={operations.deleteBills}
        onUpdateStatus={operations.updateStatus}
        onEditBill={(id) => router.push(`/bills/${id}`)}
      />
      
      {/* Create Bill Modal */}
      <CreateBillModal
        open={searchParams.get('new') === 'true'}
        onClose={() => router.replace('/bills')}
      />
    </div>
  )
}
```

### 4. Consolidate Bill Detail Page

Similar approach for the bill detail page:
- Create a `useBillDetail.ts` hook
- Simplify the line items table

## Benefits

1. **Reduced file count**: From 15+ files to ~5 files
2. **Separation of concerns**: Data logic in hooks, UI in components
3. **Improved maintainability**: Easier to understand and modify
4. **Better performance**: Fewer components, less prop drilling
5. **More consistent**: Standard patterns throughout the codebase
