import { Control, UseFormWatch, UseFormSetValue, UseFormRegister } from 'react-hook-form';
import { BillInput, BillType } from '@/zod/bills-schema';
import { Doc, Id } from '@/convex/_generated/dataModel';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { EnhancedVendorSelect } from '@/components/enhanced-vendor-select';
import { fromUTCTimestamp, toUTCTimestamp } from '@/lib/date-utils';
import { Card, CardContent } from '@/components/ui/card';
import { OrganizationSchema } from '@/zod/directory-schema';
import { z } from 'zod';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useState } from 'react';

type Organization = z.infer<typeof OrganizationSchema>;

interface BillFormFieldsProps {
  control: Control<BillInput>;
  watch: UseFormWatch<BillInput>;
  setValue: UseFormSetValue<BillInput>;
  register: UseFormRegister<BillInput>;
  suggestedVendor: {
    name: string;
    isNew: boolean;
    confidence: number;
    matchedVendorId?: Id<'organizations'>;
  } | null;
  vendors: Organization[];
  onVendorChange: (organizationId: Id<'organizations'>) => void;
  onVendorSearch: (query: string) => void;
}

export function BillFormFields({
  control,
  watch,
  setValue,
  register,
  suggestedVendor,
  vendors,
  onVendorChange,
  onVendorSearch
}: BillFormFieldsProps) {
  const docType = watch('type');
  const isStatement = docType === 'CREDIT_CARD';
  const dueDate = watch('dueDate') ?? 0;
  const billDate = watch('billDate') ?? 0;

  const handleDateChange = (date: Date | undefined, field: 'billDate' | 'dueDate') => {
    if (date) {
      const local = new Date(date);
      local.setHours(0, 0, 0, 0);
      setValue(field, toUTCTimestamp(local));
    } else {
      setValue(field, 0);
    }
  };

  const formatDate = (timestamp: number | undefined) => {
    if (!timestamp || timestamp <= 0) return 'Pick a date';
    return format(fromUTCTimestamp(timestamp), 'PPP');
  };

  const getSelectedDate = (timestamp: number | undefined) => {
    if (!timestamp || timestamp <= 0) return undefined;
    return fromUTCTimestamp(timestamp);
  };

  return (
    <Card className="relative">
      <CardContent className="space-y-2 p-3">
        {/* Document Type */}
        <fieldset>
          <label htmlFor="type" className="text-sm font-medium block mb-1">
            Document Type*
          </label>
          <Select
            value={docType}
            onValueChange={(val) => setValue('type', val as BillType)}
          >
            <SelectTrigger className="h-7 w-full">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BILL">Invoice</SelectItem>
              <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
            </SelectContent>
          </Select>
        </fieldset>

        {/* Amount or Bill Number based on type */}
        <fieldset>
          <label className="text-sm font-medium block mb-1">
            {isStatement ? 'Total Purchases*' : 'Bill No.'}
          </label>
          {isStatement ? (
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                $
              </span>
              <Input
                type="number"
                step="0.01"
                {...register('amount', { valueAsNumber: true })}
                className="pl-6 h-7"
                placeholder="0.00"
                onBlur={(e) => {
                  const val = parseFloat(e.target.value);
                  if (!isNaN(val)) {
                    e.target.value = val.toLocaleString('en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    });
                  }
                }}
              />
            </div>
          ) : (
            <Input
              {...register('billNo')}
              placeholder="Bill number"
              className="h-7"
            />
          )}
        </fieldset>

        {/* Date Fields */}
        <fieldset>
          <label className="text-sm font-medium block mb-1">
            {isStatement ? 'Due Date' : 'Bill Date'}
          </label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'justify-start text-left font-normal h-7',
                  !(isStatement ? dueDate : billDate) && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-3 w-3" />
                {formatDate(isStatement ? dueDate : billDate)}
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start">
              <Calendar
                mode="single"
                selected={getSelectedDate(isStatement ? dueDate : billDate)}
                onSelect={(date) => handleDateChange(date, isStatement ? 'dueDate' : 'billDate')}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </fieldset>

        {/* Vendor Selection */}
        <fieldset>
          <label className="text-sm font-medium block mb-1">
            Vendor*
          </label>
          <EnhancedVendorSelect
            value={watch('vendor_id')}
            onChange={onVendorChange}
            onSearch={onVendorSearch}
            organizations={vendors}
            suggestedVendor={suggestedVendor}
          />
        </fieldset>

        {/* Invoice-specific fields */}
        {!isStatement && (
          <>
            <fieldset>
              <label className="text-sm font-medium block mb-1">
                Amount*
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  $
                </span>
                <Input
                  type="number"
                  step="0.01"
                  {...register('amount', { valueAsNumber: true })}
                  className="pl-6 h-7"
                  placeholder="0.00"
                  onBlur={(e) => {
                    const val = parseFloat(e.target.value);
                    if (!isNaN(val)) {
                      e.target.value = val.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      });
                    }
                  }}
                />
              </div>
            </fieldset>

            <fieldset>
              <label className="text-sm font-medium block mb-1">Due Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'justify-start text-left font-normal h-7',
                      !dueDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-3 w-3" />
                    {formatDate(dueDate)}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start">
                  <Calendar
                    mode="single"
                    selected={getSelectedDate(dueDate)}
                    onSelect={(date) => handleDateChange(date, 'dueDate')}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </fieldset>
          </>
        )}

        {/* Memo field */}
        <fieldset>
          <label className="text-sm font-medium block mb-1">
            Memo
          </label>
          <Textarea
            {...register('memo')}
            className="resize-none h-20"
            placeholder="Enter any additional notes"
          />
        </fieldset>
      </CardContent>
    </Card>
  );
}