/**
 * CreateBillModal Component
 *
 * A modal for creating new bills/invoices and credit card statements.
 * This component handles:
 * 1. Document upload and preview
 * 2. AI-powered document parsing
 * 3. Form management for bill/statement details
 * 4. Line item management and categorization
 * 5. Vendor selection and creation
 * 6. File storage integration with Convex
 */

'use client';

import {
  useState,
  useEffect,
  useCallback,
  useRef
} from 'react';
import {
  useForm,
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Modal from '@/components/ui/modal';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import {
  Loader2,
  CheckCircle2,
  Bot,
  FileText,
  ListChecks,
  Wand2,
  X,
  CalendarIcon
} from 'lucide-react';

import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useMutation, useQuery, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { CategoryDropdown } from '@/components/category-dropdown';
import { Doc, Id } from '@/convex/_generated/dataModel';
import { EnhancedVendorSelect } from '@/components/enhanced-vendor-select';
import { TagWithChildren } from '@/zod/tags-schema';
import { BillFormFields } from './BillFormFields';
import { BillInput, BillInputSchema } from '@/zod/bills-schema';

// Date helpers
import {
  toUTCTimestamp,
  fromUTCTimestamp,
  parsePreservingDate,
  formatDisplayDate
} from '@/lib/date-utils';

// Document upload area (exists in a separate file; imported here)
import { DocumentUploadArea } from './DocumentUploadArea';

/**
 * CreateBillModalProps
 * Simple props for controlling modal visibility
 */
export interface CreateBillModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => Promise<void>;
}

/**
 * ProcessingStep type
 * Represents the different stages of document processing
 */
type ProcessingStep = 'reading' | 'analyzing' | 'processing' | 'complete';

/**
 * Interface for a single line item. Aligns with what's in our
 * zod schema (minus the optional 'id' used in the UI).
 */
interface LineItem {
  id?: Id<'lineItems'> | string;
  postDate: Date;  // Keep as Date for UI display
  vendor: string;
  description: string;
  amount: number;
  spending_category?: Id<'tags'>;
  merchantType?: string;
}

/**
 * Small sub-component that shows the 3-step progress for reading,
 * AI analyzing, and processing a document. Marked complete once finished.
 */
function ProcessingSteps({ currentStep }: { currentStep: ProcessingStep }) {
  const steps = [
    {
      icon: FileText,
      label: 'Reading document',
      description: 'Converting document to machine-readable format',
      step: 'reading' as ProcessingStep
    },
    {
      icon: Bot,
      label: 'AI Analysis',
      description: 'Extracting data with advanced AI',
      step: 'analyzing' as ProcessingStep
    },
    {
      icon: ListChecks,
      label: 'Processing Data',
      description: 'Organizing extracted information',
      step: 'processing' as ProcessingStep
    }
  ];

  const getStepState = (stepName: ProcessingStep) => {
    const stepOrder = ['reading', 'analyzing', 'processing', 'complete'];
    const currentIndex = stepOrder.indexOf(currentStep);
    const stepIndex = stepOrder.indexOf(stepName);

    if (currentStep === 'complete') return 'complete';
    if (stepIndex < currentIndex) return 'complete';
    if (stepIndex === currentIndex) return 'active';
    return 'pending';
  };

  return (
    <div className="space-y-6 py-2">
      {steps.map((step, index) => {
        const Icon = step.icon;
        const stepState = getStepState(step.step);
        const isActive = stepState === 'active';
        const isComplete = stepState === 'complete';

        return (
          <div key={step.label} className="flex items-start gap-4">
            <div className="relative">
              <div
                className={cn(
                  'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',
                  isActive
                    ? 'bg-gray-900 text-white shadow-lg shadow-gray-900/30'
                    : isComplete
                    ? 'bg-gray-900/20 text-gray-900'
                    : 'bg-gray-200/80 text-gray-500'
                )}
              >
                {isComplete ? (
                  <CheckCircle2 className="w-6 h-6" />
                ) : (
                  <Icon className="w-6 h-6" />
                )}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    'absolute top-10 bottom-0 left-1/2 w-px -translate-x-1/2 transition-all duration-300',
                    isComplete ? 'bg-gray-900/20' : 'bg-gray-200/80'
                  )}
                />
              )}
            </div>
            <div className="space-y-1.5 pt-2">
              <p
                className={cn(
                  'text-sm font-semibold leading-none transition-all duration-300',
                  isActive
                    ? 'text-gray-900'
                    : isComplete
                    ? 'text-gray-800'
                    : 'text-gray-500'
                )}
              >
                {step.label}
              </p>
              <p className={cn(
                "text-xs",
                isActive || isComplete ? "text-gray-600" : "text-gray-500"
              )}>
                {step.description}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
}

/**
 * ProcessingOverlay
 * 
 * Shows a processing overlay while the document is being read/analyzed.
 * Uses a large card style with gray tones.
 */
function ProcessingOverlay({
  isProcessing,
  processingStep
}: {
  isProcessing: boolean;
  processingStep: ProcessingStep;
}) {
  if (!isProcessing) return null;
  
  return (
    <div className="fixed inset-0 bg-white/30 backdrop-blur-lg flex items-center justify-center p-4 rounded-3xl pointer-events-auto">
      <div className="w-full max-w-2xl rounded-3xl bg-white/95 backdrop-blur-2xl border border-white/40 shadow-xl p-8">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900">Processing Document</h2>
        </div>
        <p className="text-base text-gray-700 font-medium mb-8">
          {processingStep === 'complete'
            ? 'Document processed successfully!'
            : 'This may take a few moments...'}
        </p>

        <div className="space-y-8">
          <div className="flex items-center justify-center py-4">
            <div className="relative">
              <div className="absolute inset-0 rounded-full bg-gray-900/20 animate-ping" />
              <Loader2 className="h-8 w-8 text-gray-900 animate-spin relative" />
            </div>
          </div>

          <ProcessingSteps currentStep={processingStep} />
        </div>
      </div>
    </div>
  );
}

/**
 * Sub-component for showing and managing line items in a table view.
 * 
 * Uses react-hook-form's `fields` array (via useFieldArray) to avoid
 * duplicating state. 
 */
function LineItemsSection({
  items,
  isStatement,
  handleAutoCategorize,
  isAutoCategorizing,
  addLineItem,
  removeLineItem,
  updateLineItemCategory,
  categoriesList
}: {
  items: LineItem[];
  isStatement: boolean;
  handleAutoCategorize: () => void;
  isAutoCategorizing: boolean;
  addLineItem: () => void;
  removeLineItem: (index: number) => void;
  updateLineItemCategory: (index: number, categoryId: Id<'tags'> | undefined) => void;
  categoriesList: TagWithChildren[];
}) {
  // Calculate total from fields
  const total = items.reduce((sum, item) => sum + (item.amount || 0), 0);

  return (
    <div className="border-t mt-8 pt-8">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-medium">Line Items</h3>
          <span className="text-sm text-muted-foreground">
            Total: ${total.toFixed(2)}
          </span>
          {!isStatement && items.length > 0 && (
            <span className="text-xs text-muted-foreground italic">
              Invoice line item is created automatically
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAutoCategorize}
            disabled={isAutoCategorizing}
          >
            {isAutoCategorizing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Categorizing...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Auto Categorize
              </>
            )}
          </Button>
          {isStatement && (
            <Button type="button" variant="outline" size="sm" onClick={addLineItem}>
              Add Line Item
            </Button>
          )}
        </div>
      </div>

      <div className="border rounded-lg bg-muted/30">
        <div className="max-h-[300px] overflow-y-auto relative">
          <table className="w-full">
            <thead className="sticky top-0 bg-background shadow-sm">
              <tr className="border-b bg-muted/50">
                <th className="px-2 py-1 text-left text-xs font-medium w-[120px]">Date</th>
                <th className="px-2 py-1 text-left text-xs font-medium w-[120px]">Merchant</th>
                <th className="px-2 py-1 text-left text-xs font-medium w-[200px]">Description</th>
                <th className="px-2 py-1 text-left text-xs font-medium w-[200px]">Category</th>
                <th className="px-2 py-1 text-left text-xs font-medium w-[80px]">Amount</th>
                <th className="px-2 py-1 text-left text-xs font-medium w-[40px]" />
              </tr>
            </thead>
            <tbody className="text-sm">
              {items.map((item, index) => (
                <tr
                  key={item.id ?? index} // fallback if no ID
                  className="border-b last:border-0 hover:bg-muted/50"
                >
                  <td className="px-2 py-0.5">
                    <div className="h-6 w-[120px] flex items-center gap-1 text-sm">
                      <CalendarIcon className="h-3 w-3 text-muted-foreground" />
                      {item.postDate ? formatDisplayDate(item.postDate) : 'N/A'}
                    </div>
                  </td>
                  <td className="px-2 py-0.5">
                    <div
                      className="h-6 flex items-center text-sm truncate max-w-[120px]"
                      title={item.vendor || 'No merchant'}
                    >
                      {item.vendor || 'No merchant'}
                    </div>
                  </td>
                  <td className="px-2 py-0.5">
                    <div
                      className="h-6 flex items-center text-sm max-w-[200px] truncate"
                      title={item.description || 'No description'}
                    >
                      {item.description || 'No description'}
                    </div>
                  </td>
                  <td className="px-2 py-0.5">
                      <CategoryDropdown
                        lineItem={{
                          _id: String(item.id ?? `temp_${index}`),
                          spending_category: item.spending_category
                        }}
                        onCategoryChangeAction={(categoryId) => {
                          updateLineItemCategory(index, categoryId || undefined);
                        }}
                        categories={categoriesList}
                        triggerClassName="w-full max-w-[180px]"
                      />

                  </td>
                  <td className="px-2 py-0.5">
                    <div className="h-6 flex items-center text-sm">
                      ${item.amount?.toFixed(2) ?? '0.00'}
                    </div>
                  </td>
                  <td className="px-2 py-0.5 text-center">
                    {isStatement && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeLineItem(index)}
                        className="h-5 w-5 text-muted-foreground hover:text-destructive"
                      >
                        ×
                      </Button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

/**
 * Main Export: CreateBillModal
 *
 * A modal for creating new bills and credit card statements.
 * Handles document upload, AI parsing, and data entry.
 */
export function CreateBillModal({
  open,
  onOpenChange,
  onSuccess
}: CreateBillModalProps) {
  /**
   * Handle escape key press to close modal
   */
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && open) {
        onOpenChange(false);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [open, onOpenChange]);
  
  /**
   * Safari-specific fixes for modal popovers
   */
  useEffect(() => {
    if (!open) return;
    
    // Safari-specific fix for stacking context issues
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari) {
      // Apply safari-specific fixes
      const fixSafariStacking = () => {
        // Find all popover elements that might be created later
        const fixPopovers = () => {
          document.querySelectorAll('[data-radix-popper-content-wrapper]').forEach(el => {
            (el as HTMLElement).style.zIndex = '9999';
          });
        };
        
        // Run immediately and then set up a mutation observer to catch new popovers
        fixPopovers();
        const observer = new MutationObserver((mutations) => {
          fixPopovers();
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        return () => observer.disconnect();
      };
      
      const cleanup = fixSafariStacking();
      return cleanup;
    }
  }, [open]);

  /**
   * Safari-specific fix for stacking context and z-index issues
   * This ensures the modal contents render properly on Safari
   */
  const formRef = useRef<HTMLFormElement>(null);
  useEffect(() => {
    if (!open) return;

    // Safari-specific fix for z-index stacking
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari && formRef.current) {
      // Force a repaint to fix Safari stacking issues
      const forceRepaint = () => {
        if (formRef.current) {
          formRef.current.style.transform = 'translateZ(0)';
          setTimeout(() => {
            if (formRef.current) formRef.current.style.transform = '';
          }, 0);
        }
      };
      
      // Apply fixes after a short delay to ensure DOM is ready
      setTimeout(forceRepaint, 50);
    }
  }, [open]);

  /**
   * Local states for:
   * - File & preview (Document upload)
   * - Processing status
   * - Saving status
   * - Auto-categorizing
   * - Storage ID
   * - Suggested Vendor
   */
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] =
    useState<ProcessingStep>('reading');
  const [isSaving, setIsSaving] = useState(false);
  const [isAutoCategorizing, setIsAutoCategorizing] = useState(false);
  const [storageId, setStorageId] = useState<Id<'_storage'> | undefined>(
    undefined
  );
  const [suggestedVendor, setSuggestedVendor] = useState<{
    name: string;
    isNew: boolean;
    confidence: number;
    matchedVendorId?: Id<'organizations'>;
  } | null>(null);

  /**
   * react-hook-form + useFieldArray for line items
   */
  const form = useForm<BillInput>({
    resolver: zodResolver(BillInputSchema),
    defaultValues: {
      type: 'BILL',
      billNo: '',
      billDate: Date.now(),
      vendor_id: undefined,
      amount: 0,
      dueDate: undefined,
      memo: ''
    }
  });
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    control
  } = form;

  // Separate state for line items
  const [lineItems, setLineItems] = useState<LineItem[]>([]);

  // Watch the document type field
  const docType = form.watch('type');
  const isStatement = docType === 'CREDIT_CARD';

  /**
   * Convex queries & mutations
   */
  const createBill = useMutation(api.bills.createBill);
  const createLineItems = useMutation(api.bills.createLineItems);
  const parseDocument = useAction(api.actions.parseDocumentAction.parseDocument);
  const aiCategorize = useAction(api.actions.aiCategorizeAction.aiCategorize);
  const generateUploadUrl = useMutation(api.files.fileManagement.generateUploadUrl);
  const storeDocument = useMutation(api.files.fileManagement.storeDocument);
  const createVendor = useMutation(
    api.directory.directoryOrganizations.createOrganizations
  );

  // Add vendor search state here
  const [vendorSearch, setVendorSearch] = useState('');
  
  // Single source of truth for vendors
  const vendorsQuery = useQuery(api.directory.directoryOrganizations.searchOrganizations, {
    query: vendorSearch,
    isVendor: true
  });

  // Use base vendor list when no search
  const baseVendorsQuery = useQuery(
    api.directory.directoryOrganizations.listOrganizations,
    {
      filter: {},
      pagination: { limit: 100 }
    }
  );

  // Determine which vendor list to use
  const displayedVendors = vendorSearch 
    ? (vendorsQuery || []) 
    : (baseVendorsQuery?.organizations || []);

  // Add the missing categories query
  const categoriesQuery = useQuery(api.tags.fetchTags, { 
    filter: {} 
  });

  // Fix the types in the useEffect
  const [categoriesList, setCategoriesList] = useState<TagWithChildren[]>([]);
  useEffect(() => {
    if (categoriesQuery && Array.isArray(categoriesQuery)) {
      const parentCategories = categoriesQuery
        .filter((c: { parent_id?: Id<'tags'> }) => !c.parent_id)
        .map((parent: any) => {
          const children = categoriesQuery
            .filter((child: { parent_id?: Id<'tags'> }) => child.parent_id === parent._id)
            .map((child: any) => ({
              ...child,
              parent_id: child.parent_id || undefined,
              children: []
            }));
          return {
            ...parent,
            parent_id: undefined,
            children
          } as TagWithChildren;
        });
      setCategoriesList(parentCategories);
    }
  }, [categoriesQuery]);

  /**
   * If user toggles "type" from Bill to Credit Card or vice versa,
   * ensure there's at least 1 line item for a Bill, or no forced single
   * item for a CC statement.
   */
  useEffect(() => {
    // Only run this effect when docType changes, not on every open change
    if (!open) return;
    
    // Use setTimeout to defer this work until after modal is visible
    const timer = setTimeout(() => {
      if (docType === 'BILL' && lineItems.length === 0) {
        setLineItems([{
          postDate: new Date(),
          vendor: '',
          description: 'Invoice line item',
          amount: watch('amount') || 0,
          spending_category: undefined,
          merchantType: ''
        }]);
      } else if (docType === 'CREDIT_CARD' && lineItems.length === 1 && !lineItems[0].id) {
        // If we suspect the single item is the "default" item, remove it.
        setLineItems([]);
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [docType, open]);

  /**
   * Initialize modal state when opening
   */
  useEffect(() => {
    if (open) {
      // Defer initialization until after modal is visible
      const timer = setTimeout(() => {
        // Initialize with default values
        reset({
          type: 'BILL',
          billNo: '',
          billDate: Date.now(),
          vendor_id: undefined,
          amount: 0,
          dueDate: undefined,
          memo: ''
        });
      }, 0);

      return () => clearTimeout(timer);
    }
  }, [open]);

  /**
   * onDrop: triggered by DocumentUploadArea
   * - Resets previous file states
   * - Creates preview
   * - Uploads file to Convex
   * - Sends file to AI parse
   * - Populates the form with extracted data
   */
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const droppedFile = acceptedFiles[0];
    if (!droppedFile) return;

    try {
      handleReset();
      setFile(droppedFile);
      const url = URL.createObjectURL(droppedFile);
      setPreviewUrl(url);

      setIsProcessing(true);
      setProcessingStep('reading');

      // 1) AI Analysis
      const parseDocumentPromise = (async () => {
        try {
          const base64Content = await fileToBase64(droppedFile);
          setProcessingStep('analyzing');

          const parsedData = await parseDocument({
            file: base64Content,
            fileType: droppedFile.type as
              | 'application/pdf'
              | 'image/png'
              | 'image/jpeg'
          });
          if (!parsedData) throw new Error('Failed to parse document');

          setProcessingStep('processing');
          await applyParsedDataToForm(parsedData);
        } catch (error) {
          console.error('Error parsing document:', error);
          throw error;
        }
      })();

      // 2) File Upload to Convex
      const uploadFilePromise = (async () => {
        try {
          const uploadUrl = await generateUploadUrl({
            contentType: droppedFile.type
          });
          const uploadResult = await fetch(uploadUrl, {
            method: 'POST',
            headers: { 'Content-Type': droppedFile.type },
            body: droppedFile
          });
          if (!uploadResult.ok) {
            let errorDetail = uploadResult.statusText;
            try {
              const errorBody = await uploadResult.text();
              errorDetail = errorBody || uploadResult.statusText;
            } catch (e) {
              // If reading response body fails, fallback to statusText
            }
            throw new Error(`Failed to upload file: ${errorDetail}`);
          }
          const { storageId } = await uploadResult.json();
          if (!storageId) {
            throw new Error('No storageId returned from upload response');
          }
          setStorageId(storageId);
        } catch (error) {
          console.error('Error in file upload process:', error);
          throw error;
        }
      })();

      await Promise.all([parseDocumentPromise, uploadFilePromise]);

      setIsProcessing(false);
      setProcessingStep('complete');
    } catch (error) {
      console.error('Error processing file:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to process file'
      );
      handleReset();
      setIsProcessing(false);
      setProcessingStep('reading');
    }
  }, []);

  /**
   * fileToBase64
   * Converts file into base64 for AI parsing
   */
  async function fileToBase64(localFile: File): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        const base64Content = base64.split(',')[1];
        resolve(base64Content);
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(localFile);
    });
  }

  /**
   * applyParsedDataToForm
   * Takes AI-extracted data and populates our react-hook-form with relevant fields.
   * All dates are converted to UTC timestamps for storage.
   */
  async function applyParsedDataToForm(parsedData: any) {
    if (parsedData.vendorMatch) {
      setSuggestedVendor({
        name: parsedData.vendorMatch.name,
        isNew: !parsedData.vendorMatch.exactMatch,
        confidence: parsedData.vendorMatch.confidence,
        matchedVendorId: parsedData.vendorMatch.matchedVendorId
      });
    }

    const currentTimestamp = Date.now();

    if (parsedData.type === 'bill') {
      // Always ensure we have a valid timestamp for billDate
      const billDateTimestamp = parsedData.billDate
        ? toUTCTimestamp(parsePreservingDate(parsedData.billDate))
        : Date.now();

      const newLineItem: LineItem = {
        postDate: billDateTimestamp 
          ? new Date(billDateTimestamp) 
          : new Date(),
        vendor: parsedData.vendorMatch?.name || parsedData.vendor || '',
        description: parsedData.memo || `Invoice ${parsedData.billNumber || ''}`,
        amount: parsedData.amount || 0,
        spending_category: undefined,
        merchantType: parsedData.vendor || ''
      };

      reset({
        type: 'BILL',
        billNo: parsedData.billNumber || '',
        billDate: billDateTimestamp,
        vendor_id:
          parsedData.vendorMatch?.exactMatch &&
          parsedData.vendorMatch?.matchedVendorId
            ? parsedData.vendorMatch.matchedVendorId
            : undefined,
        amount: parsedData.amount || 0,
        dueDate: parsedData.dueDate
          ? toUTCTimestamp(parsePreservingDate(parsedData.dueDate))
          : undefined,
        memo: parsedData.memo || ''
      });
      setLineItems([newLineItem]);
    } else {
      // Credit card statement
      const newLineItems =
        parsedData.lineItems?.map((item: any) => ({
          postDate: parsePreservingDate(item.postDate),
          vendor: item.merchantName,
          description: item.description,
          amount: item.amount,
          spending_category: undefined,
          merchantType: item.merchantName
        })) || [];

      reset({
        type: 'CREDIT_CARD',
        billNo: parsedData.statementNumber || '',
        billDate: currentTimestamp,
        vendor_id:
          parsedData.vendorMatch?.exactMatch &&
          parsedData.vendorMatch?.matchedVendorId
            ? parsedData.vendorMatch.matchedVendorId
            : undefined,
        amount: parsedData.totalNewPurchases || 0,
        dueDate: parsedData.dueDate
          ? toUTCTimestamp(parsePreservingDate(parsedData.dueDate))
          : undefined,
        memo: parsedData.memo || ''
      });
      setLineItems(newLineItems);
    }
  }

  /**
   * Handle auto-categorization
   */
  const handleAutoCategorize = useCallback(async () => {
    if (!lineItems.length) return;

    setIsAutoCategorizing(true);
    try {
      // Prepare items for AI categorization
      const itemsForAI = lineItems.map((item, index) => ({
        merchant_name: item.merchantType || item.vendor,
        description: item.description,
        amount: item.amount
      }));

      const result = await aiCategorize({
        lineItems: itemsForAI,
        skipDatabaseUpdate: true // Important: We want to preview the categories first
      });

      if (result.success) {
        // Get all categories to map the category strings to IDs
        const allCategories = await categoriesQuery;
        if (!Array.isArray(allCategories)) {
          throw new Error('Failed to fetch categories');
        }

        // Update line items with new categories
        const updatedLineItems = lineItems.map((item, index) => {
          const aiSuggestion = result.mappings[index.toString()]?.category;
          if (!aiSuggestion) return item;

          // Split the category string into parent and child
          const [parentName, childName] = aiSuggestion.split(':').map(s => s.trim());
          
          // Find the matching category ID
          const categoryId = allCategories.find(cat => {
            if (!cat.parent_id) return false;
            const parent = allCategories.find(p => p._id === cat.parent_id);
            return parent?.name === parentName && cat.name === childName;
          })?._id;

          return {
            ...item,
            spending_category: categoryId
          };
        });

        setLineItems(updatedLineItems);
        toast.success('Auto-categorization complete');
      }
    } catch (error) {
      console.error('Error auto-categorizing:', error);
      toast.error('Failed to auto-categorize line items');
    } finally {
      setIsAutoCategorizing(false);
    }
  }, [lineItems, aiCategorize, categoriesQuery]);

  // Line item management functions
  const addLineItem = useCallback(() => {
    setLineItems(prev => [...prev, {
      postDate: new Date(),
      vendor: '',
      description: '',
      amount: 0,
      spending_category: undefined,
      merchantType: ''
    }]);
  }, []);

  const removeLineItem = useCallback((index: number) => {
    setLineItems(prev => prev.filter((_, i) => i !== index));
  }, []);

  const updateLineItemCategory = useCallback((index: number, categoryId: Id<'tags'> | undefined) => {
    setLineItems(prev => {
      const newItems = [...prev];
      newItems[index] = { ...newItems[index], spending_category: categoryId };
      return newItems;
    });
  }, []);

  /**
   * Vendor selection changes
   */
  function handleVendorChange(organizationId: Id<'organizations'>) {
    setValue('vendor_id', organizationId);
  }

  /**
   * onSubmit: Save the Bill or CC statement
   */
  const onSubmit = async (data: BillInput) => {
    try {
      // Must have a file
      if (!file) {
        toast.error('Please upload a document');
        return;
      }
      setIsSaving(true);
      toast.loading('Saving document...');

      // Possibly create a new vendor if AI suggested a brand new one
      let finalVendorId: Id<'organizations'> | undefined = data.vendor_id;
      if (suggestedVendor?.isNew && !data.vendor_id) {
        const createVendorResult = await createVendor({
          items: [
            {
              name: suggestedVendor.name,
              is_vendor: true
            }
          ]
        });
        finalVendorId = createVendorResult.ids[0];
        toast.success(`Created new vendor: ${suggestedVendor.name}`);
      }

      if (!finalVendorId) {
        toast.error('Please select a vendor');
        setIsSaving(false);
        toast.dismiss();
        return;
      }

      // Bill data to write - dates are already timestamps from the form
      const billData = {
        type: data.type,
        billNo: data.billNo || '',
        billDate: data.billDate,
        vendor_id: finalVendorId,
        amount: data.amount,
        dueDate: data.dueDate,
        memo: data.memo || ''
      };

      // Create Bill
      const newBillId = await createBill({ bill: billData });

      // Store the uploaded file
      if (storageId) {
        await storeDocument({
          storageId,
          name: file.name,
          type: 'BILL',
          size: file.size,
          parentEntityId: newBillId as Id<'bills'>
        });
      }

      // Create line items in the DB
      if (lineItems.length > 0) {
        const now = Date.now();
        const lineItemsToCreate = lineItems
          .map((item) => {
            const postDateTimestamp = toUTCTimestamp(item.postDate);
            
            // Only create items that have all required fields
            if (!postDateTimestamp || !finalVendorId) {
              return null;
            }

            return {
              bill_id: Array.isArray(newBillId) ? newBillId[0] : newBillId,
              amount: item.amount,
              description: item.description,
              post_date: postDateTimestamp,
              merchant_name: item.merchantType || item.vendor,
              spending_category: item.spending_category,
              vendor_id: finalVendorId,
              updated_at: now
            };
          })
          .filter((item): item is NonNullable<typeof item> => item !== null);

        if (lineItemsToCreate.length > 0) {
          await createLineItems({
            lineItems: {
              items: lineItemsToCreate
            }
          });
        }
      }

      toast.success('Bill saved successfully');
      await onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving bill:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save bill');
    } finally {
      setIsSaving(false);
      toast.dismiss();
    }
  };

  /**
   * Helper to reset the entire form plus local states
   */
  function handleReset() {
    // Reset file states
    setFile(null);
    setPreviewUrl(null);
    setSuggestedVendor(null);
    setStorageId(undefined);
    setIsProcessing(false);
    setProcessingStep('reading');
    setIsAutoCategorizing(false);
    setLineItems([]);

    // Reset form in a single operation
    reset({
      type: 'BILL',
      billNo: '',
      billDate: Date.now(),
      vendor_id: undefined,
      amount: 0,
      dueDate: undefined,
      memo: ''
    });
  }

  /**
   * Handle modal close
   */
  const handleClose = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  /**
   * Cleanup effect that runs after modal is closed
   */
  useEffect(() => {
    if (!open) {
      // Run cleanup after modal is closed
      handleReset();
    }
  }, [open]);

  // Render nothing if modal is closed
  if (!open) return null;

  return (
    <Modal
      heading="Upload a Bill or Credit Card Statement"
      subheading="AI will automatically extract and categorize the bill details for you"
      ctaButton={{
        text: isSaving ? 'Saving...' : 'Submit',
        onClick: form.handleSubmit(onSubmit)
      }}
      secondaryCta={{
        text: 'Cancel',
        onClick: handleClose
      }}
      onClose={handleClose}
      className="max-w-7xl relative"
    >
      {/* Form with ref for Safari fixes */}
      <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className="modal-form-fix relative">
        {/* Grid container */}
        <div className="grid grid-cols-1 lg:grid-cols-[1.3fr_0.7fr] gap-4 relative">
          <DocumentUploadArea
            previewUrl={previewUrl}
            file={file}
            isProcessing={isProcessing}
            onDrop={onDrop}
            handleResetFile={handleReset}
            height="h-[500px]"
          />

          <BillFormFields
            control={form.control}
            watch={watch}
            setValue={setValue}
            register={register}
            suggestedVendor={suggestedVendor}
            vendors={displayedVendors}
            onVendorChange={handleVendorChange}
            onVendorSearch={setVendorSearch}
          />
        </div>

        {/* Line Items Section */}
        {(docType === 'CREDIT_CARD' || lineItems.length > 0) && (
          <LineItemsSection
            items={lineItems}
            isStatement={docType === 'CREDIT_CARD'}
            handleAutoCategorize={handleAutoCategorize}
            isAutoCategorizing={isAutoCategorizing}
            addLineItem={addLineItem}
            removeLineItem={removeLineItem}
            updateLineItemCategory={updateLineItemCategory}
            categoriesList={categoriesList}
          />
        )}
      </form>

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-white/30 backdrop-blur-lg flex items-center justify-center p-4 rounded-3xl pointer-events-auto">
          <div className="w-full max-w-2xl rounded-3xl bg-white/95 backdrop-blur-2xl border border-white/40 shadow-xl p-8">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-2xl font-bold tracking-tight text-gray-900">Processing Document</h2>
            </div>
            <p className="text-base text-gray-700 font-medium mb-8">
              {processingStep === 'complete'
                ? 'Document processed successfully!'
                : 'This may take a few moments...'}
            </p>

            <div className="space-y-8">
              <div className="flex items-center justify-center py-4">
                <div className="relative">
                  <div className="absolute inset-0 rounded-full bg-gray-900/20 animate-ping" />
                  <Loader2 className="h-8 w-8 text-gray-900 animate-spin relative" />
                </div>
              </div>

              <ProcessingSteps currentStep={processingStep} />
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
}
