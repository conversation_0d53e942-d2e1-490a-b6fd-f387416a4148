'use client';

/**
 * DocumentUploadArea Component
 *
 * This component handles:
 * 1. Drag-and-drop file uploads (using `react-dropzone`).
 * 2. Preview of PDF or image files.
 * 3. Simple zoom controls for images.
 * 4. Ability to reset (change) the chosen file.
 *
 * It is a refactored extraction from the CreateBillModal code.
 *
 * Use this component wherever you need to let users drop or select a file
 * and see a preview of their PDF or image.
 */

import React from 'react';
import { Upload } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

/**
 * Props for the DocumentUploadArea component.
 *
 * @property previewUrl - The URL to show a preview (if a file has been chosen).
 * @property file - The currently selected file (used for pdf vs image detection).
 * @property isProcessing - Flag that disables dropzone if set to true.
 * @property onDrop - Handler called when the user drops a file.
 * @property handleResetFile - Handler to clear the current file and preview.
 * @property className - Optional className for custom styling
 * @property height - Optional height override (default: 'h-[600px]')
 */
export interface DocumentUploadAreaProps {
  previewUrl: string | null;
  file: File | null;
  isProcessing: boolean;
  onDrop: (acceptedFiles: File[]) => Promise<void>;
  handleResetFile: () => void;
  className?: string;
  height?: string;
}

/**
 * DocumentUploadArea
 *
 * Main container for handling file dropping and showing the appropriate preview.
 */
export function DocumentUploadArea({
  previewUrl,
  file,
  isProcessing,
  onDrop,
  handleResetFile,
  className,
  height = 'h-[600px]'
}: DocumentUploadAreaProps) {
  // Maximum allowed file size (in bytes).
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  // Allowed MIME types.
  const ALLOWED_TYPES = ['application/pdf', 'image/png', 'image/jpeg'];

  /**
   * Sets up the dropzone for file uploads.
   *
   * - accept: Allowed file types.
   * - maxFiles: Only allow one file at a time.
   * - disabled: If isProcessing is true, disallow dropping.
   */
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    maxFiles: 1,
    disabled: isProcessing
  });

  // If no previewUrl, show the drop area. Otherwise, show the preview.
  return (
    <div className={cn(height, "w-full", className)}>
      {!previewUrl ? (
        <div
          {...getRootProps()}
          className={cn(
            'h-full w-full border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-6 cursor-pointer bg-muted/30 transition-colors duration-200',
            isDragActive ? 'border-primary bg-primary/5 scale-[0.99]' : 'border-gray-400 hover:border-gray-500 hover:bg-muted/40',
            isProcessing && 'opacity-50 cursor-not-allowed'
          )}
        >
          <input {...getInputProps()} />
          <>
            <Upload className="h-10 w-10 text-gray-400 mb-4" />
            <p className="text-sm text-gray-600 text-center">
              {isDragActive
                ? 'Drop the file here'
                : 'Drag and drop your file here, or click to select'}
            </p>
            <p className="text-xs text-gray-400 mt-2">Supports PDF, PNG, JPG</p>
          </>
        </div>
      ) :
        <div className="h-full w-full flex flex-col rounded-lg overflow-hidden border bg-muted/30">
          <div className="flex items-center justify-between px-4 py-2 border-b bg-muted/30">
            <div className="flex items-center gap-2 truncate text-sm font-medium">
              {file?.name}
            </div>
            <Button
              variant="ghost"
              size="sm"
              disabled={isProcessing}
              onClick={(e) => {
                e.preventDefault();
                handleResetFile();
              }}
              type="button"
            >
              Change file
            </Button>
          </div>
          <div className="flex-1 min-h-0 w-full">
            {file?.type === 'application/pdf' ? (
              <iframe
                src={previewUrl}
                className="w-full h-full"
                style={{ border: 'none' }}
              />
            ) : (
              <div className="w-full h-full overflow-y-auto bg-white/50">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full object-contain"
                />
              </div>
            )}
          </div>
        </div>
      }
    </div>
  );
}
