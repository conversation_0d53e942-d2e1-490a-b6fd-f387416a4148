'use client';

import { useId, useMemo, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Id } from '@/convex/_generated/dataModel';
import { BillStatus, UIBill } from '@/zod/bills-schema';
import { CollectionCard } from '@/components/ui/collection-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  ColumnDef,
  Row,
  createColumnHelper,
  flexRender
} from '@tanstack/react-table';
import {
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Search,
  TrendingDown,
  TrendingUp,
  Filter,
  Trash2,
  X,
  Edit,
  CheckCircle2
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter
} from '@/components/ui/table';

// =======================================================
// Types
// =======================================================
export interface BillsTableProps {
  bills: UIBill[];
  selectedBills: Id<'bills'>[];
  onSelectionChange: (selectedBills: Id<'bills'>[]) => void;
  onSyncBills: () => void;
  onEditBill: (billId: Id<'bills'>) => void;
  onDeleteBills?: (billIds: Id<'bills'>[]) => Promise<boolean>;
  onUpdateStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<boolean>;
}

// =======================================================
// Utility Functions
// =======================================================
// Determine UI status for bill
function determineUIStatus(bill: UIBill): 'paid' | 'pending' | 'overdue' {
  if (bill.billStatus === 'PAID') return 'paid';
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dueDate = bill.dueDate ? new Date(bill.dueDate) : null;
  
  if (dueDate && dueDate < today) return 'overdue';
  return 'pending';
}

// Get color for status badge
function getBadgeColorForStatus(status: 'paid' | 'pending' | 'overdue' | BillStatus): string {
  switch (status) {
    case 'paid':
    case 'PAID':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
    case 'pending':
    case 'UNPAID':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
    case 'overdue':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
    case 'PARTIALLY_PAID':
      return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300';
    case 'SCHEDULED':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
    case 'IN_PROCESS':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
  }
}

// Filter function for multi-column search
function multiColumnFilterFn(
  row: Row<UIBill>,
  columnId: string,
  filterValue: string
): boolean {
  const searchTerms = filterValue.toLowerCase().split(' ');
  
  // For vendor column, search in vendor field
  if (columnId === 'vendor') {
    const vendor = row.getValue<string>('vendor')?.toLowerCase() || '';
    return searchTerms.every(term => vendor.includes(term));
  }
  
  // Default: Return all rows if no filter
  return true;
}

// Filter function for status column
function statusFilterFn(
  row: Row<UIBill>,
  columnId: string,
  filterValue: string
): boolean {
  // If no filter value, show all
  if (!filterValue) return true;
  
  const billStatus = row.getValue<BillStatus | undefined>(columnId);
  const uiStatus = determineUIStatus(row.original);
  
  // We can filter by UI status or by actual bill status
  return uiStatus === filterValue.toLowerCase() || billStatus === filterValue;
}

// Convert selection array to row selection object
function convertSelectionToRowSelection(
  selectedIds: Id<'bills'>[],
  bills: UIBill[]
): Record<number, boolean> {
  const selection: Record<number, boolean> = {};
  
  // Create a lookup map for quick checking
  const selectedSet = new Set(selectedIds);
  
  // Map selection to row indexes
  bills.forEach((bill, index) => {
    if (selectedSet.has(bill._id)) {
      selection[index] = true;
    }
  });
  
  return selection;
}

// =======================================================
// Main Component
// =======================================================
export function BillsTable({
  bills,
  selectedBills,
  onSelectionChange,
  onSyncBills,
  onEditBill,
  onDeleteBills,
  onUpdateStatus
}: BillsTableProps) {
  // Basic setup
  const id = useId();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  // Table state
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'dueDate', desc: true }
  ]);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 25
  });

  // Create row selection object from the externally managed selectedBills array
  const rowSelection = useMemo(
    () => convertSelectionToRowSelection(selectedBills, bills),
    [selectedBills, bills]
  );

  // =======================================================
  // Column Definitions
  // =======================================================
  const columnHelper = createColumnHelper<UIBill>();
  
  // Use any type for columns to avoid TS complexity
  const columns = useMemo(() => [
    columnHelper.display({
      id: 'select',
      header: ({ table }) => (
        <div className="ps-1">
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            data-state={
              table.getIsSomePageRowsSelected() ? 'indeterminate' : undefined
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div
          className="ps-1"
          onClick={(e) => e.stopPropagation()}
          data-no-navigate="true"
        >
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40
    }),
    columnHelper.accessor('vendor', {
      header: () => <div className="font-medium">Vendor</div>,
      cell: (info) => <div className="font-medium">{info.getValue()}</div>,
      size: 160,
      filterFn: multiColumnFilterFn,
      enableHiding: false
    }),
    columnHelper.accessor((row) => row.category, {
      id: 'category',
      header: () => <div className="font-medium">Category</div>,
      cell: (info) => {
        const type = info.row.original.type;
        const category = info.getValue();
        const displayValue = type === 'CREDIT_CARD' ? 'Credit Card' : (category ?? 'Uncategorized');
        return (
          <div
            className={cn(
              'font-medium',
              category === 'Multiple Categories' &&
                'text-amber-600 dark:text-amber-500',
              (!category || category === 'Uncategorized') && 'text-muted-foreground italic'
            )}
          >
            {displayValue}
          </div>
        );
      },
      size: 150,
      enableSorting: true
    }),
    columnHelper.accessor('dueDate', {
      header: () => <div className="font-medium">Due Date</div>,
      cell: (info) => {
        const dueDate = info.getValue();
        return dueDate ? format(new Date(dueDate), 'MM-dd-yyyy') : '-';
      },
      size: 110,
      enableSorting: true
    }),
    // Status column
    columnHelper.accessor('billStatus', {
      header: () => <div className="font-medium">Status</div>,
      cell: (info) => {
        const bill = info.row.original;
        const uiStatus = determineUIStatus(bill);
        const currentStatus = bill.billStatus ?? 'Not Set';
        
        // Create status badge
        return (
          <StatusBadge 
            status={currentStatus} 
            uiStatus={uiStatus}
            billId={bill._id}
            onUpdateStatus={onUpdateStatus}
          />
        );
      },
      size: 120,
      filterFn: statusFilterFn,
      enableSorting: true
    }),
    columnHelper.accessor('amount', {
      header: () => <div className="text-right font-medium pe-4">Amount</div>,
      cell: (info) => {
        const amount = info.getValue();
        const formatted = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(amount);

        const percentageChange = info.row.original.percentageChange;
        const previousAmount = info.row.original.previousAmount;

        return (
          <div className="flex items-center justify-end gap-2 pe-4">
            <div className="text-right tabular-nums">{formatted}</div>
            {percentageChange !== undefined && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className={cn(
                        'flex items-center gap-1 text-xs rounded-md px-1.5 py-0.5',
                        percentageChange > 0
                          ? 'text-red-500 bg-red-500/10'
                          : percentageChange < 0
                            ? 'text-green-500 bg-green-500/10'
                            : 'text-muted-foreground bg-muted'
                      )}
                    >
                      {percentageChange > 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : percentageChange < 0 ? (
                        <TrendingDown className="h-3 w-3" />
                      ) : null}
                      <span>
                        {percentageChange === 0
                          ? 'No change'
                          : `${Math.abs(percentageChange).toFixed(1)}%`}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">
                      Previous amount:{' '}
                      {previousAmount
                        ? new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD'
                          }).format(previousAmount)
                        : 'N/A'}
                      {percentageChange !== 0 && (
                        <>
                          <br />
                          {percentageChange > 0
                            ? 'Increased'
                            : 'Decreased'}{' '}
                          by {Math.abs(percentageChange).toFixed(1)}%
                        </>
                      )}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        );
      },
      size: 160,
      enableSorting: true
    }),
    columnHelper.display({
      id: 'actions',
      header: () => (
        <div className="text-right font-medium pe-4">Actions</div>
      ),
      cell: ({ row }) => (
        <div
          className="flex justify-end pe-2"
          onClick={(e) => e.stopPropagation()}
          data-no-navigate="true"
        >
          <RowActions
            bill={row.original}
            onEditBill={onEditBill}
            onDeleteBills={onDeleteBills}
            onUpdateStatus={onUpdateStatus}
          />
        </div>
      ),
      size: 70,
      enableHiding: false
    })
  ], [columnHelper, onEditBill, onDeleteBills, onUpdateStatus]);

  // Initialize the table instance
  const table = useReactTable<UIBill>({
    data: bills,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    enableRowSelection: true,
    enableMultiRowSelection: true,
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
      rowSelection
    },
    onRowSelectionChange: (updater) => {
      let newSelection: Record<number, boolean>;
      if (typeof updater === 'function') {
        newSelection = updater(rowSelection);
      } else {
        newSelection = updater;
      }
      // Convert to external array of IDs
      const selectedIds = Object.keys(newSelection)
        .filter((idx) => newSelection[Number(idx)])
        .map((idx) => bills[Number(idx)]._id);
      onSelectionChange(selectedIds);
    }
  });

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (!onDeleteBills || !selectedBills.length) return;
    
    setIsDeleting(true);
    try {
      const success = await onDeleteBills(selectedBills);
      if (success) {
        // Clear selection after successful delete
        onSelectionChange([]);
      }
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    table.setGlobalFilter(e.target.value);
  };

  // Clear all filters
  const handleClearFilters = () => {
    table.setGlobalFilter('');
    table.resetColumnFilters();
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  // Handle clear selection
  const handleClearSelection = () => {
    onSelectionChange([]);
  };

  return (
    <CollectionCard
      heading="Bills"
      subheading={`${table.getFilteredRowModel().rows.length} bills total`}
      buttonText="Sync Bills"
      onButtonClick={onSyncBills}
      className="bg-background/30"
    >
      <div className="space-y-4">
        {/* Filters + Bulk Actions */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          {/* Search */}
          <div className="relative w-full max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id={`${id}-search-filter`}
              ref={inputRef}
              placeholder="Search vendors..."
              className="pl-8 w-full max-w-md bg-background/50"
              onChange={handleSearchChange}
            />
            {table.getState().globalFilter && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-9 w-9"
                onClick={handleClearFilters}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>
          
          {/* Actions */}
          <div className="flex items-center gap-2 w-full sm:w-auto justify-between sm:justify-end">
            {selectedBills.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {selectedBills.length} selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSelection}
                >
                  Clear
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  disabled={isDeleting}
                  onClick={handleBulkDelete}
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            )}
            
            {/* Filter dropdown - could be implemented for more advanced filtering */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 px-3">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <DropdownMenuItem 
                  onClick={() => table.getColumn('billStatus')?.setFilterValue('PAID')}
                >
                  Show Paid Bills
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => table.getColumn('billStatus')?.setFilterValue('UNPAID')}
                >
                  Show Unpaid Bills
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => table.getColumn('billStatus')?.setFilterValue('overdue')}
                >
                  Show Overdue Bills
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleClearFilters}
                >
                  Clear Filters
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <div className="overflow-hidden rounded-lg border border-border bg-background/50 backdrop-blur-sm">
            <Table>
              {/* Table Header */}
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        style={{ width: `${(header.column.columnDef as any).size || 100}px` }}
                        className={cn(
                          (header.column.id === 'amount' || header.column.id === 'actions') && 'text-right'
                        )}
                      >
                        {header.isPlaceholder ? null : (
                          <div className={cn(
                            "flex items-center gap-1",
                            header.column.getCanSort() && "cursor-pointer select-none",
                            header.column.id === 'amount' && "justify-end",
                          )}>
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <ColumnSortIndicator column={header.column} />
                            )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              
              {/* Table Body */}
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={(e) => {
                        // Check if the click is on an element that has data-no-navigate
                        const target = e.target as HTMLElement;
                        const shouldPrevent = target.closest('[data-no-navigate]');
                        if (!shouldPrevent) {
                          // Navigate to bill details
                          onEditBill(row.original._id);
                        }
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            cell.column.id === 'amount' && 'text-right',
                            cell.column.id === 'select' && 'ps-1',
                            cell.column.id === 'actions' && 'py-0',
                            'align-middle'
                          )}
                          // This ensures checkbox clicks won't navigate
                          {...(cell.column.id === 'select'
                            ? { 'data-no-navigate': 'true' }
                            : {})}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={table.getAllColumns().length}
                      className="h-24 text-center"
                    >
                      No bills found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
              
              {/* Table Footer */}
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length - 1}>
                    {table.getFilteredRowModel().rows.length} of {bills.length} bills
                  </TableCell>
                  <TableCell className="text-right">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD'
                    }).format(
                      table
                        .getFilteredRowModel()
                        .rows.reduce((total, row) => total + row.original.amount, 0)
                    )}
                  </TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground">
              Showing{' '}
              <strong>
                {table.getRowModel().rows.length ? pagination.pageIndex * pagination.pageSize + 1 : 0}
              </strong>{' '}
              to{' '}
              <strong>
                {Math.min(
                  (pagination.pageIndex + 1) * pagination.pageSize,
                  table.getFilteredRowModel().rows.length
                )}
              </strong>{' '}
              of <strong>{table.getFilteredRowModel().rows.length}</strong> results
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous Page</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        </div>
      </div>
    </CollectionCard>
  );
}

// =======================================================
// Sub-Components
// =======================================================
// Column sort indicator
function ColumnSortIndicator({ column }: { column: any }) {
  return {
    asc: <ChevronUp className="shrink-0 opacity-60" size={16} />,
    desc: <ChevronDown className="shrink-0 opacity-60" size={16} />
  }[column.getIsSorted() as string] ?? null;
}

// Status badge with popover for changing status
function StatusBadge({ 
  status, 
  uiStatus,
  billId,
  onUpdateStatus
}: { 
  status: BillStatus | string, 
  uiStatus: 'paid' | 'pending' | 'overdue',
  billId: Id<'bills'>,
  onUpdateStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<boolean>
}) {
  const statuses: BillStatus[] = ['PAID', 'UNPAID', 'PARTIALLY_PAID', 'SCHEDULED', 'IN_PROCESS', 'UNDEFINED'];
  
  const handleStatusChange = async (newStatus: BillStatus) => {
    if (onUpdateStatus) {
      await onUpdateStatus(billId, newStatus);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="inline-block cursor-pointer" data-no-navigate="true">
          <Badge
            variant="outline"
            className={cn(
              "px-2 py-0.5 capitalize font-normal transition-colors",
              getBadgeColorForStatus(uiStatus)
            )}
          >
            {status}
          </Badge>
        </div>
      </PopoverTrigger>
      {onUpdateStatus && (
        <PopoverContent className="w-auto p-2" align="start">
          <div className="space-y-2">
            <p className="text-sm font-medium">Change Status</p>
            <div className="grid grid-cols-1 gap-2">
              {statuses.map((statusOption) => (
                <Button
                  key={statusOption}
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "justify-start px-2",
                    status === statusOption && "bg-muted"
                  )}
                  onClick={() => handleStatusChange(statusOption)}
                >
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={cn(
                        "px-2 py-0.5 capitalize font-normal",
                        getBadgeColorForStatus(statusOption)
                      )}
                    >
                      {statusOption}
                    </Badge>
                    {status === statusOption && (
                      <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                    )}
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </PopoverContent>
      )}
    </Popover>
  );
}

// Row actions
function RowActions({ 
  bill,
  onEditBill,
  onDeleteBills,
  onUpdateStatus
}: { 
  bill: UIBill,
  onEditBill: (billId: Id<'bills'>) => void,
  onDeleteBills?: (billIds: Id<'bills'>[]) => Promise<boolean>,
  onUpdateStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<boolean>
}) {
  const [isDeleting, setIsDeleting] = useState(false);
  
  const handleEdit = () => {
    onEditBill(bill._id);
  };
  
  const handleDelete = async () => {
    if (!onDeleteBills) return;
    
    setIsDeleting(true);
    try {
      await onDeleteBills([bill._id]);
    } finally {
      setIsDeleting(false);
    }
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 data-[state=open]:bg-muted"
          data-no-navigate="true"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </DropdownMenuItem>
        {onDeleteBills && (
          <DropdownMenuItem
            onClick={handleDelete}
            disabled={isDeleting}
            className="text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Deleting...' : 'Delete'}
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
