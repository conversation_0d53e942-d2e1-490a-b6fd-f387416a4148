'use client';

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable
} from '@tanstack/react-table';
import { format } from 'date-fns';
import { ArrowUpDown, AlertTriangle } from 'lucide-react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CategoryDropdown, SelectionType } from '@/components/category-dropdown';
import { useToast } from '@/components/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Button } from '@/components/ui/button';

/**
 * Helper function to format numbers as US currency.
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
}

/**
 * Helper function to format a timestamp into "MM-dd-yyyy".
 */
function formatDate(timestamp: number): string {
  const date = new Date(timestamp);
  return format(date, 'MM-dd-yyyy');
}

/**
 * Shape of a single line item as returned from Convex.
 */
interface LineItem {
  _id: Id<'lineItems'>;
  _creationTime: number;
  bill_id: Id<'bills'>;
  amount: number;
  post_date: number;
  merchant_name: string;
  description: string;
  spending_category: Id<'tags'> | undefined;
  vendor_id: Id<'organizations'>;
  updated_at: number;
}

/**
 * Shape of a single category record.
 */
interface Category {
  _id: Id<'tags'>;
  name: string;
  tag_type: Id<'tag_types'>;
  children?: Array<{
    _id: Id<'tags'>;
    name: string;
    tag_type: Id<'tag_types'>;
  }>;
}

/**
 * Props for the BillLineItemsTable component.
 */
interface BillLineItemsTableProps {
  billId: Id<'bills'>;
  totalAmount: number;
  categories: Category[];
  isRefreshing?: boolean;
}

/**
 * Optimized BillLineItemsTable Component
 *
 * Renders a table of line items with support for sorting and optimistic category updates.
 * Performance improvements:
 * - Table columns are memoized.
 * - The CategoryDropdown is wrapped in a memoized CategoryCell to prevent unnecessary re-renders.
 * - Event handlers are memoized with useCallback.
 */
export function BillLineItemsTable({
  billId,
  totalAmount,
  categories,
  isRefreshing = false
}: BillLineItemsTableProps) {
  const { toast } = useToast();

  // Real-time data fetch for line items using Convex.
  const fetchedLineItems = useQuery(api.lineItems.getLineItemsByBill, { bill_id: billId }) || [];

  // Local state for sorting and optimistic updates.
  const [sorting, setSorting] = useState<SortingState>([]);
  const [optimisticLineItems, setOptimisticLineItems] = useState<Record<Id<'lineItems'>, Id<'tags'> | undefined>>({});

  // Mutation for updating the line item's category on the server.
  const updateLineItem = useMutation(api.lineItems.updateLineItem);

  // Merge fetched line items with optimistic updates.
  const lineItemsWithOptimisticUpdates = useMemo(() => {
    return fetchedLineItems.map((item) => ({
      ...item,
      spending_category: optimisticLineItems[item._id] ?? item.spending_category
    }));
  }, [fetchedLineItems, optimisticLineItems]);

  // Clear optimistic updates when fresh data is received.
  useEffect(() => {
    setOptimisticLineItems({});
  }, [fetchedLineItems]);

  // Compute the subtotal for line items.
  const actualSubtotal = useMemo(() => {
    return lineItemsWithOptimisticUpdates.reduce((sum, item) => sum + item.amount, 0);
  }, [lineItemsWithOptimisticUpdates]);

  // Check if the subtotal differs from the total bill amount.
  const hasDiscrepancy = Math.abs(totalAmount - actualSubtotal) > 0.01;

  // Memoized handler to update a line item's category with optimistic UI.
  const handleCategoryChange = useCallback(async (
    lineItemId: Id<'lineItems'>,
    categoryId: Id<'tags'> | null,
    selectionType: SelectionType
  ) => {
    const currentLineItem = fetchedLineItems.find((item) => item._id === lineItemId);
    const optimisticCategoryId = categoryId === null ? undefined : categoryId;

    // Apply optimistic update.
    setOptimisticLineItems((prev) => ({
      ...prev,
      [lineItemId]: optimisticCategoryId
    }));

    try {
      await updateLineItem({
        id: lineItemId,
        updates: { spending_category: categoryId }
      });
      toast({
        title: 'Category Updated',
        description: `Updated ${currentLineItem?.merchant_name || 'line item'} category`,
        duration: 2000
      });
    } catch (error) {
      // Revert optimistic update on failure.
      setOptimisticLineItems((prev) => {
        const { [lineItemId]: _, ...rest } = prev;
        return rest;
      });
      toast({
        variant: 'destructive',
        title: 'Failed to Update Category',
        description: `Could not update category for ${currentLineItem?.merchant_name || 'line item'}`,
        duration: 3000
      });
    }
  }, [fetchedLineItems, updateLineItem, toast]);

  /**
   * Memoized CategoryCell Component
   *
   * Wraps the CategoryDropdown to ensure it only re-renders when its effective category changes.
   */
  const CategoryCell = useMemo(() => {
    const Cell = React.memo(
      ({
        lineItem,
        currentCategory
      }: {
        lineItem: LineItem;
        currentCategory: Id<'tags'> | undefined;
      }) => {
        const onChange = useCallback((catId: Id<"tags"> | null | undefined, selectionType: SelectionType) => {
          if (catId !== undefined) {
            handleCategoryChange(lineItem._id, catId, selectionType);
          }
        }, [lineItem._id, handleCategoryChange]);

        return (
          <div className="w-[150px] text-xs">
            <CategoryDropdown
              lineItem={{ _id: lineItem._id, spending_category: currentCategory }}
              categories={categories}
              onCategoryChangeAction={onChange}
            />
          </div>
        );
      },
      (prevProps, nextProps) => prevProps.currentCategory === nextProps.currentCategory
    );
    return Cell;
  }, [handleCategoryChange, categories]);

  // Define table columns and memoize them to avoid unnecessary re-creations.
  const columns = useMemo<ColumnDef<LineItem>[]>(() => [
    {
      accessorKey: 'post_date',
      header: ({ column }) => (
        <div className="w-16">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 text-xs font-medium"
          >
            Date
            <ArrowUpDown className="ml-2 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <div className="h-6 flex items-center gap-1 text-xs whitespace-nowrap">
          {formatDate(row.getValue('post_date'))}
        </div>
      )
    },
    {
      accessorKey: 'merchant_name',
      header: ({ column }) => (
        <div className="w-28">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 text-xs font-medium"
          >
            Merchant
            <ArrowUpDown className="ml-2 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <div className="h-6 flex items-center text-xs">
          <span className="truncate max-w-[150px]" title={row.getValue('merchant_name')}>
            {row.getValue('merchant_name')}
          </span>
        </div>
      )
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <div className="w-25">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 text-xs font-medium"
          >
            Description
            <ArrowUpDown className="ml-2 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <div className="h-6 flex items-center text-xs">
          <span className="truncate max-w-[140px]" title={row.getValue('description')}>
            {row.getValue('description')}
          </span>
        </div>
      )
    },
    {
      accessorKey: 'spending_category',
      header: () => <div className="text-xs font-medium">Category</div>,
      cell: ({ row }) => {
        const item = row.original;
        const effectiveCategory = optimisticLineItems[item._id] ?? item.spending_category;
        return <CategoryCell lineItem={item} currentCategory={effectiveCategory} />;
      }
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => (
        <div className="w-16">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 text-xs font-medium px-1"
          >
            Amount
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue('amount'));
        return (
          <div className="h-6 flex items-center text-xs whitespace-nowrap px-1">
            {formatCurrency(amount)}
          </div>
        );
      }
    }
  ], [optimisticLineItems, CategoryCell]);

  // Set up the react-table instance.
  const table = useReactTable({
    data: lineItemsWithOptimisticUpdates,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: { sorting }
  });

  return (
    <div className="w-full">

      {/* Alert for any discrepancy between line items total and bill amount */}
      {hasDiscrepancy && (
        <div className="py-2 px-4">
          <Alert variant="destructive" className="mb-2">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Amount Discrepancy</AlertTitle>
            <AlertDescription className="break-normal">
              The total of line items ({formatCurrency(actualSubtotal)}) does not match the bill amount ({formatCurrency(totalAmount)}). The difference is {formatCurrency(Math.abs(totalAmount - actualSubtotal))}.
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="w-full overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="py-1">
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-1">
                      <div className="text-xs">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No line items found.
                </TableCell>
              </TableRow>
            )}
            <TableRow className="bg-muted/50 font-medium">
              <TableCell colSpan={4} className="text-right py-1 text-xs">
                Subtotal
              </TableCell>
              <TableCell className="py-1 text-xs">
                {formatCurrency(actualSubtotal)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default BillLineItemsTable;