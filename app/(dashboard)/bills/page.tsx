'use client';

import { useState, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Id } from '@/convex/_generated/dataModel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CreateBillModal } from './components/CreateBillModal';
import { PriorityBills } from '@/components/priority-bills';
import { useBills } from './hooks/useBills';
import { BillsTable } from './components/BillsTable';

// Create a client component that uses useSearchParams
function BillsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Tab definitions - moved to top
  const tabConfigs = [
    { label: 'All Bills', value: 'all', filter: () => true },
    {
      label: 'Overdue',
      value: 'overdue',
      filter: (bill: any) => {
        const dueDateObj = bill.dueDate ? new Date(bill.dueDate) : null;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // Explicitly return a boolean to satisfy TypeScript
        return Boolean(dueDateObj && dueDateObj <= today && bill.billStatus !== 'PAID');
      },
    },
    {
      label: 'Pending',
      value: 'pending',
      filter: (bill: any) => {
        const dueDateObj = bill.dueDate ? new Date(bill.dueDate) : null;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // Explicitly return a boolean to satisfy TypeScript
        return Boolean(!dueDateObj || dueDateObj > today || bill.billStatus === 'PAID');
      },
    },
    { label: 'Paid', value: 'paid', filter: (bill: any) => bill.billStatus === 'PAID' },
  ];

  // State declarations
  const [activeTab, setActiveTab] = useState(tabConfigs[0].value);
  const [selectedBills, setSelectedBills] = useState<Id<'bills'>[]>([]);

  // Use our custom hook to get all bills data and operations
  const { 
    bills, 
    priorityBills, 
    isLoading, 
    operations,
    getFilteredBills
  } = useBills();

  // Get filtered bills based on active tab
  // Cast the filter function to ensure TypeScript understands it always returns boolean
  const activeFilter = tabConfigs.find((tab) => tab.value === activeTab)?.filter || (() => true);
  const filteredBills = getFilteredBills(activeFilter as (bill: any) => boolean);

  const handleCloseModal = useCallback(() => {
    router.replace('/bills');
  }, [router]);

  const handleOpenEditModal = useCallback(
    (billId: Id<'bills'>) => {
      // Navigate to bill details page
      router.push(`/bills/${billId}`);
    },
    [router]
  );

  // URL-based modal open/close
  const isModalOpen = searchParams.get('new') === 'true';

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[200px]">
        Loading...
      </div>
    );
  }

  return (
    <div className="space-y-10">
      {/* Priority Bills */}
      {priorityBills.length > 0 && (
        <PriorityBills
          bills={priorityBills}
          onProcessBill={handleOpenEditModal}
          onViewDetails={(billId) => router.push(`/bills/${billId}`)}
          onUpdateStatus={(billId, status) => {
            // Wrapper function to convert Promise<boolean> to Promise<void>
            operations.updateStatus(billId, status);
            return Promise.resolve();
          }}
        />
      )}

      {/* Bill Management Table */}
      <Tabs defaultValue="all" className="space-y-2" onValueChange={setActiveTab}>
        <div className="w-full overflow-x-auto">
          <div className="flex items-center justify-start">
            <TabsList>
              {tabConfigs.map((tab) => (
                <TabsTrigger key={tab.value} value={tab.value}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </div>

        {tabConfigs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            <BillsTable
              bills={filteredBills}
              selectedBills={selectedBills}
              onSelectionChange={setSelectedBills}
              onSyncBills={operations.syncBills}
              onEditBill={handleOpenEditModal}
              onDeleteBills={operations.deleteBills}
              onUpdateStatus={operations.updateStatus}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create Bill Modal */}
      <CreateBillModal open={isModalOpen} onOpenChange={handleCloseModal} />
    </div>
  );
}

export default function BillsPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-[200px]">Loading...</div>}>
      <BillsPageContent />
    </Suspense>
  );
}
