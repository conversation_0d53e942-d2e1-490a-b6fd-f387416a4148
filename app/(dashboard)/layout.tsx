import type { Metada<PERSON> } from 'next';
import { GeistSans } from 'geist/font/sans';
import { GeistMono } from 'geist/font/mono';
import { Analytics } from '@vercel/analytics/react';
import { Toaster } from '@/components/ui/toaster';
import { NavigationBar } from '@/components/navigation-bar';
import { HeaderBar } from '@/components/header-bar';
import { UserDataProvider } from '@/components/user-data-provider';

// Use Geist Sans as default with fallback to system fonts
const fontSans = GeistSans;
const fontMono = GeistMono;

export const metadata: Metadata = {
  title: 'Fojo',
  description: 'Operationalize Excellence'
};

/**
 * Dashboard Layout
 *
 * This layout wraps all dashboard pages with the NavigationMenu and Header
 * to provide navigation for dashboard pages.
 */
export default function DashboardLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <UserDataProvider>
      <div className="min-h-screen">
        {/* Header Bar - Now at the top level */}
        <HeaderBar />
        
        <div className="flex min-h-[calc(100vh-3.5rem)]">
          {/* Navigation Bar */}
          <NavigationBar />
          
          {/* Main Content Area */}
          {/* Set fixed margin based on collapsed width (54px) to prevent content shift */}
          <div className="flex-1 ml-[100px]">
            {/* Main Content */}
            <main className="mt-8 min-h-[calc(100vh-3.5rem)] w-full pr-10">
              <div className="p-1 min-h-full">
                {children}
              </div>
            </main>
          </div>
        </div>
        
        <Toaster />
        <Analytics />
      </div>
    </UserDataProvider>
  );
}
