import React, { useState, use<PERSON><PERSON>back, JS<PERSON> } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import Modal from '@/components/ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Integration, UpdateIntegrationInput } from '@/zod/integrations-schema';
import { useToast } from '@/components/hooks/use-toast';
import { AlertCircle, CheckCircle2, Info, Lock, ExternalLink } from 'lucide-react';
import { cn } from '@/components/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON>ertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export interface IntegrationSetupModalProps {
  onClose: () => void;
  integration: Integration;
}

type ConfigField = {
  type: string;
  required: boolean;
  description?: string;
  sensitive?: boolean;
  default?: string;
};

export function IntegrationSetupModal({ 
  onClose, 
  integration 
}: IntegrationSetupModalProps): JSX.Element {
  const [formData, setFormData] = useState<UpdateIntegrationInput>({
    user_config: integration.user_config ? { ...integration.user_config } : {}
  });
  const [showValidation, setShowValidation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testSuccess, setTestSuccess] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);

  const { toast } = useToast();
  const updateIntegration = useMutation(api.integrations.integrations.updateIntegration);
  const deleteIntegration = useMutation(api.integrations.integrations.deleteIntegration);

  const getRequiredFields = (): string[] => {
    if (!integration.expected_config) return [];
    return Object.entries(integration.expected_config)
      .filter(([_, config]) => (config as ConfigField).required)
      .map(([key]) => key);
  };

  const hasAllRequiredFields = (): boolean => {
    const requiredFields = getRequiredFields();
    if (requiredFields.length === 0) return true;

    return (
      formData.user_config !== undefined &&
      requiredFields.every(
        (field) =>
          formData.user_config![field] !== undefined &&
          String(formData.user_config![field]).trim() !== ''
      )
    );
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('user_config.')) {
      const configKey = name.replace('user_config.', '');
      setFormData(prev => ({
        ...prev,
        user_config: {
          ...prev.user_config,
          [configKey]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    if (testSuccess) {
      setTestSuccess(false);
    }
  };

  const handleTestSetup = useCallback(async () => {
    console.log("Test Connection button clicked");
    setShowValidation(true);
    setIsTesting(true);

    try {
      console.log("Required fields check:", hasAllRequiredFields());
      if (!hasAllRequiredFields()) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive"
        });
        setIsTesting(false);
        return;
      }

      // Get the Convex HTTP URL for the action
      // Try different ways to get the Convex URL
      console.log("Environment variables:", {
        NEXT_PUBLIC_CONVEX_URL: process.env.NEXT_PUBLIC_CONVEX_URL,
        window_convexUrl: typeof window !== 'undefined' ? (window as any).__CONVEX_URL : undefined
      });
      
      // Get the actual Convex URL from the environment
      // Note: According to the HTTP actions guide, HTTP actions are exposed at
      // https://<your deployment name>.convex.site, not .convex.cloud
      const deploymentName = "avid-wildebeest-896";
      const convexUrl = `https://${deploymentName}.convex.site`;
      console.log("Using Convex URL:", convexUrl);
      
      // Construct the full URL for the HTTP action
      const actionUrl = `${convexUrl}/billCom/login`;
      console.log("Action URL:", actionUrl);

      // Prepare the request body
      // In bill_dotcom.ts, the billComLogin function expects a "password" field
      const requestBody = {
        devKey: formData.user_config?.devKey ?? integration.expected_config?.devKey?.default,
        username: formData.user_config?.apiUserName ?? integration.expected_config?.apiUserName?.default,
        password: formData.user_config?.apiPasswordOrToken ?? integration.expected_config?.apiPasswordOrToken?.default,
        organizationId: formData.user_config?.orgId ?? integration.expected_config?.orgId?.default,
        rememberMeId: formData.user_config?.rememberMeId,
        device: formData.user_config?.device,
      };
      
      console.log("Request body keys:", Object.keys(requestBody));

      // Validate required fields
      if (!requestBody.devKey || !requestBody.username || !requestBody.password) {
        throw new Error("Missing required credentials. Please check all required fields.");
      }

      // Log request details (excluding sensitive info)
      console.log('Test setup request:', {
        url: actionUrl,
        hasDevKey: !!requestBody.devKey,
        hasUsername: !!requestBody.username,
        hasPassword: !!requestBody.password,
        hasOrgId: !!requestBody.organizationId,
      });

      const response = await fetch(actionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
      }

      let result;
      try {
        result = await response.json();
      } catch (e) {
        throw new Error("Failed to parse response as JSON");
      }

      // Validate response
      if (!result) {
        throw new Error("Empty response received");
      }

      console.log('Parsed response:', result);

      if (result.success && result.data && result.data.sessionId) {
        setTestSuccess(true);
        toast({
          title: "Connection Successful",
          description: "Successfully connected to Bill.com and retrieved a session ID.",
        });
      } else {
        const errorMessage = result.error ?? result.data?.[0]?.message ?? 'Unknown error';
        setTestSuccess(false);
        console.error("Error result:", result);
        toast({
          title: "Test Failed",
          description: `Could not establish connection: ${errorMessage}`,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      setTestSuccess(false);
      
      // Enhanced error logging
      const errorDetails = {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
        response: error?.response,
        status: error?.status,
        statusText: error?.statusText,
      };
      
      console.error("Error in handleTestSetup:", errorDetails);
      
      // More descriptive error message for the user
      const errorMessage = error?.message || 'An unexpected error occurred while testing the connection';
      toast({
        title: "Test Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  }, [formData, integration, toast, hasAllRequiredFields]);

  const handleSubmit = async () => {
    setShowValidation(true);
    setIsSubmitting(true);
    
    if (!hasAllRequiredFields()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }
    
    try {
      await updateIntegration({
        integrationId: integration._id,
        data: {
          ...formData,
          status: integration.status === 'NEEDS_SETUP' && hasAllRequiredFields() 
            ? 'INACTIVE' 
            : undefined
        }
      });
      
      toast({
        title: "Success",
        description: "Integration updated successfully"
      });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update integration",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemove = async () => {
    try {
      await deleteIntegration({ integrationId: integration._id });
      toast({
        title: "Success",
        description: "Integration removed successfully"
      });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove integration",
        variant: "destructive"
      });
    } finally {
      setShowRemoveDialog(false);
    }
  };

  const formatFieldName = (key: string): string => {
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/_/g, ' ');
  };

  const renderConfigFields = () => {
    if (!integration.expected_config || Object.keys(integration.expected_config).length === 0) {
      return (
        <div className="flex items-center justify-center p-4 rounded-lg bg-muted/30">
          <Info className="h-4 w-4 mr-2 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            No configuration required for this integration.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {Object.entries(integration.expected_config).map(([key, config]) => {
          const fieldConfig = config as ConfigField;
          const fieldId = `user_config.${key}`;
          const fieldValue = formData.user_config?.[key] !== undefined 
            ? formData.user_config[key] 
            : fieldConfig.default || '';
          const isRequired = fieldConfig.required;
          const isSensitive = fieldConfig.sensitive;
          const showError = showValidation && isRequired && !fieldValue;
          
          return (
            <div key={key} className="space-y-1.5">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Label htmlFor={fieldId} className="text-sm font-medium">
                    {formatFieldName(key)}
                    {isRequired && <span className="text-red-500 ml-0.5">*</span>}
                  </Label>
                  
                  {fieldConfig.description && (
                    <TooltipProvider>
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Info className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <p className="text-xs">{fieldConfig.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  
                  {isSensitive && (
                    <TooltipProvider>
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Lock className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="text-xs">Sensitive information</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
                
                {showError && (
                  <div className="flex items-center text-xs text-red-500 font-medium">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Required
                  </div>
                )}
              </div>
              
              <div className="relative">
                <Input
                  id={fieldId}
                  name={fieldId}
                  type={isSensitive ? "password" : "text"}
                  value={fieldValue}
                  onChange={handleChange}
                  placeholder={fieldConfig.default || `Enter ${formatFieldName(key).toLowerCase()}`}
                  autoComplete="off"
                  autoCapitalize="off"
                  autoCorrect="off"
                  spellCheck="false"
                  data-lpignore="true"
                  data-1p-ignore="true"
                  className={cn(
                    "transition-all duration-200",
                    showError ? "border-red-300 ring-red-100" : "",
                    "pr-8"
                  )}
                />
                
                {testSuccess && fieldValue && (
                  <CheckCircle2 className="h-4 w-4 text-green-500 absolute right-2.5 top-2.5" />
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const getCtaButtonText = () => {
    if (isSubmitting) return "Processing...";
    return integration.status === 'NEEDS_SETUP' ? "Complete Setup" : "Save Changes";
  };

  const getSecondaryButtonText = () => {
    if (isTesting) return "Testing...";
    if (testSuccess) return "Verified";
    return "Test Connection";
  };

  const handleCtaClick = () => {
    if (isSubmitting || isTesting) return;
    handleSubmit();
  };

  const handleSecondaryClick = () => {
    if (isSubmitting || isTesting || !hasAllRequiredFields()) return;
    handleTestSetup();
  };

  const statusBadge = (
    <Badge 
      variant={integration.status === 'ACTIVE' ? "default" : 
              integration.status === 'INACTIVE' ? "outline" : 
              integration.status === 'ERROR' ? "destructive" : "secondary"}
      className="px-2 py-0.5 text-xs font-medium"
    >
      {integration.status.charAt(0) + integration.status.slice(1).toLowerCase()}
    </Badge>
  );

  return (
    <Modal
      heading={`Configure ${integration.display_name || 'Integration'}`}
      heading_badge={statusBadge}
      subheading={integration.description || (
        integration.status === 'NEEDS_SETUP' 
          ? "Set up this integration to connect with your external service" 
          : "Update the settings for this integration"
      )}
      onClose={onClose}
      ctaButton={{
        text: getCtaButtonText(),
        onClick: handleCtaClick
      }}
      secondaryCta={{
        text: getSecondaryButtonText(),
        onClick: handleSecondaryClick
      }}
    >
      <form autoComplete="off" onSubmit={(e) => e.preventDefault()} className="space-y-5">
        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => setShowRemoveDialog(true)}
            className="text-xs text-red-500 hover:text-red-600 transition-colors"
          >
            Remove Integration
          </button>
          <a 
            href="#" 
            className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1 transition-colors"
            onClick={(e) => e.preventDefault()}
          >
            <span>Documentation</span>
            <ExternalLink className="h-3 w-3" />
          </a>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className="h-5 w-1 bg-primary rounded-full" />
            <h4 className="text-sm font-medium">Configuration Details</h4>
          </div>
          
          {renderConfigFields()}
          
          {getRequiredFields().length > 0 && (
            <p className="text-xs text-muted-foreground flex items-center gap-1.5 mt-2">
              <span className="text-red-500">*</span> 
              <span>Required fields</span>
            </p>
          )}
        </div>
        
        {testSuccess && (
          <div className="flex items-center p-3 rounded-lg bg-green-50 border border-green-100 text-green-700 text-sm">
            <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>Connection verified successfully. You can now complete the setup.</span>
          </div>
        )}
      </form>

      <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently remove the {integration.display_name || 'integration'} and all its configuration.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemove}
              className="bg-destructive hover:bg-destructive/90"
            >
              Remove Integration
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Modal>
  );
}
