import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle2, Cog, PauseCircle } from 'lucide-react';
import { Integration } from '@/zod/integrations-schema';
import { IntegrationSetupModal } from './IntegrationSetupModal';
import type { IntegrationSetupModalProps } from './IntegrationSetupModal';
import { useToast } from '@/components/hooks/use-toast';

export interface IntegrationCardProps {
  integration: Integration;
}

/**
 * Card component for displaying an integration with status indicators
 * Shows visual cues for setup status and allows toggling active state
 */
export function IntegrationCard({ integration }: IntegrationCardProps) {
  const [isSetupOpen, setIsSetupOpen] = useState(false);
  const { toast } = useToast();

  const toggleStatus = useMutation(api.integrations.integrations.updateIntegration);

  const handleToggleStatus = async () => {
    try {
      await toggleStatus({ 
        integrationId: integration._id,
        data: {
          status: integration.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
        }
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update integration status",
        variant: "destructive"
      });
    }
  };
  
  // Get status icon based on integration status
  const getStatusIcon = () => {
    switch (integration.status) {
      case 'ACTIVE':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'INACTIVE':
        return <PauseCircle className="h-5 w-5 text-gray-500" />;
      case 'NEEDS_SETUP':
        return <AlertCircle className="h-5 w-5 text-amber-500" />;
      case 'ERROR':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };
  
  // Get status badge variant based on integration status
  const getStatusBadgeVariant = () => {
    switch (integration.status) {
      case 'ACTIVE':
        return 'default';
      case 'INACTIVE':
        return 'secondary';
      case 'NEEDS_SETUP':
        return 'outline';
      case 'ERROR':
        return 'destructive';
      default:
        return 'outline';
    }
  };
  
  // Determine if the integration needs setup
  const needsSetup = integration.status === 'NEEDS_SETUP';
  
  return (
    <>
      <Card className="transition-all duration-200">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <div>
              <CardTitle>{integration.display_name || 'Unnamed Integration'}</CardTitle>
              <CardDescription className="capitalize">{integration.immutable_slug.replace(/-/g, ' ')}</CardDescription>
            </div>
          </div>
          <Badge variant={getStatusBadgeVariant()}>
            {integration.status}
          </Badge>
        </CardHeader>
        <CardContent>
          {integration.description && (
            <p className="text-sm text-muted-foreground mb-4">{integration.description}</p>
          )}
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">
              Last updated: {integration.updated_at ? new Date(integration.updated_at).toLocaleDateString() : 'N/A'}
            </span>
            {integration.status !== 'NEEDS_SETUP' && (
              <div className="flex items-center space-x-2">
                <span className="text-sm">Active</span>
                <Switch 
                  checked={integration.status === 'ACTIVE'} 
                  onCheckedChange={handleToggleStatus}
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            variant={needsSetup ? "default" : "outline"} 
            size="sm" 
            onClick={() => setIsSetupOpen(true)}
            className={needsSetup ? "animate-pulse" : ""}
          >
            <Cog className="mr-2 h-4 w-4" />
            {needsSetup ? 'Setup Now' : 'Configure'}
          </Button>
        </CardFooter>
      </Card>
      
      {isSetupOpen && (
        <IntegrationSetupModal
          onClose={() => setIsSetupOpen(false)}
          integration={integration}
        />
      )}
    </>
  );
}
