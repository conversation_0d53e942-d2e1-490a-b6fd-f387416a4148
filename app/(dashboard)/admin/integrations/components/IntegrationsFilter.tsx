import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface IntegrationsFilterProps {
  onFilterChange: (query: string) => void;
}

/**
 * Component for filtering integrations by name or description
 * Provides a search input with debounced filtering
 */
export function IntegrationsFilter({ onFilterChange }: IntegrationsFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Debounce search input to avoid excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      onFilterChange(searchQuery);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery, onFilterChange]);
  
  return (
    <div className="relative w-full max-w-sm">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder="Search integrations..."
        className="pl-8 w-full"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
    </div>
  );
}
