import { useState, useEffect } from 'react';
import { Integration } from '@/zod/integrations-schema';
import { IntegrationCard } from './IntegrationCard';
import { IntegrationsFilter } from './IntegrationsFilter';

interface IntegrationsListProps {
  integrations: Integration[];
}

/**
 * Component for displaying a grid of integration cards with filtering
 * Handles client-side filtering of integrations by name, description, or type
 */
export function IntegrationsList({ integrations }: IntegrationsListProps) {
  const [filterQuery, setFilterQuery] = useState('');
  // Sort integrations by status: ACTIVE first, then INACTIVE, then NEEDS_SETUP
  const sortedIntegrations = integrations.sort((a, b) => {
    const statusOrder = { ACTIVE: 1, INACTIVE: 2, NEEDS_SETUP: 3, ERROR: 4 };
    return statusOrder[a.status] - statusOrder[b.status];
  });

  const [filteredIntegrations, setFilteredIntegrations] = useState<Integration[]>(sortedIntegrations);
  
  // Update filtered integrations when filter query changes
  useEffect(() => {
    if (!filterQuery.trim()) {
      setFilteredIntegrations(integrations);
      return;
    }
    
    const query = filterQuery.toLowerCase();
    const filtered = integrations.filter(integration => 
      (integration.display_name && integration.display_name.toLowerCase().includes(query)) ||
      (integration.description && integration.description.toLowerCase().includes(query)) ||
      integration.immutable_slug.toLowerCase().includes(query)
    );
    
    setFilteredIntegrations(filtered);
  }, [filterQuery, integrations]);
  
  return (
    <div className="space-y-6">
      <IntegrationsFilter onFilterChange={setFilterQuery} />
      
      {filteredIntegrations.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No integrations match your search</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIntegrations.map((integration) => (
            <IntegrationCard 
              key={integration._id} 
              integration={integration} 
            />
          ))}
        </div>
      )}
    </div>
  );
}
