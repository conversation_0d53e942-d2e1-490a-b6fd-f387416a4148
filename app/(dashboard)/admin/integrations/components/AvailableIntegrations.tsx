import { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Integration } from '@/zod/integrations-schema';
import { IntegrationCard } from './IntegrationCard';
import { useToast } from '@/components/hooks/use-toast';
import { CollectionCard } from '@/components/ui/collection-card';

interface AvailableIntegrationsProps {
  filterQuery: string;
}

/**
 * Component for displaying all available integrations
 * Shows canonical integrations and handles filtering
 * Separates integrations that have been set up from those that need to be set up
 */
export function AvailableIntegrations({ filterQuery }: AvailableIntegrationsProps) {
  const { toast } = useToast();
  const integrations = useQuery(api.integrations.integrations.listIntegrations, {});
  const createCanonicalIntegrations = useMutation(api.seeds.seed_integrations.createCanonicalIntegrations);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize canonical integrations if none exist
  useEffect(() => {
    const initializeIntegrations = async () => {
      if (integrations && integrations.length === 0) {
        try {
          setIsLoading(true);
          await createCanonicalIntegrations({});
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to initialize integrations",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };
    
    initializeIntegrations();
  }, [integrations, createCanonicalIntegrations, toast]);
  
  // Filter integrations based on the filter query
  const filteredIntegrations = integrations ? integrations.filter((integration: Integration) => {
    if (!filterQuery.trim()) return true;

    const query = filterQuery.toLowerCase();
    return (
      (integration.display_name && integration.display_name.toLowerCase().includes(query)) ||
      (integration.description && integration.description.toLowerCase().includes(query)) ||
      integration.immutable_slug.toLowerCase().includes(query)
    );
  }) : [];

  // Separate integrations into set up and available
  const setupIntegrations = filteredIntegrations.filter(
    (integration: Integration) => integration.status !== 'NEEDS_SETUP'
  );

  const availableIntegrations = filteredIntegrations.filter(
    (integration: Integration) => integration.status === 'NEEDS_SETUP'
  );

  const hasSetupIntegrations = setupIntegrations.length > 0;
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  if (!integrations || integrations.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No integrations available</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-8">
      {filteredIntegrations.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No integrations match your search</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Set up integrations section - only shown if there are any */}
          {hasSetupIntegrations && (
            <CollectionCard
              heading="Set Up Connections"
              subheading="Integrations that have been configured and are ready to use"
              className="mb-8"
              buttonText={undefined}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {setupIntegrations.map((integration: Integration) => (
                  <IntegrationCard 
                    key={integration._id} 
                    integration={integration} 
                  />
                ))}
              </div>
            </CollectionCard>
          )}
          
          {/* Available integrations section */}
          <CollectionCard
            heading="These integrations are available to be set up"
            subheading="Connect your digital chief of staff with these external services"
            className="mb-8"
            buttonText={undefined}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {availableIntegrations.map((integration: Integration) => (
                <IntegrationCard 
                  key={integration._id} 
                  integration={integration} 
                />
              ))}
            </div>
          </CollectionCard>
        </div>
      )}
    </div>
  );
}
