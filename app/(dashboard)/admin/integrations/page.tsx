'use client';

import { AvailableIntegrations } from './components/AvailableIntegrations';
import { CollectionCard } from '@/components/ui/collection-card';
import { IntegrationsFilter } from './components/IntegrationsFilter';
import { useState } from 'react';

/**
 * Integrations page that displays all available integrations
 * Shows canonical integrations with their setup status
 */
export default function IntegrationsPage() {
  const [filterQuery, setFilterQuery] = useState('');
  
  return (
    <div className="container mx-auto py-1">
      <div className="flex-1 p-4">
        {/* Header section in its own card */}
        <CollectionCard
          heading="Integrations"
          subheading="Connect your digital chief of staff with external services"
          className="mb-6"
          buttonText={undefined}
        >
          <IntegrationsFilter onFilterChange={setFilterQuery} />
        </CollectionCard>

        <div className="mt-4">
          <AvailableIntegrations filterQuery={filterQuery} />
        </div>
      </div>
    </div>
  );
}
