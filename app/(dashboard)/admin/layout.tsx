'use client';

import { usePathname } from 'next/navigation';
import SecondaryNavMenu from '@/components/blocks/secondary-nav-menu';


export default function AdminLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Create admin navigation items
  const adminNavItems = [
    {
      label: 'Dashboard',
      href: '/admin'
    },
    {
      label: 'Users',
      href: '/admin/users'
    },
    {
      label: 'Teams',
      href: '/admin/teams'
    },
    {
      label: 'Tags & Themes',
      href: '/admin/tags'
    },
    {
      label: 'Integrations',
      href: '/admin/integrations'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col pt-5">
      {/* Header with horizontal navigation */}
      <header>
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold tracking-tight">Admin Panel</h1>
        </div>
        <SecondaryNavMenu
          items={adminNavItems}
          activeItemClassName="font-medium"
          activeItem={pathname}
        />
      </header>

      {/* Main content area */}
      <main className="flex-1 p-5">
        <div>{children}</div>
      </main>
    </div>
  );
}
