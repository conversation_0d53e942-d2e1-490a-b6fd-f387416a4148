"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import type { User } from "@/zod/users-schema";

export default function UsersPage() {
  const result = useQuery(api.users.listUsers, { limit: 50, sortDirection: "desc" });

  if (!result) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Users</h1>
        <p className="text-muted-foreground">
          Manage and monitor all users in the system.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {result.users.map((user: User) => (
          <Card key={user._id} className="overflow-hidden">
            <CardHeader className="space-y-0 pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.image} alt={user.name || "User"} />
                  <AvatarFallback>
                    {(user.name?.charAt(0) || "U").toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {user.name || "Unnamed User"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <p className="text-muted-foreground truncate">{user.email}</p>
                <p className="text-xs text-muted-foreground mt-2">
                  Joined {formatDistanceToNow(user._creationTime)} ago
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
