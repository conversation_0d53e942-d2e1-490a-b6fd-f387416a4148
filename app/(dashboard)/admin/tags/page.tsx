"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { jaro<PERSON>inkler, normalizeTagName } from "@/components/lib/jaroWinkler";
import { Badge } from "@/components/ui/badge";
import ViewSwitcher from "@/components/blocks/view-switcher";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Info, Plus } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical } from "@/components/icons";
import { Check, X } from "lucide-react";
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CollectionCardModal } from "@/components/ui/collectionCardModal";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

/** -------------------------------------------------------------------------
 *  Types
 *  -------------------------------------------------------------------------*/

type TagId = Id<"tags">;
type TagTypeId = Id<"tag_types">;

interface Tag {
  _id: TagId;
  name: string;
  description?: string;
  tag_type: TagTypeId;
  parent_id?: TagId;
  children?: Tag[];
  color?: string;
  immutable_slug?: string;
  usage_count?: number; // Optional: for merge suggestion UI
}

interface TagType {
  _id: TagTypeId;
  name: string;
  immutable_slug: string;
}

enum ViewMode {
  CARD = "card",
  LIST = "list",
  KANBAN = "kanban", // reserved – not implemented yet
}

/** -------------------------------------------------------------------------
 *  Hooks – data fetching & persistence
 *  -------------------------------------------------------------------------*/

const useTagTypes = (): TagType[] => {
  const raw = useQuery(api.tags.listTagTypes, {})?.page ?? [];
  // Map to ensure all TagType fields are present and immutable_slug is a string
  return raw.map((tt) => ({
    _id: tt._id,
    name: tt.name,
    immutable_slug: tt.immutable_slug ?? "", // fallback to empty string if missing
  }));
};

/** Build a map for quick TagType lookup by slug */
const useTagTypeMap = (tagTypes: TagType[]): Record<string, TagType> => {
  return useMemo(() => {
    return tagTypes.reduce<Record<string, TagType>>((acc, tt) => {
      acc[tt.immutable_slug] = tt;
      return acc;
    }, {});
  }, [tagTypes]);
};

const useTagHierarchy = (tagTypeId: TagTypeId | null): Tag[] => {
  const result = useQuery(api.tags.getHierarchy, {
    params: tagTypeId ? { tag_type: tagTypeId } : {},
  });
  // If result is a single object, wrap in array; else return as is
  if (!result) return [];
  if (Array.isArray(result)) return result as Tag[];
  return [result as Tag];
};

const useFlatTags = (tagTypeId: TagTypeId | null): Tag[] => {
  const all = useQuery(api.tags.fetchTags, {
    filter: tagTypeId ? { tag_type: tagTypeId } : {},
  });
  return useMemo(() => all ?? [], [all]);
};

/** -------------------------------------------------------------------------
 *  Utilities
 *  -------------------------------------------------------------------------*/

/** LocalStorage helpers keep last‑used tag type between sessions */
const LOCAL_STORAGE_KEY = "adminTagsLastTagType";
const getStoredSlug = () => (typeof window !== "undefined" ? localStorage.getItem(LOCAL_STORAGE_KEY) : null);
const storeSlug = (slug: string) => {
  if (typeof window !== "undefined") localStorage.setItem(LOCAL_STORAGE_KEY, slug);
};

/** -------------------------------------------------------------------------
 *  Main Component
 *  -------------------------------------------------------------------------*/

const TagAdminDashboard: React.FC = () => {
  /** ---------------------- Data ---------------------- */
  const tagTypes = useTagTypes();
  const tagTypeMap = useTagTypeMap(tagTypes);

  /**
   * Default tag type selection logic:
   * 1. If a valid slug is stored in localStorage, use it.
   * 2. Otherwise, if 'general-tags' exists, use that.
   * 3. Otherwise, use the first tag type in the list.
   */
  const [selectedSlug, setSelectedSlug] = useState<string | null>(() => {
    const stored = getStoredSlug();
    if (stored && tagTypeMap[stored]) return stored;
    if (tagTypeMap["general-tags"]) return "general-tags";
    if (tagTypes.length > 0) return tagTypes[0].immutable_slug;
    return null;
  });

  /**
   * After tagTypes load, ensure selectedSlug is valid:
   * 1. If current selectedSlug is valid, do nothing.
   * 2. Else, if a valid stored slug exists, set it.
   * 3. Else, fallback to 'general-tags' or the first tag type.
   * Only update state if the new value is different from the current one.
   * This avoids infinite loops.
   */
  useEffect(() => {
    if (!tagTypes.length) return;
    // If current selectedSlug is valid, do nothing
    if (selectedSlug && tagTypeMap[selectedSlug]) return;
    // Try stored slug
    const stored = getStoredSlug();
    if (stored && tagTypeMap[stored]) {
      if (selectedSlug !== stored) setSelectedSlug(stored);
      return;
    }
    // Fallback to 'general-tags' or first tag type
    if (tagTypeMap["general-tags"]) {
      if (selectedSlug !== "general-tags") setSelectedSlug("general-tags");
    } else if (tagTypes.length > 0) {
      if (selectedSlug !== tagTypes[0].immutable_slug) setSelectedSlug(tagTypes[0].immutable_slug);
    }
  }, [tagTypes, tagTypeMap]);

  /** persist */
  useEffect(() => {
    if (selectedSlug) storeSlug(selectedSlug);
  }, [selectedSlug]);

  const selectedTagTypeId: TagTypeId | null = selectedSlug ? tagTypeMap[selectedSlug]?._id ?? null : null;

  const hierarchy = useTagHierarchy(selectedTagTypeId);
  const flatTags = useFlatTags(selectedTagTypeId);

  /** -----------------------------------------------------------------
   *  Similar Tag Detection (Jaro-Winkler Clustering)
   *  -----------------------------------------------------------------*/
  const SIMILARITY_THRESHOLD = 0.85;
  // Helper: Build clusters of similar tags (transitive closure)
  const similarTagClusters = useMemo(() => {
    if (!flatTags || flatTags.length < 2) return [];
    // Build all pairs with similarity above threshold
    const pairs: [number, number][] = [];
    for (let i = 0; i < flatTags.length; i++) {
      for (let j = i + 1; j < flatTags.length; j++) {
        const score = jaroWinkler(flatTags[i].name, flatTags[j].name);
        if (score >= SIMILARITY_THRESHOLD) {
          pairs.push([i, j]);
        }
      }
    }
    // Union-find to group indices into clusters
    const parent = Array(flatTags.length)
      .fill(0)
      .map((_, i) => i);
    function find(i: number): number {
      if (parent[i] !== i) parent[i] = find(parent[i]);
      return parent[i];
    }
    function union(i: number, j: number) {
      const pi = find(i);
      const pj = find(j);
      if (pi !== pj) parent[pi] = pj;
    }
    pairs.forEach(([i, j]) => union(i, j));
    // Group indices by root parent
    const clusters: Record<number, number[]> = {};
    for (let i = 0; i < flatTags.length; i++) {
      const root = find(i);
      if (!clusters[root]) clusters[root] = [];
      clusters[root].push(i);
    }
    // Only keep clusters with more than 1 tag
    return Object.values(clusters)
      .filter((arr) => arr.length > 1)
      .map((indices) => indices.map((i) => flatTags[i]));
  }, [flatTags]);

  /** ---------------------- Convex mutations ---------------------- */
  const saveTag = useMutation(api.tags.saveTag);
  const removeTag = useMutation(api.tags.removeTag);
  const mergeTags = useMutation(api.tags.mergeTags);

  /** ---------------------- UI state ---------------------- */
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.CARD);
  const [flatView, setFlatView] = useState(false);

  // New‑tag modal refs – do not trigger re‑render on each keystroke
  const [modalOpen, setModalOpen] = useState(false);
  const newName = useRef("");
  const newDesc = useRef("");
  const [creating, setCreating] = useState(false);
  const [createError, setCreateError] = useState<string | null>(null);

  // Merge modal state
  const [mergeModalOpen, setMergeModalOpen] = useState(false);
  const [mergeClusterIdx, setMergeClusterIdx] = useState<number | null>(null);
  const [selectedCanonical, setSelectedCanonical] = useState<TagId | null>(null);
  const [merging, setMerging] = useState(false);
  // Use a Set of cluster fingerprints (sorted tag IDs joined) instead of indices
  const [mergedClusters, setMergedClusters] = useState<Set<string>>(new Set());
  
  // Helper to create a stable identifier for a cluster
  const getClusterFingerprint = (cluster: Tag[]): string => {
    return cluster.map(tag => tag._id).sort().join('|');
  };

  /** -----------------------------------------------------------------
   *  Handlers – Tag creation, deletion & merging
   *  -----------------------------------------------------------------*/
  const openModal = () => {
    setModalOpen(true);
    newName.current = "";
    newDesc.current = "";
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const handleCreateTag = async () => {
    if (!newName.current.trim() || !selectedTagTypeId) return;
    setCreating(true);
    setCreateError(null);
    try {
      await saveTag({
        tag: {
          name: newName.current.trim(),
          description: newDesc.current.trim(),
          tag_type: selectedTagTypeId,
        },
      });
      closeModal();
    } catch (error) {
      // Handle duplicate tag error gracefully
      if (error instanceof Error && error.message.includes("same tag type already exists")) {
        setCreateError(`A tag named "${newName.current.trim()}" already exists in this tag type.`);
      } else {
        setCreateError("An error occurred while creating the tag. Please try again.");
        console.error("Tag creation error:", error);
      }
    } finally {
      setCreating(false);
    }
  };

  // Merge UI handlers
  const openMergeModal = (idx: number) => {
    setMergeClusterIdx(idx);
    setSelectedCanonical(null);
    setMergeModalOpen(true);
  };
  
  const closeMergeModal = () => {
    setMergeModalOpen(false);
    setMergeClusterIdx(null);
    setSelectedCanonical(null);
  };
  
  const handleSelectCanonical = (tagId: TagId) => {
    setSelectedCanonical(tagId);
  };
  
  const handleConfirmMerge = async () => {
    if (mergeClusterIdx === null || selectedCanonical === null) return;
    setMerging(true);
    const cluster = similarTagClusters[mergeClusterIdx];
    const tagIdsToMerge = cluster.map(t => t._id).filter(id => id !== selectedCanonical);
    try {
      await mergeTags({
        canonicalTagId: selectedCanonical,
        tagIdsToMerge,
      });
      // Store the cluster fingerprint instead of the index
      const fingerprint = getClusterFingerprint(cluster);
      setMergedClusters(prev => new Set([...prev, fingerprint]));
      closeMergeModal();
    } finally {
      setMerging(false);
    }
  };

  /** -----------------------------------------------------------------
   *  Rendering helpers
   *  -----------------------------------------------------------------*/
  const TagTypeSelect = () => (
    <select
      className="border rounded px-2 py-1 text-sm"
      value={selectedSlug ?? ""}
      onChange={(e) => setSelectedSlug(e.target.value || null)}
      disabled={!tagTypes.length}
    >
      {tagTypes.length === 0 ? (
        <option value="">No tag types found</option>
      ) : (
        tagTypes.map((tt) => (
          <option key={tt._id} value={tt.immutable_slug}>
            {tt.name}
          </option>
        ))
      )}
    </select>
  );

  /** ----------------------------------------------------------------- */
  return (
    <div className="p-4">
      {/* ---------------- Similar Tag Merge Suggestions ---------------- */}
      {selectedSlug === "general-tags" && similarTagClusters.length > 0 && (
        <Card className="w-full mx-auto mb-6 border border-amber-200 bg-amber-50/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-amber-800 flex items-center gap-2">
              <span>Potential Duplicate Tags Detected</span>
              <Info className="h-5 w-5 text-amber-500" />
            </CardTitle>
            <CardDescription className="text-amber-700">
              The following tags appear very similar. Consider merging them to reduce clutter.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {similarTagClusters.map((cluster, idx) => {
                // Check if this cluster's fingerprint is in the mergedClusters set
                const fingerprint = getClusterFingerprint(cluster);
                const isMerged = mergedClusters.has(fingerprint);
                return (
                  <div
                    key={idx}
                    className={`border rounded-lg p-3 ${
                      isMerged
                        ? "bg-green-50 border-green-200"
                        : "bg-amber-50/80 border-amber-100"
                    } relative`}
                  >
                    <div className="flex flex-wrap items-center gap-2">
                      {isMerged ? (
                        <div className="flex items-center gap-1 text-green-700 font-medium shrink-0 mr-1">
                          <Check className="h-4 w-4" />
                          <span className="text-sm">Merged</span>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          className="shrink-0 h-7 mr-1"
                          onClick={() => openMergeModal(idx)}
                        >
                          Merge
                        </Button>
                      )}
                      {cluster.map((tag) => (
                        <span
                          key={tag._id}
                          className={`inline-block px-2 py-1 rounded-md font-medium text-sm ${
                            isMerged
                              ? "bg-green-100 text-green-800"
                              : "bg-amber-100 text-amber-800"
                          }`}
                        >
                          {tag.name}
                          {typeof tag.usage_count === "number" ? (
                            <span className="ml-1 text-xs opacity-75">{`(${tag.usage_count})`}</span>
                          ) : null}
                        </span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Merge Modal */}
      <Dialog open={mergeModalOpen} onOpenChange={open => { if (!open) closeMergeModal(); }}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Merge Tags</DialogTitle>
            <DialogDescription>
              Select the canonical tag to keep. All entities tagged with the others will be re-tagged to the selected tag, and the old tags will be deleted.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-wrap gap-3 mt-3 mb-4">
            {mergeClusterIdx !== null &&
              similarTagClusters[mergeClusterIdx].map(tag => {
                const selected = selectedCanonical === tag._id;
                return (
                  <button
                    key={tag._id}
                    type="button"
                    className={`rounded-lg border px-4 py-2 flex flex-col items-center min-w-[120px] transition-all
                      ${selected 
                        ? "bg-green-50 border-green-300 shadow-sm" 
                        : "bg-gray-50 border-gray-200 hover:border-gray-300 hover:bg-gray-100/50"
                      }
                    `}
                    onClick={() => handleSelectCanonical(tag._id)}
                    style={{ outline: selected ? "2px solid #10b981" : undefined }}
                  >
                    <span className="font-medium text-base flex items-center gap-1">
                      {tag.name}
                      {selected && <Check className="w-4 h-4 text-green-600" />}
                    </span>
                    {typeof tag.usage_count === "number" && (
                      <span className="text-xs text-gray-500 mt-1">{tag.usage_count} uses</span>
                    )}
                  </button>
                );
              })}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeMergeModal} disabled={merging}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmMerge}
              disabled={!selectedCanonical || merging}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {merging ? "Merging…" : "Confirm Merge"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Card className="w-full mx-auto mb-6">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div>
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl font-semibold">Tag Administration</CardTitle>
                <div className="bg-gray-100 rounded-full p-1" title="Tags are organised by type">
                  <Info className="h-4 w-4 text-gray-500" />
                </div>
              </div>
              <CardDescription>Manage your tags and tag types.</CardDescription>
            </div>
            <div className="flex flex-wrap gap-2 items-center">
              <TagTypeSelect />
              <ViewSwitcher viewMode={viewMode} setViewMode={mode => setViewMode(mode as ViewMode)} className="ml-1" />
              <button
                type="button"
                className={`ml-1 px-3 py-1 rounded-md border text-xs transition-colors ${flatView ? "bg-gray-100 border-gray-300" : "hover:bg-gray-50"}`}
                onClick={() => setFlatView((p) => !p)}
              >
                Flat View
              </button>
              <Button className="ml-1 whitespace-nowrap" onClick={openModal}>
                <Plus className="mr-2 h-4 w-4" /> Add Tag
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* ---------------- New Tag Modal ---------------- */}
      {modalOpen && (
        <CollectionCardModal
          heading="Create New Tag"
          subheading="Add a new parent tag to this tag type"
          onClose={closeModal}
          className="max-w-sm"
          primaryCTA={{ text: creating ? "Creating…" : "Create Tag", onClick: handleCreateTag }}
          secondaryCTA={{ text: "Cancel", onClick: closeModal }}
        >
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="new-tag-name">Tag Name</Label>
              <Input id="new-tag-name" defaultValue="" onChange={(e) => (newName.current = e.target.value)} autoFocus />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="new-tag-description">Description</Label>
              <Input id="new-tag-description" defaultValue="" onChange={(e) => (newDesc.current = e.target.value)} />
            </div>
            {createError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded text-sm">
                {createError}
              </div>
            )}
          </div>
        </CollectionCardModal>
      )}

      {/* ---------------- Views ---------------- */}
      {viewMode === ViewMode.CARD && !flatView && (
        <>
          {selectedSlug === "general-tags" ? (
            // Render GeneralTagsGrid using flatTags for the chip view
            <GeneralTagsGrid
              tags={flatTags}
              saveTagMutation={saveTag}
              removeTagMutation={removeTag}
              openModal={openModal}
            />
          ) : (
            // Render standard hierarchical TagCard grid
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 items-stretch">
              {hierarchy.map((tag) => (
                <div key={tag._id} className="h-full">
                  <TagCard tag={tag} saveTag={saveTag} removeTag={removeTag} />
                </div>
              ))}
              {/* New tag card (root) */}
              <Card
                onClick={openModal}
                className="h-full border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-all cursor-pointer group flex items-center justify-center"
              >
                <div className="flex flex-col items-center gap-2">
                  <div className="p-3 rounded-full bg-muted/50 group-hover:bg-muted">
                    <Plus className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <p className="text-sm font-medium text-muted-foreground">Create New Tag</p>
                </div>
              </Card>
            </div>
          )}
        </>
      )}

      {flatView && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Tags (Flat List)</h2>
          {flatTags.length === 0 ? (
            <p className="text-muted-foreground text-sm">No tags found for this tag type.</p>
          ) : (
            <ul className="divide-y divide-muted">
              {flatTags.map((t) => (
                <li key={t._id} className="py-2 flex flex-col">
                  <span className="font-medium">{t.name}</span>
                  {t.description && <span className="text-xs text-muted-foreground">{t.description}</span>}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

/** -------------------------------------------------------------------------
 *  Reusable Components – fully typed & memoised
 *  -------------------------------------------------------------------------*/

// Based on commit 18d0721c2886fc28e5e4f027ad0673d1e432218b
const GeneralTagsGrid = ({
  tags,
  saveTagMutation,
  removeTagMutation,
  openModal,
}: {
  tags: Tag[];
  saveTagMutation: ReturnType<typeof useMutation<typeof api.tags.saveTag>>;
  removeTagMutation: ReturnType<typeof useMutation<typeof api.tags.removeTag>>;
  openModal: () => void;
}) => (
  <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
    {tags.map((tag) => (
      <GeneralTagCard
        key={tag._id}
        tag={tag}
        saveTagMutation={saveTagMutation}
        removeTagMutation={removeTagMutation}
      />
    ))}
    {/* Add New Tag Chip */}
    <div
      onClick={openModal}
      className="rounded-md px-2 py-1.5 bg-gray-50 text-xs flex items-center justify-center border border-dashed border-gray-300 hover:border-gray-400 hover:bg-gray-100 transition-all cursor-pointer"
      style={{ minHeight: '32px' }}
    >
      <Plus className="h-3 w-3 mr-1 text-gray-500" />
      <span className="text-gray-600">Add Tag</span>
    </div>
  </div>
);

// Based on commit 18d0721c2886fc28e5e4f027ad0673d1e432218b and existing TagCard structure
const GeneralTagCard = ({
  tag,
  saveTagMutation,
  removeTagMutation,
}: {
  tag: Tag;
  saveTagMutation: ReturnType<typeof useMutation<typeof api.tags.saveTag>>;
  removeTagMutation: ReturnType<typeof useMutation<typeof api.tags.removeTag>>;
}) => {
  const [editing, setEditing] = useState(false);
  const [editValue, setEditValue] = useState(tag.name);
  const [loading, setLoading] = useState(false);
  const [editError, setEditError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const handleSave = async () => {
    const newName = editValue.trim();
    if (!newName || newName === tag.name) return setEditing(false);
    setLoading(true);
    setEditError(null);
    try {
      await saveTagMutation({
        tag: {
          id: tag._id,
          name: newName,
          tag_type: tag.tag_type,
        },
      });
      setEditing(false);
    } catch (error) {
      if (error instanceof Error && error.message.includes("same tag type already exists")) {
        setEditError(`A tag named "${newName}" already exists in this tag type.`);
      } else {
        setEditError("An error occurred while saving the tag.");
        console.error("Tag save error:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  const openDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    setDeleting(true);
    try {
      await removeTagMutation({ id: { id: tag._id } });
      setDeleteDialogOpen(false);
    } finally {
      setDeleting(false);
    }
  };

  return (
    <>
      <div className="rounded-md px-2 py-1.5 bg-gray-50 text-xs flex items-center border border-gray-200 shadow-sm w-full group relative hover:bg-gray-100/50 transition-colors">
        {editing ? (
          <>
            <div className="flex-1 mr-1">
              <Input
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="min-w-0 w-full rounded border px-2 py-1 text-xs h-6 focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={loading}
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleSave();
                  if (e.key === "Escape") setEditing(false);
                }}
              />
              {editError && (
                <div className="text-red-600 text-xs mt-1 px-1">{editError}</div>
              )}
            </div>
            <div className="flex shrink-0 items-center">
              <button
                className="text-green-600 hover:text-green-700 p-0.5 rounded shrink-0"
                title="Save"
                disabled={loading}
                onClick={handleSave}
              >
                <Check className="w-3.5 h-3.5" />
              </button>
              <button
                className="text-gray-400 hover:text-red-500 p-0.5 rounded shrink-0"
                title="Cancel"
                disabled={loading}
                onClick={() => setEditing(false)}
              >
                <X className="w-3.5 h-3.5" />
              </button>
            </div>
          </>
        ) : (
          <>
            <span className="flex-1 truncate mr-1">{tag.name}</span>
            <div className="opacity-0 group-hover:opacity-100 transition-opacity absolute right-0 top-0 bottom-0 flex items-center bg-gray-50 group-hover:bg-gray-100/50 pr-1">
              <TagMenu
                onEdit={() => {
                  setEditValue(tag.name);
                  setEditing(true);
                }}
                onDelete={openDeleteDialog}
              />
            </div>
          </>
        )}
      </div>
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Tag</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the tag <span className="font-semibold">{tag.name}</span>? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={deleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={deleting}>
              {deleting ? "Deleting…" : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};


interface TagCardProps {
  tag: Tag;
  saveTag: ReturnType<typeof useMutation<typeof api.tags.saveTag>>;
  removeTag: ReturnType<typeof useMutation<typeof api.tags.removeTag>>;
}

const TagCard: React.FC<TagCardProps> = React.memo(({ tag, saveTag, removeTag }) => {
  const [editing, setEditing] = useState(false);
  const nameRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [editError, setEditError] = useState<string | null>(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  // --- Dialog state for delete confirmation ---
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{ id: Id<"tags">; name: string } | null>(null);
  const [deleting, setDeleting] = useState(false);
  // --- Inline editing state for child tags ---
  const [editingChildId, setEditingChildId] = useState<Id<"tags"> | null>(null);
  const [childEditValue, setChildEditValue] = useState("");
  const [childEditLoading, setChildEditLoading] = useState(false);
  const [childEditError, setChildEditError] = useState<string | null>(null);

  const handleSave = async () => {
    const newName = nameRef.current?.value.trim();
    if (!newName || newName === tag.name) return setEditing(false);
    setLoading(true);
    setEditError(null);
    try {
      await saveTag({
        tag: {
          id: tag._id,
          name: newName,
          tag_type: tag.tag_type,
          parent_id: tag.parent_id,
        },
      });
      setEditing(false);
    } catch (error) {
      // Handle duplicate tag error gracefully
      if (error instanceof Error && error.message.includes("same tag type already exists")) {
        setEditError(`A tag named "${newName}" already exists in this tag type.`);
      } else {
        setEditError("An error occurred while saving the tag.");
        console.error("Tag save error:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handler to open dialog for a tag (parent or child)
  const openDeleteDialog = (id: Id<"tags">, name: string) => {
    setDeleteTarget({ id, name });
    setDeleteDialogOpen(true);
  };

  // Handler to actually delete
  const handleDelete = async () => {
    if (!deleteTarget) return;
    setDeleting(true);
    try {
      await removeTag({ id: { id: deleteTarget.id } });
      setDeleteDialogOpen(false);
      setDeleteTarget(null);
    } finally {
      setDeleting(false);
    }
  };

  // Handler for saving child tag name
  const handleChildSave = async (child: Tag) => {
    const newName = childEditValue.trim();
    if (!newName || newName === child.name) {
      setEditingChildId(null);
      return;
    }
    setChildEditLoading(true);
    setChildEditError(null);
    try {
      await saveTag({
        tag: {
          id: child._id,
          name: newName,
          tag_type: child.tag_type,
          parent_id: child.parent_id,
        },
      });
      setEditingChildId(null);
    } catch (error) {
      // Handle duplicate tag error gracefully
      if (error instanceof Error && error.message.includes("same tag type already exists")) {
        setChildEditError(`A tag named "${newName}" already exists in this tag type.`);
      } else {
        setChildEditError("An error occurred while saving the tag.");
        console.error("Child tag save error:", error);
      }
    } finally {
      setChildEditLoading(false);
    }
  };

  // Handler for starting child edit
  const startChildEdit = (child: Tag) => {
    setEditingChildId(child._id);
    setChildEditValue(child.name);
  };

  // Handler for canceling child edit
  const cancelChildEdit = () => {
    setEditingChildId(null);
    setChildEditValue("");
  };

  return (
    <Card className="flex flex-col h-full border border-gray-200 hover:border-gray-300 transition-colors">
      <CardHeader className="p-4 pb-2 group">
        <div className="flex items-center justify-between">
          {editing ? (
            <div className="flex-1 mr-2">
              <Input
                ref={nameRef}
                defaultValue={tag.name}
                className="w-full"
                disabled={loading}
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleSave();
                  if (e.key === "Escape") setEditing(false);
                }}
              />
              {editError && (
                <div className="text-red-600 text-xs mt-1">{editError}</div>
              )}
            </div>
          ) : (
            <h3 className="font-medium text-md truncate">{tag.name}</h3>
          )}
          <TagMenu
            onEdit={() => setEditing(true)}
            onDelete={() => openDeleteDialog(tag._id, tag.name)}
          />
        </div>
        {tag.description && (
          <p className="text-xs text-gray-500 mt-1 line-clamp-2">{tag.description}</p>
        )}
        <Badge variant="outline" className="text-xs h-5 px-1.5 mt-2 bg-gray-50">
          {tag.children?.length ?? 0} child tag{(tag.children?.length ?? 0) === 1 ? "" : "s"}
        </Badge>
      </CardHeader>
      <CardContent className="p-2 pt-0 flex-1">
        <ul className="space-y-1 ml-1">
          {tag.children?.length ? (
            tag.children.map((child) => (
              <li
                key={child._id}
                className="rounded-md px-2 py-1.5 bg-gray-50 text-sm flex items-center group relative hover:bg-gray-100/50 transition-colors"
              >
                {editingChildId === child._id ? (
                  <>
                    <div className="min-w-0 flex-1">
                      <input
                        className="w-full rounded border px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        value={childEditValue}
                        onChange={e => setChildEditValue(e.target.value)}
                        disabled={childEditLoading}
                        autoFocus
                        onKeyDown={e => {
                          if (e.key === "Enter") handleChildSave(child);
                          if (e.key === "Escape") cancelChildEdit();
                        }}
                      />
                      {childEditError && (
                        <div className="text-red-600 text-xs mt-1">{childEditError}</div>
                      )}
                    </div>
                    <div className="flex shrink-0 items-center ml-1">
                      <button
                        className="text-green-600 hover:text-green-700 p-1 rounded shrink-0"
                        title="Save"
                        disabled={childEditLoading}
                        onClick={() => handleChildSave(child)}
                      >
                        <Check className="w-3.5 h-3.5" />
                      </button>
                      <button
                        className="text-gray-400 hover:text-red-500 p-1 rounded shrink-0"
                        title="Cancel"
                        disabled={childEditLoading}
                        onClick={cancelChildEdit}
                      >
                        <X className="w-3.5 h-3.5" />
                      </button>
                    </div>
                  </>
                ) : (
                  <>
                    <span className="flex-1 truncate">{child.name}</span>
                    <TagMenu
                      onEdit={() => startChildEdit(child)}
                      onDelete={() => openDeleteDialog(child._id, child.name)}
                    />
                  </>
                )}
              </li>
            ))
          ) : (
            <li className="text-xs text-gray-500 py-2 text-center">No child tags</li>
          )}
        </ul>
      </CardContent>
      <div className="p-3 pt-0 mt-auto">
        <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full rounded-md border-dashed p-1.5 text-gray-500 hover:bg-gray-50 hover:text-gray-700 transition-colors">
              <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 16 16">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8 3.333v9.334M3.333 8h9.334" />
              </svg>
              <span className="text-xs">Add Tag</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent align="center" className="w-64 p-4">
            <AddChildTagForm parent={tag} saveTag={saveTag} removeTag={removeTag} onAdded={() => setPopoverOpen(false)} />
          </PopoverContent>
        </Popover>
      </div>
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Tag</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the tag <span className="font-semibold">{deleteTarget?.name}</span>? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={deleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={deleting}>
              {deleting ? "Deleting…" : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
});
TagCard.displayName = "TagCard";

/** Tag action menu */
interface TagMenuProps {
  onEdit?: () => void;
  onDelete?: () => void;
}
const TagMenu: React.FC<TagMenuProps> = ({ onEdit, onDelete }) => (
  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-5 w-5 p-0 ml-2">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {onEdit && <DropdownMenuItem onClick={onEdit}>Edit</DropdownMenuItem>}
        {onDelete && (
          <DropdownMenuItem className="text-destructive" onClick={onDelete}>
            Delete Tag
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
);

/** Add child tag form inside popover */
interface AddChildTagFormProps {
  parent: Tag;
  saveTag: ReturnType<typeof useMutation<typeof api.tags.saveTag>>;
  removeTag: ReturnType<typeof useMutation<typeof api.tags.removeTag>>;
  onAdded?: () => void;
}
const AddChildTagForm: React.FC<AddChildTagFormProps> = ({ parent, saveTag, removeTag, onAdded }) => {
  // Use state for controlled input
  const [inputValue, setInputValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [addError, setAddError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const value = inputValue.trim();
    if (!value) return;
    setLoading(true);
    setAddError(null);
    try {
      await saveTag({
        tag: {
          name: value,
          tag_type: parent.tag_type,
          parent_id: parent._id,
        },
      });
      setInputValue(""); // Clear input after successful add
      if (onAdded) onAdded(); // Close popover
    } catch (error) {
      // Handle duplicate tag error gracefully
      if (error instanceof Error && error.message.includes("same tag type already exists")) {
        setAddError(`A tag named "${value}" already exists in this tag type.`);
      } else {
        setAddError("An error occurred while creating the tag.");
        console.error("Child tag creation error:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-2">
      <label className="text-xs font-medium">Tag Name</label>
      <Input
        value={inputValue}
        onChange={e => setInputValue(e.target.value)}
        placeholder="Enter tag name"
        disabled={loading}
        autoFocus
      />
      {addError && (
        <div className="text-red-600 text-xs mt-1">{addError}</div>
      )}
      <Button type="submit" disabled={loading || !inputValue.trim()} className="mt-2">
        {loading ? "Adding…" : "Add Tag"}
      </Button>
    </form>
  );
};

export default TagAdminDashboard;
