'use client';
import { useQuery, useMutation } from 'convex/react';
import React, { useState, useRef, useCallback, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import ViewSwitcher from '@/components/blocks/view-switcher';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  Search,
  Users,
  Plus,
  Edit,
  Trash,
  Move,
  PlusCircle,
  Info,
  MoreVertical
} from '@/components/icons';
import { useToast } from "@/components/hooks/use-toast";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { zid } from "convex-helpers/server/zod";
import {
  PopoverWithSearch,
  PopoverWithSearchTrigger,
  PopoverWithSearchContent,
} from '@/components/ui/PopoverWithSearch';
import { CollectionCard } from '@/components/ui/collection-card';
import { TeamsAdminTable, type PeopleTableItem } from './components/teamsTable';
import { CollectionCardModal } from '@/components/ui/collectionCardModal';
import { Textarea } from '@/components/ui/textarea';

/**
 * Interface representing a team in Convex.
 */
export interface Team {
  _id: Id<"teams">;
  _creationTime: number;
  name: string;
  description?: string;
  slug?: string;
}

/**
 * Local interface for user data with teams, adapted for UserWithPerson data
 */
interface LocalUser {
  id: Id<"people">; // Corresponds to personId, used for keys/drag-drop
  userId: Id<"users">; // The actual user ID
  name: string;
  email?: string;
  role?: string; // Corresponds to person's title
  image?: string; // Add image field
  teams: Id<"teams">[];
}

const TeamAdminDashboard = () => {
  /**
   * ---------------------------------------------------------------------
   * Data Fetching
   * ---------------------------------------------------------------------
   */
  const { toast } = useToast();

  // Teams
  const teams = useQuery(api.relationships.teams.listTeams, {}) || [];

  // Convert string IDs to Convex IDs
  const typedTeams = teams.map(team => ({
    ...team,
    _id: team._id as Id<"teams">
  }));

  // Fetch combined user and person data using the new query
  const usersWithPeople = useQuery(api.users.listUsersWithPeopleAndTeams, {}) || [];

  /**
   * ---------------------------------------------------------------------
   * Mutations
   * ---------------------------------------------------------------------
   */
  const createTeamMutation = useMutation(api.relationships.teams.createTeam);
  const deleteTeamMutation = useMutation(api.relationships.teams.deleteTeam);
  const updateUserMutation = useMutation(api.users.updateUsers);

  /**
   * Placeholder for updating teams (not yet implemented on the backend).
   */
  const updateTeam = async (id: Id<"teams">, updates: { name: string; description: string }) => {
    toast({
      title: "Info",
      description: "Team update functionality needs to be implemented in the Convex backend",
    });
    // Normally you'd call a real mutation here.
  };

  /**
   * ---------------------------------------------------------------------
   * Derived Data
   * ---------------------------------------------------------------------
   */

  // Adapt LocalUser creation from the new data source
  const mappedUsers: LocalUser[] = useMemo(() => usersWithPeople.map((up): LocalUser => ({
    id: up.personId, // Use personId for the 'id' field expected by drag-and-drop
    userId: up.userId, // Keep track of the actual userId
    name: up.name,
    email: up.email,
    role: up.title, // Use title for role
    image: up.image, // Map the image field
    teams: up.teams || [] // Use teams directly from the combined object
  })), [usersWithPeople]);


  // Return array of users who have no teams, optionally filtered by search query.
  // Now filters the already combined and filtered list.
  const getUnassignedUsers = (search: string): LocalUser[] => {
    const unassigned = mappedUsers.filter(u => !u.teams || u.teams.length === 0);
    if (!search) return unassigned;

    return unassigned.filter(
      (user) =>
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email?.toLowerCase().includes(search.toLowerCase()) ||
        user.role?.toLowerCase().includes(search.toLowerCase())
    );
  };

  /**
   * Get users that belong to a specific team
   */
  const getTeamUsers = (teamId: Id<"teams">): LocalUser[] => {
    // Filters the already combined and filtered list.
    return mappedUsers.filter(user => user.teams && user.teams.includes(teamId));
  };

  /**
   * Prepare data for TeamsAdminTable using the new combined data source
   */
  const tableItems = useMemo((): PeopleTableItem[] => {
    // Map directly from usersWithPeople which already contains linked data
    return usersWithPeople.map(up => {
      // Map the teams array from the user part of the combined object
      const userTeams = up.teams?.map(teamId => {
        const team = typedTeams.find(t => t._id === teamId);
        if (!team) return null;
        return {
          _id: team._id,
          name: team.name,
          image: undefined,
          slug: team.slug
        };
      }).filter((t): t is NonNullable<typeof t> => t !== null) || [];

      // Construct the PeopleTableItem using data from the combined object
      return {
        id: up.personId, // Use personId for the table row ID
        title: up.name,
        description: up.title || '', // Use title as description
        email: up.email || '',
        phone: '', // Phone is not in UserWithPerson, maybe add later if needed
        team: userTeams,
        // lastUpdated is not directly available, maybe use user._creationTime or person.updated_at if added to query
      } satisfies PeopleTableItem;
    });
  }, [usersWithPeople, typedTeams]);

  /**
   * ---------------------------------------------------------------------
   * UI State
   * ---------------------------------------------------------------------
   */

  // Which view to show: card layout, list/table layout, or kanban layout
  const [viewMode, setViewMode] = useState<'card' | 'list' | 'kanban'>('card');

  // Search box (used for unassigned users)
  const [searchQuery, setSearchQuery] = useState('');

  // New Team Modal
  const [newTeamDialogOpen, setNewTeamDialogOpen] = useState(false);
  const newTeamNameRef = useRef<HTMLInputElement>(null);
  const newTeamDescriptionRef = useRef<HTMLInputElement>(null);

  // Edit Team Modal
  // If not null, we are editing this team. Its presence shows the modal.
  const [teamBeingEdited, setTeamBeingEdited] = useState<Team | null>(null);
  const editTeamNameRef = useRef<HTMLInputElement>(null);
  const editTeamDescriptionRef = useRef<HTMLTextAreaElement>(null);

  // Delete Team Modal
  // If not null, we are deleting this team. Its presence shows the modal.
  const [teamBeingDeleted, setTeamBeingDeleted] = useState<Team | null>(null);

  /**
   * ---------------------------------------------------------------------
   * Handlers: Team Creation, Updating, Deletion
   * ---------------------------------------------------------------------
   */

  /**
   * Creates a new team using the input from the "Create Team" modal.
   */
  const handleCreateTeam = async () => {
    const nameValue = newTeamNameRef.current?.value.trim() || '';
    if (!nameValue) return;

    const descriptionValue = newTeamDescriptionRef.current?.value.trim() || '';

    try {
      await createTeamMutation({
        name: nameValue,
        description: descriptionValue,
        slug: nameValue.toLowerCase().replace(/\s+/g, '-')
      });

      toast({
        title: 'Success',
        description: 'Team created successfully.'
      });
      setNewTeamDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to create team',
        variant: 'destructive'
      });
    }
  };

  /**
   * Called when user clicks "Save Changes" in the Edit Team modal.
   */
  const handleSaveTeam = async () => {
    if (!teamBeingEdited) return;

    const updatedName = editTeamNameRef.current?.value.trim() || '';
    const updatedDescription = editTeamDescriptionRef.current?.value.trim() || '';

    try {
      await updateTeam(teamBeingEdited._id, {
        name: updatedName,
        description: updatedDescription,
      });
      toast({
        title: 'Success',
        description: 'Team updated successfully.',
      });
      setTeamBeingEdited(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update team',
        variant: 'destructive'
      });
    }
  };

  /**
   * Called when user confirms "Delete Team" in the Delete Team modal.
   */
  const handleDeleteTeam = async () => {
    if (!teamBeingDeleted) return;

    try {
      await deleteTeamMutation({ id: teamBeingDeleted._id });
      toast({
        title: 'Success',
        description: 'Team deleted successfully.'
      });
      setTeamBeingDeleted(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to delete team',
        variant: 'destructive'
      });
    }
  };

  /**
   * ---------------------------------------------------------------------
   * Handlers: Hello Pangea Drag & Drop
   * ---------------------------------------------------------------------
   */

  /**
   * Handles the end of a drag operation.
   * This is where we update the user's teams in the database.
   * @param result - The result of the drag operation
   */
   const handleDragEnd = useCallback(
    async (result: DropResult) => {
      if (!result.destination) {
        return; // Drag was cancelled
      }

      const sourceDroppableId = result.source.droppableId;
      const destinationDroppableId = result.destination.droppableId;

      if (sourceDroppableId === destinationDroppableId) {
        return; // Reorder within the same team, handled by the library
      }

      const [_, userId, __, sourceTeamId] = result.draggableId.split("-");

      let newTeamId: Id<"teams"> | undefined;
      if (destinationDroppableId !== 'unassigned-users') {
        newTeamId = destinationDroppableId.replace('team-', '') as Id<"teams">;
      }

      // Get the user from the mappedUsers (which now includes userId)
      // Note: userId here is actually the personId because of how draggableId is constructed
      const user = mappedUsers.find((u) => u.id === userId);

      if (!user) {
        console.error("Dragged user not found in mappedUsers:", userId);
        return;
      }

      const userTeams = user.teams || [];

      // Start with the current teams
      let updatedTeams = [...userTeams];

      // If source is a team (not unassigned), remove the source team
      if (sourceTeamId && sourceTeamId !== "unassigned") {
        updatedTeams = updatedTeams.filter(teamId => teamId !== sourceTeamId);
      }

      // If destination is a team (not unassigned), add the destination team
      if (destinationDroppableId !== 'unassigned-users') {
        const destTeamId = destinationDroppableId.replace('team-', '') as Id<"teams">;
        // Only add if not already in the team
        if (!updatedTeams.includes(destTeamId)) {
          updatedTeams.push(destTeamId);
        }
      } else {
        // If destination is unassigned, remove from all teams
        updatedTeams = [];
      }

      try {
        // We already have the userId from the mappedUser object
        if (!user.userId) {
           throw new Error('User ID not found for this person');
        }

        // Update the user's teams in the database using the actual userId
        await updateUserMutation({
          id: user.userId, // Use the correct userId
          updates: { teams: updatedTeams },
        });
        toast({
          title: 'Success',
          description: "User's team updated successfully",
        });
      } catch (error: unknown) {
        console.error('Error updating user teams:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error
              ? error.message
              : "Failed to update user's team",
          variant: 'destructive',
        });
      }
    },
    [mappedUsers, updateUserMutation, toast] // Removed 'people' dependency
  );


  /**
   * ---------------------------------------------------------------------
   * Table (List) View: Selection State
   * ---------------------------------------------------------------------
   */
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Check/uncheck single row
  const handleItemCheckboxChange = (checked: boolean, id: string | Id<"people">) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id.toString()]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id.toString()));
    }
  };

  // Check/uncheck all rows
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(tableItems.map(item => item.id.toString()));
    } else {
      setSelectedItems([]);
    }
  };

  /**
   * ---------------------------------------------------------------------
   * Render
   * ---------------------------------------------------------------------
   */
  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="p-4">
        {/* Page Intro Card */}
        <Card className="w-full mx-auto mb-6">
          <CardHeader className="pb-2">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
              <div>
                <div className="flex items-center gap-2">
                  <CardTitle className="text-2xl font-bold">Team Administration</CardTitle>
                  <div className="bg-muted rounded-full p-1" title="Users can belong to multiple teams">
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
                <CardDescription>
                  Manage your teams and team members. Users can belong to multiple teams.
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <div className="relative flex-1 md:w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    className="pl-8 w-full"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <ViewSwitcher
                  viewMode={viewMode}
                  setViewMode={setViewMode}
                  className="ml-2"
                />
                <Button onClick={() => setNewTeamDialogOpen(true)} className="whitespace-nowrap">
                  <Plus className="mr-2 h-4 w-4" /> Add Team
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Unassigned Users Section (Card View) */}
        {viewMode === 'card' && getUnassignedUsers(searchQuery).length > 0 && (
          <Droppable 
            droppableId="unassigned-users"
            renderClone={(provided, snapshot, rubric) => {
              const user = getUnassignedUsers(searchQuery)[rubric.source.index];
              return (
                <div
                  ref={provided.innerRef}
                  {...provided.draggableProps}
                  {...provided.dragHandleProps}
                  className="rounded-lg p-2 cursor-move transition-colors bg-background border shadow-md select-none"
                  style={{
                    ...provided.draggableProps.style,
                    width: '250px' // Fixed width for the clone
                  }}
                >
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={user.image} alt={user.name} />
                      <AvatarFallback className="text-xs">
                        {user.name // Use name directly
                          ?.split(' ')
                          .map((n) => n[0])
                          .join('') || '??'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm truncate">{user.name}</p>
                      {/* Role removed */}
                    </div>
                    <Move className="h-4 w-4 text-muted-foreground ml-auto" />
                  </div>
                </div>
              );
            }}
          >
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
              >
                <CollectionCard
                  heading="Users Without Teams"
                  subheading="Drag these users to assign them to teams"
                  className="mb-6"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {getUnassignedUsers(searchQuery).map((user, index) => (
                      <Draggable
                        key={`user-${user.id}-unassigned`}
                        draggableId={`user-${user.id}-unassigned`}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="rounded-lg p-2 cursor-grab active:cursor-grabbing transition-colors hover:bg-muted select-none border border-transparent hover:border-primary/20"
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={user.image} alt={user.name} />
                                <AvatarFallback className="text-xs">
                        {user.name // Use name directly
                          ?.split(' ')
                          .map((n) => n[0])
                          .join('') || '??'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm truncate">{user.name}</p>
                      {/* Role removed */}
                              </div>
                              <Move className="h-4 w-4 text-muted-foreground ml-auto" />
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                </CollectionCard>
              </div>
            )}
          </Droppable>
        )}

        {/* List (Table) View */}
        {viewMode === 'list' && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <TeamsAdminTable
                items={tableItems}
                selectedItems={selectedItems}
                onItemCheckboxChange={handleItemCheckboxChange}
                onSelectAll={handleSelectAll}
                baseRoute="/admin/users"
                paginationEnabled
              />
            </CardContent>
          </Card>
        )}

        {/* Teams Grid (Card View) */}
        {viewMode === 'card' && (
          <Droppable droppableId="teams-grid" isDropDisabled={true}>
            {(provided) => (
              <div
                className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                ref={provided.innerRef}
                {...provided.droppableProps}
              >
                {typedTeams.map((team) => (
                  <div key={team._id} className="w-full">
                    <Card className="h-full border flex flex-col">
                      <CardHeader className="p-3 pb-2">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-md">{team.name}</h3>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => {
                                  setTeamBeingEdited(team);
                                }}
                              >
                                <Edit className="h-3.5 w-3.5 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={() => {
                                  setTeamBeingDeleted(team);
                                }}
                              >
                                <Trash className="h-3.5 w-3.5 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-xs text-muted-foreground line-clamp-2 flex-1">
                            {team.description}
                          </p>
                          <Badge variant="outline" className="text-xs h-5 px-1.5 flex items-center gap-1">
                            {getTeamUsers(team._id).length}
                            <Users className="h-2.5 w-2.5" />
                          </Badge>
                        </div>
                      </CardHeader>

                      <CardContent className="p-1 pt-0 flex-1">
                        <Droppable 
                          droppableId={`team-${team._id}`}
                          renderClone={(provided, snapshot, rubric) => {
                            const user = getTeamUsers(team._id)[rubric.source.index];
                            return (
                              <div
                                // Clone rendering - uses getTeamUsers result
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className="rounded-lg p-1.5 cursor-move transition-colors bg-background border shadow-md select-none"
                                style={{
                                  ...provided.draggableProps.style,
                                  width: '250px' // Fixed width for the clone
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  <Avatar className="h-5 w-5">
                                    <AvatarImage src={user.image} alt={user.name} />
                                    <AvatarFallback className="text-xs">
                                      {user.name // Use name directly
                                        ?.split(' ')
                                        .map((n: string) => n[0])
                                        .join('') || '??'}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm truncate">{user.name}</p>
                                    {/* Role removed */}
                                  </div>
                                  <Move className="h-3.5 w-3.5 text-muted-foreground ml-auto" />
                                </div>
                              </div>
                            );
                          }}
                        >
                          {(provided) => (
                            <div
                              className="space-y-0"
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                            >
                              {getTeamUsers(team._id).length === 0 ? (
                                <p className="text-xs text-muted-foreground py-1 text-center">
                                  No members yet
                                </p>
                              ) : (
                                getTeamUsers(team._id).map((user, index) => (
                                  <Draggable
                                    key={`user-${user.id}-team-${team._id}`}
                                    draggableId={`user-${user.id}-team-${team._id}`}
                                    index={index}
                                  >
                                    {(provided) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className="rounded-lg p-1.5 cursor-grab active:cursor-grabbing transition-colors hover:bg-muted select-none border border-transparent hover:border-primary/20"
                                      >
                                        <div className="flex items-center gap-2">
                                          <Avatar className="h-5 w-5">
                                            <AvatarImage src={user.image} alt={user.name} />
                                            <AvatarFallback className="text-xs">
                                              {user.name // Use name directly
                                                ?.split(' ')
                                                .map((n: string) => n[0])
                                                .join('') || '??'}
                                            </AvatarFallback>
                                          </Avatar>
                                          <div className="flex-1 min-w-0">
                                            <p className="text-sm truncate">{user.name}</p>
                                            {/* Role removed */}
                                          </div>
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-5 w-5 p-0"
                                              >
                                                <MoreVertical className="h-3 w-3" />
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                              <DropdownMenuItem
                                                className="text-destructive focus:text-destructive"
                                              >
                                                <Move className="h-3.5 w-3.5 mr-2" />
                                                Move to team
                                              </DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </div>
                                    )}
                                  </Draggable>
                                ))
                              )}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </CardContent>

                      <CardFooter className="p-1 pt-0 mt-auto">
                        {/* Add member with PopoverWithSearch */}
                        <PopoverWithSearch teamId={team._id} className="w-full">
                          <PopoverWithSearchTrigger asChild className="w-full">
                            <Button
                              variant="outline"
                              className="w-full rounded-lg border-dashed border-muted-foreground/30 p-1.5 flex items-center justify-center text-muted-foreground hover:bg-muted transition-colors"
                            >
                              <PlusCircle className="h-3 w-3 mr-1" />
                              <span className="text-xs">Add Member</span>
                            </Button>
                          </PopoverWithSearchTrigger>
                          <PopoverWithSearchContent
                            teamId={team._id}
                            className="w-72 data-[state=open]:animate-in data-[state=closed]:animate-out 
                                     data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 
                                     data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 
                                     data-[side=bottom]:slide-in-from-top-2 
                                     data-[side=left]:slide-in-from-right-2 
                                     data-[side=right]:slide-in-from-left-2 
                                     data-[side=top]:slide-in-from-bottom-2"
                          >
                            <div>Search content goes here</div>
                          </PopoverWithSearchContent>
                        </PopoverWithSearch>
                      </CardFooter>
                    </Card>
                  </div>
                ))}

                {/* Create New Team Card */}
                <Card
                  onClick={() => setNewTeamDialogOpen(true)}
                  className="h-full border-2 border-dashed border-muted-foreground/25 
                            hover:border-muted-foreground/50 transition-all cursor-pointer 
                            group flex items-center justify-center"
                >
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-3 rounded-full bg-muted/50 group-hover:bg-muted">
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <p className="text-sm font-medium text-muted-foreground">Create New Team</p>
                  </div>
                </Card>
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        )}

        {/**
         * -------------------------------------------------------------------
         * Modals
         * -------------------------------------------------------------------
         */}

        {/* Create Team Modal */}
        {newTeamDialogOpen && (
          <CollectionCardModal
            heading="Create New Team"
            subheading="Add a new team to your organization"
            onClose={() => setNewTeamDialogOpen(false)}
            className="max-w-sm"
            primaryCTA={{
              text: "Create Team",
              onClick: handleCreateTeam
            }}
            secondaryCTA={{
              text: "Cancel",
              onClick: () => setNewTeamDialogOpen(false)
            }}
          >
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="new-team-name">Team Name</Label>
                <Input
                  id="new-team-name"
                  ref={newTeamNameRef}
                  placeholder="Enter team name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="new-team-description">Description</Label>
                <Input
                  id="new-team-description"
                  ref={newTeamDescriptionRef}
                  placeholder="Enter team description"
                />
              </div>
            </div>
          </CollectionCardModal>
        )}

        {/* Edit Team Modal */}
        {teamBeingEdited && (
          <CollectionCardModal
            heading="Edit Team"
            subheading="Edit team details"
            onClose={() => setTeamBeingEdited(null)}
            className="max-w-sm"
            primaryCTA={{
              text: "Save Changes",
              onClick: handleSaveTeam
            }}
            secondaryCTA={{
              text: "Cancel",
              onClick: () => setTeamBeingEdited(null)
            }}
          >
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Team Name</Label>
                <Input
                  id="edit-name"
                  defaultValue={teamBeingEdited?.name || ''}
                  ref={editTeamNameRef}
                  placeholder="Enter team name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  defaultValue={teamBeingEdited?.description || ''}
                  ref={editTeamDescriptionRef}
                  placeholder="Enter team description"
                  className="min-h-[100px] resize-none"
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = target.scrollHeight + 'px';
                  }}
                />
              </div>
            </div>
          </CollectionCardModal>
        )}

        {/* Delete Team Modal */}
        {teamBeingDeleted && (
          <CollectionCardModal
            heading="Delete Team"
            subheading={`Are you sure you want to delete the team "${teamBeingDeleted?.name}"? This action cannot be undone.`}
            onClose={() => setTeamBeingDeleted(null)}
            className="max-w-sm"
            primaryCTA={{
              text: "Delete Team",
              onClick: handleDeleteTeam
            }}
            secondaryCTA={{
              text: "Cancel",
              onClick: () => setTeamBeingDeleted(null)
            }}
          >
            <div className="text-center py-4">
              <p className="text-neutral-600">
                This will permanently delete the team and remove all team associations. 
                Team members will not be deleted.
              </p>
            </div>
          </CollectionCardModal>
        )}
      </div>
    </DragDropContext>
  );
};

export default TeamAdminDashboard;
