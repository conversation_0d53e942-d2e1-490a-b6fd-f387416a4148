'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
  SortingState,
  PaginationState
} from '@tanstack/react-table';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowUp, ArrowDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { Id } from "@/convex/_generated/dataModel";

// Define PeopleTableItem interface for the table
export interface PeopleTableItem {
  id: Id<"people">;
  title: string; // This will be the person's name
  description?: string; // Description or title
  email?: string;
  phone?: string;
  team?: { _id: Id<"teams">; name: string; image?: string; slug?: string }[]; // Added proper ID typing
  lastUpdated?: number;
}

// Define TeamsAdminTableProps interface
interface TeamsAdminTableProps {
  items: PeopleTableItem[];
  selectedItems: (string | Id<"people">)[];
  onItemCheckboxChange: (checked: boolean, id: string | Id<"people">) => void;
  onSelectAll: (checked: boolean) => void;
  baseRoute?: string;
  columns?: ColumnDef<PeopleTableItem>[];
  paginationEnabled?: boolean;
}

/**
 * TeamsAdminTable - A table component for displaying people data
 * 
 * This component is based on the TemplateTable but customized for the people/teams admin view
 */
export const TeamsAdminTable = ({
  items,
  selectedItems,
  onItemCheckboxChange,
  onSelectAll,
  baseRoute = '/admin/users',
  columns: customColumns,
  paginationEnabled = false,
}: TeamsAdminTableProps) => {
  // State for sorting
  const [sorting, setSorting] = React.useState<SortingState>([]);
  // State for pagination (if paginationEnabled is true)
  const [pagination, setPagination] = React.useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  // State for search term
  const [searchTerm, setSearchTerm] = React.useState("");

  // Filter items based on search term (searching in title)
  const filteredItems = useMemo(() => {
    return items.filter(item =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [items, searchTerm]);

  /**
   * Define default columns that can be overridden by custom columns
   */
  const defaultColumns = useMemo<ColumnDef<PeopleTableItem>[]>(
    () => [
      // Selection column
      {
        id: 'select',
        header: ({ table }) => (
          <div className="flex items-center">
            <Checkbox
              checked={
                selectedItems.length === items.length &&
                items.length > 0
              }
              onCheckedChange={(checked: boolean) => {
                onSelectAll(checked);
              }}
              className="border-muted-foreground"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center">
            <Checkbox
              checked={selectedItems.includes(row.original.id.toString())}
              onCheckedChange={(checked: boolean) =>
                onItemCheckboxChange(checked, row.original.id)
              }
              className="border-muted-foreground"
            />
          </div>
        ),
        enableSorting: false,
        size: 50
      },

      // Title and description
      {
        accessorKey: 'title',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Name
              {sort === 'asc' && <ArrowUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ArrowDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          <div className="flex flex-col">
            <Link
              href={`${baseRoute}/${row.original.id}`}
              className="hover:underline"
            >
              <div className="font-medium truncate">{row.original.title}</div>
            </Link>
            {row.original.description && (
              <div className="text-sm text-muted-foreground line-clamp-1 mt-1">
                {row.original.description}
              </div>
            )}
          </div>
        ),
        size: 300
      },

      // Email
      {
        accessorKey: 'email',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Email
              {sort === 'asc' && <ArrowUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ArrowDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          <div className="text-sm">{row.original.email}</div>
        ),
        size: 200
      },

      // Phone
      {
        accessorKey: 'phone',
        header: ({ column }) => {
          const sort = column.getIsSorted();
          return (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => column.toggleSorting(sort === 'asc')}
            >
              Phone
              {sort === 'asc' && <ArrowUp className="ml-2 h-4 w-4" />}
              {sort === 'desc' && <ArrowDown className="ml-2 h-4 w-4" />}
            </div>
          );
        },
        cell: ({ row }) => (
          <div className="text-sm">{row.original.phone}</div>
        ),
        size: 150
      },

      // Team
      {
        id: 'team',
        header: 'Team',
        cell: ({ row }) => (
          row.original.team && row.original.team.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {row.original.team.map((team) => (
                <div
                  key={team._id}
                  className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary text-primary-foreground hover:bg-primary/80"
                >
                  {team.name}
                </div>
              ))}
            </div>
          ) : null
        ),
        enableSorting: false
      }
    ],
    [items, selectedItems, onItemCheckboxChange, onSelectAll, baseRoute]
  );

  // Use custom columns if provided, otherwise use default columns
  const tableColumns = customColumns || defaultColumns;

  // Initialize TanStack table with optional pagination using filteredItems
  const table = useReactTable({
    data: filteredItems,
    columns: tableColumns,
    state: {
      sorting,
      ...(paginationEnabled ? { pagination } : {})
    },
    onSortingChange: setSorting,
    ...(paginationEnabled
      ? { getPaginationRowModel: getPaginationRowModel(), onPaginationChange: setPagination }
      : {}),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel()
  });

  return (
    <>
      {/* Search Bar */}
      <div className="mb-4">
        <Input
          placeholder="Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-64"
        />
      </div>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} className="border-b bg-muted/50">
              {headerGroup.headers.map((header) => (
                <TableHead
                  key={header.id}
                  className={`text-left p-1 text-sm font-medium ${
                    header.id === 'select' ? 'w-[50px] px-1' : ''
                  } ${header.id === 'title' ? 'pl-2 pr-3 w-[300px]' : ''}`}
                  style={{
                    width:
                      header.getSize() !== 150 ? header.getSize() : undefined
                  }}
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : ''
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </div>
                  )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={tableColumns.length}
                className="p-4 text-center text-muted-foreground"
              >
                No items found
              </TableCell>
            </TableRow>
          ) : (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className="border-b last:border-b-0 hover:bg-muted/50"
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={`p-1 ${
                      cell.column.id === 'select' ? 'w-[50px] px-1' : ''
                    } ${
                      cell.column.id === 'title'
                        ? 'pl-2 pr-3 max-w-[300px]'
                        : ''
                    }`}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      {paginationEnabled && (
        <div className="flex items-center justify-center space-x-2 py-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="bg-white/50"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm text-slate-600">
            Page {pagination.pageIndex + 1} of {table.getPageCount()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="bg-white/50"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </>
  );
};
