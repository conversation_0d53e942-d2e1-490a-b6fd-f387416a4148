'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { useQuery, useMutation, useConvexAuth } from 'convex/react'; // Added useConvexAuth
import confetti from "canvas-confetti";
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel'; // Added Doc
import { useToast } from '@/components/hooks/use-toast';
import TasksCard from './TasksCard';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { MorphingPopover, MorphingPopoverContent } from '@/components/ui/morphing-popover';
import { But<PERSON> } from '@/components/ui/button';
import { TaskStatusEnum } from '@/zod/tasks-schema';
import { subDays, subMonths, subYears, startOfDay } from 'date-fns'; // Added startOfDay
import { Spinner } from '@/components/ui/spinner';
import { z } from 'zod';

// Define the time filter options
const TIME_FILTER_OPTIONS = [
  { value: 'today', label: 'Today', days: 0 },
  { value: '1w', label: '1 week', days: 7 },
  { value: '2w', label: '2 weeks', days: 14 },
  { value: '1m', label: '1 month', days: 30 },
  { value: '2m', label: '2 months', days: 60 },
  { value: '3m', label: '3 months', days: 90 }
];

// Define the status options for each column
const COLUMN_STATUS_OPTIONS = {
  todo: ['todo'],
  inProgress: ['in_progress', 'blocked'],
  completed: ['completed', 'cancelled']
};

// Define the status display names
const STATUS_DISPLAY_NAMES: Record<string, string> = {
  todo: 'To Do',
  in_progress: 'In Progress',
  blocked: 'Blocked',
  completed: 'Completed',
  cancelled: 'Cancelled'
};

// Define the column display names
const COLUMN_DISPLAY_NAMES: Record<string, string> = {
  todo: 'To Do',
  inProgress: 'In Progress',
  completed: 'Completed'
};

// Define the confetti function type at the top of the file, near other imports
type ConfettiFn = (options?: confetti.Options) => Promise<any>;

/**
 * KanbanView Component
 * 
 * Displays tasks in a Kanban board with three columns:
 * - To Do: Tasks with status "todo"
 * - In Progress: Tasks with status "in_progress" or "blocked"
 * - Completed: Tasks with status "completed" or "cancelled"
 * 
 * Supports drag and drop between columns to update task status.
 */
const KanbanView: React.FC = () => {
  const { toast } = useToast();
  
  // State for time filter (stored in localStorage)
  const [timeFilter, setTimeFilter] = useState<string>(() => {
    // Initialize from localStorage or default to '1m' (1 month)
    if (typeof window !== 'undefined') {
      return localStorage.getItem('tasksTimeFilter') || '1m';
    }
    return '1m';
  });
  
  // State for status filters
  const [inProgressStatusFilter, setInProgressStatusFilter] = useState<string[]>(COLUMN_STATUS_OPTIONS.inProgress);
  const [completedStatusFilter, setCompletedStatusFilter] = useState<string[]>(COLUMN_STATUS_OPTIONS.completed);

  // State for the new "My Tasks" filter
  const [showMyTasksOnly, setShowMyTasksOnly] = useState(true); // Default to true

  // State for popover
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [activeTask, setActiveTask] = useState<{
    id: Id<'tasks'>,
    destinationColumn: string,
    position?: { top: number, left: number }
  } | null>(null);

  // Store refs to draggable items using useRef instead of useState to avoid re-renders
  const draggableRefs = useRef<Record<string, HTMLElement | null>>({});
  
  // Reference to the completed column for confetti
  const completedColumnRef = useRef<HTMLDivElement>(null);

  // Get authenticated user identity
  const { isLoading: authLoading, isAuthenticated } = useConvexAuth();
  const currentUser = useQuery(api.users.currentUser);

  // Fetch all teams for lookup
  const allTeams = useQuery(api.teams.listTeams, { pagination: { limit: 1000 } }); // Use listTeams query
  // Removed allOrgs query

  // Calculate the cutoff timestamp based on the time filter
  const updatedAtAfterTimestamp = useMemo(() => {
    if (timeFilter === 'all') {
      return undefined; // No time filter
    }
    const timeOption = TIME_FILTER_OPTIONS.find(option => option.value === timeFilter);
    if (!timeOption || timeOption.days === undefined) return undefined; // Should not happen with 'all' handled

    if (timeOption.value === 'today') {
      return startOfDay(new Date()).getTime();
    }

    // For other options, calculate based on days
    const now = new Date();
    let cutoffDate: Date;

    if (timeOption.days <= 30) {
      cutoffDate = subDays(now, timeOption.days);
    } else if (timeOption.days <= 180) {
      cutoffDate = subMonths(now, Math.floor(timeOption.days / 30));
    } else {
      cutoffDate = subYears(now, Math.floor(timeOption.days / 365));
    }
    return startOfDay(cutoffDate).getTime(); // Use start of the day for consistency

  }, [timeFilter]);

  // Fetch tasks with backend filtering
  const tasksResult = useQuery(api.tasks.listTasks, {
    filter: {
      updated_at_after: updatedAtAfterTimestamp,
    },
    pagination: {
      // limit: 100, // Removed limit - let backend handle filtering all tasks
      sortBy: 'updated_at',
      sortDirection: 'desc'
    }
  });

  // Update task status mutation
  const updateTask = useMutation(api.tasks.updateTasks);
  
  // Save time filter to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('tasksTimeFilter', timeFilter);
    }
  }, [timeFilter]);

  // REMOVED filterTasksByTime function

  // Create lookup map for teams (contributors)
  const teamMap = useMemo(() => {
    // Use allTeams.teams and ensure the map uses the correct types
    if (!allTeams?.teams) return new Map<Id<'teams'>, Doc<'teams'>>(); 
    // Assuming allTeams.teams returns data matching TeamOutputSchema, map it back to Doc<'teams'> structure if needed,
    // or adjust the Map type. Let's assume the structure is compatible enough for now.
    // The key needs to be Id<'teams'>.
    return new Map(allTeams.teams.map((team: any) => [team._id as Id<'teams'>, team as Doc<'teams'>])); 
  }, [allTeams]);


  // --- Optimistic state for drag-and-drop ---
  const [optimisticTasks, setOptimisticTasks] = useState<Doc<'tasks'>[]>([]);
  const [pendingStatusUpdate, setPendingStatusUpdate] = useState<{
    id: Id<'tasks'>;
    originalStatus: string;
  } | null>(null);

  // Sync optimisticTasks with server tasks when they change (unless a pending update is in progress)
  useEffect(() => {
    if (tasksResult?.tasks && !pendingStatusUpdate) {
      setOptimisticTasks(
        tasksResult.tasks.map((t) => ({
          ...t,
          _id: t._id as Id<'tasks'>,
        }))
      );
    }
  }, [tasksResult, pendingStatusUpdate]);

  // Group tasks by column, incorporating filtering and sorting
  const getTasksByColumn = useCallback(() => {
    // Ensure required data is loaded (check allTeams instead of allOrgs)
    if (!optimisticTasks || !currentUser || !allTeams) { 
      return {
        todo: [],
        inProgress: [],
        completed: []
      };
    }

    let processedTasks = optimisticTasks;
    const userId = currentUser._id;
    // The 'teams' field on the user doc is already an array of team IDs
    const userTeamIds = currentUser.teams || []; 

    // 1. Apply "My Tasks" Filter (if enabled)
    if (showMyTasksOnly) {
      processedTasks = processedTasks.filter(task => {
        const isDriver = task.driver === userId;
        // Check if contributors array includes the user ID or any of the user's team IDs
        const isContributor = task.contributors?.some((contributorId: Id<'users'> | Id<'teams'>) => 
          contributorId === userId || userTeamIds.includes(contributorId as Id<'teams'>)
        );
        return isDriver || isContributor;
      });
    }

    // 2. Sort Tasks
    processedTasks.sort((a, b) => {
      // Helper to assign role rank
      const getRoleRank = (task: any) => {
        if (task.driver === userId) return 0;
        if (task.contributors?.some((contributorId: any) => contributorId === userId || userTeamIds.includes(contributorId as Id<'teams'>))) return 1;
        return 2;
      };
      const aRank = getRoleRank(a);
      const bRank = getRoleRank(b);

      if (aRank !== bRank) return aRank - bRank;
      // Secondary sort: updated_at descending (fallback to _creationTime if missing)
      const aUpdated = a.updated_at ?? a._creationTime ?? 0;
      const bUpdated = b.updated_at ?? b._creationTime ?? 0;
      return bUpdated - aUpdated;
    });

    // 3. Group into Columns
    return {
      todo: processedTasks.filter(task =>
        COLUMN_STATUS_OPTIONS.todo.includes(task.status || 'todo')
      ),
      inProgress: processedTasks.filter(task =>
        COLUMN_STATUS_OPTIONS.inProgress.includes(task.status || '') &&
        inProgressStatusFilter.includes(task.status || '')
      ),
      completed: processedTasks.filter(task =>
        COLUMN_STATUS_OPTIONS.completed.includes(task.status || '') &&
        completedStatusFilter.includes(task.status || '')
      )
    };
  }, [optimisticTasks, currentUser, showMyTasksOnly, inProgressStatusFilter, completedStatusFilter, teamMap, allTeams]); 

  // Callback for storing draggable refs
  const setDraggableRef = useCallback((id: string, element: HTMLElement | null) => {
    draggableRefs.current[id] = element;
  }, []);
  
  // Handle drag end
  const handleDragEnd = (result: DropResult) => {
    const { source, destination, draggableId } = result;
    
    // If dropped outside a droppable area or in the same column, do nothing
    if (!destination || source.droppableId === destination.droppableId) {
      return;
    }
    
    // Get the task ID from the draggableId
    const taskId = draggableId as Id<'tasks'>;
    const destCol = destination.droppableId;
    const statusOptions =
      destCol === 'todo'
        ? COLUMN_STATUS_OPTIONS.todo
        : destCol === 'inProgress'
        ? COLUMN_STATUS_OPTIONS.inProgress
        : COLUMN_STATUS_OPTIONS.completed;

    if (destCol === 'todo') {
      // Update status to todo directly
      handleStatusUpdate(taskId, 'todo');
    } else {
      // For inProgress and completed columns, open popover to select specific status
      // Find the original status
      const originalStatus =
        (optimisticTasks.find((t) => t._id === taskId)?.status) ||
        (tasksResult?.tasks.find((t) => t._id === taskId)?.status) ||
        '';
      // Optimistically move the task to the destination column (using the first status as a placeholder)
      const tempStatus = statusOptions[0] || destCol;
      setOptimisticTasks((prev) =>
        prev.map((t) =>
          t._id === taskId ? { ...t, status: tempStatus } : t
        )
      );
      setPendingStatusUpdate({ id: taskId, originalStatus });

      // Get the position of the destination column
      let position = { top: 100, left: 100 };
      const destColumnNode = document.querySelector(`[data-kanban-column-id="${destCol}"]`);
      if (destColumnNode) {
        const rect = destColumnNode.getBoundingClientRect();
        position = {
          top: rect.top + rect.height / 2 - 100,
          left: rect.left + rect.width / 2 - 100,
        };
        if (position.left < 10) position.left = 10;
        if (position.left + 200 > window.innerWidth) position.left = window.innerWidth - 210;
        if (position.top < 10) position.top = 10;
        if (position.top + 300 > window.innerHeight) position.top = window.innerHeight - 310;
      }

      setActiveTask({
        id: taskId,
        destinationColumn: destCol,
        position,
      });
      setPopoverOpen(true);
    }
  };
  
  // Function to create a confetti canvas with proper z-index
  const createConfettiCanvas = useCallback(() => {
    // Create a canvas element
    const canvas = document.createElement('canvas');
    canvas.className = 'confetti-canvas'; // Use the CSS class we defined
    document.body.appendChild(canvas);
    
    // Create the confetti instance with fallback
    const myConfetti = confetti.create(canvas, {
      resize: true,
      useWorker: true
    });
    
    // Create a fallback function with the same signature
    const fallbackConfetti: ConfettiFn = () => Promise.resolve();
    
    return {
      confettiInstance: (myConfetti || fallbackConfetti) as ConfettiFn,
      canvas: canvas
    };
  }, []);
  
  // Function to trigger confetti - simplified without fallbacks
  const triggerConfetti = useCallback((taskId: Id<'tasks'>) => {
    // Add a short delay to allow the DOM to update after drag completes
    setTimeout(() => {
      // Create a new confetti canvas for this animation
      const { confettiInstance, canvas } = createConfettiCanvas();
      
      // Try to find the specific task card by its ID
      const taskCard = document.querySelector(`[data-id="${taskId}"]`);
      
      // Only proceed if the task card exists
      if (!taskCard) return;
        
      const rect = taskCard.getBoundingClientRect();
      
      // Calculate the origin point (center-top of the card)
      const originX = (rect.left + rect.width / 2) / window.innerWidth;
      const originY = rect.top / window.innerHeight;
      
      // Mark the promise as intentionally ignored with void
      void confettiInstance({
        origin: { x: originX, y: originY },
        particleCount: 150,
        spread: 70
      }).then(() => {
        // Remove the canvas after animation completes
        setTimeout(() => {
          canvas.remove();
        }, 3000);
      });
    }, 100); // 100ms delay to allow for DOM updates
  }, [createConfettiCanvas]);
  
  // Handle status update
  const handleStatusUpdate = async (taskId: Id<'tasks'>, newStatus: string) => {
    try {
      // First update the status
      await updateTask({
        updates: {
          id: taskId,
          updates: {
            status: newStatus as z.infer<typeof TaskStatusEnum>,
            // updated_at is handled server-side in the updateSingleTask function
          },
        },
      });

      // Show confetti only for completed status (not cancelled)
      if (newStatus === 'completed') {
        triggerConfetti(taskId);
      }

      toast({
        title: 'Status updated',
        description: `Task status changed to ${STATUS_DISPLAY_NAMES[newStatus]}`,
      });
    } catch (error: unknown) {
      console.error('Error updating task status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update task status',
        variant: 'destructive',
      });
    } finally {
      setPopoverOpen(false);
      setActiveTask(null);
    }
  };
  
  // Get the status options for the active task's destination column
  const getStatusOptionsForPopover = () => {
    if (!activeTask) return [];
    
    if (activeTask.destinationColumn === 'inProgress') {
      return COLUMN_STATUS_OPTIONS.inProgress;
    } else if (activeTask.destinationColumn === 'completed') {
      return COLUMN_STATUS_OPTIONS.completed;
    }
    
    return [];
  };
  
  // Handle status filter change for In Progress column
  const handleInProgressStatusFilterChange = (status: string, checked: boolean) => {
    if (checked) {
      setInProgressStatusFilter(prev => [...prev, status]);
    } else {
      setInProgressStatusFilter(prev => prev.filter(s => s !== status));
    }
  };
  
  // Handle status filter change for Completed column
  const handleCompletedStatusFilterChange = (status: string, checked: boolean) => {
    if (checked) {
      setCompletedStatusFilter(prev => [...prev, status]);
    } else {
      setCompletedStatusFilter(prev => prev.filter(s => s !== status));
    }
  };
  
  // Get tasks grouped by column
  const tasksByColumn = getTasksByColumn();
  
  return (
    <div className="container mx-auto py-6">
      {/* Filters Row */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        {/* Time filter */}
        <div className="flex items-center gap-2">
          <Label htmlFor="time-filter-select" className="text-sm font-medium">Time Filter:</Label>
          <Select value={timeFilter} onValueChange={setTimeFilter}>
            <SelectTrigger id="time-filter-select" className="w-[180px]">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              {TIME_FILTER_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* "My Tasks" Filter Checkbox */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="my-tasks-filter"
            checked={showMyTasksOnly}
            onCheckedChange={(checked) => setShowMyTasksOnly(checked === true)}
          />
          <Label htmlFor="my-tasks-filter" className="text-sm font-medium">
            Show only my tasks (Driver/Contributor)
          </Label>
        </div>
      </div>

      {/* Loading state */}
      {!tasksResult && (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
        </div>
      )}
      
      {/* Kanban board */}
      {tasksResult && (
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Todo column */}
            <div data-kanban-column-id="todo">
              <KanbanColumn
                id="todo"
                title={COLUMN_DISPLAY_NAMES.todo}
                tasks={tasksByColumn.todo}
                isDropDisabled={popoverOpen}
                setDraggableRef={setDraggableRef}
                currentUserId={currentUser?._id}
                teamMap={teamMap}
              />
            </div>
            
            {/* In Progress column */}
            <div data-kanban-column-id="inProgress">
              <KanbanColumn
                id="inProgress"
                title={COLUMN_DISPLAY_NAMES.inProgress}
                tasks={tasksByColumn.inProgress}
                isDropDisabled={popoverOpen}
                statusFilter={inProgressStatusFilter}
                onStatusFilterChange={handleInProgressStatusFilterChange}
                statusOptions={COLUMN_STATUS_OPTIONS.inProgress}
                setDraggableRef={setDraggableRef}
                currentUserId={currentUser?._id}
                teamMap={teamMap}
              />
            </div>
            
            {/* Completed column */}
            <div ref={completedColumnRef} data-kanban-column-id="completed">
              <KanbanColumn
                id="completed"
                title={COLUMN_DISPLAY_NAMES.completed}
                tasks={tasksByColumn.completed}
                isDropDisabled={popoverOpen}
                statusFilter={completedStatusFilter}
                onStatusFilterChange={handleCompletedStatusFilterChange}
                statusOptions={COLUMN_STATUS_OPTIONS.completed}
                setDraggableRef={setDraggableRef}
                currentUserId={currentUser?._id}
                teamMap={teamMap}
              />
            </div>
          </div>
        </DragDropContext>
      )}
      
      {/* Status selection popover */}
      <MorphingPopover open={popoverOpen} onOpenChange={(open) => {
        if (!open && pendingStatusUpdate) {
          // User cancelled: revert the optimistic update
          setOptimisticTasks(prev =>
            prev.map(t =>
              t._id === pendingStatusUpdate.id ? { ...t, status: pendingStatusUpdate.originalStatus } : t
            )
          );
          setPendingStatusUpdate(null);
        }
        setPopoverOpen(open);
      }}>
        {popoverOpen && activeTask?.position && (
          <MorphingPopoverContent 
            className="w-64"
            top={activeTask.position.top}
            left={activeTask.position.left}
          >
            <div className="space-y-4">
              <h3 className="font-medium">Select Status</h3>
              <div className="flex flex-col gap-2">
                {getStatusOptionsForPopover().map(status => (
                  <Button
                    key={status}
                    variant="outline"
                    className="justify-start"
                    onClick={() => {
                      activeTask && handleStatusUpdate(activeTask.id, status);
                      setPendingStatusUpdate(null);
                    }}
                  >
                    {STATUS_DISPLAY_NAMES[status]}
                  </Button>
                ))}
              </div>
              <Button 
                variant="ghost" 
                className="w-full" 
                onClick={() => {
                  setPopoverOpen(false);
                  setActiveTask(null);
                  // Revert optimistic update on cancel
                  if (pendingStatusUpdate) {
                    setOptimisticTasks(prev =>
                      prev.map(t =>
                        t._id === pendingStatusUpdate.id ? { ...t, status: pendingStatusUpdate.originalStatus } : t
                      )
                    );
                    setPendingStatusUpdate(null);
                  }
                }}
              >
                Cancel
              </Button>
            </div>
          </MorphingPopoverContent>
        )}
      </MorphingPopover>
    </div>
  );
};

interface KanbanColumnProps {
  id: string;
  title: string;
  tasks: any[];
  isDropDisabled: boolean;
  statusFilter?: string[];
  onStatusFilterChange?: (status: string, checked: boolean) => void;
  statusOptions?: string[];
  setDraggableRef: (id: string, element: HTMLElement | null) => void;
  currentUserId?: Id<'users'>;
  // Keep only teamMap prop
  teamMap: Map<Id<'teams'>, Doc<'teams'>>;
}

/**
 * KanbanColumn Component
 * 
 * Represents a column in the Kanban board.
 * Displays a list of tasks and allows for filtering by status.
 */
const KanbanColumn: React.FC<KanbanColumnProps> = ({
  id,
  title,
  tasks,
  isDropDisabled,
  statusFilter,
  onStatusFilterChange,
  statusOptions,
  setDraggableRef,
  currentUserId,
  // Destructure only teamMap
  teamMap
}) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  
  return (
    <Card className="flex flex-col h-full backdrop-blur-none">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">{title}</h3>
          <div className="text-sm text-muted-foreground">{tasks.length}</div>
        </div>
        
        {/* Status filter (only for In Progress and Completed columns) */}
        {statusOptions && statusFilter && onStatusFilterChange && (
          <div className="mt-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full text-xs"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              Filter Status
            </Button>
            
            {isFilterOpen && (
              <div className="mt-2 p-2 border rounded-md bg-background">
                <div className="space-y-2">
                  {statusOptions.map(status => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`${id}-${status}`}
                        checked={statusFilter.includes(status)}
                        onCheckedChange={(checked) => 
                          onStatusFilterChange(status, checked === true)
                        }
                      />
                      <Label htmlFor={`${id}-${status}`} className="text-xs">
                        {STATUS_DISPLAY_NAMES[status]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      <Droppable droppableId={id} isDropDisabled={isDropDisabled}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`flex-1 p-2 overflow-y-auto min-h-[300px] ${
              snapshot.isDraggingOver ? 'bg-muted/50' : ''
            }`}
          >
            {tasks.length === 0 ? (
              <div className="h-full flex items-center justify-center text-sm text-muted-foreground">
                No tasks
              </div>
            ) : (
              <div className="space-y-3">
                {tasks.map((task: Doc<'tasks'>, index) => ( // Added type annotation for task
                  <Draggable
                    key={task._id}
                    draggableId={task._id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={(el) => {
                          // Store ref in draggableRefs and pass to provided.innerRef
                          setDraggableRef(task._id, el);
                          provided.innerRef(el);
                        }}
                        data-id={task._id} // Add data-id attribute for easier selection
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={`${snapshot.isDragging ? 'opacity-70' : ''}`}
                      >
                        <TasksCard
                          task={task}
                          currentUserId={currentUserId}
                          // driverDetails will be fetched inside TasksCard
                          // Pass only team contributor details derived from teamMap
                          contributorDetails={(task.contributors ?? []).map((id: Id<'users'> | Id<'teams'>) => {
                            const team = teamMap.get(id as Id<'teams'>);
                            if (team) {
                              // Only return team details here
                              return { _id: team._id, name: team.name, type: 'team' as const };
                            }
                            // User contributor details will be handled inside TasksCard if needed, or fetched there
                            return null;
                          // Explicitly type 'c' in the filter predicate
                          }).filter((c: { _id: Id<'teams'>; name: string; type: 'team'; } | null): c is { _id: Id<'teams'>; name: string; type: 'team'; } => c !== null)}
                        />
                      </div>
                    )}
                  </Draggable>
                ))}
              </div>
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </Card>
  );
};

export default KanbanView;
