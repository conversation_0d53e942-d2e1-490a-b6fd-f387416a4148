'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { Doc, Id } from '@/convex/_generated/dataModel';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import * as Icons from '@/components/icons';
import { motion, useTransform, AnimatePresence, useMotionValue, useSpring } from 'motion/react';
import { createPortal } from 'react-dom';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; 
import { Spinner } from '@/components/ui/spinner'; // Import Spinner

// Define status color mapping
const statusColorMap: Record<string, string> = {
  todo: 'bg-gray-100 text-gray-800 border-gray-300',
  in_progress: 'bg-yellow-100 text-yellow-800 border-yellow-300',
  blocked: 'bg-red-100 text-red-800 border-red-300',
  completed: 'bg-green-100 text-green-800 border-green-300',
  cancelled: 'bg-purple-100 text-purple-800 border-purple-300'
};

// Define status display names
const statusDisplayNames: Record<string, string> = {
  todo: 'To Do',
  in_progress: 'In Progress',
  blocked: 'Blocked',
  completed: 'Completed',
  cancelled: 'Cancelled'
};

// Define importance color mapping
const importanceColorMap: Record<string, string> = {
  low: 'bg-blue-100 text-blue-800 border-blue-300',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-300',
  high: 'bg-orange-100 text-orange-800 border-orange-300',
  critical: 'bg-red-100 text-red-800 border-red-300'
};

// Define importance display names
const importanceDisplayNames: Record<string, string> = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  critical: 'Critical'
};

interface TasksCardProps {
  task: Doc<'tasks'> & {
    badges?: string[];
  };
  onClick?: () => void;
  currentUserId?: Id<'users'>;
  // Removed driverDetails prop, will fetch internally
  contributorDetails?: { _id: Id<'teams'>; name: string; type: 'team' }[]; // Only team details needed now
}

// Icon map to associate string names with Lucide icon components
const iconMap: Record<string, React.ComponentType<any>> = {
  thumbsUp: Icons.ThumbsUp,
  checkCircle: Icons.CheckCircle,
  xCircle: Icons.XCircle,
  clock: Icons.Clock,
  userIcon: Icons.UsersIcon,
  // Add more icons as needed
};

// Add this new component before the TasksCard component
const DriverAvatar: React.FC<{ 
  driverPerson: any | undefined, 
  driverId: Id<"users"> | undefined 
}> = ({ driverPerson, driverId }) => {
  if (!driverId) {
    return (
      <Avatar className="h-5 w-5" title="No driver assigned">
        <AvatarImage src="/placeholder-user.jpg" alt="No driver" />
        <AvatarFallback>
          <Icons.UsersIcon className="h-3 w-3" />
        </AvatarFallback>
      </Avatar>
    );
  }

  if (driverPerson === undefined) {
    return (
      <Avatar className="h-5 w-5" title="Loading driver...">
        <AvatarFallback>
          <Spinner />
        </AvatarFallback>
      </Avatar>
    );
  }

  const driverInitials = driverPerson?.name?.charAt(0).toUpperCase() ?? '?';
  const driverName = driverPerson?.name ?? '...';

  return (
    <Avatar className="h-5 w-5" title={`Driver: ${driverName}`}>
      <AvatarImage src={driverPerson?.image ?? undefined} alt={driverName} />
      <AvatarFallback className="text-xs">
        {driverInitials}
      </AvatarFallback>
    </Avatar>
  );
};

/**
 * TasksCard Component
 * 
 * Compact card displaying task information.
 * Optimized for vertical space efficiency.
 * Shows title, status, description, badges, assignee, and last update time.
 * Displays driver avatar and team contributor badges.
 */
const TasksCard: React.FC<TasksCardProps> = ({
  task,
  onClick,
  currentUserId, // Keep currentUserId if needed for other logic, otherwise remove
  // driverDetails removed
  contributorDetails
}) => {
  // Fetch driver's person details using the driver's user ID
  const driverPerson = useQuery(
    api.directory.directoryPeople.getPersonByUserId,
    task.driver ? { userId: task.driver } : 'skip' // Skip query if no driver
  );

  // State for accent bar hover
  const [isAccentHovered, setIsAccentHovered] = useState(false);

  // Spring animation config
  const springConfig = { stiffness: 100, damping: 5 };
  const x = useMotionValue(0);
  const rotate = useSpring(
    useTransform(x, [-100, 100], [-45, 45]),
    springConfig
  );
  const translateX = useSpring(
    useTransform(x, [-100, 100], [-50, 50]),
    springConfig
  );

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const halfWidth = event.currentTarget.offsetWidth / 2;
    x.set(event.nativeEvent.offsetX - halfWidth);
  };

  // Format dates for display
  const formattedDueDate = task.due_date
    ? formatDistanceToNow(new Date(task.due_date), { addSuffix: true })
    : 'No due date';

  // Format updated time
  const formattedUpdateTime = task.updated_at
    ? formatDistanceToNow(new Date(task.updated_at), { addSuffix: true, includeSeconds: false }).replace('about ', '')
    : formatDistanceToNow(new Date(task._creationTime), { addSuffix: true, includeSeconds: false }).replace('about ', '');

  // Get status color class
  const statusColorClass = task.status
    ? statusColorMap[task.status]
    : 'bg-gray-100 text-gray-800 border-gray-300';

  // Fetch badges if they exist
  const badges = useQuery(
    api.badges.getBadgesByIds,
    task.badges && task.badges.length > 0 
      ? { badgeIds: task.badges.map(id => id as Id<"badges">) } 
      : 'skip'
  );

  const router = useRouter();

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.push(`/tasks/${task._id}`);
    }
  };

  // Define accent colors based on status, using CSS variables from globals.css
  const getAccentColor = (status: string | undefined) => {
    switch (status) {
      case 'todo':
        return 'hsl(var(--chart-10) / 0.4)'; // Gray, increased opacity
      case 'in_progress':
        return 'hsl(var(--chart-4) / 0.4)'; // Yellow, increased opacity
      case 'completed':
        return 'hsl(var(--chart-9) / 0.5)'; // Green, increased opacity
      case 'blocked':
        return 'hsl(var(--chart-3) / 0.4)'; // Red, increased opacity
      case 'cancelled':
        return 'hsl(var(--chart-5) / 0.4)'; // Purple, increased opacity
      default:
        return 'hsl(var(--chart-10) / 0.4)'; // Gray, increased opacity
    }
  };

  // Define accent colors based on status for dark mode, using CSS variables from globals.css
  const getAccentColorDark = (status: string | undefined) => {
    switch (status) {
      case 'todo':
        return 'hsl(var(--chart-10) / 0.7)'; // Gray, increased opacity
      case 'in_progress':
        return 'hsl(var(--chart-4) / 0.7)'; // Yellow, increased opacity
      case 'completed':
        return 'hsl(var(--chart-9) / 0.8)'; // Green, increased opacity
      case 'blocked':
        return 'hsl(var(--chart-3) / 0.7)'; // Red, increased opacity
      case 'cancelled':
        return 'hsl(var(--chart-5) / 0.7)'; // Purple, increased opacity
      default:
        return 'hsl(var(--chart-10) / 0.7)'; // Gray, increased opacity
    }
  };

  const accentColor = getAccentColor(task.status);
  const accentColorDark = getAccentColorDark(task.status);

  // Get status display name
  const statusName = task.status
    ? statusDisplayNames[task.status]
    : 'To Do';

  // Add state for tooltip position
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const accentBarRef = React.useRef<HTMLDivElement>(null);

  // Update tooltip position when accent bar is hovered
  const updateTooltipPosition = () => {
    if (accentBarRef.current) {
      const rect = accentBarRef.current.getBoundingClientRect();
      // Add window scroll offset to position
      setTooltipPosition({
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY
      });
    }
  };

  return (
    <Card
      className="transition-all hover:shadow-md cursor-pointer rounded-lg overflow-hidden border border-gray-200 relative"
      onClick={handleCardClick}
      onMouseMove={handleMouseMove}
    >
      {/* Accent bar with ref and position updating */}
      <div
        ref={accentBarRef}
        className="absolute left-0 top-0 bottom-0 w-2 z-10 hover:bg-opacity-80"
        style={{
          backgroundColor: `var(--tw-content-bg, ${accentColor})`,
          boxShadow: `0 0 8px 1px ${accentColor}`,
        }}
        onMouseEnter={() => {
          setIsAccentHovered(true);
          updateTooltipPosition();
        }}
        onMouseLeave={() => {
          setIsAccentHovered(false);
        }}
      />

      {/* Portal the tooltip outside the card */}
      {typeof window !== 'undefined' && isAccentHovered && createPortal(
        <AnimatePresence mode="popLayout">
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.6 }}
            animate={{
              opacity: 1,
              y: 0,
              scale: 1,
              transition: {
                type: "spring",
                stiffness: 260,
                damping: 10,
              },
            }}
            exit={{ opacity: 0, y: 20, scale: 0.6 }}
            className="fixed flex text-xs flex-col items-center justify-center rounded-md shadow-xl px-4 py-2 pointer-events-none"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.9)',
              whiteSpace: "nowrap",
              position: 'fixed',
              left: `${tooltipPosition.x - 96}px`,
              top: `${tooltipPosition.y - 40}px`,
              zIndex: 99999,
              boxShadow: '0 0 10px rgba(0,0,0,0.5)',
            }}
          >
            <div
              className="font-bold relative z-30 text-base"
              style={{ color: accentColor, textShadow: `0 0 8px ${accentColor}` }}
            >
              {statusName}
            </div>
            <div className="text-white text-xs">
              Status
            </div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      <CardContent className="p-3 pl-4">
        <div className="flex flex-col space-y-1">
          {/* Header: Full width with smaller font */}
          <div className="w-full pb-0.5">
            {/* Removed Driver Indicator Icon Span */}
            <h3 className="text-sm font-medium line-clamp-1">{task.name}</h3>

            {/* Description directly below heading */}
            {task.short_description && (
              <p className="text-xs text-muted-foreground line-clamp-1 py-0 mt-0.5">
                {task.short_description}
              </p>
            )}
          </div>

          {/* Badges section */}
          {badges && badges.length > 0 && (
            <div className="flex flex-wrap gap-1 items-center py-1">
              {badges.map((badge: Doc<"badges">) => (
                <Badge
                  key={badge._id}
                  variant="secondary"
                  className="text-xs font-light py-0 h-5"
                  style={{
                    backgroundColor: `${badge.color}0D`,
                    color: badge.color,
                    borderColor: `${badge.color}1A`
                  }}
                >
                  {badge.icon ? (
                    iconMap[badge.icon] ? (
                      React.createElement(iconMap[badge.icon], {
                        className: 'mr-1 h-3 w-3'
                      })
                    ) : (
                      <Icons.HelpCircle className="mr-1 h-3 w-3" />
                    )
                  ) : null}
                  {badge.name}
                </Badge>
              ))}
            </div>
          )}

          {/* Importance badge if available */}
          {task.importance && (
            <div className="flex flex-wrap gap-1 items-center py-1">
              <Badge
                variant="secondary"
                className="text-xs font-light py-0 h-5"
                style={{
                  backgroundColor: `${importanceColorMap[task.importance].split(' ')[0]}`,
                  color: `${importanceColorMap[task.importance].split(' ')[1]}`,
                  borderColor: `${importanceColorMap[task.importance].split(' ')[2]}`
                }}
              >
                {importanceDisplayNames[task.importance]}
              </Badge>
            </div>
          )}

          {/* Team Contributors Chips - Moved below description/badges/importance, above footer */}
          {contributorDetails && contributorDetails.filter(c => c.type === 'team').length > 0 && (
            <div className="flex flex-wrap gap-1 items-center py-1">
              {contributorDetails.filter(c => c.type === 'team').map((contributor) => (
                <Badge
                  key={contributor._id}
                  variant="outline" // Use outline or secondary
                  className="text-xs font-light py-0 h-5"
                >
                  {/* Removed Team icon */}
                  {contributor.name}
                </Badge>
              ))}
            </div>
          )}


          {/* Bottom row: Driver Avatar, Due date and last updated */}
          <div className="flex justify-between items-center text-xs pt-1">
            <div className="flex items-center gap-3">
               {/* Moved Driver Avatar Here */}
               <DriverAvatar driverPerson={driverPerson} driverId={task.driver} />
              {/* Due date - more compact */}
              <div className="flex items-center" title={`Due: ${formattedDueDate}`}>
                <span className="text-muted-foreground mr-1">Due</span>
                <span className="font-medium">
                  {formattedDueDate.replace(' ago', '').replace('in ', '')}
                </span>
              </div>
            </div>

            {/* Last updated - just icon and time */}
            <div
              className="flex items-center text-muted-foreground"
              title={`Updated: ${formattedUpdateTime}`}
            >
              <Icons.Clock className="h-3 w-3 mr-1" />
              <span>
                {formattedUpdateTime.replace(' ago', '').replace('in ', '')}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TasksCard;
