'use client';

import { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

// This page now acts as a redirector to the default overview tab
export default function TaskDetailsRedirectPage() {
  const params = useParams();
  const router = useRouter();
  const taskId = params.id as string;

  useEffect(() => {
    if (taskId) {
      // Redirect to the overview page for this task
      router.replace(`/tasks/${taskId}/overview`);
    }
    // Add dependencies to useEffect
  }, [taskId, router]);

  // Render minimal loading state while redirect happens
  return (
     <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Loading task...</p>
      </div>
  );
}
