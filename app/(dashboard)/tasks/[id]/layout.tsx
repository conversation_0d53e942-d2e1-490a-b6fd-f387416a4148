'use client';

// Import necessary hooks and components
import React, { useState, useEffect, ReactNode } from 'react';
import { usePara<PERSON>, useRouter, usePathname } from 'next/navigation';
import { useQuery, useMutation } from 'convex/react';
import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useToast } from '@/components/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu imports
import SecondaryNavMenu from "@/components/blocks/secondary-nav-menu";
import TaskActionBar from '@/components/tasks/TaskActionBar'; // Use TaskActionBar
import { ArrowLeft, Trash2, AlertCircle, MoreVertical } from 'lucide-react'; // Added MoreVertical
import CollectionCardModal from "@/components/ui/collectionCardModal"; // For delete modal
import { Badge } from "@/components/ui/badge"; // For delete modal
import { getStatusStyle, formatStatus } from '@/lib/utils/task-status-styles'; // For delete modal
import { formatRelativeTime } from "@/lib/date-utils"; // For delete modal

// Define the props for the layout component
interface TaskDetailLayoutProps {
  children: ReactNode;
}

export default function TaskDetailLayout({ children }: TaskDetailLayoutProps) {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const taskId = params.id as string | undefined; // Allow undefined
  const { toast } = useToast();

  // State for title editing
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [titleValue, setTitleValue] = useState('');

  // State for saving status (used for title saves)
  const [isSaving, setIsSaving] = useState(false);

  // State for delete confirmation modal
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Fetch task data - skip if taskId is undefined
  const task = useQuery(
    api.tasks.getTask,
    taskId ? { taskId: taskId as Id<'tasks'> } : 'skip'
  );

  // Fetch assigned tags - skip if taskId is undefined
  const assignedTags = useQuery(
    api.tags.getTagsForTaggable,
    taskId ? {
      taggable_type: "task",
      taggable_id: taskId as Id<'tasks'>
    } : 'skip'
  ) ?? []; // Default to empty array

  // Mutations
  const deleteTask = useMutation(api.tasks.deleteTasks);
  const updateTask = useMutation(api.tasks.updateTasks);
  const removeTagFromTaggable = useMutation(api.tags.removeTagFromTaggable);

  // Effect to handle task deletion while on the page or invalid ID
  useEffect(() => {
    if (task === null) { // If query ran and returned null (not found)
      toast({ title: "Task not found", variant: "destructive" });
      router.push("/tasks");
    }
  }, [task, router, toast]);

  // Update title state when task loads or changes
  useEffect(() => {
    if (task) {
      setTitleValue(task.name || '');
    }
  }, [task]);

  // Handle title save on blur
  const handleTitleSave = async () => {
    if (!taskId || !task) return; // Guard against missing ID or task data
    if (titleValue.trim() !== '' && titleValue !== task.name) {
      try {
        setIsSaving(true);
        await updateTask({
          updates: {
            id: taskId as Id<'tasks'>,
            updates: { name: titleValue.trim() }
          }
        });
        toast({
          title: "Title updated",
          description: "The title has been updated successfully."
        });
      } catch (error: unknown) {
        toast({
          title: "Error updating title",
          description: error instanceof Error ? error.message : "There was an error updating the title.",
          variant: "destructive"
        });
        setTitleValue(task.name || '');
      } finally {
        setIsSaving(false);
      }
    } else if (titleValue.trim() === '') {
      setTitleValue(task.name || '');
    }
    setIsEditingTitle(false);
  };

   // Handler for removing a tag
  const handleRemoveTag = async (tagId: Id<'tags'>) => {
     if (!taskId) return;
    try {
      await removeTagFromTaggable({
        taggable_type: "task",
        taggable_id: taskId as Id<'tasks'>,
        tagId: tagId
      });
      toast({
        title: 'Success',
        description: 'Tag removed successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error removing tag',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      });
    }
  };

  // Function to open the delete confirmation modal
  const openDeleteModal = () => {
    setIsDeleteModalOpen(true);
  };

    // Function to close the delete confirmation modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  // Handle delete action with optimistic navigation and delay
  const handleDelete = async () => {
    // Add explicit check for task and taskId
    if (!taskId || !task) return;
    // Capture name before navigation
    const taskName = task.name || 'Untitled Task';

    // Close the modal first
    setIsDeleteModalOpen(false);
    // Set saving state to disable buttons
    setIsSaving(true);

    // Optimistically navigate away first
    router.push('/tasks');

    // Show initial toast
    toast({
      title: "Deleting task...",
      description: `"${taskName}" is being deleted.`
    });

    // Add a delay to allow navigation/unmount before mutation
    await new Promise(resolve => setTimeout(resolve, 500)); // Updated delay to 500ms

    // Perform the delete mutation in the background
    try {
      const result = await deleteTask({ taskIds: [taskId as Id<'tasks'>] });

      // Handle potential partial failures from the mutation if needed
      if (result.failedIds && result.failedIds.length > 0) {
         toast({
           title: "Error deleting task",
           description: `Failed to delete "${taskName}". ${result.failedIds[0].error}`,
           variant: "destructive",
           duration: 10000
         });
      }
      // No success toast needed as user is navigated away
    } catch (error: unknown) {
      // Show error toast if background deletion fails
      toast({
        title: "Error deleting task",
        description: `Failed to delete "${taskName}". ${error instanceof Error ? error.message : ''}`,
        variant: "destructive",
        duration: 10000 // Show error for longer
      });
    }
    // No need to reset isSaving as the component should unmount
  };

  // Determine active tab based on pathname
  const getCurrentActiveTab = () => {
    if (pathname?.endsWith('/activity')) return 'activity';
    return 'overview'; // Default to overview
  };
  const activeTab = getCurrentActiveTab();

  // Define nav items with correct hrefs
  const navItems = [
    { label: "Overview", href: `/tasks/${taskId}/overview` },
    { label: "Activity", href: `/tasks/${taskId}/activity` }
    // Add more items like "Details" if needed
  ];

  // Handle loading state
  if (task === undefined) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Loading task details...</p>
      </div>
    );
  }

   // Handle not found state (redundant due to useEffect, but safe)
  if (task === null) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <AlertCircle className="h-12 w-12 text-destructive" />
        <p className="mt-4 text-lg font-semibold">Task not found</p>
        <p className="text-muted-foreground">The requested task could not be loaded.</p>
        <Button variant="outline" className="mt-4" asChild>
          <Link href="/tasks">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tasks
          </Link>
        </Button>
      </div>
    );
  }

  // Format short description
  const shortDescription = task.short_description || 'No description available';

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="px-3 pt-3 pb-1"> {/* Reduced bottom padding */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" asChild>
              <Link href="/tasks">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              {isEditingTitle ? (
                <Input
                  type="text"
                  value={titleValue}
                  onChange={(e) => setTitleValue(e.target.value)}
                  onBlur={handleTitleSave}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') e.currentTarget.blur();
                    else if (e.key === 'Escape') {
                      setTitleValue(task.name || '');
                      setIsEditingTitle(false);
                    }
                  }}
                  autoFocus
                  className="text-2xl font-bold text-gray-900 bg-[hsl(var(--input))] border-none focus:outline-none focus:ring-2 focus:ring-primary/20 rounded px-1 w-full"
                  disabled={isSaving}
                />
              ) : (
                <h1
                  className="text-2xl font-bold text-gray-900 cursor-pointer hover:bg-gray-50 rounded px-1"
                  onClick={() => {
                    setTitleValue(task.name || '');
                    setIsEditingTitle(true);
                  }}
                >
                  {task.name || 'Untitled Task'}
                </h1>
              )}
              <p className="text-sm text-muted-foreground">{shortDescription}</p>
            </div>
          </div>
          <div className="flex gap-2">
            {/* More Options Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" disabled={isSaving}>
                  <MoreVertical className="h-4 w-4" />
                  <span className="sr-only">More options</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {/* Trigger for the existing custom modal */}
                <DropdownMenuItem
                  onClick={openDeleteModal} // Still use openDeleteModal to show the custom modal
                  className="text-red-500 focus:bg-red-50 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Task
                </DropdownMenuItem>
                {/* Add other options here if needed */}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Tabs navigation using SecondaryNavMenu */}
        <div className="mt-2"> {/* Reduced margin */}
          <SecondaryNavMenu
            items={navItems}
            activeItem={activeTab}
            // Rely on component's internal logic for active state
          />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Action Bar */}
        <TaskActionBar
          item={task}
          updateTask={updateTask}
          tags={assignedTags}
          onRemoveTag={handleRemoveTag}
        />
        {/* Render the specific tab's content */}
        <div className="p-4"> {/* Add padding around children */}
          {children}
        </div>
      </main>
       {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
         <CollectionCardModal
          heading="Confirm Task Deletion"
          subheading="This action cannot be undone"
          primaryCTA={{
            text: isSaving ? "Deleting..." : "Delete Task", // Update button text while saving
            onClick: handleDelete,
            // disabled: isSaving, // Removed disabled prop - modal doesn't support it
          }}
          secondaryCTA={{
            text: "Cancel",
            // disabled: isSaving, // Removed disabled prop
            onClick: closeDeleteModal,
          }}
          onClose={closeDeleteModal}
          showFooter={true}
          className="max-w-sm"
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              You are about to permanently delete the following task:
            </p>

            <div className="bg-gray-100/50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900">{task.name}</h3>
              {task.short_description && (
                <p className="text-sm text-gray-600 mt-1">
                  {task.short_description}
                </p>
              )}
              <div className="mt-2 flex items-center">
                <Badge
                  variant="outline"
                  className={`${getStatusStyle(task?.status)}`}
                >
                  {formatStatus(task?.status)}
                </Badge>
                <span className="text-xs text-gray-500 ml-2">
                  Created {formatRelativeTime(task._creationTime)}
                </span>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              <p>
                This will permanently remove this task and all associated
                data from the system.
              </p>
              <p className="mt-2 font-medium text-red-600">
                This action cannot be reversed.
              </p>
            </div>
          </div>
        </CollectionCardModal>
      )}
    </div>
  );
}
