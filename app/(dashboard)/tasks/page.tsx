'use client';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import TemplateFilters, { FilterOption } from '../../../components/blocks/template-filters';
import ViewSwitcher from '../../../components/blocks/view-switcher';
import { useToast } from '@/components/hooks/use-toast';
import TemplateTable from '../../../components/blocks/template-table';
import CollectionCard from '@/components/ui/collection-card';
import TasksCard from './components/TasksCard';
import KanbanView from './components/KanbanView';
import { Button } from '@/components/ui/button';
 
import { Spinner } from '@/components/ui/spinner';
import { TaskStatusEnum, TaskImportanceEnum } from '@/zod/tasks-schema';
 
/**
 * Tasks Dashboard Page
 * 
 * Displays a list of tasks with filtering and pagination.
 * Supports three view modes:
 * - Kanban: Drag-and-drop board with columns for different task statuses
 * - Card: Grid layout of task cards
 * - Table: Tabular data presentation with sorting and filtering
 * 
 * The Kanban view is the default and allows users to visually manage task
 * workflow by dragging cards between status columns.
 */
export default function TasksPage() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>('all');
  const [importanceFilter, setImportanceFilter] = useState<string | null>('all');
  const [viewMode, setViewMode] = useState<'card' | 'list' | 'kanban'>('kanban'); // Default to kanban view
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [cursor, setCursor] = useState<any>(null);
  const [allTasks, setAllTasks] = useState<any[]>([]);

  // Fetch tasks from Convex with pagination
  const tasksResult = useQuery(api.tasks.listTasks, {
    filter: {},
    pagination: {
      limit: 25,
      cursor: cursor,
      sortBy: '_creationTime',
      sortDirection: 'desc'
    }
  });

  // Update allTasks when new data is fetched
  React.useEffect(() => {
    if (tasksResult?.tasks) {
      if (cursor) {
        // Append new tasks to existing ones
        setAllTasks(prev => [...prev, ...tasksResult.tasks]);
      } else {
        // Initial load
        setAllTasks(tasksResult.tasks);
      }
    }
  }, [tasksResult, cursor]);

  // Status options for filtering
  const statusOptions: FilterOption[] = [
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'blocked', label: 'Blocked' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  // Importance options for filtering
  const importanceOptions: FilterOption[] = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  // ===========================
  // FILTERING LOGIC 
  // ===========================

  // Filter tasks based on search term, status filter, and importance filter
  const filteredTasks = allTasks?.filter((task) => {
    // Text search in name and description
    const matchesSearch =
      !searchTerm ||
      (task.name &&
        task.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (task.description &&
        task.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Status filter
    const matchesStatus =
      !statusFilter ||
      statusFilter === 'all' ||
      task.status === statusFilter;

    // Importance filter
    const matchesImportance =
      !importanceFilter ||
      importanceFilter === 'all' ||
      task.importance === importanceFilter;

    return matchesSearch && matchesStatus && matchesImportance;
  }) || [];

  // ===========================
  // EVENT HANDLERS
  // ===========================

  // Handle item checkbox change for table view
  const handleItemCheckboxChange = (checked: boolean, id: string) => {
    if (checked) {
      setSelectedItems((prev) => [...prev, id]);
    } else {
      setSelectedItems((prev) => prev.filter((itemId) => itemId !== id));
    }
  };

  // Handle select all checkbox for table view
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredTasks.map((item) => item._id));
    } else {
      setSelectedItems([]);
    }
  };

  const router = useRouter();
  
  // Handle task click
  const handleTaskClick = (id: Id<'tasks'>) => {
    // Navigate to task details
    router.push(`/tasks/${id}`);
  };

  // Handle load more button click
  const handleLoadMore = () => {
    if (tasksResult?.continuation) {
      setCursor(tasksResult.continuation);
    }
  };

  // Convert tasks to table items format
  const tableItems = filteredTasks.map(task => ({
    id: task._id,
    title: task.name,
    description: task.description || '',
    status: task.status || 'todo',
    lastUpdated: task.updated_at || task._creationTime,
    importance: task.importance,
    assignedTo: task.assigned_to,
    dueDate: task.due_date,
    // Add any other fields needed for the table
  }));

  return (
    <div className="container mx-auto py-6">
      {/* Header section with title, subtitle, and view switcher */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Tasks</h1>
          <p className="text-muted-foreground">
            Manage and track all tasks across your organization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <ViewSwitcher viewMode={viewMode} setViewMode={setViewMode} />
        </div>
      </div>

     

      {/* Loading state */}
      {!tasksResult && (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
        </div>
      )}
        
       {/* Table View */}
      {viewMode === "list" && tasksResult && (
        <div>
          {filteredTasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No tasks found. Try adjusting your filters or create a new task.
            </div>
          ) : (
            <TemplateTable
              items={tableItems}
              paginationEnabled={true}
              selectedItems={selectedItems}
              onItemCheckboxChange={handleItemCheckboxChange}
              onSelectAll={handleSelectAll}
              baseRoute="/tasks"
            />
          )}
        </div>
      )}

      {/* Kanban View */}
      {viewMode === 'kanban' && tasksResult && (
        <KanbanView />
      )}

      {/* Card View */}
      {viewMode === 'card' && tasksResult && (
        <>
          {/* Filters section */}
          <TemplateFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            primaryFilter={statusFilter}
            onPrimaryFilterChange={setStatusFilter}
            primaryFilterOptions={statusOptions}
            primaryFilterLabel="Status"
            secondaryFilter={importanceFilter}
            onSecondaryFilterChange={setImportanceFilter}
            secondaryFilterOptions={importanceOptions}
            secondaryFilterLabel="Importance"
            title="Filters"
            description="Filter tasks by name, status, or importance"
          />

       
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTasks.length === 0 ? (
                <div className="col-span-full text-center py-8 text-muted-foreground">
                  No tasks found. Try adjusting your filters or create a new task.
                </div>
              ) : (
                filteredTasks.map((task) => (
                  <TasksCard
                    key={task._id}
                    task={task}
                    onClick={() => handleTaskClick(task._id)}
                  />
                ))
              )}
            </div>
            
            {/* Load More Button */}
            {filteredTasks.length > 0 && tasksResult.continuation && (
              <div className="flex justify-center mt-8">
                <Button 
                  variant="outline" 
                  onClick={handleLoadMore}
                >
                  Load More
                </Button>
              </div>
            )}
         </>
      )}
    </div>
  );
}
