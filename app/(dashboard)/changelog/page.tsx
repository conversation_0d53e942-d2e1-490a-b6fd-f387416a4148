'use client';

import React from 'react';
import { Timeline } from '@/components/timeline';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { changelogData } from '@/lib/changelogData';

// Define types for changelog entries
type ChangelogItemImageStyle = {
  maxWidth: string;
  height: string;
};

type ChangelogItem = {
  headline: string;
  items: string[];
  image?: string;
  imageStyle?: ChangelogItemImageStyle;
  link?: string;
};

export default function ChangelogPage() {
  // Transform the data structure for the Timeline component
  const data = changelogData.map(entry => ({
    title: entry.date,
    content: (
      <div>
        {entry.entries.map((item: ChangelogItem, index) => (
          <React.Fragment key={index}>
            {index > 0 && <div className="mt-6"></div>}
            <p className="text-foreground text-sm md:text-base font-medium mb-3">
              {item.headline}
            </p>
            <ul className="list-disc pl-5 space-y-1 text-foreground text-sm md:text-base mb-4">
              {item.items.map((bulletItem, bulletIndex) => (
                <li key={bulletIndex}>{bulletItem}</li>
              ))}
            </ul>
            {item.image && (
              <div className="mt-4 mb-6 flex justify-center">
                <div className="rounded-lg overflow-hidden border border-border/50 shadow-sm">
                  <Image
                    src={item.image}
                    alt={`Screenshot for ${item.headline}`}
                    width={600}
                    height={400}
                    style={{
                      maxWidth: item.imageStyle?.maxWidth || '100%',
                      height: item.imageStyle?.height || 'auto'
                    }}
                    unoptimized={item.image.endsWith('.gif')}
                  />
                </div>
              </div>
            )}
            {item.link && (
              <div className="mt-2 mb-4">
                <Link href={item.link} passHref>
                  <Button variant="outline" size="sm" className="text-sm">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {item.link.includes('meeting-notes') ? 'Try Meeting Notes' :
                     item.link.includes('tasks') ? 'Try Tasks' :
                     item.link.includes('decisions') ? 'Try Decisions' :
                     item.link.includes('projects') ? 'Try Projects' :
                     'Try this feature'}
                  </Button>
                </Link>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    ),
  }));

  return (
    <div className="min-h-screen w-full">
      <div className="container py-8 max-w-7xl">
        <h1 className="text-3xl font-semibold mb-1 text-foreground">Changelog</h1>
        <p className="text-sm text-foreground/70 mb-10">
          Track the latest updates and improvements to the FOJO platform
        </p>
        <Timeline data={data} />
      </div>
    </div>
  );
}
