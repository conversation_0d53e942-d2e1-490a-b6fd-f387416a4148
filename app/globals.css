@tailwind base;
@tailwind components;
@tailwind utilities;

 

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 216.9 19.1% 26.7%;
    
    /* Component colors */
    --card: 210 40% 98% / 0;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 210 40% 98%;
    --popover-foreground: 216.9 19.1% 26.7%;
    --overlay: sky-50;
    --primary: 34.3 100% 91.8%;
    --primary-foreground: 0 0% 32.2%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 20 5.9% 90%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 210 40% 96.1%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.75rem;

    /* this changes how far the main content is pushed to the right must match the simplified-navigation.tsx */
    --sidebar-width-current: 210px;

    /* Chart colors for data visualizations */
    --chart-1: 221.2 83.2% 53.3%;    /* Blue */
    --chart-2: 226.7 70.7% 40.2%;    /* Indigo */
    --chart-3: 355.6 100% 94.7%;     /* Red */
    --chart-4: 47.9 95.8% 53.1%;     /* Yellow */
    --chart-5: 262.1 83.3% 57.8%;    /* Purple */
    --chart-6: 24.6 95% 53.1%;       /* Orange */
    --chart-7: 187.9 100% 42.2%;     /* Teal */
    --chart-8: 332.2 84.1% 60.2%;    /* Pink */
    --chart-9: 142.1 76.2% 36.3%;    /* Green */
    --chart-10: 215.4 16.3% 46.9%;   /* Gray */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --card: 215 30% 15%;
    --card-foreground: 210 40% 98%;
    --popover: var(--input);
    --popover-foreground: var(--input-foreground);
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Chart colors for dark mode */
    --chart-1: 217.2 91.2% 59.8%;    /* Blue */
    --chart-2: 226.7 57.9% 65.1%;    /* Indigo */
    --chart-3: 346.8 77.2% 49.8%;    /* Red */
    --chart-4: 48 96.5% 58.8%;       /* Yellow */
    --chart-5: 263.4 70% 50.4%;      /* Purple */
    --chart-6: 20.5 90.2% 48.2%;     /* Orange */
    --chart-7: 187.9 86.5% 53.1%;    /* Teal */
    --chart-8: 331.3 94.2% 81.4%;    /* Pink */
    --chart-9: 142.1 70.6% 45.3%;    /* Green */
    --chart-10: 215 20.2% 65.1%;     /* Gray */
  }

  /* Apply background and text colors to body */
  body {
    @apply bg-background text-foreground;
  }
  }

/* Global utilities */
html {
  scroll-behavior: smooth;
}

.rounded-all * {
  @apply rounded-md;
}


/* Color blobs - base properties */
.color-blob {
  position: fixed;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.4;
  z-index: -999;
  transform: translate(var(--x, 0), var(--y, 0));
  transition: transform 7s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Individual color blobs */
.color-blob-1 {
  width: 40vw;
  height: 40vw;
  background: hsl(var(--primary) / 0.25);
  top: 5%;
  right: 5%;
}

.color-blob-2 {
  width: 35vw;
  height: 35vw;
  background: hsl(var(--chart-2) / 0.25);
  bottom: 5%;
  left: 5%;
}

.color-blob-3 {
  width: 25vw;
  height: 25vw;
  background: hsl(var(--chart-5) / 0.25);
  top: 65%;
  right: 5%;
}

.color-blob-4 {
  width: 30vw;
  height: 30vw;
  background: hsl(var(--chart-6) / 0.15);
  top: 5%;
  left: 10%;
}

.color-blob-5 {
  width: 20vw;
  height: 20vw;
  background: hsl(var(--chart-7) / 0.25);
  bottom: 45%;
  right: 40%;
}


.dark .color-blob {
  opacity: 0.15;
  filter: blur(90px);
}

.dark .color-blob-1 { background: hsl(var(--primary) / 0.6); }
.dark .color-blob-2 { background: hsl(var(--chart-2) / 0.6); }
.dark .color-blob-3 { background: hsl(var(--chart-5) / 0.6); }
.dark .color-blob-4 { background: hsl(var(--chart-6) / 0.5); }
.dark .color-blob-5 { background: hsl(var(--chart-7) / 0.45); }

/* ================ COMPONENTS ================ */
/* Glassmorphic Navigation */
.glass-nav {
  background: hsla(var(--background) / 0.65);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-right: 1px solid hsla(var(--border) / 0.35);
  box-shadow: 0 0 20px hsla(var(--primary) / 0.08);
  z-index: 20;
}

.glass-nav a.bg-accent {
  background: hsla(var(--primary) / 0.15);
  border: 1px solid hsla(var(--primary) / 0.25);
  box-shadow: 0 0 10px hsla(var(--primary) / 0.15);
}

.glass-nav button:has(.h-5.w-5) {
  background: hsla(var(--background) / 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsla(var(--border) / 0.5);
  transition: all 0.2s ease;
}

.glass-nav button:has(.h-5.w-5):hover {
  background: hsla(var(--background) / 0.9);
  box-shadow: 0 0 15px hsla(var(--primary) / 0.25);
}

/* Content Area */
.glass-content {
  position: relative;
  background: transparent;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 15;
}


/* Select items hover state */
.select-item:hover {
  background-color: hsl(var(--accent));
}

/* Fix for modal form fields */
.modal-form-fix * {
  pointer-events: auto !important;
  z-index: auto !important;
}

/* Confetti canvas styling */
.confetti-canvas {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 9999 !important; /* Ensure it's above everything */
}
