'use client';

const MOVEMENT_RANGE = 10; // Maximum movement in vw/vh units
const MIN_INTERVAL = 5000; // Minimum time between movements (5 seconds)
const MAX_INTERVAL = 10000; // Maximum time between movements (10 seconds)
const TRANSITION_DURATION = 20; // Duration of movement animation in seconds

// Configuration type for blob animations
type BlobConfig = {
  minInterval?: number;   // Minimum time between movements in ms
  maxInterval?: number;   // Maximum time between movements in ms
  transitionDuration?: number;  // Duration of each movement in seconds
  movementRange?: number;  // Maximum distance blob can move in vw/vh units
}

function getRandomValue(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

function moveBlob(blob: HTMLElement, config: BlobConfig = {}) {
  const range = config.movementRange ?? MOVEMENT_RANGE;
  const x = getRandomValue(-range, range);
  const y = getRandomValue(-range, range);
  
  // Set the transition duration
  const duration = config.transitionDuration ?? TRANSITION_DURATION;
  blob.style.transition = `transform ${duration}s cubic-bezier(0.4, 0, 0.2, 1)`;
  
  blob.style.setProperty('--x', `${x}vw`);
  blob.style.setProperty('--y', `${y}vh`);
  
  // Schedule next movement
  const minInterval = config.minInterval ?? MIN_INTERVAL;
  const maxInterval = config.maxInterval ?? MAX_INTERVAL;
  const nextInterval = getRandomValue(minInterval, maxInterval);
  setTimeout(() => moveBlob(blob, config), nextInterval);
}

export function initBlobAnimations(config: BlobConfig = {}) {
  // Wait for DOM to be ready
  if (typeof window === 'undefined') return;
  
  // Start animations after a small delay to ensure everything is loaded
  setTimeout(() => {
    const blobs = document.querySelectorAll('.color-blob');
    
    blobs.forEach((blob) => {
      // Start each blob with a random delay
      const initialDelay = getRandomValue(0, 2000);
      setTimeout(() => moveBlob(blob as HTMLElement, config), initialDelay);
    });
  }, 100);
} 