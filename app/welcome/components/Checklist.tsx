import React from "react";
import { useOnboarding } from "../context/OnboardingContext";
import { Card } from "@/components/ui/card";
import { Circle, CheckCircle, ListChecks } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";

const Checklist = () => {
  const { checklist, updateChecklistItem } = useOnboarding();

  return (
    <Card className="p-6 mb-8 neo-morphism max-w-3xl">
      <div className="flex items-center mb-4">
        <ListChecks className="text-wealth-DEFAULT mr-2" size={24} />
        <h3 className="text-xl font-medium text-wealth-DEFAULT">Document Checklist</h3>
      </div>

      <Accordion type="multiple" className="w-full">
        {Object.entries(checklist).map(([category, items]) => (
          <AccordionItem key={category} value={category} className="border-b-0">
            <AccordionTrigger className="py-3 hover:no-underline">
              <div className="flex items-center gap-2">
                <div className="mr-2 text-wealth-muted">
                  <Circle size={16} />
                </div>
                <span className="font-medium">{category}</span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-1">
                {items.map((item) => (
                  <div
                    key={item.id}
                    className={`p-1 border rounded-lg flex items-center gap-3 transition-all ${
                      item.completed
                        ? "border-green-200 bg-green-50"
                        : "border-wealth-subtle"
                    }`}
                  >
                    <Checkbox
                      checked={item.completed}
                      onCheckedChange={(checked) => updateChecklistItem(item.id, checked === true)}
                      className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <span className="text-sm">{item.title}</span>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </Card>
  );
};

export default Checklist;
