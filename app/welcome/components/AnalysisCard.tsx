import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { FileText, Info } from "lucide-react";
import { useOnboarding } from "../context/OnboardingContext";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

const AnalysisCard = () => {
  const { documents, selectedDocumentId } = useOnboarding();
  const selectedDocument = selectedDocumentId ? documents.find(d => d._id === selectedDocumentId) : null;

  // Get the document record to access the ai_analysis
  const document = useQuery(api.files.files.getDocumentById, 
    selectedDocumentId ? { documentId: selectedDocumentId } : "skip"
  );

  // Sync the document status from the database to the local state
  const { updateDocument } = useOnboarding();
  
  React.useEffect(() => {
    if (document && selectedDocument && document.status !== selectedDocument.status) {
      updateDocument(selectedDocument._id, { status: document.status });
    }
  }, [document, selectedDocument, updateDocument]);

  if (!selectedDocument) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Document Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Select a document to view its analysis</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Document Analysis: {selectedDocument.name}</CardTitle>
      </CardHeader>
      <CardContent>
        {selectedDocument.status === 'processing' && (
          <div className="space-y-2">
            <p className="text-muted-foreground">Processing document...</p>
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 rounded-full border-2 border-muted-foreground border-t-transparent animate-spin" />
              <span className="text-sm text-muted-foreground">This may take a few moments</span>
            </div>
          </div>
        )}
        {selectedDocument.status === 'error' && (
          <p className="text-destructive">Error processing document</p>
        )}
        {selectedDocument.status === 'completed' && document?.ai_analysis && (
          <div className="space-y-6">
            {/* Analysis */}
            <div>
              <h3 className="font-semibold mb-2">Analysis</h3>
              <p className="text-sm whitespace-pre-wrap">{document.ai_analysis}</p>
            </div>

            {/* No Analysis Available */}
            {!document.ai_analysis && (
              <p className="text-muted-foreground">No analysis available yet</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AnalysisCard;
