
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

type NavigationButtonsProps = {
  backPath: string;
  nextPath: string;
  nextLabel?: string;
  onNext?: () => boolean;
  onBack?: () => void;
};

const NavigationButtons = ({
  backPath,
  nextPath,
  nextLabel = "Continue",
  onNext,
  onBack,
}: NavigationButtonsProps) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
    router.push(backPath);
  };

  const handleNext = () => {
    if (onNext) {
      const canProceed = onNext();
      if (!canProceed) {
        return;
      }
    }
    router.push(nextPath);
  };

  return (
    <div className="flex justify-end gap-3 mt-8">
      <Button variant="outline" onClick={handleBack}>
        Back
      </Button>
      <Button onClick={handleNext}>{nextLabel}</Button>
    </div>
  );
};

export default NavigationButtons;
