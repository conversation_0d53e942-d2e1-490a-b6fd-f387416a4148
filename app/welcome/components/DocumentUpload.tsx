import React, { useCallback, useState } from "react";
import { Upload, FileText, CheckSquare, Info, Trash } from "lucide-react";
import { useOnboarding } from "../context/OnboardingContext";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
 
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";

interface Document {
  _id: Id<"documents">;
  name: string;
  type: string;
  file?: File;
  uploadedAt: Date;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  insights?: string[];
  summary?: string;
  // Convex IDs to track the file in the database
  fileId?: Id<"files">;
  storageId?: Id<"_storage">;
}

const DocumentUpload = () => {
  const { addDocument, checklist, removeDocument, documents, setSelectedDocumentId } = useOnboarding();
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Id<"documents"> | null>(null);
  
  // Get current user
  const currentUser = useQuery(api.users.currentUser);
  
  // Get Convex mutations
  const generateUploadUrl = useMutation(api.files.fileManagement.generateUploadUrl);
  const storeDocument = useMutation(api.files.fileManagement.storeDocument);
  const updateDocumentAndChecklist = useMutation(api.files.files.updateDocumentAndChecklist);
  const scheduleDocumentAnalysis = useMutation(api.files.files.scheduleDocumentAnalysis);
  const deleteFile = useMutation(api.files.fileManagement.deleteFile);
  
  // Extract document types from the grouped checklist structure
  const documentTypes = Array.from(
    new Set(
      Object.values(checklist)
        .flatMap(items => items.flatMap(item => item.documentTypes))
    )
  ).sort();
  
  // Auto-select the first document type
  const [selectedType, setSelectedType] = useState(documentTypes.length > 0 ? documentTypes[0] : "General Document");

  const handleFiles = useCallback(async (files: FileList) => {
    if (isUploading) return;
    setIsUploading(true);
    
    try {
      for (const file of Array.from(files)) {
        // Check file type
        const validTypes = [
          "application/pdf", 
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "text/plain"
        ];
        
        if (!validTypes.includes(file.type)) {
          toast.error(`Invalid file type: ${file.name}. Please upload PDF, DOC, DOCX, or TXT files.`);
          continue;
        }
        
        // Check file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`File too large: ${file.name}. Maximum size is 10MB.`);
          continue;
        }

        try {
          // 1. Generate upload URL
          const uploadUrl = await generateUploadUrl({ contentType: file.type });

          // 2. Upload file to Convex storage
          const uploadResult = await fetch(uploadUrl, {
            method: 'POST',
            headers: { 'Content-Type': file.type },
            body: file
          });

          if (!uploadResult.ok) {
            throw new Error(`Upload failed: ${uploadResult.statusText}`);
          }

          const { storageId } = await uploadResult.json();

          // 3. Store document
          const result = await storeDocument({
            storageId: storageId,
            name: file.name,
            type: 'DOCUMENT',
            size: file.size,
            category: selectedType
          });

          toast.success(`Uploaded: ${file.name}`);
          
          // 4. Schedule document analysis
          if (currentUser) {
            try {
              setIsAnalyzing(true);
              
              // Schedule the document analysis and get the document ID
              const documentId = await scheduleDocumentAnalysis({
                storageId,
                userId: currentUser._id
              });
              
              toast.success(`Document analysis scheduled`);
              
              // Create a document object with the correct structure
              const newDocument: Document = {
                _id: documentId,
                name: file.name,
                type: selectedType,
                uploadedAt: new Date(),
                status: 'processing',
                fileId: result.fileId,
                storageId: storageId as Id<"_storage">
              };
              
              // Add the document to the local state
              addDocument(newDocument);
              
              // Set the selected document ID to the newly created document
              setSelectedDocumentId(documentId);
              
            } catch (error) {
              console.error('Analysis scheduling error:', error);
              toast.error(`Failed to schedule analysis for ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            } finally {
              setIsAnalyzing(false);
            }
          }
        } catch (error) {
          console.error('Upload error:', error);
          toast.error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } finally {
      setIsUploading(false);
      setSelectedType(documentTypes.length > 0 ? documentTypes[0] : "General Document");
    }
  }, [selectedType, addDocument, generateUploadUrl, storeDocument, scheduleDocumentAnalysis, documentTypes, isUploading, currentUser, setSelectedDocumentId]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedType) {
      toast.error("Unable to upload document. Please try again.");
      e.target.value = "";
      return;
    }
    
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
      e.target.value = "";
    }
  }, [selectedType, handleFiles]);

  const handleDocumentClick = (documentId: Id<"documents">) => {
    setSelectedDocumentId(documentId);
  };

  const handleDeleteClick = (documentId: Id<"documents">) => {
    setDocumentToDelete(documentId);
    setDialogOpen(true);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Upload Documents</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Dropzone Area */}
        <div className="flex items-center justify-center w-full">
          <label
            htmlFor="dropzone-file"
            className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-background hover:bg-accent/50"
          >
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <Upload className="h-6 w-6 mb-2 text-muted-foreground" />
              <p className="mb-2 text-sm text-muted-foreground">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-muted-foreground">PDF, DOC, DOCX, or TXT</p>
            </div>
            <input
              id="dropzone-file"
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept=".pdf,.doc,.docx,.txt"
            />
          </label>
        </div>

        {/* Uploaded Documents List */}
        {documents.length > 0 && (
          <div className="grid grid-cols-1 gap-4">
            {documents.map((doc) => (
              <div
                key={doc._id.toString()}
                className="flex items-center justify-between p-4 rounded-lg border cursor-pointer hover:bg-accent"
                onClick={() => handleDocumentClick(doc._id)}
              >
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{doc.name}</p>
                    <p className="text-sm text-muted-foreground">{doc.type}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(doc._id);
                  }}
                >
                  <Trash size={14} />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                if (documentToDelete) {
                  // Find the document in the local state
                  const docToDelete = documents.find(doc => doc._id === documentToDelete);
                  
                  // If the document has a Convex file ID, delete it from the database
                  if (docToDelete?.fileId) {
                    try {
                      await deleteFile({ fileId: docToDelete.fileId });
                      toast.success(`Deleted: ${docToDelete.name}`);
                    } catch (error) {
                      console.error('Delete error:', error);
                      toast.error(`Failed to delete ${docToDelete.name} from database: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    }
                  }
                  
                  // Remove from local state
                  removeDocument(documentToDelete);
                }
                setDialogOpen(false);
                setDocumentToDelete(null);
              }}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default DocumentUpload;
