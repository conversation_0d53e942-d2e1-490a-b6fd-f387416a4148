import React from "react";
import { useOnboarding } from "../context/OnboardingContext";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Trash, FileText, CheckSquare, Info } from "lucide-react";
import { Id } from "@/convex/_generated/dataModel";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";

const DocumentList = () => {
  const { documents, removeDocument } = useOnboarding();
  const [selectedDocument, setSelectedDocument] = React.useState<Id<"documents"> | null>(null);

  if (documents.length === 0) {
    return null;
  }

  const handleRemove = (id: Id<"documents">, e: React.MouseEvent) => {
    e.stopPropagation();
    removeDocument(id);
  };

  const selectedDocumentData = documents.find(doc => doc._id === selectedDocument);

  return (
    <Card className="p-6 mb-8 neo-morphism">
      <h3 className="text-xl font-medium mb-4 text-wealth-DEFAULT">Uploaded Documents</h3>
      
      <div className="space-y-4">
        {documents.map((document) => (
          <div
            key={document._id.toString()}
            className="flex items-center p-3 border border-wealth-subtle rounded-lg hover:bg-wealth-subtle/30 transition-colors cursor-pointer"
            onClick={() => setSelectedDocument(document._id)}
          >
            <div className="mr-3 text-wealth-DEFAULT">
              <FileText size={24} />
            </div>
            
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-wealth-DEFAULT truncate">{document.name}</h4>
              <div className="flex items-center text-xs text-wealth-muted">
                <span>{document.type}</span>
                <span className="mx-2">•</span>
                <span>{document.uploadedAt.toLocaleDateString()}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {document.status === 'uploading' && (
                <div className="text-xs px-2 py-1 bg-wealth-subtle text-wealth-muted rounded-full">
                  Uploading...
                </div>
              )}
              
              {document.status === 'processing' && (
                <div className="text-xs px-2 py-1 bg-wealth-subtle text-wealth-muted rounded-full animate-pulse-subtle">
                  Processing...
                </div>
              )}
              
              {document.status === 'completed' && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="text-green-600">
                        <CheckSquare size={20} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Successfully processed</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              
              {document.status === 'error' && (
                <div className="text-xs px-2 py-1 bg-red-100 text-red-600 rounded-full">
                  Error
                </div>
              )}
              
              {document.parsedData?.analysis && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="text-wealth-DEFAULT">
                        <Info size={18} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>AI has analyzed this document</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              
              <Button
                variant="ghost"
                size="icon"
                className="text-wealth-muted hover:text-red-500"
                onClick={(e) => handleRemove(document._id, e)}
              >
                <Trash size={18} />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <Dialog open={!!selectedDocument} onOpenChange={(open) => !open && setSelectedDocument(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedDocumentData?.name}</DialogTitle>
            <DialogDescription>
              {selectedDocumentData?.type} • {selectedDocumentData?.uploadedAt.toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>
          
          {selectedDocumentData?.parsedData?.analysis && (
            <div className="p-4 bg-wealth-subtle rounded-lg">
              <h4 className="font-medium mb-2 flex items-center text-wealth-DEFAULT">
                <Info size={16} className="mr-2" /> AI Insights
              </h4>
              <div className="text-sm text-wealth-muted">
                {selectedDocumentData.parsedData.analysis}
              </div>
            </div>
          )}
          
          <div className="flex justify-end gap-2 mt-4">
            <Button
              variant="outline"
              onClick={() => setSelectedDocument(null)}
            >
              Close
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (selectedDocument) {
                  removeDocument(selectedDocument);
                  setSelectedDocument(null);
                }
              }}
            >
              Delete Document
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default DocumentList;
