'use client';

import React from 'react';
import { Person } from '@/zod/directory-schema'; // Import Person type
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface AdvisorCardProps {
  advisor: Person | null | undefined;
}

/**
 * AdvisorCard component displays an advisor's information
 * Shows their image in an avatar and their name and title to the right
 */
const AdvisorCard: React.FC<AdvisorCardProps> = ({ advisor }) => {
  return (
    <div className="flex flex-row items-center rounded-lg p-4 shadow-md w-full max-w-xs gap-4">
      <Avatar className="h-16 w-16 border-2 border-white">
        <AvatarImage src={advisor?.image} alt={advisor?.name || ""} />
        <AvatarFallback>
          {advisor?.name
            ?.split(' ')
            .map((n: string) => n[0])
            .join('') || ''}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        <h3 className="font-medium text-gray-800">
          {advisor?.name || 'Advisor'}
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          {advisor?.title || 'Team Member'}
        </p>
      </div>
    </div>
  );
};

export default AdvisorCard;
