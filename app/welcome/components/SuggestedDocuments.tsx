
import React from "react";
import { useOnboarding } from "../context/OnboardingContext";
import { Card } from "@/components/ui/card";
import { Info } from "lucide-react";

const SuggestedDocuments = () => {
  const { suggestedDocuments } = useOnboarding();

  if (suggestedDocuments.length === 0) {
    return null;
  }

  return (
    <Card className="p-6 mb-8 neo-morphism border-l-4 border-l-wealth-accent">
      <div className="flex items-start">
        <div className="mr-3 mt-1 text-wealth-accent">
          <Info size={20} />
        </div>
        <div>
          <h3 className="text-lg font-medium mb-2 text-wealth-DEFAULT">
            Suggested Additional Documents
          </h3>
          <p className="text-sm text-wealth-muted mb-4">
            Based on your uploads, we recommend providing these additional documents to help create a comprehensive financial picture.
          </p>
          
          <div className="space-y-2">
            {suggestedDocuments.map((doc, index) => (
              <div 
                key={index} 
                className="p-3 bg-wealth-subtle/50 rounded-lg border border-wealth-subtle"
              >
                <h4 className="font-medium text-wealth-DEFAULT">{doc}</h4>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SuggestedDocuments;
