
import React, { ReactNode } from "react";
import { useOnboarding } from "../context/OnboardingContext";
import { useRouter } from "next/navigation";

type OnboardingLayoutProps = {
  children: ReactNode;
  title: string;
  subtitle: string;
};

const OnboardingLayout = ({ children, title, subtitle }: OnboardingLayoutProps) => {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-wealth-light flex flex-col">
      

      {/* Content */}
      <main className="flex-1 container max-w-7xl mx-auto px-6 py-10">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8 text-center animate-fade-down">
            <h1 className="text-3xl font-semibold text-wealth-DEFAULT mb-2">{title}</h1>
            <p className="text-wealth-muted text-balance">{subtitle}</p>
          </div>
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 border-t border-wealth-subtle glass-morphism">
        <div className="container max-w-7xl mx-auto px-6 flex justify-between items-center">
          <div className="text-sm text-wealth-muted">
            © 2023 Cresset Capital Management, LLC
          </div>
          <div className="flex gap-6">
            <div className="text-sm text-wealth-muted">Privacy Policy</div>
            <div className="text-sm text-wealth-muted">Terms of Service</div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default OnboardingLayout;
