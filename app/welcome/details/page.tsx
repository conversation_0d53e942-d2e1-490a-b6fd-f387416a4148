import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import UserDetailsForm from "./components/UserDetailsForm";
import UserSummaryCard from "./components/UserSummaryCard";

export default function DetailsPage() {
  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Your Details</CardTitle>
            <CardDescription>
              We&apos;ve taken the time to fill out this form for you based on the documents you&apos;ve provided.
              Please review and make any necessary corrections.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserDetailsForm />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI-Generated Summary</CardTitle>
            <CardDescription>
              A comprehensive summary of your profile based on the analyzed documents and information.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserSummaryCard />
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 