"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

export default function UserSummaryCard() {
  // Get current user data and summary
  const currentUserData = useQuery(api.users.currentUser);
  const userSummary = currentUserData?._id ? 
    useQuery(api.users.getSummary, { userId: currentUserData._id }) : 
    undefined;

  if (!currentUserData || !userSummary) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* Professional Summary */}
      <div>
        <h3 className="text-lg font-medium mb-2">Professional Summary</h3>
        <p className="text-muted-foreground">
          {userSummary.professionalSummary || "No professional summary available yet."}
        </p>
      </div>

      {/* Key Skills */}
      <div>
        <h3 className="text-lg font-medium mb-2">Key Skills</h3>
        <div className="flex flex-wrap gap-2">
          {userSummary.skills?.map((skill, index) => (
            <Badge key={index} variant="secondary">
              {skill}
            </Badge>
          )) || "No skills identified yet."}
        </div>
      </div>

      {/* Experience Highlights */}
      <div>
        <h3 className="text-lg font-medium mb-2">Experience Highlights</h3>
        <ul className="list-disc list-inside space-y-2 text-muted-foreground">
          {userSummary.experienceHighlights?.map((highlight, index) => (
            <li key={index}>{highlight}</li>
          )) || <li>No experience highlights available yet.</li>}
        </ul>
      </div>

      {/* Education */}
      <div>
        <h3 className="text-lg font-medium mb-2">Education</h3>
        <ul className="list-disc list-inside space-y-2 text-muted-foreground">
          {userSummary.education?.map((edu, index) => (
            <li key={index}>{edu}</li>
          )) || <li>No education information available yet.</li>}
        </ul>
      </div>

      {/* Certifications */}
      <div>
        <h3 className="text-lg font-medium mb-2">Certifications</h3>
        <div className="flex flex-wrap gap-2">
          {userSummary.certifications?.map((cert, index) => (
            <Badge key={index} variant="outline">
              {cert}
            </Badge>
          )) || "No certifications identified yet."}
        </div>
      </div>

      <div className="text-sm text-muted-foreground mt-6">
        <p>
          This summary is automatically generated based on the documents you&apos;ve provided.
          You can update your information in the form above if you notice any discrepancies.
        </p>
      </div>
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div>
        <Skeleton className="h-6 w-40 mb-2" />
        <Skeleton className="h-20 w-full" />
      </div>
      
      <div>
        <Skeleton className="h-6 w-32 mb-2" />
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-6 w-20" />
          ))}
        </div>
      </div>

      <div>
        <Skeleton className="h-6 w-44 mb-2" />
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-4 w-full" />
          ))}
        </div>
      </div>
    </div>
  );
} 