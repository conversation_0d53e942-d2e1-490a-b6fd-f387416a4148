"use client";

import React, { useEffect } from 'react';
import { TypewriterEffect } from '@/components/ui/typewriter-effect';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Button } from '@/components/ui/button';
import AdvisorCard from './components/AdvisorCard';
import { useRouter } from 'next/navigation';

const WelcomeScreen = () => {
  const router = useRouter();
  
  // Get the current user
  const user = useQuery(api.users.currentUser);
  const checkNewUserMutation = useMutation(api.users.checkNewUser);
  
  // Debug log for user data
  console.log("[Welcome] User data:", user);
  
  // First, ensure the user has a corresponding person record
  useEffect(() => {
    if (user) {
      console.log("[Welcome] Running checkNewUser mutation");
      checkNewUserMutation();
    }
  }, [user, checkNewUserMutation]);
  
  // Get the person record directly using the user's ID
  const person = useQuery(
    api.directory.directoryPeople.getPersonByUserId, 
    user?._id ? { userId: user._id } : "skip"
  );

  // Debug log for person data
  console.log("[Welcome] Person data:", person);

  // Debug log for rendering conditions
  console.log("[Welcome] Render conditions:", {
    hasUser: !!user,
    hasUserId: !!user?._id,
    hasPerson: !!person,
    hasPersonName: !!person?.name,
    firstName: person?.name ? person.name.split(" ")[0] : ""
  });

  // Get the three specific advisors by name using individual queries
  const katherine = useQuery(api.directory.directoryPeople.getPersonByName, {
    name: "Katherine Dumont"
  });
  
  const james = useQuery(api.directory.directoryPeople.getPersonByName, {
    name: "James Longinotti"
  });
  
  const spencer = useQuery(api.directory.directoryPeople.getPersonByName, {
    name: "Spencer Gribble"
  });
  
  // Combine the advisors into an array, filtering out any undefined results
  const advisors = [katherine, james, spencer].filter(Boolean);

  // Only use the person's first name when available
  const firstName = person?.name ? person.name.split(" ")[0] : "";

  return (
    <div className="w-full max-w-3xl mx-auto p-6 flex flex-col items-center justify-center">
      <div className="w-full">
        <div className="text-center mb-8">
          {person?.name && (
            <TypewriterEffect 
              words={[
                { text: "Hello, ", className: "text-gray-700" },
                { text: firstName, className: "text-sky-600" }
              ]}
              className="text-6xl font-bold mb-2"
              initialDelay={2}
            />
          )}
          <p className="text-2xl text-gray-600">Welcome to Cresset Capital</p>
        </div>
        
        {/* Two-column layout starting from "You've built..." text */}
        <div className="flex flex-row gap-x-8">
          {/* Left column - Text content */}
          <div className="w-1/2">
            <p className="text-lg text-gray-600 leading-relaxed mb-6">
              You've built and sold a successful business. Now let us help you preserve your independence and legacy with the same care and strategic thinking that made your business thrive.
            </p>
            
            {/* Get Started button */}
            <Button 
              variant="default" 
              size="lg" 
              className="rounded-md font-medium"
              onClick={() => router.push('/welcome/documents')}
            >
              Get Started
            </Button>
          </div>
          
          {/* Right column - Advisor Team */}
          <div className="w-1/2 flex flex-col justify-center items-center">
            <h2 className="text-2xl font-semibold mb-4">Your Advisor Team</h2>
            <div className="flex flex-col items-center gap-6">
              {advisors.map((advisor) => (
                <AdvisorCard key={advisor?._id} advisor={advisor} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
