'use client';

import React from 'react';
import { useReducedMotion, motion } from 'motion/react';
import { GlassmorphicContainer } from '@/components/ui/glassmorphic-container';
import { OnboardingProvider } from './context/OnboardingContext';

export default function ClientLayout({
  children
}: {
  children: React.ReactNode;
}) {
  // Respect user's reduced motion preference
  const shouldReduceMotion = useReducedMotion();

  // Reduced animation settings when user prefers reduced motion
  const getAnimationSettings = (originalAnimation: any) => {
    return shouldReduceMotion ? { scale: 1, x: 0, y: 0 } : originalAnimation;
  };

  return (
      <div className="relative overflow-x-hidden">
        {/* Enhanced background gradient for glassmorphic effect */}
        <div className="fixed inset-0 bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 -z-10"></div>

        {/* Enhanced animated color splashes with more vibrant but still subtle colors */}
        <motion.div
          className="fixed top-[-10%] left-[-5%] w-[45rem] h-[45rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#4c1d95', '#701a75', '#312e81'],
            scale: [1, 1.1, 0.95, 1],
            x: [0, 20, -20, 0],
            y: [0, -20, 20, 0]
          })}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut'
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed top-[-15%] right-[-10%] w-[40rem] h-[40rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#1e3a8a', '#1d4ed8', '#1e40af'],
            scale: [1, 0.9, 1.1, 1],
            x: [0, -30, 30, 0],
            y: [0, 30, -30, 0]
          })}
          transition={{
            duration: 30,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 2
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed top-[40%] left-[-10%] w-[35rem] h-[35rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#ec4899', '#f472b6', '#db2777'],
            scale: [1, 1.15, 0.9, 1]
          })}
          transition={{
            duration: 35,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 5
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        <motion.div
          className="fixed bottom-[-15%] right-[-5%] w-[35rem] h-[35rem] rounded-full opacity-20 blur-3xl -z-5 transform-gpu"
          animate={getAnimationSettings({
            backgroundColor: ['#06b6d4', '#22d3ee', '#0891b2'],
            scale: [1, 0.8, 1.2, 1]
          })}
          transition={{
            duration: 28,
            repeat: Infinity,
            repeatType: 'mirror',
            ease: 'easeInOut',
            delay: 7
          }}
          style={{ willChange: 'transform, opacity' }}
        />

        {/* Main content area with glassmorphic effect */}
        <main className="relative z-10 min-h-screen flex items-center justify-center p-6">
          <GlassmorphicContainer className="w-full max-w-7xl">
            <div className="p-6">
              <OnboardingProvider>
                {children}
              </OnboardingProvider>
            </div>
          </GlassmorphicContainer>
        </main>
      </div>
  );
}
