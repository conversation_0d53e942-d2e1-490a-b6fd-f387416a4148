"use client";

import React, { useEffect } from "react";
import OnboardingLayout from "../components/OnboardingLayout";
import { useOnboarding } from "../context/OnboardingContext";
import DocumentUpload from "../components/DocumentUpload";
import Checklist from "../components/Checklist";
import SuggestedDocuments from "../components/SuggestedDocuments";
import NavigationButtons from "../components/NavigationButtons";
import AnalysisCard from "../components/AnalysisCard";

const OnboardingDocuments = () => {
  const { setStep } = useOnboarding();

  useEffect(() => {
    setStep(2);
  }, [setStep]);

  const handleNextStep = () => {
    // All documents are optional, so always return true
    return true;
  };

  return (
    <OnboardingLayout
      title="Document Upload"
      subtitle="Upload your financial documents to help us create a comprehensive picture of your wealth"
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1 space-y-8">
          <Checklist />
          <SuggestedDocuments />
        </div>

        <div className="lg:col-span-2 space-y-8">
          <DocumentUpload />
          <AnalysisCard />
          <NavigationButtons
            backPath="/welcome"
            nextPath="/welcome/review"
            onNext={handleNextStep}
          />
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default OnboardingDocuments;
