import React, { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { zid } from "convex-helpers/server/zod";
import type { 
  Document as DocumentType, 
  ChecklistItem as ChecklistItemType, 
  UserChecklistItem as UserChecklistItemType,
  DocumentDefinition as DocumentDefinitionType
} from "@/zod/documents-schema";

type PersonalInfo = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  birthDate: Date | null;
  maritalStatus: string;
  spouseName?: string;
  children: { name: string; age: string }[];
  address: string;
  city: string;
  state: string;
  zipCode: string;
  occupation: string;
  companyName?: string;
  businessSale?: {
    companyName: string;
    saleAmount: string;
    saleDate: Date | null;
  };
};

// Interface for parsed data from AI analysis
export interface ParsedData {
  documentType: string;
  summary: string;
  analysis: string;
  entities?: string[];
  maritalStatus?: string;
  spouses?: Array<{
    name?: string;
    dateOfBirth?: string;
  }>;
  children?: Array<{
    name?: string;
    dateOfBirth?: string;
  }>;
  assets?: Array<{
    name?: string;
    value?: number;
    type?: string;
    description?: string;
  }>;
}

export interface Document {
  _id: Id<"documents">;
  name: string;
  type: string;
  file?: File;
  uploadedAt: Date;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  parsedData?: ParsedData;
  // Convex IDs to track the file in the database
  fileId?: Id<"files">;
  storageId?: Id<"_storage">;
}

interface UserChecklistItem {
  _id: Id<"user_document_checklist">;
  definition: {
    _id: Id<"document_definitions">;
    name: string;
    description?: string;
    category?: string;
  };
  status: string;
  updated_at?: number;
}

export interface ChecklistItem {
  id: string;
  title: string;
  documentTypes: string[];
  completed: boolean;
  documentId?: string;
  description?: string;
}

type GroupedChecklist = Record<string, ChecklistItemType[]>;

interface OnboardingContextType {
  step: number;
  setStep: (step: number) => void;
  personalInfo: PersonalInfo;
  setPersonalInfo: React.Dispatch<React.SetStateAction<PersonalInfo>>;
  documents: DocumentType[];
  addDocument: (document: DocumentType) => void;
  removeDocument: (id: Id<"documents">) => void;
  updateDocument: (id: Id<"documents">, updates: Partial<DocumentType>) => void;
  checklist: GroupedChecklist;
  updateChecklistItem: (id: string, completed: boolean, documentId?: string) => void;
  suggestedDocuments: string[];
  selectedDocumentId: Id<"documents"> | null;
  setSelectedDocumentId: (id: Id<"documents"> | null) => void;
}

const defaultPersonalInfo: PersonalInfo = {
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  birthDate: null,
  maritalStatus: "",
  children: [],
  address: "",
  city: "",
  state: "",
  zipCode: "",
  occupation: "",
  businessSale: {
    companyName: "",
    saleAmount: "",
    saleDate: null,
  },
};

// Empty default checklist - will be populated from document_definitions table
const defaultChecklist: GroupedChecklist = {};

export const OnboardingContext = createContext<OnboardingContextType>({
  step: 1,
  setStep: () => {},
  personalInfo: defaultPersonalInfo,
  setPersonalInfo: () => {},
  documents: [],
  addDocument: () => {},
  removeDocument: () => {},
  updateDocument: () => {},
  checklist: {},
  updateChecklistItem: () => {},
  suggestedDocuments: [],
  selectedDocumentId: null,
  setSelectedDocumentId: () => {},
});

export const OnboardingProvider = ({ children }: { children: ReactNode }) => {
  const [step, setStep] = useState(1);
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>(defaultPersonalInfo);
  const [documents, setDocuments] = useState<DocumentType[]>([]);
  const [checklist, setChecklist] = useState<GroupedChecklist>({});
  const [suggestedDocuments, setSuggestedDocuments] = useState<string[]>([
    "Last Will and Testament",
    "Power of Attorney",
    "Life Insurance Policy"
  ]);
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | null>(null);
  
  // Replace useAuth with currentUser query
  const user = useQuery(api.users.currentUser);
  
  // Add a guard to prevent queries from running without a user
  const documentDefinitions = useQuery(
    api.files.files.getDocumentDefinitions, 
    user ? { category: undefined } : "skip"
  ) as DocumentDefinitionType[] | undefined;
  
  const userChecklist = useQuery(
    api.files.files.getUserDocumentChecklist, 
    user?._id ? { userId: user._id } : "skip"
  ) as UserChecklistItemType[] | undefined;
  
  // Get the mutation to update the user document checklist
  const updateUserDocumentChecklistMutation = useMutation(api.files.files.updateUserDocumentChecklist);
  
  // Map document definitions to checklist items and group by category
  useEffect(() => {
    if (documentDefinitions?.length && userChecklist?.length) {
      // Create a map of document definition IDs to their completed status
      const completionStatusMap = new Map<Id<"document_definitions">, boolean>();
      
      userChecklist.forEach((item) => {
        completionStatusMap.set(item.document_definition_id, item.status === 'completed');
      });
      
      // Map document definitions to checklist items
      const newChecklist = documentDefinitions.map((definition) => {
        const isCompleted = completionStatusMap.has(definition._id)
          ? completionStatusMap.get(definition._id)
          : false;
        
        const documentTypes = definition.category 
          ? [definition.category] 
          : ["Document"];
        
        return {
          id: definition._id,
          title: definition.name || "Unnamed Document",
          description: definition.description,
          documentTypes,
          completed: isCompleted || false
        } as ChecklistItemType;
      });

      // Group checklist items by category
      const groupedChecklist = newChecklist.reduce<GroupedChecklist>((acc, item) => {
        const category = item.documentTypes[0] || "Other";
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(item);
        return acc;
      }, {});
      
      setChecklist(groupedChecklist);
    }
  }, [documentDefinitions, userChecklist]);

  const addDocument = (document: DocumentType) => {
    setDocuments(prev => {
      const index = prev.findIndex(d => d._id === document._id);
      if (index >= 0) {
        const newDocs = [...prev];
        newDocs[index] = document;
        return newDocs;
      }
      return [...prev, document];
    });
  };

  const removeDocument = (id: Id<"documents">) => {
    setDocuments(prev => prev.filter(d => d._id !== id));
    if (selectedDocumentId === id) {
      setSelectedDocumentId(null);
    }
    // Update checklist items that reference this document
    setChecklist(prev => {
      const newChecklist = { ...prev };
      Object.entries(newChecklist).forEach(([category, items]) => {
        newChecklist[category] = items.map(item => {
          if (item.documentId === id.toString()) {
            return { ...item, completed: false, documentId: undefined };
          }
          return item;
        });
      });
      return newChecklist;
    });
  };

  const updateDocument = (id: Id<"documents">, updates: Partial<DocumentType>) => {
    setDocuments(prev => 
      prev.map(doc => 
        doc._id === id ? { ...doc, ...updates } : doc
      )
    );
  };

  const updateChecklistItem = (id: string, completed: boolean, documentId?: string) => {
    setChecklist(prev => {
      const newChecklist = { ...prev };
      Object.entries(newChecklist).forEach(([category, items]) => {
        newChecklist[category] = items.map(item => {
          if (item.id === id) {
            return { ...item, completed, documentId };
          }
          return item;
        });
      });
      return newChecklist;
    });
  };

  const updateSuggestedDocuments = (documentType: string) => {
    setSuggestedDocuments(prev => 
      prev.filter(type => type !== documentType)
    );
  };

  const value: OnboardingContextType = {
    step,
    setStep,
    personalInfo,
    setPersonalInfo,
    documents,
    addDocument,
    removeDocument,
    updateDocument,
    checklist,
    updateChecklistItem,
    suggestedDocuments,
    selectedDocumentId,
    setSelectedDocumentId,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
};
