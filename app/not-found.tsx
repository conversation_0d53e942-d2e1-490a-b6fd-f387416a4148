'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  // Log when the not-found page is rendered
  useEffect(() => {
    console.log('Not Found page rendered', {
      url: typeof window !== 'undefined' ? window.location.href : 'server-side',
      timestamp: new Date().toISOString()
    });
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="max-w-md">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">Page Not Found</h2>
        
        <p className="text-gray-600 mb-8">
          The page you're looking for doesn't exist or has been moved.
          If you were redirected here after logging in, please try refreshing the page.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="default">
            <Link href="/home">
              Go to Home
            </Link>
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => {
              if (typeof window !== 'undefined') {
                window.location.reload();
              }
            }}
          >
            Refresh Page
          </Button>
        </div>
      </div>
    </div>
  );
}
