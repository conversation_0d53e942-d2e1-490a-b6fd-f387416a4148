# Project Timeline Component Analysis

This document provides a comprehensive analysis of the Project Timeline component, focusing on its functionality, data flow, and integration with the Convex backend. The component displays a chronological list of project updates and allows users to add new updates, including linking meeting notes.

## 1. Overview

The Project Timeline component (`app/(dashboard)/projects/components/project-timeline.tsx`) displays a chronological list of updates for a project. It supports:

- Displaying updates of different types (MANUAL, TASK, DECISION, MEETING, AI, PROJECT)
- Filtering updates by time range (week, 2 weeks, month, quarter, all time)
- Searching updates by title/description
- Adding manual updates through an inline editor
- Linking existing meeting notes as timeline updates
- Expanding/collapsing updates to view/edit content
- Changing the date of updates

## 2. Data Model

### Database Tables

The component interacts with several Convex database tables:

1. **projectUpdates**: Stores project update records
   - `project_id`: Reference to the project
   - `content`: HTML content of the update
   - `date_of_update`: Timestamp of the update
   - `update_type`: Type of update (MA<PERSON>AL, TASK, DECISION, MEETING, AI, PROJECT)
   - `source_id`: Reference to the source entity (e.g., meeting note file ID for MEETING type)
   - `created_by`: User who created the update
   - `short_description`: Auto-generated summary of the update

2. **files**: Stores file metadata, including meeting notes
   - `title`: Title of the file
   - `docType`: Type of document (e.g., MEETING_NOTES)
   - `short_description`: Auto-generated summary of the file content

3. **meeting_notes**: Stores meeting note content
   - `fileId`: Reference to the file record
   - `content`: HTML content of the meeting note
   - `meetingDate`: Date of the meeting

## 3. Component Structure

The component is structured as follows:

```jsx
<ProjectTimeline>
  {/* Header with Add Update button */}
  <Popover> {/* Add Update popover */}
    <PopoverContent>
      {/* Step 1: Select update type */}
      {/* Step 2: Search meeting notes */}
    </PopoverContent>
  </Popover>
  
  {/* Filters and controls */}
  
  {/* Timeline content */}
  <Droppable> {/* For drag-and-drop functionality */}
    {/* Inline editor for manual updates */}
    {/* List of project updates */}
    {/* Add Update button at bottom */}
  </Droppable>
  
  {/* Manual Update Dialog */}
</ProjectTimeline>
```

## 4. Key Features

### 4.1 Update Types

The component supports multiple update types, each with its own icon and styling:

- **MANUAL**: User-created updates (blue pen icon)
- **TASK**: Task-related updates (green clipboard icon)
- **DECISION**: Decision-related updates (purple gavel icon)
- **MEETING**: Meeting note updates (orange users icon)
- **AI**: AI-generated updates (indigo sparkles icon)
- **PROJECT**: Project-related updates (teal folder icon)

### 4.2 Adding Updates

Users can add updates in two ways:

1. **Manual Updates**: Users can create text updates directly in the timeline using the TiptapEditor.
2. **Meeting Note Links**: Users can search for and link existing meeting notes to the timeline.

The "Add Update" button opens a popover with two options:
- "Manual Update": Opens the inline TiptapEditor
- "Meeting Note": Opens a search interface to find and link meeting notes

### 4.3 Meeting Note Integration

The component allows linking meeting notes to the project timeline:

1. User clicks "Add Update" and selects "Meeting Note"
2. User searches for a meeting note by title
3. User selects a meeting note from the results
4. The system creates a new project update with:
   - `update_type: 'MEETING'`
   - `source_id`: Set to the meeting note's file ID
   - `content`: Set to the meeting note's short description
   - `date_of_update`: Set to the meeting note's meeting date

When displayed in the timeline, meeting note updates include a link to the original meeting note page.

## 5. Data Flow

### 5.1 Fetching Updates

1. The component uses the `listByProject` query from `api.projectUpdates` to fetch updates for the current project.
2. Updates are filtered by time range and search query.
3. Updates are displayed in chronological order (oldest first).

### 5.2 Creating Manual Updates

1. User clicks "Add Update" and selects "Manual Update"
2. The inline TiptapEditor appears
3. User enters content and saves
4. The `createManualUpdate` mutation is called with:
   - `project_id`: Current project ID
   - `content`: HTML content from the editor
   - `date_of_update`: Current timestamp
5. The timeline refreshes to show the new update

### 5.3 Linking Meeting Notes

1. User clicks "Add Update" and selects "Meeting Note"
2. User enters a search term
3. The `searchMeetingNotes` query from `api.files` is called to find matching meeting notes
4. User selects a meeting note
5. The `createMeetingLinkUpdate` mutation is called with:
   - `projectId`: Current project ID
   - `meetingNoteFileId`: Selected meeting note's file ID
6. The backend:
   - Fetches the meeting note details using `getMeetingNote`
   - Creates a new project update with type 'MEETING'
   - Sets the content to the meeting note's short description
   - Sets the date to the meeting note's meeting date
7. The timeline refreshes to show the new update

### 5.4 Updating Content

1. User expands an update by clicking on it
2. User edits the content in the TiptapEditor
3. The `updateProjectUpdateContent` mutation is called with:
   - `update_id`: ID of the update being edited
   - `content`: New HTML content
4. The timeline refreshes to show the updated content

### 5.5 Updating Date

1. User clicks on the date of an expanded update
2. A date picker appears
3. User selects a new date
4. The `updateProjectUpdateDate` mutation is called with:
   - `update_id`: ID of the update being edited
   - `date_of_update`: New timestamp
5. The timeline refreshes to show the updated date

## 6. Backend Implementation

### 6.1 Mutations

#### createManualUpdate
- Creates a new project update with type 'MANUAL'
- Sets the source_id to the project ID
- Returns the ID of the new update

#### createMeetingLinkUpdate
- Fetches the meeting note details using `getMeetingNote`
- Creates a new project update with type 'MEETING'
- Sets the source_id to the meeting note's file ID
- Sets the content to the meeting note's short description
- Sets the date to the meeting note's meeting date
- Returns the ID of the new update

#### updateProjectUpdateContent
- Updates the content of an existing project update
- Verifies the user is the creator of the update

#### updateProjectUpdateDate
- Updates the date of an existing project update
- Verifies the user is the creator of the update

### 6.2 Queries

#### listByProject
- Fetches project updates for a specific project
- Sorts by date (most recent first)
- Supports pagination

#### searchMeetingNotes
- Searches for meeting notes by title
- Returns matching files with docType 'MEETING_NOTES'
- Supports pagination

## 7. UI Components

### 7.1 Update Card

Each update is displayed as a card with:
- Icon based on update type
- Title (derived from short_description or content)
- Relative date (e.g., "2 days ago")
- Badge showing the update type
- User avatar and name

When expanded, the card shows:
- TiptapEditor with the update content
- Date picker for changing the date

### 7.2 Add Update Popover

The "Add Update" popover has two steps:

1. **Select Type**:
   - "Manual Update" button
   - "Meeting Note" button

2. **Search Meeting Notes** (if "Meeting Note" selected):
   - Back button
   - Search input
   - Scrollable list of meeting note results
   - Each result shows title and date

## 8. State Management

The component uses several state variables:

- `isAddUpdatePopoverOpen`: Controls the visibility of the Add Update popover
- `popoverStep`: Tracks the current step in the Add Update flow ('selectType' or 'searchMeeting')
- `meetingSearchTerm`: Stores the current meeting note search term
- `debouncedMeetingSearchTerm`: Debounced version of the search term to reduce API calls
- `isCreatingLink`: Tracks whether a meeting note link is being created
- `isAddingInlineUpdate`: Controls the visibility of the inline editor
- `editingUpdateId`: Tracks the ID of the update being edited
- `refreshTrigger`: Used to trigger a refresh of the updates
- `expandedUpdateIds`: Tracks which updates are expanded
- `datePickerOpen`: Tracks which update's date picker is open
- `selectedDate`: Stores the selected date in the date picker

## 9. Implementation Details

### 9.1 Meeting Note Search

The meeting note search uses a debounced input to reduce API calls:

```jsx
const [meetingSearchTerm, setMeetingSearchTerm] = useState("");
const [debouncedMeetingSearchTerm] = useDebounce(meetingSearchTerm, 300);

const meetingNotes = useQuery(
  api.files.searchMeetingNotes,
  debouncedMeetingSearchTerm ? {
    searchQuery: debouncedMeetingSearchTerm,
    paginationOpts: { numItems: 10, cursor: null }
  } : 'skip'
);
```

### 9.2 Meeting Note Links

Meeting note updates include a link to the original meeting note:

```jsx
{update.update_type === 'MEETING' && update.source_id ? (
  <a 
    href={`/documents/meeting-notes/${update.source_id}`}
    className="hover:underline hover:text-primary"
    onClick={(e) => e.stopPropagation()} // Prevent card expansion when clicking the link
  >
    {title}
  </a>
) : (
  title
)}
```

### 9.3 Popover Reset

The popover state is reset when it closes:

```jsx
useEffect(() => {
  if (!isAddUpdatePopoverOpen) {
    setPopoverStep('selectType');
    setMeetingSearchTerm('');
  }
}, [isAddUpdatePopoverOpen]);
```

## 10. Future Enhancements

Potential improvements to the Project Timeline component:

1. **Pagination**: Add pagination for large numbers of updates
2. **Filtering by Type**: Allow filtering updates by type
3. **Rich Media**: Support for images and attachments in updates
4. **Comments**: Allow commenting on updates
5. **Notifications**: Notify users of new updates
6. **Sorting Options**: Allow sorting by different criteria
7. **Bulk Actions**: Allow selecting and acting on multiple updates
8. **Timeline Visualization**: Add a visual timeline representation
9. **Export**: Allow exporting the timeline as a report
10. **Integration with Other Entity Types**: Support linking other entity types (e.g., decisions, tasks)
