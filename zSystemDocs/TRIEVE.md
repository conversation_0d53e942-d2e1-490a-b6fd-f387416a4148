# Trieve Utilities Architecture

This document outlines the architecture for Trieve integration in the product-vision codebase.

## Overview

The Trieve integration is structured in a layered architecture:

1. **Utility Layer**: Common functions and helpers in `utils/` directory
2. **Actions Layer**: Core Trieve API interactions in `actions/trieveActions.ts` 
3. **Mutation Layer**: File operations that trigger Trieve indexing in `files.ts`

## Files and Responsibilities

### 1. `utils/fileUtils.ts`

Generic utilities for file operations:
- `getSubTableData()`: Retrieves content and category from the appropriate sub-table
- `getCompleteFileRecord()`: Gets a file record with its sub-table data combined
- `fileHasContent()`: Checks if a file exists and has non-empty content
- `validateFileExists()`: Validates file existence with standardized error

### 2. `utils/trieveUtils.ts`

Trieve-specific utilities:
- `TRIEVE_OPERATION_DELAY`: Standard delay constant (currently 20000ms)
- `prepareTreivedDocData()`: Prepares document data for Trieve operations (used within actions)
- `checkTrieveConfiguration()`: Verifies Trieve SDK is configured
- `handleTrieveError()`: Standardized error handling
- `chunkContentByParagraphs()`: Content chunking for Trieve
// Note: scheduleTrieveOperation has been removed and replaced by the debouncing utility

### 3. `actions/trieveActions.ts`

Core Trieve actions:
- `diagnosticCheckTrieveGroup`: Diagnostic tool for Trieve groups
- `trieveUpsertDocumentChunks`: Core document chunking and indexing
- `performTrieveReindex`: Action for reindexing a document
- `reindexAllDocumentsOfType`: Bulk reindexing action
- `updateDocument`: Deletes and re-indexes a document

### 4. `deleteDocument.ts`

Simple mutation to delete a document from Trieve.

## Usage Examples

### Scheduling a Debounced Trieve Operation

Trieve indexing (and re-indexing) is now triggered automatically within the `createKnowledgeBaseArticle`, `updateKnowledgeBaseArticle`, and `updateMeetingNote` mutations in `convex/files.ts`. These mutations use the `scheduleDebouncedJob` utility from `convex/utils/schedulerUtils.ts` to handle debouncing.

```typescript
// Example within updateMeetingNote in convex/files.ts:
await scheduleDebouncedJob(ctx, {
  documentId: typedFileId,
  jobIdFieldName: "trieveIndexingJobId", // Tracks the pending job in the 'files' table
  jobToSchedule: api.actions.trieveActions.trieveUpsertDocumentChunks, // The action to run
  jobArgs: { fileId: typedFileId }, // Args for the action
  delayMs: TRIEVE_OPERATION_DELAY, // The debounce delay
});
```

### Preparing Document Data for Trieve

```typescript
const docData = await prepareTreivedDocData(ctx, fileId, {
  // Optional overrides
  title: "New Title",
  content: "New Content"
});
```

### Error Handling

```typescript
try {
  // Trieve operation
} catch (error) {
  return handleTrieveError("operation-name", fileId, error);
}
```

## Best Practices

1. **Use standard delay**: Always use `TRIEVE_OPERATION_DELAY` constant
2. **Standardized error handling**: Use `handleTrieveError()` function
3. **Abstract Trieve operations**: Call the core actions (`trieveUpsertDocumentChunks`, etc.) rather than implementing logic directly. Use `scheduleDebouncedJob` for triggering.
4. **Keep utilities separated**: `fileUtils.ts` handles general file operations; `trieveUtils.ts` handles Trieve-specific helpers (chunking, error handling); `schedulerUtils.ts` handles debounced job scheduling.

Core Trieve Concepts Used:

Groups: Each FOJO document (files record) corresponds to one Trieve Group. This links all chunks derived from that document.
Chunks: The content of each FOJO document is split into smaller pieces (currently by paragraph using chunkContentByParagraphs) which become individual Trieve Chunks.
Tracking IDs:
Group tracking_id: Uses the Convex files._id.
Chunk tracking_id: Uses a composite ID ${files._id}_chunk_${index}. This allows for consistent updates and deletions.
Tags (tag_set): Used for filtering. Currently includes Type, Org, and Project.
Metadata: Stores additional, non-indexed information about the group or chunk.
chunk_html: The primary content field for embedding and searching. The current implementation formats this well by labeling distinct pieces of information.
General Data Flow (trieveUpsertDocumentChunks action):

Identify Document: Takes either a full doc object or just a fileId. If only fileId is provided, it fetches the latest data from Convex.
Fetch Data (if needed):
Gets the main files record using api.files.getFileRecordBasic.
Gets content and category from the relevant sub-table (knowledge_base or meeting_notes) using api.files.getSubTableDataPublic.
Check Trieve Config: Ensures API keys are set.
Ensure Group Exists: Creates or updates the Trieve Group associated with the fileId.
Chunk Content: Splits the main content field into paragraphs.
Fetch Existing Chunks: Retrieves current chunks for the group from Trieve.
Prepare New Chunks: Formats each paragraph into the chunk_html structure, adding metadata and tags.
Diff & Sync: Compares new chunks with existing ones:
Creates new chunks.
Updates chunks where chunk_html has changed.
Deletes chunks that are no longer present in the new content.
Clear Job ID: Removes the scheduled job ID from the files table upon success.
Trieve Architecture for KNOWLEDGE_BASE
Trieve Group:

tracking_id: files._id (e.g., k1x...)
name: files.title (e.g., "Onboarding New Engineers")
description: "FOJO document [files._id]"
tag_set: ["Type:KNOWLEDGE_BASE", "Org:[orgId]", "Project:[projectId]"] (Project tag added if files.parentEntityId is a project ID)
metadata: { "title": "...", "docType": "KNOWLEDGE_BASE", "projectId": "...", "orgId": "..." }
Trieve Chunk (for each paragraph):

tracking_id: "[files._id]_chunk_[index]" (e.g., k1x..._chunk_0)
group_tracking_ids: ["[files._id]"]
time_stamp: files._creationTime (Converted to ISO String)
tag_set: Same as the Group's tag_set.
metadata: { "documentId": "[files._id]", "title": "...", "docType": "KNOWLEDGE_BASE", "chunkIndex": index }
chunk_html:
Title: [files.title]
Short Description: [files.short_description || 'N/A']
Category: [knowledge_base.category || 'N/A']
Content: [paragraph_from_knowledge_base.content]
Field Sources for KNOWLEDGE_BASE:

files._id: Convex ID of the file record.
files.title: Title of the KB article.
files.short_description: AI-generated summary stored in the file record.
files.parentEntityId: Used to determine projectId if applicable.
files._creationTime: Used for time_stamp.
knowledge_base.category: Category assigned to the KB article.
knowledge_base.content: The main HTML content from the TipTap editor.
orgId: Currently a placeholder. Needs to be sourced correctly (e.g., from user session).
Trieve Architecture for MEETING_NOTES
Trieve Group:

tracking_id: files._id (e.g., m2y...)
name: files.title (e.g., "Project Alpha Kickoff Meeting")
description: "FOJO document [files._id]"
tag_set: ["Type:MEETING_NOTES", "Org:[orgId]", "Project:[projectId]"] (Project tag added if files.parentEntityId is a project ID)
metadata: { "title": "...", "docType": "MEETING_NOTES", "projectId": "...", "orgId": "..." }
Trieve Chunk (for each paragraph):

tracking_id: "[files._id]_chunk_[index]" (e.g., m2y..._chunk_0)
group_tracking_ids: ["[files._id]"]
time_stamp: files._creationTime (Converted to ISO String)
tag_set: Same as the Group's tag_set.
metadata: { "documentId": "[files._id]", "title": "...", "docType": "MEETING_NOTES", "chunkIndex": index }
chunk_html:
Title: [files.title]
Short Description: [files.short_description || 'N/A']
Category: [meeting_notes.category || 'N/A']
Content: [paragraph_from_meeting_notes.content]
Field Sources for MEETING_NOTES:

files._id: Convex ID of the file record.
files.title: Title of the meeting note.
files.short_description: AI-generated summary stored in the file record (generated after AI notes).
files.parentEntityId: Used to determine projectId if applicable.
files._creationTime: Used for time_stamp.
meeting_notes.category: Category assigned to the meeting note.
meeting_notes.content: Currently, this is the AI-generated notes content derived from the transcript. The raw transcript and manualNotes fields are not explicitly indexed by the trieveUpsertDocumentChunks action as it stands.
orgId: Currently a placeholder. Needs to be sourced correctly.
Observations and Potential Enhancements:

Meeting Notes Content: The current setup primarily indexes the AI-generated notes (meeting_notes.content). For comprehensive search, consider also indexing:
The raw meeting_notes.transcript.
The meeting_notes.manualNotes. This might involve modifying trieveUpsertDocumentChunks to fetch these fields and potentially creating separate chunks or appending them to the chunk_html with appropriate labels (e.g., Transcript: ..., Manual Notes: ...).
orgId Placeholder: The hardcoded PLACEHOLDER_ORG_ID needs to be replaced with a dynamic value, likely fetched from the user's session or the document's context within the organization.
chunk_html Formatting: The current labeled format (Title: ...\nContent: ...) aligns well with Trieve's recommendations for improving embedding quality.
Indexed Fields vs. Metadata: The use of tag_set for Type, Org, and Project is appropriate for filtering performance. Other details like title, docType, etc., are suitable for metadata.
Chunking Strategy: Chunking by paragraph is a reasonable default. Depending on content structure, exploring Trieve's file upload endpoint with vision model chunking (if applicable to the source format) or heading-based chunking could be alternatives for specific document types in the future.
This architecture provides a solid foundation. The main areas for consideration are resolving the orgId placeholder and deciding whether to index the raw transcript and manual notes for meetings alongside the AI-generated content.


Overall Approach:

Each document (files record) becomes a Trieve Group, identified by files._id.
Content is split into paragraphs, each becoming a Trieve Chunk linked to the group. Chunk IDs are ${files._id}_chunk_${index}.
Tags (tag_set) include Type:[docType], Org:[orgId], and optionally Project:[projectId].
chunk_html is formatted with labels (Title, Short Description, Category, Content) for better embedding.
Specific Fields Saved:

For Knowledge Base:

Group: 
- tracking_id (files._id), 
- name (files.title), 
- tag_set, 
metadata (title, docType, projectId, orgId).

Chunk: 
tracking_id, group_tracking_ids, time_stamp (files._creationTime), tag_set, metadata (documentId, title, docType, chunkIndex), chunk_html (includes files.title, files.short_description, knowledge_base.category, and the paragraph from knowledge_base.content).


For Meeting Notes:
Group: tracking_id (files._id), name (files.title), tag_set, metadata (title, docType, projectId, orgId).
Chunk: tracking_id, group_tracking_ids, time_stamp (files._creationTime), tag_set, metadata (documentId, title, docType, chunkIndex), chunk_html (includes files.title, files.short_description, meeting_notes.category, and the paragraph from meeting_notes.content (AI Notes only)).
Key Considerations:

orgId Source: The orgId is currently a placeholder. We need to define how to retrieve the correct organization ID (e.g., from the user's session or document context).
Meeting Notes Content: The current process only indexes the AI-generated notes (meeting_notes.content). Should we also index the raw transcript and manualNotes for better searchability? This would require modifying the trieveUpsertDocumentChunks action.



