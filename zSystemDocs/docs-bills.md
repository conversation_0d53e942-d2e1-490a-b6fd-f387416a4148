# Bills System Architecture

## Overview
The bills system handles financial documents with two main flows:
1. **Credit Card Statements** - Manually uploaded with multiple line items from different merchants
2. **Invoices** - Can be manually uploaded or synced from Bill.com, with line items typically matching the bill's vendor

## Core Components

### 1. Data Models

#### Bills Schema
```typescript
bills: defineTable({
  amount: v.number(),
  billDate: v.number(),
  billNo: v.string(),
  type: v.union(v.literal('BILL'), v.literal('CREDIT_CARD')),
  vendor_id: v.id('organizations'),
  // ... other fields
})
.index('by_vendor', ['vendor_id'])
.index('by_type', ['type'])
```

#### Line Items Schema
```typescript
lineItems: defineTable({
  bill_id: v.id('bills'),
  amount: v.number(),
  post_date: v.number(),
  merchant_name: v.string(),  // For credit card transactions
  description: v.string(),
  spending_category: v.optional(v.id('tags')),
  vendor_id: v.id('organizations'),  // Links to the vendor organization
})
.index('by_bill', ['bill_id'])
.searchIndex('search_merchant_name', {
  searchField: 'merchant_name',
})
```

## Key Files

### Frontend Components
- `app/(dashboard)/bills/page.tsx` - Main bills list view
- `app/(dashboard)/bills/[id]/page.tsx` - Bill detail view
- `app/(dashboard)/bills/[id]/bill-line-items-table.tsx` - Line items table
- `app/(dashboard)/bills/components/BillsTable.tsx` - Bills data table
- `app/(dashboard)/bills/hooks/useBills.ts` - Custom hook for bills data management

### Backend
- `convex/bills.ts` - Main bill-related queries and mutations
- `convex/lineItems.ts` - Line item operations
- `convex/integrations/billCom.ts` - Bill.com integration

## Data Flow

### 1. Credit Card Flow

#### Upload & Parsing
1. User uploads a credit card statement PDF
2. PDF is processed (potentially using an external service)
3. System creates:
   - One bill record with type 'CREDIT_CARD'
   - Multiple line item records, each with:
     - `merchant_name` set to the actual merchant
     - `vendor_id` set to the credit card company (e.g., AMEX)

#### Key Implementation
```typescript
// In bills.ts - Simplified credit card bill creation
const billId = await insertBill(ctx, {
  type: 'CREDIT_CARD',
  vendor_id: creditCardVendorId,  // e.g., AMEX
  // ... other fields
});

// Create line items for each transaction
await Promise.all(transactions.map(tx => 
  ctx.db.insert('lineItems', {
    bill_id: billId,
    merchant_name: tx.merchant,  // Actual merchant name
    vendor_id: creditCardVendorId,  // Still AMEX
    // ... other fields
  })
));
```

### 2. Manual Invoice Flow

#### Upload & Processing
1. User uploads an invoice document
2. User fills in details including vendor
3. System creates:
   - One bill record with type 'BILL'
   - Line items inherit vendor from the bill

#### Key Implementation
```typescript
// In bills.ts - Simplified manual invoice creation
const billId = await insertBill(ctx, {
  type: 'BILL',
  vendor_id: inputVendorId,
  // ... other fields
});

// Create line items
await Promise.all(inputLineItems.map(item => 
  ctx.db.insert('lineItems', {
    bill_id: billId,
    merchant_name: vendor.name,  // Inherit from bill's vendor
    vendor_id: inputVendorId,    // Same as bill's vendor
    // ... other fields
  })
));
```

### 3. Bill.com Integration Flow

#### Sync Process
1. User triggers sync with Bill.com
2. System fetches bills and vendors from Bill.com
3. For each bill:
   - Matches or creates vendor organization
   - Creates/updates bill record
   - Creates/updates line items

#### Key Implementation
```typescript
// In bills.ts - Simplified Bill.com sync
const vendorOrg = await findOrCreateVendor(billComBill.vendorId, billComBill.vendorName);

const billId = await insertBill(ctx, {
  type: 'BILL',
  vendor_id: vendorOrg._id,
  // ... other fields
});

// Create line items
await Promise.all(billComBill.lineItems.map(item => 
  ctx.db.insert('lineItems', {
    bill_id: billId,
    merchant_name: vendorOrg.name,  // Use vendor name from organization
    vendor_id: vendorOrg._id,       // Link to vendor organization
    // ... other fields
  })
));
```
