# Guide: How the `files` Table and Sub-Tables (`knowledge_base`, `meeting_notes`) Work Together

This guide explains the architecture and usage patterns for the parent-child file model in your Convex backend, focusing on the `files` table and its sub-tables: `knowledge_base` and `meeting_notes`. It covers how data is structured, linked, searched, and managed, with references to your schema and API logic.

---

## 1. Data Model Overview

### Parent-Child Structure

- **`files`**: The parent table. Each file record represents a document, knowledge base article, meeting note, contract, etc. It stores metadata (title, type, owner, etc.).
- **Sub-tables**: Each file of a certain `docType` (e.g., `KNOWLEDGE_BASE`, `MEETING_NOTES`) has a corresponding child record in its sub-table, linked by `fileId`.
  - **`knowledge_base`**: Stores the main content and category for knowledge base articles.
  - **`meeting_notes`**: Stores meeting-specific data (content, manual notes, transcript, attendees, etc.).

#### Mermaid Diagram: Table Relationships

```mermaid
erDiagram
  FILES {
    id _id PK
    string title
    string docType
    ...
  }
  KNOWLEDGE_BASE {
    id _id PK
    id fileId FK
    string content
    string category
    ...
  }
  MEETING_NOTES {
    id _id PK
    id fileId FK
    string content
    string manualNotes
    string transcript
    ...
  }
  FILE_RELATIONSHIPS {
    id _id PK
    id file_id FK
    string subject_type
    id subject_id
    ...
  }

  FILES ||--o{ KNOWLEDGE_BASE : "fileId"
  FILES ||--o{ MEETING_NOTES : "fileId"
  FILES ||--o{ FILE_RELATIONSHIPS : "file_id"
```

---

## 2. Creation, Update, and Deletion Flows

### Creation

- **Knowledge Base Article**
  1. Insert into `files` with `docType: 'KNOWLEDGE_BASE'`.
  2. Insert into `knowledge_base` with `fileId` referencing the parent file.
  3. Optionally, create `file_relationships` for initial links to other entities.

- **Meeting Note**
  1. Insert into `files` with `docType: 'MEETING_NOTES'`.
  2. Insert into `meeting_notes` with `fileId` referencing the parent file.
  3. Optionally, create `file_relationships` for initial links.

### Update

- Update the parent `files` record (e.g., title, metadata).
- Update the child sub-table record (e.g., content, manual notes).
- Add/remove relationships in `file_relationships` as needed.

### Deletion

- Delete all `file_relationships` for the file.
- Delete the sub-table record (`knowledge_base` or `meeting_notes`).
- Delete the parent `files` record.

---

## 3. Where Information is Stored

| Table            | Key Fields                                      | Purpose                                      |
|------------------|-------------------------------------------------|----------------------------------------------|
| `files`          | `title`, `docType`, `ownerId`, `updated_at`     | Metadata for all files                       |
| `knowledge_base` | `fileId`, `content`, `category`                 | Main content for knowledge base articles     |
| `meeting_notes`  | `fileId`, `content`, `manualNotes`, `attendees` | Main content and metadata for meeting notes  |
| `file_relationships` | `file_id`, `subject_type`, `subject_id`     | Links files to other entities (tasks, etc.)  |

- **Content**: Stored in the sub-table (`content` field).
- **Metadata**: Stored in `files`.
- **Relationships**: Stored in `file_relationships`.

---

## 4. Searching and Retrieval

### Indexes and Search

- **`files`**
  - `.index('by_docType', ['docType'])`: Filter by type (e.g., all knowledge base articles).
  - `.searchIndex('search_title', { searchField: 'title' })`: Full-text search on title.
  - `.index('by_ownerId', ['ownerId'])`: Filter by owner.

- **`knowledge_base`**
  - `.index('by_file', ['fileId'])`: Find by parent file.
  - `.searchIndex('search_content', { searchField: 'content' })`: Full-text search on content.
  - `.vectorIndex('content_embedding_idx', ...)`: Semantic/vector search.

- **`meeting_notes`**
  - `.index('by_file', ['fileId'])`
  - `.searchIndex('search_content', { searchField: 'content' })`
  - `.searchIndex('search_manualNotes', { searchField: 'manualNotes' })`
  - `.vectorIndex('content_embedding_idx', ...)`, etc.

### Query Patterns

- **List all knowledge base articles**: Query `files` with `docType: 'KNOWLEDGE_BASE'`.
- **Get full article**: Fetch file by ID, then fetch `knowledge_base` by `fileId`.
- **Search by title/content**: Use `.searchIndex` on `files` or sub-table.
- **Semantic search**: Use `.vectorIndex` on sub-table embeddings.

---

## 5. Linking and Relationships

### How Linking Works

- **file_relationships**: Stores links between a file and any other entity (task, project, decision, etc.).
  - Fields: `file_id`, `subject_type`, `subject_id`, `linked_at`, `featuredFile`
- **initialRelationships**: When creating a file, you can pass an array of relationships to create in bulk.
- **getRelatedEntities**: Query to fetch all entities related to a file via `file_relationships`.

#### Example: Linking a Knowledge Base Article to a Project

- When creating the article, pass an initial relationship:
  ```js
  initialRelationships: [
    { subject_type: "project", subject_id: "projects:abc123" }
  ]
  ```
- This creates a `file_relationships` record linking the file to the project.

---

## 6. Example Usage Patterns

### Creating a Knowledge Base Article

```js
await createKnowledgeBaseArticle({
  title: "How to Use the API",
  content: "Step-by-step guide...",
  ownerId: "users:xyz",
  initialRelationships: [
    { subject_type: "project", subject_id: "projects:abc123" }
  ]
});
```

### Searching for Meeting Notes

```js
await searchMeetingNotes({
  searchQuery: "quarterly review",
  pagination: { numItems: 10 }
});
```

### Fetching Related Entities

```js
await getRelatedEntities({ fileId: "files:abc123" });
// Returns all linked projects, tasks, etc.
```

---

## 7. Summary Table: How It All Connects

| Action                | Parent Table | Sub-Table         | Linking Table         | Search/Indexing         |
|-----------------------|-------------|-------------------|----------------------|-------------------------|
| Create Article        | files       | knowledge_base    | file_relationships   | search_title, search_content, vectorIndex |
| Create Meeting Note   | files       | meeting_notes     | file_relationships   | search_title, search_content, vectorIndex |
| Link to Project/Task  | files       | -                 | file_relationships   | -                       |
| Search by Title       | files       | -                 | -                    | search_title            |
| Search by Content     | -           | knowledge_base/meeting_notes | - | search_content, vectorIndex |

---

## 8. Visual: End-to-End Flow

```mermaid
flowchart TD
  subgraph File Creation
    A[Create file in files] --> B{docType}
    B -- KNOWLEDGE_BASE --> C[Create in knowledge_base]
    B -- MEETING_NOTES --> D[Create in meeting_notes]
  end
  C & D --> E[Create file_relationships (optional)]
  E --> F[Search/Link/Display]
```

---

## 9. Key Takeaways

- The `files` table is the parent for all document types.
- Each sub-table (e.g., `knowledge_base`, `meeting_notes`) stores type-specific content, linked by `fileId`.
- Relationships to other entities are managed via `file_relationships`.
- Searching is supported at both the parent (title) and sub-table (content, semantic) levels.
- All CRUD operations are coordinated to keep parent, child, and relationship records in sync.

---

**For more details, see the code in `convex/schema.ts` and `convex/files.ts`. This guide is designed to help developers understand and extend the file/document architecture in your Convex backend.**
