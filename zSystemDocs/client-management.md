# RIA Client Management System - Core Entities & Relationships Guide 💤

## Overview

Our client management system is built around **relationships** - how people, organizations, and clients connect to each other. This guide covers the core entities (people, organizations, and clients) and their relationships. Legal entities like trusts, LLCs, and foundations will be covered in a separate system.

## Core Concepts

### 1. The Three Main Entity Types

**People**: Individuals in our directory
- Examples: <PERSON> (client), <PERSON> (spouse), Attorney <PERSON> (service provider)
- Stored in the `people` table
- Can have user accounts if they have a `user_id`

**Organizations**: Service provider companies and external entities
- Examples: <PERSON> Stanley, ABC Accounting Firm, Johnson Law Firm, XYZ Lawn Care
- Stored in the `organizations` table
- Can be vendors (`is_vendor: true`)
- NOT used for trusts or other legal entities (those will be modeled separately)

**Clients**: People or service organizations with whom we have a business relationship
- A client IS a person or organization that has been marked with client status
- Stored in the `clients` table with references to either a person or organization
- Examples: <PERSON> (individual client), <PERSON> (individual client)
- Note: Trusts and other legal entities owned by clients will be modeled separately (not as organization clients)

### 2. Key Principle: Clients Are Business Relationships with People or Organizations

A **client** represents our business relationship with a person or organization and includes:
- `client_type`: Either 'person' or 'organization'
- `client_status`: active, on_hold, prospective, or former
- `client_tier`: platinum, gold, silver, bronze, or none
- Business-specific data: net worth, communication frequency, client since date
- Short description and research fields for AI-enhanced insights

**Example**:
- John Smith exists as a person in our directory
- We create a client record that references John Smith
- This client record tracks our business relationship with John

## Flexible Relationship System

Our system uses a **universal relationship model** that can connect any combination of people, organizations, and clients through the `relationships` table.

### Relationship Types Define the Rules

Each relationship type (defined in `relationship_types` table) specifies:

**Core Properties**:
- `relationship_name`: The name of the relationship (e.g., "Spouse", "Attorney", "Trustee")
- `relationship_category`: Grouping for similar types (e.g., "family", "professional", "business")
- `valid_combinations`: Which entity types can have this relationship

**Behavior Flags**:
- `is_client_family`: Part of client's family unit
- `is_bidirectional`: Relationship works both ways
- `implies_client_status`: If source is client, should target be too?
- `is_principal_type`: Makes someone a decision-maker

**Custom Fields Schema**: Define additional fields specific to each relationship type

### Example Relationship Types

#### 1. Family Relationships
**Configuration**:
```
- Name: "Spouse", "Child", "Parent", "Sibling"
- Category: "family"
- Valid: Person ↔ Person
- is_client_family: true
- is_bidirectional: true (for spouse/sibling) or false (for parent/child)
```

**Examples**:
- John Smith ↔ Jane Smith (Spouse)
- John Smith → Tommy Smith (Child)
- Jane Smith → Tommy Smith (Child)

#### 2. Authority/Principal Relationships
**Configuration**:
```
- Name: "Power of Attorney", "Authorized Agent", "Financial Representative"
- Category: "business"
- Valid: Person → Client (another person)
- is_principal_type: true
- Custom fields: authority_level, authority_limit
```

**Examples**:
- Adult Child → Elderly Parent Client (Power of Attorney)
- Spouse → Incapacitated Spouse Client (Financial Representative)
- Business Partner → Business Partner Client (Authorized Agent)

#### 3. Service Provider Relationships
**Configuration**:
```
- Name: "Attorney", "Accountant", "Financial Advisor"
- Category: "professional"
- Valid: Client → Person/Organization
- is_client_family: false
```

**Examples**:
- John Smith (client) → Mike Johnson (Attorney - individual)
- John Smith (client) → Johnson Law Firm (Law Firm - organization)
- Jane Doe (client) → ABC Accounting (Accounting Firm - organization)
- John Smith (client) → Morgan Stanley (Financial Services - organization)
- Jane Doe (client) → XYZ Lawn Care (Property Maintenance - organization)

#### 4. Professional Relationships
**Configuration**:
```
- Name: "Employee", "Partner", "Board Member"
- Category: "professional"
- Valid: Person → Organization
- is_client_family: false
```

**Examples**:
- Mike Johnson → Johnson Law Firm (Partner)
- Jane Smith → XYZ Foundation (Board Member)

#### 5. Client-to-Client Relationships
**Configuration**:
```
- Name: "Spouse", "Family Member", "Business Partner", "Referral Source"
- Category: "business"
- Valid: Client → Client
```

**Examples**:
- John Smith (client) ↔ Jane Smith (client) - Spouse
- Business Partner A (client) ↔ Business Partner B (client) - Business Partners
- Existing Client → New Client - Referral Source

## How the System Works in Practice

### Setting Up an Individual Client

**Step 1**: Create person in directory
```javascript
// Create person record
{
  name: "John Smith",
  email: "<EMAIL>",
  phone: "555-1234",
  title: "CEO",
  description: "Technology entrepreneur and investor"
}
```

**Step 2**: Create client record
```javascript
// Create client record referencing the person
{
  client_name: "John Smith Wealth Management",
  client_type: "person",
  // Reference to John Smith's person ID
  client_status: "active",
  client_tier: "gold",
  client_since: [date],
  client_net_worth: 5000000,
  client_communication_frequency: "monthly"
}
```

**Step 3**: Create family relationships
```javascript
// Spouse relationship
{
  source_type: "person",
  source_id: [John's person ID],
  target_type: "person",
  target_id: [Jane's person ID],
  relationship_type_id: [Spouse relationship type ID],
  is_active: true
}
```

**Step 4**: Add service providers
```javascript
// Attorney relationship
{
  source_type: "client",
  source_id: [John's client ID],
  target_type: "person",
  target_id: [Attorney's person ID],
  relationship_type_id: [Attorney relationship type ID],
  is_active: true
}
```

**Step 5**: Assign staff
```javascript
// Client assignment
{
  client_id: [John's client ID],
  user_id: [Advisor's user ID],
  assignment_role: "Lead Advisor",
  is_primary_assignment: true
}
```

### Setting Up a Business Service Organization

**Step 1**: Create organization
```javascript
// Create organization record
{
  name: "Johnson Law Firm",
  description: "Full service law firm specializing in estate planning",
  is_vendor: true,
  website: "www.johnsonlaw.com",
  phone: "555-LAW-FIRM"
}
```

**Step 2**: Create people at the organization
```javascript
// Create attorney record
{
  name: "Mike Johnson",
  email: "<EMAIL>",
  title: "Managing Partner",
  description: "Estate planning attorney with 20 years experience"
}
```

**Step 3**: Link attorney to firm
```javascript
// Professional relationship
{
  source_type: "person",
  source_id: [Mike Johnson's person ID],
  target_type: "organization",
  target_id: [Johnson Law Firm's organization ID],
  relationship_type_id: [Partner relationship type ID],
  is_active: true
}
```

**Step 4**: Connect to clients
```javascript
// Client to law firm relationship
{
  source_type: "client",
  source_id: [John Smith's client ID],
  target_type: "organization",
  target_id: [Johnson Law Firm's organization ID],
  relationship_type_id: [Law Firm relationship type ID],
  is_active: true
}

// Client to specific attorney relationship
{
  source_type: "client",
  source_id: [John Smith's client ID],
  target_type: "person",
  target_id: [Mike Johnson's person ID],
  relationship_type_id: [Attorney relationship type ID],
  is_active: true
}
```

## Staff Assignment System

Staff are assigned to clients through the `client_assignments` table:

**Key Features**:
- **Users** (staff) are assigned to **Clients**
- **Assignment metadata**:
  - `assignment_role`: Describes the role (e.g., "Lead Advisor", "Tax Specialist")
  - `is_primary_assignment`: Boolean flag for primary responsibility
- Multiple staff can be assigned to one client
- One staff member can be assigned to multiple clients

**Example Assignment Structure**:
```
John Smith (Client)
├── Sarah Johnson (Primary Assignment - Lead Advisor)
├── Mike Brown (Secondary Assignment - Tax Specialist)
└── Lisa Davis (Secondary Assignment - Administrative Assistant)

Jane Smith (Client)
├── Sarah Johnson (Primary Assignment - Lead Advisor)
└── Lisa Davis (Secondary Assignment - Administrative Assistant)
```

## Common Workflows

### 1. Complete Family Client Setup

**Scenario**: Setting up the Smith family as clients with full service network

```
1. Create People:
   - John Smith (husband)
   - Jane Smith (wife)
   - Tommy Smith (son, 16)
   - Sarah Smith (daughter, 22)

2. Create Service Provider Organizations:
   - Johnson Law Firm
   - ABC Accounting
   - Morgan Stanley
   - Elite Lawn Care

3. Create Service Provider People:
   - Mike Johnson (attorney at Johnson Law Firm)
   - Lisa Chen (CPA at ABC Accounting)
   - Tom Wilson (advisor at Morgan Stanley)

4. Create Clients:
   - John Smith (individual client)
   - Jane Smith (individual client)
   - Sarah Smith (individual client - adult child)

5. Establish Relationships:
   Family:
   - John ↔ Jane (Spouse)
   - John → Tommy (Child)
   - John → Sarah (Child)
   - Jane → Tommy (Child)
   - Jane → Sarah (Child)

   Service Provider to Organization:
   - Mike Johnson → Johnson Law Firm (Partner)
   - Lisa Chen → ABC Accounting (Senior Accountant)
   - Tom Wilson → Morgan Stanley (Financial Advisor)

   Client to Service Provider:
   - John (client) → Mike Johnson (Attorney)
   - John (client) → Johnson Law Firm (Law Firm)
   - Jane (client) → Mike Johnson (Attorney)
   - John (client) → ABC Accounting (Accounting Firm)
   - Jane (client) → ABC Accounting (Accounting Firm)
   - John (client) → Elite Lawn Care (Property Maintenance)
   - Sarah (client) → Tom Wilson (Financial Advisor)

   Client-to-Client:
   - John (client) ↔ Jane (client) - Spouse
   - John (client) → Sarah (client) - Family Member

6. Assign Staff:
   - Sarah Johnson → John Smith (Primary - Lead Advisor)
   - Sarah Johnson → Jane Smith (Primary - Lead Advisor)
   - Mark Wilson → Sarah Smith (Primary - Next Gen Specialist)
```

**Note**: Trusts and other legal entities owned by the Smith family would be tracked separately in a legal entities system (not covered in this guide).

### 2. Adding Adult Children as Clients

**Scenario**: Sarah Smith (22) becomes a client

```
1. Sarah already exists as a person (from family relationships)

2. Create client record:
   - client_name: "Sarah Smith Investment Account"
   - client_type: "person"
   - client_status: "active"
   - client_tier: "silver"

3. Maintain existing family relationships (no changes needed)

4. Add her own service providers if different from parents

5. Assign staff (might be same advisor as parents)
```

### 3. Handling Complex Service Provider Networks

**Scenario**: Client has multiple related service providers

```
1. Create Service Organizations:
   - Johnson Law Firm (main firm)
   - Johnson Estate Planning LLC (specialized division)
   - ABC Accounting (CPA firm)
   - XYZ Financial Planning (RIA firm)

2. Create Service People:
   - Mike Johnson (Senior Partner)
   - Susan Johnson (Estate Attorney)
   - Lisa Chen (Lead CPA)
   - Robert Zhang (Tax Specialist)

3. Create Professional Relationships:
   - Mike Johnson → Johnson Law Firm (Senior Partner)
   - Susan Johnson → Johnson Law Firm (Associate)
   - Susan Johnson → Johnson Estate Planning LLC (Managing Member)
   - Lisa Chen → ABC Accounting (Partner)
   - Robert Zhang → ABC Accounting (Senior Tax Manager)

4. Create Client-to-Service Relationships:
   - John Smith (client) → Johnson Law Firm (Primary Law Firm)
   - John Smith (client) → Mike Johnson (Lead Attorney)
   - John Smith (client) → Susan Johnson (Estate Planning Attorney)
   - John Smith (client) → ABC Accounting (Accounting Firm)
   - John Smith (client) → Lisa Chen (Primary CPA)
   - John Smith (client) → Robert Zhang (Tax Advisor)

5. Create Service Provider Cross-References:
   - Johnson Law Firm → ABC Accounting (Preferred Accounting Partner)
   - ABC Accounting → Johnson Law Firm (Preferred Legal Partner)
```

## Querying and Reporting

### Common Query Patterns

#### "Who is part of John Smith's family network?"
```sql
-- Get all family relationships where John is involved
SELECT * FROM relationships r
JOIN relationship_types rt ON r.relationship_type_id = rt._id
WHERE rt.is_client_family = true
AND (
  (r.source_type = 'person' AND r.source_id = [John's ID])
  OR (r.target_type = 'person' AND r.target_id = [John's ID])
)
```

#### "Who can make decisions for an elderly client?"
```sql
-- Get all people with authority relationships to the client
SELECT * FROM relationships r
JOIN relationship_types rt ON r.relationship_type_id = rt._id
WHERE rt.is_principal_type = true
AND r.target_type = 'client'
AND r.target_id = [Elderly client's ID]
-- This would return POA holders, authorized agents, etc.
```

#### "What is the complete service network for a client?"
```sql
-- Get all service provider relationships
SELECT * FROM relationships r
WHERE r.source_type = 'client'
AND r.source_id = [Client ID]
AND r.relationship_type_id IN (
  SELECT _id FROM relationship_types
  WHERE relationship_category = 'professional'
)
```

#### "Which staff members are assigned to the Smith family?"
```sql
-- Get all staff assignments for Smith-related clients
SELECT * FROM client_assignments ca
JOIN clients c ON ca.client_id = c._id
WHERE c.client_name LIKE '%Smith%'
ORDER BY ca.is_primary_assignment DESC
```

#### "What service providers does the Smith family use?"
```sql
-- Get all service relationships for Smith family clients
SELECT * FROM relationships r
JOIN clients c ON r.source_id = c._id
WHERE r.source_type = 'client'
AND c.client_name LIKE '%Smith%'
AND r.relationship_type_id IN (
  SELECT _id FROM relationship_types
  WHERE relationship_category = 'professional'
)
```

## Benefits of This System

### 1. **Flexibility**
- New relationship types can be added without schema changes
- Custom fields per relationship type allow for specific data capture
- Supports complex family and business structures

### 2. **Comprehensive Tracking**
- Complete family networks including extended family
- Full professional service provider networks
- Historical relationship data with start/end dates

### 3. **Clear Authority**
- Principal relationships clearly define decision-makers
- Authority limits and levels can be tracked
- Succession planning through relationship updates

### 4. **Efficient Staff Management**
- Clear assignment of primary and supporting advisors
- Workload visibility across the team
- Specialization tracking (tax, estate, investment)

### 5. **Compliance and Risk Management**
- Complete audit trail of relationships
- Conflict of interest identification
- Clear documentation of authority and decision rights

### 6. **Business Intelligence**
- Referral source tracking
- Client network analysis
- Cross-selling opportunity identification
- Service provider performance tracking

## Best Practices

### 1. **Data Entry Standards**
- Always create person/organization records before client records
- Use consistent naming conventions for clients
- Document relationship end dates when relationships change

### 2. **Relationship Management**
- Review and update relationships quarterly
- Document authority changes immediately
- Keep service provider information current

### 3. **Staff Assignment**
- Always designate one primary advisor per client
- Document specialty roles in assignment_role
- Update assignments during staff transitions

### 4. **Custom Fields Usage**
- Use relationship type custom fields for important metadata
- Examples: marriage dates, authority limits, retainer amounts
- Keep custom field data updated during reviews

## Future Enhancements

### Legal Entities (Out of Scope)
The system will eventually support tracking of legal entities owned by clients, such as:
- Trusts (Revocable, Irrevocable, Charitable, etc.)
- LLCs and Corporations
- Foundations
- Partnerships

These will be modeled as separate entities that clients "own" or control, rather than as organizations or clients themselves. This will allow for proper tracking of:
- Ownership structures
- Beneficiary relationships
- Trust distributions
- Entity documentation

*Note: Legal entity management is not covered in this guide and will be implemented as a separate system component.*

## Security and Privacy Considerations

### 1. **Access Control**
- Staff can only see clients they're assigned to
- Family relationship data requires appropriate permissions
- Service provider information may have viewing restrictions

### 2. **Data Sensitivity**
- Net worth and financial data are highly sensitive
- Family relationships may reveal private information
- Health-related custom fields need special protection

### 3. **Audit Requirements**
- All relationship changes are tracked with timestamps
- Staff assignment changes are logged
- Client status changes require documentation

---

*This guide reflects the flexible, relationship-based architecture of our RIA client management system's core entities. Organizations represent service providers (law firms, accounting firms, etc.), while trusts and other legal entities owned by clients will be managed in a separate system. The relationship framework is designed to handle complex family structures, professional networks, and service provider relationships while maintaining clear records for compliance and effective client service.*
