# Tagging & Tag Reporter – Detailed Functional Specification
 
## 1. Purpose and Goals

- **Tagging** enables users to organize, categorize, and analyze all major entities in FOJO (Projects, Tasks, Decisions, Investment Decisions, Documents, etc.) using flexible, structured labels.
- **Tag Reporter** provides a powerful, AI-driven reporting tool that allows users to group tagged items, generate summaries, and compare groups for insights—especially valuable for investment decision analysis.

---

## 2. Tagging System Overview

### 2.1 What is a Tag?

A **Tag** is a label you can attach to any item in FOJO. Tags help you:
- Group similar items (e.g., all “2025 Deals”)
- Filter and search for information quickly
- Track themes, priorities, or custom attributes

**Example:** Tag an investment decision with “Private Equity”, “Q2 2025”, and “High Priority”.

### 2.2 Tag Types

Tags are organized into **Tag Types**—categories that define the purpose or context of a tag.

- **Examples of Tag Types:**
  - **Investment Theme** (e.g., “Real Estate”, “Venture Capital”)
  - **Asset Class** (e.g., “Equities”, “Fixed Income”)
  - **Project Status** (e.g., “In Progress”, “Completed”)
  - **Expense Category** (e.g., “Travel”, “Consulting”)

**Why Tag Types Matter:**  
They ensure tags are used consistently and make it possible to filter, report, and analyze by category.

### 2.3 Hierarchical Tags

FOJO supports **hierarchical tags**, allowing you to create parent and child tags for more granular organization. For example:
- Parent Tag: “Real Estate”
  - Child Tags: “Commercial”, “Residential”
- Parent Tag: “Technology”
  - Child Tags: “AI”, “Cloud”, “Cybersecurity”

Hierarchical tags help you:
- Build multi-level categories for complex domains
- Filter or report at both broad and detailed levels
- Visualize relationships between tags in the admin and reporting UI

### 2.4 Tagging Workflow

**Where you can tag:**  
- Projects
- Tasks
- Decisions (including Investment Decisions)
- Documents

**How to tag:**
1. Open the item’s detail page.
2. Locate the “Tags” section (often in the sidebar or near the top).
3. Click to add tags from the list, or search for an existing tag.
4. (If permitted) Create a new tag if the one you need doesn’t exist.
5. Tags may be grouped by type for easier selection.
6. **Hierarchical tags** are displayed in a tree or nested list, making it easy to select both parent and child tags.

**Best Practices:**
- Use existing tags where possible for consistency.
- Apply multiple tags to capture all relevant attributes.
- Use tag types and hierarchy to clarify the meaning of each tag.

---

## 3. Admin: Creating and Managing Tags

### 3.1 Admin Tag Management Section

FOJO provides a dedicated **Admin section** for managing tags and tag types.

**Key Features:**
- **Create, edit, and delete tags**: Add new tags, rename existing ones, or remove unused tags.
- **Manage tag types**: Define new tag types (e.g., “Investment Theme”, “Expense Category”) and set their properties.
- **Build tag hierarchies**: Organize tags into parent/child relationships using a drag-and-drop or tree interface.
- **Set tag colors and descriptions**: Make tags visually distinct and easy to understand.
- **Control permissions**: Restrict tag creation/editing to admins or specific user roles.

**How to Access:**
- Go to the Admin area and select “Tags” or “Tag Management”.
- Use the UI to browse, search, and organize tags and tag types.

**Best Practices:**
- Regularly review and clean up unused tags.
- Use clear, descriptive names for tags and tag types.
- Maintain a logical hierarchy for easier navigation and reporting.

---

## 4. Tagging Investment Decisions

### 4.1 Why Tag Investment Decisions?

Tagging investment decisions allows you to:
- Track and analyze your investment pipeline by theme, asset class, or priority.
- Group similar deals for reporting and review.
- Filter and compare investments across different criteria.
- **Surface Strategic Patterns:** By tagging investments with themes, you can see which strategies are most active, which are underrepresented, and where your capital is concentrated.
- **Enable Dynamic Reporting:** Because themes and other tags are managed in the Admin UI, your reporting adapts instantly to new strategies or market shifts.

### 4.2 Dynamic Investment Themes

When working with investment decisions, the list of available **Investment Themes** is **dynamically generated** from the current set of tags in the “Investment Theme” tag type. This means:
- Admins can add new themes at any time in the Admin section.
- Users always see the latest set of themes when tagging a decision.
- No need for code changes to update available themes—just manage tags in the Admin UI.

### 4.3 Tag Types for Investments

Common tag types for investment decisions include:
- **Theme:** “Venture Capital”, “Real Estate”, “Private Equity”
- **Asset Class:** “Equities”, “Fixed Income”, “Alternatives”
- **Stage:** “Pipeline”, “Approved”, “Closed”
- **Priority:** “High”, “Medium”, “Low”
- **Year/Quarter:** “2025”, “Q2 2025”

### 4.4 Tagging Process

1. **Navigate to the Investment Decision:**  
   Open the detail page for the investment decision you want to tag.

2. **Add Tags:**  
   In the “Tags” section, select relevant tags for each tag type. For example, you might tag a deal as:
   - Theme: “Venture Capital” (dynamically appears if created in Admin)
   - Asset Class: “Alternatives”
   - Priority: “High”
   - Year: “2025”

3. **Save Changes:**  
   Tags are saved automatically or via a “Save” button, depending on the interface.

4. **Review and Edit:**  
   You can add, remove, or change tags at any time to keep your data accurate.

---

## 5. Tag Reporter – Feature Deep Dive

### 5.1 Why a Flexible, AI-Powered Reporter?

- **Empower Decision-Makers:** The Tag Reporter lets you build your own groupings and analyses on the fly, without waiting for custom dashboards or IT support.
- **AI as an Analyst:** The system doesn’t just show you data—it interprets it, highlights trends, and surfaces insights you might miss.
- **Adapt to New Questions:** As your business changes, you can create new tag types, new groupings, and new analyses instantly.
- **Bridge Human and Machine Intelligence:** The combination of user-driven grouping and AI-generated analysis means you get both context and computation.

### 5.2 User Workflow

#### Step 1: Access the Tag Reporter

- Navigate to the Tag Reporter section (often found in the Reports or Investments area).

#### Step 2: Organize Tags into Columns

- The interface presents a **Kanban board**.
- **Pool of Tags:** All available tags (filtered by tag type) are shown in a pool, including hierarchical tags (parent and child tags are visually grouped).
- **Columns:** Create columns to represent groups you want to analyze (e.g., “2025 Deals”, “Real Estate”, “High Priority”).
- **Drag & Drop:** Move tags from the pool into columns. Each column can contain one or more tags, including both parent and child tags.

#### Step 3: Generate the Report

- Click the **“Generate Report”** button.
- FOJO’s AI analyzes all items (e.g., investment decisions) linked to the tags in each column.

#### Step 4: Review AI Summaries

- For each column, the AI writes a detailed summary, including:
  - Key patterns and trends
  - Totals (e.g., total investment amount, number of deals)
  - Notable attributes or outliers
- If there are multiple columns, the AI also generates an **overall comparison**:
  - Highlights similarities and differences between groups
  - Points out trends, gaps, or opportunities

#### Step 5: Download or Share

- Download the report as a CSV for further analysis or sharing.
- Copy summaries or comparisons to share with your team.

### 5.3 Tag Reporter Functional Details

- **Tag Pool:** Only tags relevant to the selected tag types are shown, including hierarchical tags (parent/child structure is preserved).
- **Column Management:** Add, rename, or delete columns as needed.
- **Drag-and-Drop:** Move tags freely between pool and columns. Hierarchical tags can be moved as groups or individually.
- **Report Generation:** AI uses all items linked to the tags in each column for analysis.
- **AI Summaries:** Written in clear, business-friendly language for each column.
- **AI Comparison:** Only generated if two or more columns have tags.
- **Download Feature:** Export the full report (including all columns, tags, and AI-generated analysis) as a CSV file for offline review or sharing.
- **Visual Feedback:** The UI provides clear feedback for drag-and-drop, report generation, and download actions.
- **Error Handling:** Users are notified of any issues (e.g., failed report generation).

### 5.4 Example Use Case: Investment Decision Analysis

**Scenario:**  
You want to compare all “Venture Capital” deals in 2025 to “Real Estate” deals in 2025.

**Workflow:**
1. Filter tags to show only “Theme” and “Year” types.
2. Create two columns: “Venture Capital 2025” and “Real Estate 2025”.
3. Drag the “Venture Capital” and “2025” tags into the first column; “Real Estate” and “2025” into the second.
4. Click “Generate Report”.
5. Review the AI summaries for each group and the overall comparison.
6. Download the results or share with stakeholders.

---

## 6. Detailed Requirements & Acceptance Criteria

### 6.1 Tagging

- Users can add, remove, and edit tags on all major entities.
- Tag types and hierarchy are clearly presented in the UI.
- Tagging is available on investment decisions with all relevant tag types.
- Investment themes are dynamically generated from the current set of tags in the “Investment Theme” tag type.
- Tagging changes are saved and reflected in real time.

### 6.2 Admin Tag Management

- Admins can create, edit, delete, and organize tags and tag types.
- Hierarchical tags (parent/child) can be created and managed via drag-and-drop or tree interface.
- Tag colors, descriptions, and permissions can be set.
- All changes are immediately available to users in tagging and reporting interfaces.

### 6.3 Tag Reporter

- Users can filter tags by type and organize them into columns.
- Hierarchical tags are supported in the pool and columns.
- Kanban board supports drag-and-drop for tags and columns.
- “Generate Report” button triggers AI analysis.
- AI generates a summary for each column and a comparison if multiple columns exist.
- Users can download reports as CSV, including all tags, columns, and AI-generated analysis.
- All actions are tracked and reflected in the UI.

### 6.4 Investment Decision Tagging

- Investment decisions support tagging with all configured tag types.
- Tags are visible and editable on the decision detail page.
- Tagging supports grouping, filtering, and reporting.

---

## 7. User Experience Notes

- **Clarity:** All tag types and tags should have clear, descriptive names.
- **Guidance:** Tooltips or help icons explain how to use tags and the Tag Reporter.
- **Permissions:** Tag creation/editing may be restricted based on user role.
- **Performance:** Tagging and reporting actions should be fast and responsive.
- **Accessibility:** All features should be usable with keyboard and screen readers.

---

## 8. Design Criteria

- **Consistent UI:** Tagging and Tag Reporter interfaces match FOJO’s design system.
- **Visual Feedback:** Drag-and-drop, report generation, and download actions provide clear feedback.
- **Error Handling:** Users are notified of any issues (e.g., failed report generation).
- **Scalability:** System supports large numbers of tags and items without performance loss.

---

## 9. Acceptance Criteria

- Users can tag investment decisions and other entities with multiple tags and tag types.
- Tag Reporter allows flexible grouping and analysis of tagged items, including hierarchical tags.
- AI-generated summaries and comparisons are accurate, clear, and actionable.
- Reports can be downloaded and shared, including all relevant details.
- All features are accessible and performant.
- Admins can fully manage tags, tag types, and hierarchies.

---

## 10. Cost, Timeline, and Support

- **Cost:** Included as part of the FOJO platform (unless customizations are requested).
- **Timeline:** Features are available in the current release; enhancements may be delivered incrementally.
- **Support:** For questions or issues, contact your FOJO administrator or support team.

---

## 11. Acknowledgement

By using the Tagging and Tag Reporter features, users agree to follow best practices for data organization and analysis as outlined in this document.
