# Guide: Creating a New OpenAI Tool for the Realtime Conversation Agent

This guide outlines the steps required to add a new function-calling tool for use by the AI within the `RealtimeConversationAgent`.

## Overview of the Tool Architecture

The process involves defining the tool's capabilities and connecting the AI's request to the application's backend logic. The flow is as follows:

1.  **Tool Definition:** A JSON schema defines the tool's name, description, and input parameters (`lib/openai-tools.ts`).
2.  **Session Configuration:** The frontend agent (`useRealtimeConversationAgent`) is configured with the available tool schemas when initiating a session with the OpenAI Realtime API via a Convex HTTP action (`convex/http.ts`).
3.  **AI Tool Invocation:** During the conversation, the AI model decides to use a tool and sends a `function_call` event back to the frontend agent via the WebRTC data channel.
4.  **Frontend Handling:** The `useRealtimeConversationAgent` hook receives the event, identifies the tool name, and parses the arguments.
5.  **Backend Adapter Call:** The frontend agent uses the appropriate Convex React hook (`useMutation`, `useAction`, or `useQuery`) to call a dedicated "adapter" function in the Convex backend (`convex/openai_tools/`).
6.  **Backend Execution:** The adapter function validates the arguments (implicitly via Convex validators) and calls the actual business logic function (e.g., in `convex/tasks.ts`, `convex/projects.ts`) or interacts directly with the database (`ctx.db`).
7.  **Result Return:** The backend function returns the result to the frontend agent.
8.  **Feedback to AI:** The frontend agent sends the result (or error) back to the OpenAI API via the data channel as a `function_call_output` event.
9.  **Conversation Continues:** The AI model uses the tool's result to formulate its next response.

```mermaid
sequenceDiagram
    participant FE as Frontend (useRealtimeConversationAgent)
    participant OpenAI as OpenAI Realtime API
    participant ConvexHTTP as Convex HTTP Action (/openai/...)
    participant ConvexAdapter as Convex Adapter Fn (openai_tools/)
    participant ConvexLogic as Convex Business Logic Fn

    FE->>ConvexHTTP: Request Session (incl. tool schemas)
    ConvexHTTP->>OpenAI: POST /v1/realtime/sessions
    OpenAI-->>ConvexHTTP: Session Token
    ConvexHTTP-->>FE: Session Token
    FE->>OpenAI: Establish WebRTC (SDP Exchange)
    Note over FE, OpenAI: Conversation Starts...
    OpenAI->>FE: Event: function_call (name, args)
    FE->>FE: Identify tool, parse args
    FE->>ConvexAdapter: Call Adapter Fn (via useMutation/Action)
    ConvexAdapter->>ConvexLogic: Call Business Logic Fn
    ConvexLogic-->>ConvexAdapter: Return Result
    ConvexAdapter-->>FE: Return Result
    FE->>OpenAI: Event: function_call_output (result)
    OpenAI->>FE: Event: AI Response (text/audio)
    Note over FE, OpenAI: Conversation Continues...
```

## Steps to Create a New Tool

### Step 1: Define/Identify the Core Business Logic (Convex Function)

*   **Purpose:** Determine the specific action the tool should perform within the application.
*   **Implementation:**
    *   Create (or identify an existing) Convex query, mutation, or action that encapsulates this logic.
    *   Place this function in the relevant domain file (e.g., `convex/tasks.ts`, `convex/decisions.ts`, `convex/files.ts`).
    *   Use standard Convex practices, including Zod validation (`zQuery`, `zMutation`) if applicable.

*   **Example (Hypothetical `createTask` mutation in `convex/tasks.ts`):**
    ```typescript
    // convex/tasks.ts
    import { z } from 'zod';
    import { v } from 'convex/values';
    import { mutation } from './_generated/server';
    import { zid } from 'convex-helpers/server/zod'; // Assuming convex-helpers

    // Example Zod schema for task creation args
    const CreateTaskArgsSchema = z.object({
      name: z.string().min(1),
      projectId: zid('projects').optional(),
      status: z.enum(["todo", "in_progress", "done"]).optional(),
      // ... other fields
    });

    export const createTask = mutation({
      args: { // Use Convex validators matching the Zod schema
        name: v.string(),
        projectId: v.optional(v.id('projects')),
        status: v.optional(v.union(
          v.literal("todo"),
          v.literal("in_progress"),
          v.literal("done")
        )),
        // ... other fields
      },
      handler: async (ctx, args) => {
        // ... implementation to create the task in the database ...
        const taskId = await ctx.db.insert("tasks", {
          name: args.name,
          projectId: args.projectId,
          status: args.status ?? "todo", // Default status
          // ... other fields
        });
        console.log(`Task created with ID: ${taskId}`);
        return { success: true, taskId };
      },
    });
    ```

### Step 2: Create the Tool Schema (`lib/openai-tools.ts`)

*   **Purpose:** Define how the AI understands and interacts with your tool.
*   **Implementation:**
    *   Open `lib/openai-tools.ts`.
    *   Define a new constant holding the JSON schema for your tool. Follow the structure of existing tools (`updateProjectSchema`, `searchPeopleAndTeamsSchema`).
    *   **Crucially:**
        *   Set `"strict": true`.
        *   Set `"additionalProperties": false` within the `inputSchema`.
        *   Define `name` (unique, snake_case), `description` (clear instructions for the AI), and `inputSchema`.
        *   The `inputSchema.properties` must match the arguments expected by your *adapter* function (Step 3). Use `["type", "null"]` for optional properties.
    *   Use the `createOpenAITool` helper function to create the final tool object.
    *   Add the new tool object to the `openAITools` export.

*   **Example (Schema for `create_task`):**
    ```typescript
    // lib/openai-tools.ts
    import { createOpenAITool } from './utils';

    // ... existing schemas ...

    export const createTaskSchema = {
      "name": "create_task",
      "description": "Creates a new task in the system. Requires a task name. Can optionally link to a project and set an initial status.",
      "inputSchema": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "description": "The name/title of the task."
          },
          "projectId": {
            "type": ["string", "null"], // Matches v.optional(v.id('projects'))
            "description": "Optional ID of the project to link this task to."
          },
          "status": {
            "type": ["string", "null"], // Matches v.optional(v.union(...))
            "enum": ["todo", "in_progress", "done"],
            "description": "Optional initial status for the task (defaults to 'todo')."
          }
          // ... other properties matching Step 1 args ...
        },
        "required": ["name"], // Only name is strictly required
        "additionalProperties": false
      },
      "strict": true
    };

    export const createTaskTool = createOpenAITool(createTaskSchema);

    export const openAITools = {
      updateProject: updateProjectTool,
      searchPeopleAndTeams: searchPeopleAndTeamsTool,
      createTask: createTaskTool // Add the new tool
    };
    ```

### Step 3: Create the Adapter Function (`convex/openai_tools/`)

*   **Purpose:** Act as a bridge between the AI tool call (with its specific schema) and the core business logic function.
*   **Implementation:**
    *   Create a new file if needed (e.g., `convex/openai_tools/openai_tasks.ts`) or add to an existing one.
    *   Define a new Convex `mutation`, `query`, or `action`.
    *   The function name should indicate its purpose (e.g., `openai_createTask`).
    *   The `args` definition **must** use Convex validators (`v.*`) that exactly match the `inputSchema.properties` defined in Step 2.
    *   The `handler` should:
        *   Call the core business logic function (from Step 1) using `ctx.runMutation`, `ctx.runQuery`, or `ctx.runAction`.
        *   Pass the arguments received from the AI tool call.
        *   Return the result from the business logic function.
        *   (Alternatively, for very simple operations, it might interact with `ctx.db` directly).

*   **Example (Adapter for `create_task`):**
    ```typescript
    // convex/openai_tools/openai_tasks.ts (New file)
    import { mutation } from '../_generated/server';
    import { v } from 'convex/values';
    import { api } from '../_generated/api';

    /**
     * 🤖 Adapter mutation to create a task based on AI tool input.
     * Calls the existing 'createTask' mutation in convex/tasks.ts.
     */
    export const openai_createTask = mutation({
      // Args MUST match the inputSchema in lib/openai-tools.ts
      args: {
        name: v.string(),
        projectId: v.optional(v.id('projects')),
        status: v.optional(v.union(
          v.literal("todo"),
          v.literal("in_progress"),
          v.literal("done")
        )),
        // ... other args matching schema ...
      },
      handler: async (ctx, args) => {
        console.log("AI Tool: openai_createTask called with args:", args);
        // Call the actual business logic mutation
        const result = await ctx.runMutation(api.tasks.createTask, args);
        return result; // Return the result { success: boolean, taskId: Id<"tasks"> }
      },
    });
    ```
    *Remember to import `api` from `../_generated/api` and reference the correct path to your business logic function (e.g., `api.tasks.createTask`).*

### Step 4: Integrate into Frontend Agent (`components/conversations/RealtimeConversationAgent.ts`)

*   **Purpose:** Handle the `function_call` event for the new tool and trigger the backend adapter function.
*   **Implementation:**
    *   Open `components/conversations/RealtimeConversationAgent.ts`.
    *   Import the API reference for your new adapter function (e.g., `api.openai_tools.openai_tasks.openai_createTask`).
    *   Inside the `useRealtimeConversationAgent` hook, instantiate the corresponding Convex React hook:
        ```typescript
        const createTaskMutation = useMutation(api.openai_tools.openai_tasks.openai_createTask);
        ```
    *   Locate the `dc.onmessage` handler. Find the section that processes `function_call` events (search for `serverEvent.response.output[0]?.type === 'function_call'`).
    *   Add an `else if` block to check if `functionName` matches your new tool's name (e.g., `'create_task'`).
    *   Inside this block:
        *   Log the call and arguments.
        *   Call the Convex hook you instantiated (e.g., `createTaskMutation(args)`).
        *   Use `.then()` and `.catch()` to handle the result.
        *   Send the result or error back to OpenAI using the `sendDataChannelMessage` helper, formatting it as a `conversation.item.create` event with `item.type: "function_call_output"`. Ensure the `output` is stringified JSON.

*   **Example (Handling `create_task`):**
    ```typescript
    // components/conversations/RealtimeConversationAgent.ts

    // ... imports ...
    import { api } from '@/convex/_generated/api'; // Ensure api is imported
    import { useMutation, useAction } from 'convex/react'; // Ensure useMutation/useAction are imported

    export const useRealtimeConversationAgent = (props: RealtimeConversationAgentProps): RealtimeConversationAgentReturn => {
      // ... existing state and refs ...

      // Instantiate hooks for adapter functions
      const updateProjectMutation = useMutation(api.openai_tools.openai_projects.updateProjectFromAITool);
      const searchDirectoryAction = useAction(api.directory.directoryActions.searchDirectoryAction);
      const createTaskMutation = useMutation(api.openai_tools.openai_tasks.openai_createTask); // Add hook for new tool

      // ... getEphemeralToken, startConversation, stopConversation ...

      // Inside dc.onmessage handler:
      dc.onmessage = (event) => {
        // ... existing event parsing ...

        if (
          serverEvent.type === 'response.done' &&
          serverEvent.response?.output &&
          serverEvent.response.output[0]?.type === 'function_call'
        ) {
          // ... existing function call logging ...
          const functionName = serverEvent.response.output[0].name;
          const callId = serverEvent.response.output[0].call_id;

          try {
            const args = JSON.parse(serverEvent.response.output[0].arguments);
            console.log(`Function "${functionName}" called with args:`, args);

            // ... existing checks for 'update_project', 'directory_search_people_and_teams' ...

            // Add handler for the new tool
            else if (functionName === 'create_task') {
              console.log("%c✅ TASK CREATION REQUESTED ✅", "background: #FF9800; color: white; padding: 4px; border-radius: 4px;");
              console.log("Task creation args:", args);

              createTaskMutation(args)
                .then((result: any) => {
                  console.log("Task creation result:", result);
                  const outputEvent = {
                    type: "conversation.item.create",
                    item: {
                      type: "function_call_output",
                      call_id: callId,
                      output: JSON.stringify(result) // Send back { success: boolean, taskId: Id<"tasks"> }
                    }
                  };
                  // Use the helper function to send the message
                  sendDataChannelMessage(outputEvent).catch(console.error);
                  if (onUpdateComplete) onUpdateComplete(result);
                })
                .catch((err: any) => {
                  console.error("Error creating task:", err);
                  const errorEvent = {
                    type: "conversation.item.create",
                    item: {
                      type: "function_call_output",
                      call_id: callId,
                      output: JSON.stringify({
                        error: "Failed to create task",
                        message: err instanceof Error ? err.message : "Unknown error"
                      })
                    }
                  };
                  // Use the helper function to send the error
                  sendDataChannelMessage(errorEvent).catch(console.error);
                  if (onError) onError(err instanceof Error ? err : new Error("Unknown error creating task"));
                });
            }

            // ... existing 'else' block for unhandled functions ...

          } catch (parseError) {
            console.error("Error parsing function arguments:", parseError);
          }
        }
        // ... other event type handlers ...
      };

      // ... rest of the hook ...

      // Add the sendDataChannelMessage helper if it doesn't exist
      const sendDataChannelMessage = async (message: any) => {
          if (!dataChannelRef.current || dataChannelRef.current.readyState !== 'open') {
              console.error('Data channel not ready for sending message:', message);
              return;
          }
          try {
              dataChannelRef.current.send(JSON.stringify(message));
          } catch (error) {
              console.error('Error sending data channel message:', error);
          }
      };


      return { /* ... existing return values ... */ };
    };
    ```

### Step 5: Add Tool to Session Configuration

*   **Purpose:** Inform OpenAI that the new tool is available for the conversation session.
*   **Implementation:**
    *   Locate the component where `useRealtimeConversationAgent` is being used (e.g., `components/fojoassistant.tsx` or similar).
    *   Import the new tool object from `lib/openai-tools.ts` (e.g., `createTaskTool`).
    *   Add the imported tool object to the `tools` array within the `sessionConfig` prop passed to the `useRealtimeConversationAgent` hook.

*   **Example:**
    ```typescript
    // e.g., in components/fojoassistant.tsx
    import { useRealtimeConversationAgent } from '@/components/conversations/RealtimeConversationAgent';
    import { openAITools } from '@/lib/openai-tools'; // Import the tools object

    // ... component setup ...

    const agentConfig = {
      // Include ALL desired tools here
      tools: [
        openAITools.updateProject,
        openAITools.searchPeopleAndTeams,
        openAITools.createTask // Add the new tool
      ],
      // ... other session config options ...
    };

    const agent = useRealtimeConversationAgent({
      sessionConfig: agentConfig,
      // ... other props ...
    });

    // ... rest of component ...
    ```

## Key Considerations

*   **Schema Strictness:** Adhere strictly to the OpenAI Realtime API schema requirements (`strict: true`, `additionalProperties: false`, `["type", "null"]` for optional fields) in `lib/openai-tools.ts`. Mismatches can cause silent failures.
*   **Argument Matching:** Ensure the `inputSchema` in `lib/openai-tools.ts` perfectly matches the `args` definition (using `v` validators) in the corresponding `convex/openai_tools/` adapter function.
*   **Adapter Pattern:** Use the adapter functions in `convex/openai_tools/` to keep AI-specific logic separate from core business logic.
*   **Naming Conventions:** Use clear, consistent naming (snake_case for tool names, descriptive names for functions).
*   **Error Handling:** Implement robust error handling in both the frontend agent (sending error messages back to OpenAI) and the backend functions.
*   **Convex Functions:** Remember to run `npx convex dev` or `npx convex deploy` after adding or modifying Convex functions.
*   **Testing:** Thoroughly test the tool invocation and execution flow.

By following these steps, you can effectively extend the capabilities of the FOJO AI agent with new tools.
