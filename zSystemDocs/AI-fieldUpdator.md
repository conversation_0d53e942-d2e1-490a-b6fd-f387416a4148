# AI-Triggered Field Updater Reference (Updated)

This reference explains how Convex features update fields using AI, triggered by database changes. It covers the workflow for different document types, including:
- **Short Description Generation:** For Knowledge Base articles, Tasks, and Decisions (triggered by content/description changes).
- **AI Notes Generation:** For Meeting Notes (triggered by transcript changes).

**Key Files Involved:**
1.  `convex/functions.ts`: Defines database triggers (for KB, Tasks, Decisions, Meeting Notes).
2.  `convex/files.ts`: Contains `updateMeetingNote` mutation (updates meeting note data), `internalUpdateNotesAndClearScheduleFlag` (clears AI notes job flag).
3.  `convex/utils/shortDescriptionUtils.ts`: Contains `scheduleFieldGeneration` helper for short descriptions.
4.  `convex/actions/orchestrators.ts`: Contains `generateAndStoreField` action (for short descriptions).
5.  `convex/actions/meetingNoteActions.ts`: Contains `updateAiNotesFromTranscript` action (generates AI notes from transcript).
6.  `convex/actions/aiUtils.ts`: Contains `generateDescriptionWithPrompt` action (core AI call).
7.  `convex/utils/fieldUtils.ts`: Contains `updateAnyField` mutation (generic field update).
8.  `convex/schema.ts`: Defines table structures, including `shortDescriptionJobId` (for KB/Tasks/Decisions via `files` table) and `aiNotesJobId`, `isAiNotesUpdateScheduled` (for Meeting Notes via `meeting_notes` table).

---

## Workflow Diagrams

### Path A: Short Description Generation (KB, Tasks, Decisions - Trigger-Based)

```mermaid
graph TD
    A[DB Change: KB/Tasks/Decisions Content/Description] --> B{Trigger Fires (functions.ts)}
    B --> C[scheduleFieldGeneration (shortDescriptionUtils.ts)]
    C --> D{Scheduler: runAfter(delay)}
    D --> E[generateAndStoreField (orchestrators.ts)]
    E --> F[generateDescriptionWithPrompt (aiUtils.ts)]
    F --> G[AI Model]
    G --> F
    F --> E
    E --> H[updateAnyField (fieldUtils.ts)]
    H --> I[DB Update: short_description field]
    E --> J[internalClearScheduledJobId (schedulerUtils.ts)]
    J --> K[DB Update: Clear shortDescriptionJobId]
```
*Note: `scheduleFieldGeneration` uses the `scheduleDebouncedJob` utility internally, which handles job cancellation and storing the `shortDescriptionJobId`.*

### Path B: AI Notes Generation (Meeting Notes - Trigger-Based with Rate Limiting)

```mermaid
graph TD
    subgraph DB Update (e.g., via updateMeetingNote)
        A[Update meeting_notes.transcript]
    end
    A --> B{Trigger Fires (functions.ts - meeting_notes)}
    B --> C{Is isAiNotesUpdateScheduled true?}
    C -- No --> D[Schedule updateAiNotesFromTranscript (scheduler.runAfter + 30s delay)]
    D --> E[Store new aiNotesJobId & Set isAiNotesUpdateScheduled=true (db.patch)]
    C -- Yes --> F[Skip Scheduling (Already Running/Scheduled)]

    subgraph Scheduled Action (30s later)
        G(Job Runs) --> H[updateAiNotesFromTranscript (meetingNoteActions.ts)]
        H --> I[Get Transcript]
        I --> J[generateDescriptionWithPrompt (aiUtils.ts)]
        J --> K[AI Model]
        K --> J
        J --> H
        H --> L[Update meeting_notes.content (via updateMeetingNote)]
        H --> M[internalUpdateNotesAndClearScheduleFlag (files.ts)]
        M --> N[DB Update: Set isAiNotesUpdateScheduled=false & Clear aiNotesJobId]
    end
    E --> G
```

---

## Step-by-Step Overview

### 1. Initiation: Database Triggers

*   **File:** `convex/functions.ts`
*   **Mechanism:** Database triggers (`triggers.register`) detect relevant changes:
    *   **KB, Tasks, Decisions:** Changes to `content` or `description` fields trigger `short_description` generation.
    *   **Meeting Notes:** Changes to the `transcript` field trigger AI notes generation.
*   **Action:**
    *   **KB, Tasks, Decisions:** Calls `scheduleFieldGeneration` helper function.
    *   **Meeting Notes:** Directly checks the `isAiNotesUpdateScheduled` flag and schedules `updateAiNotesFromTranscript` if the flag is false.

### 2. Scheduling the AI Task

*   **KB, Tasks, Decisions (Short Description):**
    *   **File:** `convex/utils/shortDescriptionUtils.ts` (using `scheduleDebouncedJob` from `convex/utils/schedulerUtils.ts`)
    *   **Function:** `scheduleFieldGeneration` uses `scheduleDebouncedJob`.
    *   **Purpose:** Schedules the `generateAndStoreField` action after `SHORT_DESCRIPTION_DEBOUNCE_DELAY`. Handles job cancellation and stores the `shortDescriptionJobId` in the relevant `files` record.
*   **Meeting Notes (AI Notes):**
    *   **File:** `convex/functions.ts` (within the `meeting_notes` trigger)
    *   **Mechanism:** Directly calls `ctx.scheduler.runAfter` to schedule `updateAiNotesFromTranscript` after a 30-second delay.
    *   **Rate Limiting:** Before scheduling, it checks the `isAiNotesUpdateScheduled` flag on the `meeting_notes` record. If `true`, it skips scheduling. If `false`, it schedules the job and immediately patches the record to set `isAiNotesUpdateScheduled` to `true` and stores the new `aiNotesJobId`.

### 3. AI Generation Action

*   **Short Description (KB, Tasks, Decisions):**
    *   **File:** `convex/actions/orchestrators.ts`
    *   **Function:** `generateAndStoreField`
    *   **Purpose:** High-level action invoked by the scheduler. Calls `generateDescriptionWithPrompt`, then `updateAnyField` to store the result in `short_description`. Finally, calls `internalClearScheduledJobId` to clear the job ID from the `files` record.
*   **AI Notes (Meeting Notes):**
    *   **File:** `convex/actions/meetingNoteActions.ts`
    *   **Function:** `updateAiNotesFromTranscript`
    *   **Purpose:** Action invoked by the scheduler. Fetches the transcript, calls `generateDescriptionWithPrompt` (using the `meeting-notes-from-transcript` prompt slug), updates the `content` field of the `meeting_notes` record (via `api.files.updateMeetingNote`), and finally calls `internalUpdateNotesAndClearScheduleFlag` to reset the rate-limiting flag and clear the job ID.
*   **Core AI Call (Both Paths):**
    *   **File:** `convex/actions/aiUtils.ts`
    *   **Function:** `generateDescriptionWithPrompt`
    *   **Purpose:** Fetches a stored AI prompt (`prompts` table) using the provided `promptSlug`, combines it with the source text, and invokes Google Gemini via the AI SDK.

### 4. Database Updates & Cleanup

*   **Generic Field Update:**
    *   **File:** `convex/utils/fieldUtils.ts`
    *   **Function:** `updateAnyField` - Used by `generateAndStoreField` to update `short_description`.
*   **Meeting Note Content Update:**
    *   **File:** `convex/actions/meetingNoteActions.ts`
    *   **Mechanism:** `updateAiNotesFromTranscript` calls `api.files.updateMeetingNote` to update the `content` field.
*   **Job ID / Flag Clearing:**
    *   **Short Description:** `generateAndStoreField` calls `internalClearScheduledJobId` (`convex/utils/schedulerUtils.ts`) to clear `shortDescriptionJobId` from the `files` record.
    *   **AI Notes:** `updateAiNotesFromTranscript` calls `internalUpdateNotesAndClearScheduleFlag` (`convex/files.ts`) to set `isAiNotesUpdateScheduled` to `false` and clear `aiNotesJobId` from the `meeting_notes` record.

---

## Key Notes (Updated)

*   The system uses database triggers (`convex/functions.ts`) for initiating AI field updates for all relevant types (KB, Tasks, Decisions, Meeting Notes).
*   **Short Description Generation** uses a debouncing mechanism (`scheduleDebouncedJob`) involving job cancellation and storing a job ID (`shortDescriptionJobId`) in the `files` table.
*   **AI Notes Generation (Meeting Notes)** uses a **rate-limiting** mechanism:
    *   A boolean flag (`isAiNotesUpdateScheduled`) prevents scheduling if a job is already pending.
    *   The flag is set immediately when a job *is* scheduled.
    *   The flag is cleared only *after* the scheduled action (`updateAiNotesFromTranscript`) successfully completes.
    *   This ensures the AI generation runs at most once per 30-second interval, using the latest transcript available when the action executes.
*   Different actions (`generateAndStoreField` vs. `updateAiNotesFromTranscript`) handle the specific logic for updating the target field (`short_description` vs. `content`) and clearing the respective job IDs/flags.
