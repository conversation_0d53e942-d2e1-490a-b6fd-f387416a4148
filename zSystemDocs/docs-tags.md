# Tagging System Deep Dive

This document provides a comprehensive analysis of the tagging system within the FOJO application, covering the database schema, backend logic, frontend implementation, and data flow.

## 1. Overview

The tagging system allows users to categorize and organize various entities (like tasks, projects, people, files) using a flexible, hierarchical structure. Key features include:

-   **Tags:** Individual labels that can be applied to entities.
-   **Tag Types:** Categories for tags (e.g., "Project", "Expense Category"), allowing for grouping and specific handling.
-   **Hierarchy:** Tags can be nested under parent tags.
-   **Taggings:** The mechanism linking tags to specific application entities.
-   **Admin UI:** A dedicated interface for managing tags and tag types.

## 2. Database Schema (`convex/schema.ts`)

Three core tables define the tagging system:

1.  **`tags` Table:**
    *   `name`: (String) The display name of the tag.
    *   `description`: (Optional String) Longer description.
    *   `tag_type`: (Optional ID linking to `tag_types`) The category of the tag. Crucial for grouping and special handling.
    *   `parent_id`: (Optional ID linking to `tags`) Enables hierarchical nesting.
    *   `color`: (Optional String) UI color hint.
    *   `immutable_slug`: (Optional String) A stable identifier for programmatic use.
    *   `updated_at`: (Number) Timestamp of the last update.
    *   *Indexes:* `by_parent`, `by_updated`, `by_name`, `by_parent_name`.

2.  **`tag_types` Table:**
    *   `name`: (String) The name of the tag category (e.g., "Project").
    *   `description`: (Optional String) Description of the type.
    *   `color`: (Optional String) Default color for tags of this type.
    *   `immutable_slug`: (Optional String) A stable identifier used to recognize specific, predefined types (e.g., "expense_category", "general-tags"). **This is key for special handling.**
    *   `updated_at`: (Number) Timestamp of the last update.
    *   *Indexes:* `by_name`, `by_updated`.

3.  **`taggings` Table:**
    *   `tag_id`: (ID linking to `tags`) The tag being applied.
    *   `taggable_type`: (String Union, e.g., "person", "project", "file") The type of entity being tagged.
    *   `taggable_id`: (ID Union, e.g., `Id<"people">`, `Id<"projects">`) The specific ID of the entity being tagged.
    *   `assigned_at`: (Number) Timestamp when the tag was linked.
    *   *Indexes:* `by_tag`, `by_taggable` (composite on `taggable_type`, `taggable_id`).

## 3. Zod Schemas (`zod/tags-schema.ts`, `zod/tagTypes-schema.ts`)

These files provide data validation and TypeScript types:

*   **Type Safety:** Use `zid("tags")` and `zid("tag_types")` for strong typing of ID references.
*   **Base & Full Schemas:** Define `TagBaseSchema`/`TagTypeBaseSchema` (user fields) and `TagSchema`/`TagTypeSchema` (including Convex system fields `_id`, `_creationTime`).
*   **Unified Operations:** Schemas like `SaveTagArgsSchema`, `TagFetchFilterSchema`, `SaveTagTypeSchema` consolidate arguments for create/update and get/list operations.
*   **Hierarchy:** `TagWithChildrenSchema` uses `z.lazy` to define the recursive structure for representing tag trees. `TagHierarchyResponseSchema` defines the output for hierarchy queries (single tree or array of trees).
*   **Taggings:** `TaggingSchema`, `TaggableTypeSchema`, and `TaggableIdSchema` define the structure and allowed types/IDs for linking tags to other entities.

## 4. Backend Logic (`convex/tags.ts`)

This module centralizes all backend operations for tags, types, and taggings.

**Key Functions & Logic:**

*   **`saveTag` (Mutation):**
    *   Handles both creation and updates.
    *   **Type Inheritance:** If `parent_id` is set, it *forces* the child's `tag_type` to match the parent's.
    *   **Hierarchy Update:** If a tag's `tag_type` changes, `updateChildrenTypes` is called to recursively update all descendants.
    *   **Validation:** Checks for empty names, circular references (`hasCircularReference`), and duplicate names under the same parent (`checkDuplicateNameAtParent`).
*   **`removeTag` (Mutation):**
    *   Calls `deleteRecursively` to delete the tag, all its descendants, and any `taggings` associated with them.
*   **`fetchTags` (Query):**
    *   Retrieves tags based on filters (`id`, `tag_type`, `parentId`, `searchText`).
    *   Calls `enrichTagsWithSlug` to add the `immutable_slug` from the related `tag_types` record to the results.
*   **`getHierarchy` (Query):**
    *   Builds the nested `TagWithChildren` structure using `buildHierarchy` and `getChildren`.
    *   Can filter the entire hierarchy by `tag_type`.
    *   Also calls `enrichTagsWithSlug`.
*   **`getStats` (Query):**
    *   Aggregates statistics like total tags, root tags, sub-tags, and counts by type.
*   **`listTagTypes` (Query):**
    *   Lists tag types with pagination and search.
*   **`getTagType` (Query):**
    *   Retrieves a single tag type by its `_id`.
*   **`getTagTypeBySlug` (Query):**
    *   **Crucial for special handling:** Retrieves a tag type using its unique `immutable_slug`.
*   **`createTagType`, `updateTagType` (Mutations):**
    *   Standard CRUD operations for tag types, ensuring name uniqueness.
*   **`deleteTagType` (Mutation):**
    *   Deletes a tag type *only if* no tags currently use it.
*   **`seedDefaultTagTypes` (Mutation):**
    *   Ensures predefined tag types ("Expense Category", "Project", "Department", "Campaign", "General Tags") exist by checking/creating/updating based on their `immutable_slug`.
*   **Tagging Functions (`getTagsForTaggable`, `addTagToTaggable`, `removeTagFromTaggable`, `setTagsForTaggable`):**
    *   Manage the links between tags and other application entities stored in the `taggings` table.

**Helper Functions:**

*   `hasCircularReference`: Prevents a tag from being its own ancestor.
*   `checkDuplicateNameAtParent`: Ensures unique tag names within the same level of the hierarchy.
*   `deleteRecursively`: Handles cascading deletes for tags and taggings.
*   `updateChildrenTypes`: Propagates tag type changes down the hierarchy.
*   `enrichTagsWithSlug`: Adds the `immutable_slug` from the `tag_types` table to tag objects.

## 5. Frontend Implementation (`app/(dashboard)/admin/tags/page.tsx`)

This React component provides the user interface for managing tags and types.

**Key Aspects:**

*   **Data Fetching:** Uses `useQuery` to fetch tag types (`api.tags.listTagTypes`), a flat list of tags (`api.tags.fetchTags` based on filters), and the hierarchical tag structure (`api.tags.getHierarchy` based on selected type).
*   **Tag Type Selection:** A `<Select>` dropdown allows users to filter the view by tag type. It uses the fetched `tagTypes` state.
*   **View Modes:** Supports 'card' and 'list' views (`viewMode` state).
*   **Hierarchical vs. Flat Display:**
    *   The `isHierarchicalTagType` function checks if the selected type's name is *not* 'tags'.
    *   If hierarchical, it uses the `HierarchicalTagView` component, which renders nested structures (columns in card mode, expandable rows in list mode) based on the data from `api.tags.getHierarchy`.
    *   If *not* hierarchical (specifically "General tags"), it renders a flat grid or list of tags belonging to that type, using data primarily from `api.tags.fetchTags`.
*   **Special Handling ("General tags"):** Explicitly checks for the name 'tags' (case-insensitive) to apply the flat view logic and display the name as "General tags".
*   **CRUD Operations:**
    *   **Tags:** Uses `CreateTagModal` for adding/editing tags, calling `api.tags.saveTag`. Delete buttons call `api.tags.removeTag`. Bulk delete is available in list view.
    *   **Tag Types:** Allows creation via a form (`handleCreateTagType` -> `api.tags.createTagType`). Editing and deletion are handled via buttons in the type selector dropdown (`handleUpdateTagType` -> `api.tags.updateTagType`, `handleDeleteTagType` -> `api.tags.deleteTagType`).
*   **UI State:** Manages selected type, view mode, search, filters, expanded nodes, selected tags, and editing state using `useState`.
*   **Search:** Debounced search input updates `filterState.search`, refetching the *flat* list (`api.tags.fetchTags`). It does *not* filter the hierarchy view from `api.tags.getHierarchy`.
*   **Visual Cues:** Uses tag/type colors for borders or dots.

## 6. Data Flow Examples

**A. Creating a New Tag:**

1.  User selects a Tag Type (e.g., "Project") from the dropdown.
2.  User clicks the "New Tag" button.
3.  `CreateTagModal` opens, potentially pre-filled with the selected `tag_type`.
4.  User enters the tag name (e.g., "Project Phoenix") and other details.
5.  User clicks "Save".
6.  Modal calls `createTag` mutation (`api.tags.saveTag`) with the new tag data.
7.  Backend `saveTag` function:
    *   Validates the name.
    *   Checks for duplicates under the same parent (if any).
    *   Inserts the new tag into the `tags` table with the specified `tag_type`.
8.  Convex reactivity updates `useQuery` results (`api.tags.fetchTags`, `api.tags.getHierarchy`).
9.  Frontend UI re-renders, showing the new tag in the appropriate list/hierarchy.
10. Modal closes.

**B. Fetching and Displaying Hierarchy:**

1.  User selects a hierarchical Tag Type (e.g., "Department") from the dropdown.
2.  `selectedTagTypeValue` state updates.
3.  `useEffect` triggers, updating `filterState.tagType`.
4.  `useQuery(api.tags.getHierarchy, { params: { tag_type: selectedTypeId } })` re-fetches data.
5.  Backend `getHierarchy` function:
    *   Calls `buildHierarchy` with the `tagTypeFilter`.
    *   Recursively fetches root tags and their children matching the type using `getChildren`.
    *   Calls `enrichTagsWithSlug` to add slugs to all fetched tags.
    *   Returns the nested `TagWithChildren[]` structure.
6.  Frontend receives the hierarchical data (`apiTagHierarchy`).
7.  `isHierarchicalTagType` returns `true`.
8.  `HierarchicalTagView` component renders the data, either as columns/cards or an expandable table, based on `viewMode`.

**C. Applying a Tag to a Task:**

1.  (In a different UI, e.g., Task Detail Page) User selects a tag (e.g., "Urgent") to apply to a task.
2.  UI component calls `addTagToTaggable` mutation (`api.tags.addTagToTaggable`) with:
    *   `tagId`: ID of the "Urgent" tag.
    *   `taggable_type`: "task".
    *   `taggable_id`: ID of the specific task.
3.  Backend `addTagToTaggable` function:
    *   Checks if the tagging already exists in the `taggings` table.
    *   If not, inserts a new record into `taggings` linking the tag and the task.
4.  (In Task Detail Page) `useQuery(api.tags.getTagsForTaggable, { taggable_type: "task", taggable_id: taskId })` updates, showing the newly applied tag.

## 7. Special Tag Type Handling

*   **Identification:** The primary mechanism for identifying special types is the `immutable_slug` field in the `tag_types` table (e.g., "general-tags", "expense_category").
*   **Backend:**
    *   `getTagTypeBySlug` allows fetching types directly by their slug.
    *   `seedDefaultTagTypes` uses slugs to ensure standard types exist.
    *   The core logic (`saveTag`, `fetchTags`, etc.) primarily uses the `tag_type` ID for linking and filtering, but the slug is available via `enrichTagsWithSlug` if needed for specific backend rules (though none are apparent in `convex/tags.ts` itself beyond seeding).
*   **Frontend (`app/(dashboard)/admin/tags/page.tsx`):**
    *   The `isHierarchicalTagType` function specifically checks if the *name* is 'tags' (case-insensitive) to apply flat list rendering. This relies on the *name* rather than the *slug* for this specific UI behavior.
    *   The type selector dropdown sorts types, placing "General tags" first and "Expense Category" last, indicating awareness of these specific types.
    *   Other parts of the application could potentially use `getTagTypeBySlug` or filter tags based on the `immutable_slug` (added by `enrichTagsWithSlug`) to apply type-specific logic or UI elements elsewhere.

This concludes the deep dive into the tagging system.
