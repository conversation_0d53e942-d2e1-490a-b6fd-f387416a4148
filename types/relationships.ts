import { Doc, Id } from "@/convex/_generated/dataModel";
import { api } from "@/convex/_generated/api";
import { FunctionReturnType } from "convex/server";

// The shape of the data returned by the listRelationshipTypesForClient query
export type RelationshipTypeForClient = FunctionReturnType<typeof api.clients.relationshipQueries.listRelationshipTypesForClient>[number];

// Props for the main modal component
export interface AddRelationshipModalProps {
  clientId: Id<"clients">;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // Optional callback when relationship is created successfully
}

// Form state for the modal
export interface RelationshipFormState {
  selectedTypeId: Id<"relationship_types"> | null;
  selectedTargetType: "person" | "organization" | "client" | null;
  selectedTargetId: Id<"people"> | Id<"organizations"> | Id<"clients"> | null;
  isActive: boolean;
  startDate: number | undefined;
  notes: string;
  customFields: Record<string, any>;
}

// Entity search result type
export interface EntitySearchResult {
  _id: Id<"people"> | Id<"organizations"> | Id<"clients">;
  name: string;
  type: "person" | "organization" | "client";
  description?: string;
  imageUrl?: string;
}

// Step definitions for the modal workflow
export type ModalStep = "selectType" | "selectTarget" | "addDetails";

// Target type options based on relationship type
export interface TargetTypeOption {
  value: "person" | "organization" | "client";
  label: string;
  description: string;
}
