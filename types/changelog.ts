/**
 * Types for the changelog data structure
 */

// Style for changelog item images
export type ChangelogItemImageStyle = {
  maxWidth: string;
  height: string;
};

// Individual changelog item
export type ChangelogItem = {
  headline: string;
  items: string[];
  image?: string;
  imageStyle?: ChangelogItemImageStyle;
  link?: string;
};

// Changelog entry containing date and items
export type ChangelogEntry = {
  date: string;
  entries: ChangelogItem[];
};
