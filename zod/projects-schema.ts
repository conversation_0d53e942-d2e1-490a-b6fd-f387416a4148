import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Define common enums
export const ProjectStatusEnum = z.enum([
  'in_progress',
  'perpetual',
  'not_started',
  'completed',
  'paused',
  'cancelled'
]);

export const ProjectPriorityEnum = z.enum(['low', 'medium', 'high', 'urgent']);

// Base schema for a project (common fields)
export const ProjectBaseSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(), // Added short_description
  status: ProjectStatusEnum.optional(),
  owner_id: zid('users').optional(),
  priority: ProjectPriorityEnum.optional(),
  updated_at: z.number().optional(),
  // DCI Fields
  driver: zid('users').optional(),
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
  informed: z.array(z.union([zid('users'), zid('teams')])).optional()
});

// Complete project schema including Convex-specific fields
export const ProjectSchema = ProjectBaseSchema.extend({
  _id: zid('projects'),
  _creationTime: z.number(),
});

// For project creation (single)
export const SingleProjectInput = ProjectBaseSchema;

// For bulk project creation
export const MultipleProjectsInput = z.array(SingleProjectInput);

// For project updates (partial fields)
export const ProjectUpdateSchema = ProjectBaseSchema.partial();

// For bulk project updates
export const BulkProjectUpdateSchema = z.object({
  id: zid('projects').optional(),
  ids: z.array(zid('projects')).optional(),
  updates: ProjectUpdateSchema
});

// For listing projects
export const ListProjectsArgs = z.object({
  filter: z
    .object({
      name: z.string().optional(),
      status: ProjectStatusEnum.optional(),
      owner_id: zid('users').optional(),
      priority: ProjectPriorityEnum.optional(),
      tag_ids: z.array(zid('tags')).optional() // Support multiple tag IDs for filtering
    })
    .optional()
    .default({}),
  sortBy: z
    .enum(['_creationTime', 'name', 'updated_at'])
    .optional()
    .default('_creationTime'),
  sortDirection: z.enum(['asc', 'desc']).optional().default('desc'),
  paginationOpts: z
    .object({
      numItems: z.number().optional().default(10),
      cursor: z.any().optional()
    })
    .optional()
    .default({})
});

// For updating projects
export const UpdateProjectsArgs = BulkProjectUpdateSchema;

// For deleting projects
export const DeleteProjectsArgs = z.object({
  projectIds: z.array(zid('projects'))
});

// Response type for paginated results
export const PaginatedProjectsResponse = z.object({
  page: z.array(ProjectSchema),
  isDone: z.boolean(),
  continueCursor: z.any().nullable()
});

export type PaginatedProjects = z.infer<typeof PaginatedProjectsResponse>;

// Export inferred types
export type ProjectStatus = z.infer<typeof ProjectStatusEnum>;
export type ProjectPriority = z.infer<typeof ProjectPriorityEnum>;
export type Project = z.infer<typeof ProjectSchema>;
export type ProjectBase = z.infer<typeof ProjectBaseSchema>;
export type ProjectUpdate = z.infer<typeof ProjectUpdateSchema>;
export type BulkProjectUpdate = z.infer<typeof BulkProjectUpdateSchema>;
export type ListProjectsInput = z.infer<typeof ListProjectsArgs>;
export type UpdateProjectsInput = z.infer<typeof UpdateProjectsArgs>;
export type DeleteProjectsInput = z.infer<typeof DeleteProjectsArgs>;


// ==========================================
// Schemas for OpenAI Tool Integration
// ==========================================

// Schema for the nested updates object within the tool input
// Corresponds to the 'updates.updates' part of the openai_update_project.json schema
export const OpenAIToolProjectUpdateSchema = z.object({
  name: z.string().optional().describe('The new name for the project.'),
  description: z.string().optional().describe('The new description for the project.'),
  short_description: z.string().optional().describe('A new short description for the project.'),
  status: ProjectStatusEnum.optional().describe('The new status for the project.'),
  priority: ProjectPriorityEnum.optional().describe('The new priority for the project.'),
  owner_id: zid('users').optional().describe('The Convex ID (_id) of the new owner (user ID).')
}).describe('An object containing the project fields to update. Include only the fields you want to change.');

// Schema matching the exact structure of openai_update_project.json inputSchema
// Corresponds to the top-level 'updates' object in the openai_update_project.json schema
export const OpenAIUpdateProjectInputSchema = z.object({
    updates: z.object({ // Matches the top-level 'updates' property in the JSON
        id: zid('projects').describe("The Convex ID (_id) of the project to update."),
        updates: OpenAIToolProjectUpdateSchema // Matches the nested 'updates' object in the JSON
    }).describe("Specifies the project ID and the fields to update.")
});


// Schema for manual timeline items used in ManualUpdateDialog
export const ManualTimelineItemSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  status: z.string(),
  tags: z.array(z.string()),
  hasRisk: z.boolean(),
  type: z.string(),
  updateDetails: z.string(),
  attachments: z.array(
    z.object({
      name: z.string(),
      url: z.string(),
      type: z.string()
    })
  ).optional()
});

export type ManualTimelineItem = z.infer<typeof ManualTimelineItemSchema>;
