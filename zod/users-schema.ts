import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

/**
 * Base schema for user fields
 * Contains all the fields that can be set when creating or updating a user
 */
export const UserBaseSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.optional(z.string()),
  image: z.optional(z.string().url('Invalid image URL')),
  emailVerificationTime: z.optional(z.number()),
  roles: z.optional(z.array(zid('roles'))),
  teams: z.optional(z.array(zid('teams'))),
  tokenIdentifier: z.optional(z.string()),
  last_active: z.optional(z.number()),
  phoneVerificationTime: z.optional(z.number()),
  // Additional user details fields
  title: z.optional(z.string()),
  department: z.optional(z.string()),
  phone: z.optional(z.string()),
  location: z.optional(z.string()),
  timezone: z.optional(z.string()),
  bio: z.optional(z.string()),
  preferences: z.optional(z.object({
    communicationPreferences: z.optional(z.array(z.string())),
    workingHours: z.optional(z.object({
      start: z.string(),
      end: z.string()
    })),
    notificationSettings: z.optional(z.object({
      email: z.boolean(),
      push: z.boolean(),
      sms: z.boolean()
    }))
  }))
});

/**
 * Complete user schema including system fields
 * This represents a user document as stored in the database
 */
export const UserSchema = UserBaseSchema.extend({
  _id: zid('users'),
  _creationTime: z.number()
});

/**
 * Schema for creating a new user
 * Email is the only required field
 */
export const CreateUserSchema = UserBaseSchema;

/**
 * Schema for updating an existing user
 * All fields are optional since we only update what's provided
 */
export const UpdateUserSchema = UserBaseSchema.partial();

/**
 * Schema for user input with ID
 * Used for operations that require a user ID
 */
export const UserWithIdSchema = z.object({
  userId: zid('users')
});

/**
 * Schema for filtering users
 * Used for search and list operations
 */
export const UserFilterSchema = z.object({
  email: z.string().optional(),
  name: z.string().optional(),
  tokenIdentifier: z.string().optional()
});

/**
 * Schema for pagination parameters
 * Used for list operations
 */
export const UserPaginationSchema = z.object({
  cursor: z.any().optional(),
  limit: z.number().min(1).max(100).default(10),
  sortDirection: z.enum(['asc', 'desc']).default('desc')
});

/**
 * Schema representing the combined data from users and people tables
 * Used for admin views that need linked user and person details.
 */
export const UserWithPersonSchema = z.object({
  userId: zid('users'),
  personId: zid('people'),
  name: z.string(),
  email: z.string().email(),
  title: z.string().optional(),
  image: z.string().url().optional(),
  teams: z.array(zid('teams')).default([]), // Default to empty array if missing
});

/**
 * Type definitions derived from Zod schemas
 */
export type User = z.infer<typeof UserSchema>;
export type CreateUserInput = z.infer<typeof CreateUserSchema>;
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>;
export type UserWithId = z.infer<typeof UserWithIdSchema>;
export type UserFilter = z.infer<typeof UserFilterSchema>;
export type UserPagination = z.infer<typeof UserPaginationSchema>;
export type UserWithPerson = z.infer<typeof UserWithPersonSchema>;
