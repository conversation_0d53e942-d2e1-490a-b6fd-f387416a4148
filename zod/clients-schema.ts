import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { TagSchema } from './tags-schema';

// ========================================
// SHARED ENUMS AND TYPES
// ========================================

// Client type enum - whether the client is a person or organization
export const ClientTypeSchema = z.enum(['person', 'organization']);
export type ClientType = z.infer<typeof ClientTypeSchema>;

// Client status enum
export const ClientStatusSchema = z.enum(['active', 'on_hold', 'prospective', 'former']);
export type ClientStatus = z.infer<typeof ClientStatusSchema>;

// Client tier system enum
export const ClientTierSchema = z.enum(['platinum', 'gold', 'silver', 'bronze', 'none']);
export type ClientTier = z.infer<typeof ClientTierSchema>;

// Communication frequency enum
export const CommunicationFrequencySchema = z.enum([
  'weekly',
  'monthly', 
  'quarterly',
  'twice_yearly',
  'annually',
  'as_needed'
]);
export type CommunicationFrequency = z.infer<typeof CommunicationFrequencySchema>;

// ========================================
// CLIENT SCHEMAS
// ========================================

// Base Client Schema - fields required for creation
export const BaseClientSchema = z.object({
  // Core identification
  client_name: z.string().min(1, 'Client name is required'),
  client_type: ClientTypeSchema,
  
  // Status and tier
  client_status: ClientStatusSchema,
  client_tier: ClientTierSchema,
  
  // Communication preferences
  client_communication_frequency: CommunicationFrequencySchema,
  
  // Optional fields
  client_description: z.string().optional(),
  client_short_description: z.string().optional(),
  client_research: z.string().optional(),
  client_badges: z.array(zid('badges')).optional(),
  
  // Financial data
  client_net_worth: z.number().min(0).optional(),
  
  // Key dates (timestamps)
  client_since: z.number().optional(),
  client_last_contact: z.number().optional(),
  
  // Metadata
  updated_at: z.number()
});

// Input schema for creating a client
export const ClientInputSchema = BaseClientSchema.omit({ updated_at: true });
export type ClientInput = z.infer<typeof ClientInputSchema>;

// Complete Client schema including system fields
export const ClientSchema = BaseClientSchema.extend({
  _id: zid('clients'),
  _creationTime: z.number()
});
export type Client = z.infer<typeof ClientSchema>;

// Update schema - all fields optional except ID
export const ClientUpdateSchema = BaseClientSchema.partial();
export type ClientUpdate = z.infer<typeof ClientUpdateSchema>;

// Schema for bulk client operations (simplified to avoid ZodEffects)
export const BulkClientOperationSchema = z.object({
  id: zid('clients').optional(),
  ids: z.array(zid('clients')).optional(),
  updates: ClientUpdateSchema
});
export type BulkClientOperation = z.infer<typeof BulkClientOperationSchema>;

// Alternative schemas for more specific bulk operations
export const BulkClientOperationSingleSchema = z.object({
  id: zid('clients'),
  updates: ClientUpdateSchema
});

export const BulkClientOperationMultipleSchema = z.object({
  ids: z.array(zid('clients')),
  updates: ClientUpdateSchema
});

// ========================================
// CLIENT ASSIGNMENT SCHEMAS
// ========================================

// Base assignment schema
export const BaseClientAssignmentSchema = z.object({
  client_id: zid('clients'),
  user_id: zid('users'),
  updated_at: z.number()
});

// Input schema for creating an assignment
export const ClientAssignmentInputSchema = BaseClientAssignmentSchema.omit({ updated_at: true });
export type ClientAssignmentInput = z.infer<typeof ClientAssignmentInputSchema>;

// Complete assignment schema with system fields
export const ClientAssignmentSchema = BaseClientAssignmentSchema.extend({
  _id: zid('client_assignments'),
  _creationTime: z.number()
});
export type ClientAssignment = z.infer<typeof ClientAssignmentSchema>;

// Update schema for assignments
export const ClientAssignmentUpdateSchema = BaseClientAssignmentSchema
  .omit({ client_id: true, user_id: true })
  .partial();
export type ClientAssignmentUpdate = z.infer<typeof ClientAssignmentUpdateSchema>;

// ========================================
// QUERY AND FILTER SCHEMAS
// ========================================

// Client filter schema
export const ClientFilterSchema = z.object({
  client_status: ClientStatusSchema.optional(),
  client_tier: ClientTierSchema.optional(),
  client_type: ClientTypeSchema.optional(),
  client_communication_frequency: CommunicationFrequencySchema.optional(),
  
  // Date range filters
  client_since_start: z.number().optional(),
  client_since_end: z.number().optional(),
  
  // Search
  search_text: z.string().optional(),
  
  // Net worth range
  min_net_worth: z.number().optional(),
  max_net_worth: z.number().optional(),
  
  // Activity filters
  days_since_last_contact: z.number().optional()
});
export type ClientFilter = z.infer<typeof ClientFilterSchema>;

// Assignment filter schema
export const ClientAssignmentFilterSchema = z.object({
  client_id: zid('clients').optional(),
  user_id: zid('users').optional(),
});
export type ClientAssignmentFilter = z.infer<typeof ClientAssignmentFilterSchema>;

// Pagination schema (reusable)
export const PaginationSchema = z.object({
  limit: z.number().min(1).max(100).default(50),
  cursor: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc')
});
export type Pagination = z.infer<typeof PaginationSchema>;

// Client query schema combining filters and pagination
export const ClientQuerySchema = z.object({
  filter: ClientFilterSchema.optional(),
  pagination: PaginationSchema.optional()
});
export type ClientQuery = z.infer<typeof ClientQuerySchema>;

// ========================================
// RESPONSE SCHEMAS
// ========================================

// Paginated response schema
export const PaginatedClientResponseSchema = z.object({
  page: z.array(ClientSchema),
  continuationToken: z.string().optional(),
  isDone: z.boolean()
});
export type PaginatedClientResponse = z.infer<typeof PaginatedClientResponseSchema>;

// Client with assignments (joined data)
export const ClientWithAssignmentsSchema = ClientSchema.extend({
  assignments: z.array(z.object({
    assignment: ClientAssignmentSchema,
    user: z.object({
      _id: zid('users'),
      name: z.string().optional(),
      email: z.string()
    })
  }))
});
export type ClientWithAssignments = z.infer<typeof ClientWithAssignmentsSchema>;

// ========================================
// BULK OPERATION SCHEMAS
// ========================================

// Bulk assignment creation
export const BulkAssignmentCreateSchema = z.object({
  client_id: zid('clients'),
  assignments: z.array(z.object({
    user_id: zid('users'),
  }))
});
export type BulkAssignmentCreate = z.infer<typeof BulkAssignmentCreateSchema>;

// Bulk assignment update
export const BulkAssignmentUpdateSchema = z.object({
  updates: z.array(z.object({
    assignment_id: zid('client_assignments'),
  }))
});
export type BulkAssignmentUpdate = z.infer<typeof BulkAssignmentUpdateSchema>;

// ========================================
// STATS AND ANALYTICS SCHEMAS
// ========================================

// Client statistics schema
export const ClientStatsSchema = z.object({
  total_clients: z.number(),
  active_clients: z.number(),
  prospective_clients: z.number(),
  clients_by_tier: z.record(z.string(), z.number()),
  clients_by_status: z.record(z.string(), z.number()),
  total_net_worth: z.number(),
  average_net_worth: z.number()
});
export type ClientStats = z.infer<typeof ClientStatsSchema>;

// Staff workload schema
export const StaffWorkloadSchema = z.object({
  user_id: zid('users'),
  user_name: z.string(),
  primary_clients: z.number(),
  secondary_clients: z.number(),
  total_clients: z.number(),
  clients_by_tier: z.record(z.string(), z.number())
});
export type StaffWorkload = z.infer<typeof StaffWorkloadSchema>;

// ========================================
// VALIDATION HELPERS
// ========================================

// Helper to validate net worth ranges
export const validateNetWorthRange = (min?: number, max?: number): boolean => {
  if (min === undefined || max === undefined) return true;
  return min <= max;
};

// Helper to get display name for client tier
export const getClientTierDisplayName = (tier: ClientTier): string => {
  const displayNames: Record<ClientTier, string> = {
    platinum: 'Platinum',
    gold: 'Gold',
    silver: 'Silver',
    bronze: 'Bronze',
    none: 'Standard'
  };
  return displayNames[tier];
};

// Helper to get display name for client status
export const getClientStatusDisplayName = (status: ClientStatus): string => {
  const displayNames: Record<ClientStatus, string> = {
    active: 'Active',
    on_hold: 'On Hold',
    prospective: 'Prospective',
    former: 'Former'
  };
  return displayNames[status];
};

// ========================================
// ========================================
// CLIENT DETAILS PAGE VIEW MODEL
// ========================================

// This schema defines the complete data structure for the client details page.
// It's designed to be fetched in a single, unified query.

const ClientTeamMemberSchema = z.object({
  assignmentId: zid('client_assignments'),
  is_primary_assignment: z.boolean(),
  userId: zid('users'),
  name: z.string().optional(),
  email: z.string(),
  image: z.string().optional(),
  title: z.string().optional(),
});

const ClientRelationshipSchema = z.object({
  relationshipId: zid('relationships'),
  relation: z.object({
    _id: z.string(),
    name: z.string(),
    type: z.string(), // e.g., 'person', 'organization'
    description: z.string().optional(),
  }),
  relationshipType: z.string(),
  relationshipCategory: z.string().optional(), // e.g., 'Family', 'Professional'
});

const ClientActiveItemSchema = z.object({
  _id: z.string(),
  type: z.enum(['task', 'decision', 'project']),
  name: z.string(),
  status: z.string().optional(),
  due_date: z.number().optional(),
});

export const ClientDetailsPageDataSchema = z.object({
  // Core client data
  client: z.object({
    _id: zid('clients'),
    _creationTime: z.number(),
    client_name: z.string(),
    client_type: ClientTypeSchema,
    client_status: ClientStatusSchema,
    client_tier: ClientTierSchema,
    client_communication_frequency: CommunicationFrequencySchema,
    client_description: z.string().optional(),
    client_short_description: z.string().optional(),
    client_net_worth: z.number().optional(),
    client_since: z.number().optional(),
    client_last_contact: z.number().optional(),
    // Assuming contact info is stored directly or needs to be joined
    primary_contact_email: z.string().email().optional(),
    primary_contact_phone: z.string().optional(),
    office_address: z.string().optional(),
  }),

  // Team members assigned to the client
  teamMembers: z.array(ClientTeamMemberSchema),

  // Relationships (Family & Professional Providers)
  relationships: z.array(ClientRelationshipSchema),

  // Active items (tasks, decisions, etc.)
  activeItems: z.array(ClientActiveItemSchema),

  // Principal Contact (if applicable)
  principalContact: ClientRelationshipSchema.optional(),

  // Tags associated with the client
  tags: z.array(TagSchema),
});

export type ClientDetailsPageData = z.infer<typeof ClientDetailsPageDataSchema>;


// ========================================
// EXPORT COLLECTIONS
// ========================================

// Export all client-related schemas
export const ClientSchemas = {
  // Core schemas
  Client: ClientSchema,
  ClientInput: ClientInputSchema,
  ClientUpdate: ClientUpdateSchema,
  
  // Assignment schemas
  ClientAssignment: ClientAssignmentSchema,
  ClientAssignmentInput: ClientAssignmentInputSchema,
  ClientAssignmentUpdate: ClientAssignmentUpdateSchema,
  
  // Filter schemas
  ClientFilter: ClientFilterSchema,
  ClientAssignmentFilter: ClientAssignmentFilterSchema,
  
  // Query schemas
  ClientQuery: ClientQuerySchema,
  Pagination: PaginationSchema,
  
  // Response schemas
  PaginatedClientResponse: PaginatedClientResponseSchema,
  ClientWithAssignments: ClientWithAssignmentsSchema,
  
  // Bulk operation schemas
  BulkClientOperation: BulkClientOperationSchema,
  BulkAssignmentCreate: BulkAssignmentCreateSchema,
  BulkAssignmentUpdate: BulkAssignmentUpdateSchema,
  
  // Stats schemas
  ClientStats: ClientStatsSchema,
  StaffWorkload: StaffWorkloadSchema
} as const;

// Export all types
export type ClientSchemaTypes = {
  [K in keyof typeof ClientSchemas]: z.infer<typeof ClientSchemas[K]>
};
