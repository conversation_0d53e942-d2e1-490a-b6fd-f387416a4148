/**
 * tags-schema.ts
 *
 * Zod schemas & types for the "tags" domain.
 * - Uses `zid("tags")` to strongly type references to the `tags` table in Convex.
 * - The new `SaveTagArgsSchema` unifies create & update.
 * - The new `TagFetchFilterSchema` unifies single-tag get & multi-tag list/filter.
 */

import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { Id } from "../convex/_generated/dataModel";
import { TagTypeEnum } from "./tagTypes-schema";

/* --------------------------------------------------------------------------------
   1. Base Tag Schema (User-Defined Fields) + Complete Tag schema definition
// Updated to ensure all fields necessary for UI display are present
// name: string - The tag's display name (required)
// color: string - The tag's color (optional, e.g., HEX or CSS color)
// icon: string - Optional icon name for visual distinction
// description: string - Optional description for tooltips
   System Fields)
---------------------------------------------------------------------------------- */

/**
 * Base set of user-defined fields for a Tag (no `_id` or `_creationTime`).
 * Matches the fields in `schema.ts` for the `tags` table, except for Convex system fields.
 */
export const TagBaseSchema = z.object({
  /** Short display name of the tag. */
  name: z.string().min(1, "Tag name is required"),

  /** Optional longer description. */
  description: z.string().optional(),

  /** Type/category of the tag (reference to a tag_type document). */
  tag_type: zid("tag_types"),

  /** Parent tag reference if it is a child tag. */
  parent_id: zid("tags").optional(),

  /** Last update timestamp. Usually set by the server at time of update. */
  updated_at: z.number().optional(),

  /** Optional color used in UI highlights. */
  color: z.string().optional(),

  /** A non-changing slug for programmatic reference. */
  immutable_slug: z.string().optional()
});

/**
 * Complete shape of a Tag document **as stored** in the `tags` table,
 * extending the base schema with Convex's system fields.
 */
export const TagSchema = TagBaseSchema.extend({
  /** Unique Convex document ID for this tag. */
  _id: zid("tags"),

  /** Creation time as assigned by Convex (milliseconds since Unix epoch). */
  _creationTime: z.number()
});

/* --------------------------------------------------------------------------------
   2. Unified Save Args (Replaces Create/Update)
---------------------------------------------------------------------------------- */

/**
 * **SaveTagArgsSchema**
 * Used for both creating and updating a Tag.
 * - If `id` is **omitted**, create a new tag.
 * - If `id` is **provided**, update the existing tag with that ID.
 */
export const SaveTagArgsSchema = z.object({
  /** ID of the tag to update (omit or `undefined` to create). */
  id: zid("tags").optional(),

  /** Tag name (required for create; optional for update, but min(1) if present). */
  name: z.string().min(1, "Tag name cannot be empty"),

  /** Optional description. */
  description: z.string().optional(),

  /** Tag type. Enforced at creation, may be overridden based on parent. */
  tag_type: zid("tag_types"),

  /** Parent ID or `null` to remove parent, or leave undefined if no change. */
  parent_id: zid("tags").nullable().optional(),

  /** Optional color. */
  color: z.string().optional(),

  /** A non-changing slug for programmatic reference. */
  immutable_slug: z.string().optional()
});

/* --------------------------------------------------------------------------------
   3. Fetch Filter (Unifies Single Get & Multi-List)
---------------------------------------------------------------------------------- */

/**
 * **TagFetchFilterSchema**
 * - If `id` is present, fetch exactly one Tag with that ID.
 * - Otherwise, optionally filter by `tag_type`, `parentId`, or `searchText`.
 */
export const TagFetchFilterSchema = z.object({
  id: zid("tags").optional(),
  tag_type: zid("tag_types").optional(),
  parentId: zid("tags").optional(),
  searchText: z.string().optional()
});

/* --------------------------------------------------------------------------------
   4. Remove / Delete Schema
---------------------------------------------------------------------------------- */

/**
 * **RemoveTagArgsSchema**
 * Arguments for removing a Tag.
 */
export const RemoveTagArgsSchema = z.object({
  id: zid("tags")
});

/* --------------------------------------------------------------------------------
   5. Tag Hierarchy (Self-Referential)
---------------------------------------------------------------------------------- */

/**
 * **TagWithChildrenInterface**  
 * Combines the complete Tag object with an array of nested children.
 * Declared as an interface so TypeScript sees `children` as recursively the same shape.
 */
export interface TagWithChildrenInterface extends z.infer<typeof TagSchema> {
  children: TagWithChildrenInterface[];
  _id: Id<"tags">; // Ensures ID is typed to the "tags" table
  tag_type: Id<"tag_types">; // Add tag_type explicitly
}

/**
 * **TagWithChildrenSchema**  
 * Zod schema that includes the recursive `children` property. 
 * We use `z.lazy` to reference `TagWithChildrenSchema` inside itself.
 */
export const TagWithChildrenSchema = TagSchema.extend({
  children: z.array(z.lazy(() => TagWithChildrenSchema)).default([])
}) as z.ZodType<TagWithChildrenInterface>;

/**
 * **GetHierarchyArgsSchema**
 * Args to retrieve a hierarchy of tags.
 * - `id`: Optional parent tag ID. If omitted, might fetch top-level.
 * - `tag_type`: Optional filter by tag type.
 */
export const GetHierarchyArgsSchema = z.object({
  id: zid("tags").optional(),
  tag_type: zid("tag_types").optional()
});

/**
 * **TagHierarchyResponseSchema**
 * The return shape of a tag hierarchy fetch:
 * either a single tree (one tag + children) or an array (for multiple root nodes).
 */
export const TagHierarchyResponseSchema = z.union([
  TagWithChildrenSchema,
  z.array(TagWithChildrenSchema)
]);

/**
 * **AllHierarchiesResponseSchema**
 * The return shape for fetching all tag hierarchies at once.
 * Maps each tag type ID to an array of tag hierarchies (roots for that type).
 */
export const AllHierarchiesResponseSchema = z.record(z.string(), z.array(TagWithChildrenSchema));

/* --------------------------------------------------------------------------------
   6. Listing, Sorting, & Filtering
---------------------------------------------------------------------------------- */

/** Fields on which we can sort tags. */
export const TagSortBySchema = z.enum(["name", "updated_at", "tag_type"]);

/** Ascending or descending sort order. */
export const SortDirectionSchema = z.enum(["asc", "desc"]);

/**
 * **TagFilterSchema**
 * Common optional filters for listing tags:
 * - `tag_type`: Filter by tag type.
 * - `parent_id`: Filter by parent tag ID.
 * - `searchText`: Filter by substring in name/description.
 */
export const TagFilterSchema = z.object({
  tag_type: zid("tag_types").optional(),
  parent_id: zid("tags").optional(),
  searchText: z.string().optional()
});

/**
 * **TagListArgsSchema**
 * Extends the basic filter fields by adding optional sorting params.
 */
export const TagListArgsSchema = TagFilterSchema.extend({
  sort_by: TagSortBySchema.optional(),
  sort_direction: SortDirectionSchema.optional()
});

/* --------------------------------------------------------------------------------
   7. Tag Statistics
---------------------------------------------------------------------------------- */

export const TagStatsSchema = z.object({
  total: z.number(),
  rootTags: z.number(),
  subTags: z.number(),
  byType: z.record(z.string(), z.number()),
  averageSubTagsPerTag: z.number()
});

/* --------------------------------------------------------------------------------
   8. TypeScript Type Inferences
---------------------------------------------------------------------------------- */

/** Complete Tag (with Convex system fields). */
export type Tag = z.infer<typeof TagSchema>;

/** Tag with self-referential `children` array (recursive). */
export type TagWithChildren = TagWithChildrenInterface;

/** Data required to create or update a Tag. */
export type SaveTagArgs = z.infer<typeof SaveTagArgsSchema>;

/** Arguments for removing a Tag. */
export type RemoveTagArgs = z.infer<typeof RemoveTagArgsSchema>;

/** Combined filter for single or multiple Tag fetches. */
export type TagFetchFilter = z.infer<typeof TagFetchFilterSchema>;

/** Args for retrieving a Tag hierarchy. */
export type GetHierarchyArgs = z.infer<typeof GetHierarchyArgsSchema>;

/** Possible response from a Tag hierarchy fetch. */
export type TagHierarchyResponse = z.infer<typeof TagHierarchyResponseSchema>;

/** Allowed sort fields. */
export type TagSortBy = z.infer<typeof TagSortBySchema>;

/** Allowed sort directions (asc/desc). */
export type SortDirection = z.infer<typeof SortDirectionSchema>;

/** Combined filters & sorting for listing tags. */
export type TagListArgs = z.infer<typeof TagListArgsSchema>;

/** Overall stats about Tags. */
export type TagStats = z.infer<typeof TagStatsSchema>;

/* --------------------------------------------------------------------------------
   9. Polymorphic Tagging Schema
---------------------------------------------------------------------------------- */

/**
 * Defines the possible types of entities that can be tagged.
 * Based on the `taggable_type` field in the `taggings` table.
 */
export const TaggableTypeSchema = z.union([
  z.literal("person"),
  z.literal("organization"),
  z.literal("task"),
  z.literal("decision"),
  z.literal("project"),
  z.literal("file"), // Added file type
  z.literal("client")
  // Add more literals as needed based on convex/schema.ts
]);

/**
 * Defines the possible ID types for taggable entities.
 * Based on the `taggable_id` field in the `taggings` table.
 */
export const TaggableIdSchema = z.union([
  zid("people"),
  zid("organizations"),
  zid("tasks"),
  zid("decisions"),
  zid("projects"),
  zid("files"), // Added files ID type
  zid("clients")
  // Add more zids as needed based on convex/schema.ts
]);

/**
 * Complete shape of a Tagging document **as stored** in the `taggings` table.
 */
export const TaggingSchema = z.object({
  /** Unique Convex document ID for this tagging record. */
  _id: zid('taggings'),

  /** Creation time as assigned by Convex (milliseconds since Unix epoch). */
  _creationTime: z.number(),

  /** ID of the tag being applied. */
  tag_id: zid("tags"),

  /** The type of entity being tagged (e.g., "person", "organization"). */
  taggable_type: TaggableTypeSchema,

  /** The ID of the specific entity being tagged. */
  taggable_id: TaggableIdSchema,

  /** Timestamp for when the tag was assigned (milliseconds since Unix epoch). */
  assigned_at: z.number()
});

/** TypeScript type for a Tagging document. */
export type Tagging = z.infer<typeof TaggingSchema>;

/** TypeScript type for the possible taggable entity types. */
export type TaggableType = z.infer<typeof TaggableTypeSchema>;

/** TypeScript type for the possible taggable entity IDs. */
export type TaggableId = z.infer<typeof TaggableIdSchema>;
