import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { TagSchema } from './tags-schema'; // Import TagSchema

// Organization Schema
export const OrganizationSchema = z.object({
  _id: zid('organizations'),
  _creationTime: z.number(),
  name: z.string(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional(),
  parent_org_id: zid('organizations').optional(),
  is_vendor: z.boolean().default(false),
  billComVendorId: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().optional(), // Added website field
  updated_at: z.number().optional(),
  shortDescriptionJobId: zid('_scheduled_functions').optional()
});

// Person Schema
export const PersonSchema = z.object({
  _id: zid('people'),
  _creationTime: z.number(),
  name: z.string(),
  email: z.string().optional(),
  phone: z.string().optional(),
  user_id: zid('users').optional(),
  image: z.string().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional(),
  updated_at: z.number().optional(),
  shortDescriptionJobId: zid('_scheduled_functions').optional()
});

// Organization-People Relationship Schema
export const OrgPeopleRelationshipSchema = z.object({
  _id: zid('organization_people'),
  _creationTime: z.number(),
  organization_id: zid('organizations'),
  person_id: zid('people'),
  role: z.string().optional(),
  updated_at: z.number().optional()
});

// Directory Note Schema
export const DirectoryNoteSchema = z.object({
  _id: zid('directory_notes'),
  _creationTime: z.number(),
  subject_type: z.enum(['PERSON', 'ORGANIZATION']),
  subject_id: z.union([zid('people'), zid('organizations')]),
  content: z.string(),
  updated_at: z.number().optional()
});

// Organization with People Count (extended schema)
export const OrganizationWithPeopleCountSchema = OrganizationSchema.extend({
  peopleCount: z.number()
});

// Organization with Tags (extended schema)
export const OrganizationWithTagsSchema = OrganizationSchema.extend({
  tags: z.array(TagSchema).default([]) // Add tags array
});

// Person with Tags (extended schema)
export const PersonWithTagsSchema = PersonSchema.extend({
  tags: z.array(TagSchema).default([]) // Add tags array
});

// Duplicate Group Schema (for duplicate detection)
export const DuplicateGroupSchema = z.object({
  ids: z.array(z.union([zid('people'), zid('organizations')])),
  name: z.string(),
  similarity: z.number()
});

// Input Schemas
export const CreateOrganizationSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional(),
  parent_org_id: zid('organizations').optional(),
  is_vendor: z.boolean().optional(),
  billComVendorId: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().optional() // Added website field
});

export const UpdateOrganizationSchema = z.object({
  id: zid('organizations'),
  name: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional(),
  parent_org_id: zid('organizations').optional(),
  is_vendor: z.boolean().optional(),
  billComVendorId: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().optional() // Added website field
});

export const CreatePersonSchema = z.object({
  name: z.string(),
  email: z.string().optional(),
  phone: z.string().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional()
});

export const UpdatePersonSchema = z.object({
  id: zid('people'),
  name: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  research: z.string().optional()
});

// TypeScript types derived from Zod schemas
export type Organization = z.infer<typeof OrganizationSchema>;
export type Person = z.infer<typeof PersonSchema>;
export type OrganizationWithPeopleCount = z.infer<
  typeof OrganizationWithPeopleCountSchema
>;
export type OrganizationWithTags = z.infer<typeof OrganizationWithTagsSchema>; // Add type
export type PersonWithTags = z.infer<typeof PersonWithTagsSchema>; // Add type
export type DirectoryNote = z.infer<typeof DirectoryNoteSchema>;
export type OrgPeopleRelationship = z.infer<typeof OrgPeopleRelationshipSchema>;
export type DuplicateGroup = z.infer<typeof DuplicateGroupSchema>;

// Directory Search Result Item Schema
export const DirectorySearchResultItemSchema = z.object({
  _id: z.union([zid('people'), zid('teams')]),
  _creationTime: z.number(),
  name: z.string(),
  image: z.string().optional(),
  type: z.enum(['user', 'person', 'team']),
  user_id: zid('users').optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
});

export type DirectorySearchResultItem = z.infer<typeof DirectorySearchResultItemSchema>;
