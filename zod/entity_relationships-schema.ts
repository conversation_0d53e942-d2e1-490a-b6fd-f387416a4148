import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { PaginationSchema } from './pagination-schema';

// Reusable types for entities
const EntityTypeEnum = z.union([
  z.literal("project"),
  z.literal("decision"),
  z.literal("task"),
  // Add more entity types here if the schema expands
]);

const EntityIdUnion = z.union([
  zid("projects"),
  zid("decisions"),
  zid("tasks"),
  // Add more entity ID types here if the schema expands
]);

// Complete EntityRelationship schema (mirrors convex/schema.ts)
export const EntityRelationship = z.object({
  _id: zid('entity_relationships'), // Using zid here for consistency
  _creationTime: z.number(),
  entity_source_type: EntityTypeEnum,
  entity_source_id: EntityIdUnion,
  entity_target_type: EntityTypeEnum,
  entity_target_id: EntityIdUnion,
  entity_relationship_type: z.string().optional(),
});

// Schema for creating a new relationship
export const CreateEntityRelationshipInput = z.object({
  entity_source_type: EntityTypeEnum,
  entity_source_id: EntityIdUnion,
  entity_target_type: EntityTypeEnum,
  entity_target_id: EntityIdUnion,
  entity_relationship_type: z.string().optional(),
});

// Schema for updating relationships - likely only the type might be updatable
// Adjust required fields based on actual update use cases
export const UpdateEntityRelationshipInput = z.object({
    entity_relationship_type: z.string().optional(),
    // It's unlikely source/target would be updated, usually relationships are deleted and recreated.
    // Add source/target fields here if updates are needed.
}).partial(); // Using partial allows updating only specific fields if needed later

// Schema for bulk updates (if needed, e.g., changing relationship type for many)
export const BulkRelationshipUpdateSchema = z.object({
  ids: z.array(zid('entity_relationships')),
  updates: UpdateEntityRelationshipInput
});

// Query filter schema
export const EntityRelationshipFilterSchema = z
  .object({
    entity_source_type: EntityTypeEnum.optional(),
    entity_source_id: EntityIdUnion.optional(),
    entity_target_type: EntityTypeEnum.optional(),
    entity_target_id: EntityIdUnion.optional(),
    entity_relationship_type: z.string().optional(),
  })
  .optional();

// Export the imported PaginationSchema for use in functions
export { PaginationSchema };
