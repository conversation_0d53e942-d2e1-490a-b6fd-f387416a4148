import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
// Import RelationshipLink for mutation args
import { RelationshipLinkSchemaDiscriminated } from "./files-schema"; 

// Attendee can be either a user or a person
export const AttendeeSchema = z.object({
  id: z.union([zid("users"), zid("people")]),
  name: z.string(),
  type: z.union([z.literal("user"), z.literal("person")])
});

export const ActionItemSchema = z.object({
  task: z.string(),
  assignee: z.union([zid("users"), zid("people")]).optional(),
  assigneeName: z.string().optional(),
  status: z.enum(["todo", "in_progress", "blocked", "completed", "cancelled"]).default("todo"),
  dueDate: z.number().optional()
});

/**
 * Schema for meeting notes that matches the Convex schema
 * Note: The base schema only includes fields defined in the Convex schema
 */
export const MeetingNotesBaseSchema = z.object({
  _id: zid("meeting_notes"),
  _creationTime: z.number(),
  fileId: zid("files"), // Corrected field name
  category: z.string().optional(),
  // ownerId is in the parent 'files' table, not here
  content: z.string().optional(), // AI-assisted notes
  manualNotes: z.string().optional(), // Manual notes
  transcript: z.string().optional(), // Added for transcript feature
  meetingDate: z.number().optional(), // Added from Convex schema
  attendees: z.array(z.union([zid("organizations"), zid("people")])).optional(), // Added from Convex schema
  updated_at: z.number().optional() // Made optional to match Convex schema
});

/**
 * Extended schema for meeting notes that includes UI-specific fields
 * These fields are not stored in the database but are used in the UI
 */
export const MeetingNotesExtendedSchema = MeetingNotesBaseSchema.extend({
  meetingDate: z.number().optional(),
  attendees: z.array(AttendeeSchema).optional(),
  actionItems: z.array(ActionItemSchema).optional(),
});

// For backward compatibility, keep the original schema name
export const MeetingNotesSchema = MeetingNotesExtendedSchema;

// Type exports
export type MeetingNotesBase = z.infer<typeof MeetingNotesBaseSchema>;
export type MeetingNotes = z.infer<typeof MeetingNotesSchema>;
export type Attendee = z.infer<typeof AttendeeSchema>; // Keep if used elsewhere, though not in base schema
export type ActionItem = z.infer<typeof ActionItemSchema>; // Keep if used elsewhere

// --- Schemas for Meeting Note Mutations ---

// Schema for createMeetingNote mutation arguments
export const CreateMeetingNoteArgsSchema = z.object({
  title: z.string().min(1, "Title is required"),
  ownerId: zid('users'),
  content: z.string().optional(), 
  meetingDate: z.number().optional(),
  attendees: z.array(z.union([zid("organizations"), zid("people")])).optional(),
  category: z.string().optional(),
  initialRelationships: z.array(RelationshipLinkSchemaDiscriminated).optional(),
});

// Schema for updateMeetingNote mutation arguments
export const UpdateMeetingNoteArgsSchema = z.object({
  fileId: zid('files'), // Use fileId to identify the note
  title: z.string().min(1, "Title cannot be empty").optional(),
  content: z.string().optional(), // AI-assisted notes
  manualNotes: z.string().optional(), // Manual notes
  transcript: z.string().optional(), // Added for transcript feature
  meetingDate: z.number().optional(),
  attendees: z.array(z.union([zid("organizations"), zid("people")])).optional(),
  category: z.string().optional(),
  relationshipsToAdd: z.array(RelationshipLinkSchemaDiscriminated).optional(),
  relationshipsToRemove: z.array(RelationshipLinkSchemaDiscriminated).optional(), 
});

// Schema for deleteMeetingNote mutation arguments
// Reuses the schema from files-schema as it only needs fileId
export { DeleteKnowledgeBaseArticleArgsSchema as DeleteMeetingNoteArgsSchema } from "./files-schema";

// --- End Schemas for Meeting Note Mutations ---

// Inferred types for mutation args
export type CreateMeetingNoteArgs = z.infer<typeof CreateMeetingNoteArgsSchema>;
export type UpdateMeetingNoteArgs = z.infer<typeof UpdateMeetingNoteArgsSchema>;
// Delete uses the same type as KB delete
