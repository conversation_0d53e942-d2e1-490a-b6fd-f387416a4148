import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Base schema for project updates
export const ProjectUpdate = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  project_id: zid('projects'),
  short_description: z.string().optional(),
  content: z.string(),
  badges: z.array(zid('badges')).optional(),
  date_of_update: z.number().optional(),
  update_type: z.enum(['TASK', 'DECISION', 'MEETING', 'MANUAL', 'AI']),
  source_id: zid('projects').or(zid('tasks')).or(zid('decisions')).or(zid('files')),
  created_by: zid('users'),
  updated_at: z.number()
});

// Input schema for creating a manual project update
export const CreateManualUpdateInput = z.object({
  project_id: zid('projects'),
  content: z.string(),
  short_description: z.string().optional(),
  badges: z.array(zid('badges')).optional(),
  date_of_update: z.number().optional()
});

// Input schema for updating a project update's content
export const UpdateProjectUpdateContentInput = z.object({
  update_id: zid('projectUpdates'),
  content: z.string().optional(),
  short_description: z.string().optional(),
  badges: z.array(zid('badges')).optional(),
  date_of_update: z.number().optional()
});

// Input schema for updating a project update's date
export const UpdateProjectUpdateDateInput = z.object({
  update_id: zid('projectUpdates'),
  date_of_update: z.number()
});

// Schema for listing project updates with pagination
export const ListProjectUpdatesInput = z.object({
  project_id: zid('projects'),
  limit: z.number().optional(),
  cursor: z.any().optional()
});

// Output schema for listing project updates
export const ListProjectUpdatesOutput = z.object({
  projectUpdates: z.array(ProjectUpdate),
  continuation: z.any().optional()
});

// Export types for use in other files
export type ProjectUpdateType = z.infer<typeof ProjectUpdate>;
export type CreateManualUpdateInputType = z.infer<typeof CreateManualUpdateInput>;
export type UpdateProjectUpdateContentInputType = z.infer<typeof UpdateProjectUpdateContentInput>;
