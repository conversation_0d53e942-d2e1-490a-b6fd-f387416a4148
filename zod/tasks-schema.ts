import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Define task status enum
export const TaskStatusEnum = z.enum([
  'todo',
  'in_progress',
  'blocked',
  'completed',
  'cancelled'
]);

// Define task importance enum
export const TaskImportanceEnum = z.enum(['low', 'medium', 'high', 'critical']);

// Complete Task schema (includes all fields from the database)
export const Task = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  name: z.string(),
  short_description: z.string().optional(),
  description: z.string().optional(),
  project_id: zid('projects').optional(),
  decision_id: zid('decisions').optional(),
  due_date: z.number().optional(),
  status: TaskStatusEnum.optional(),
  importance: TaskImportanceEnum.optional(),
  driver: zid('users').optional(), // D: Driver (single user)
  approver: z.array(z.union([zid('users'), zid('teams')])).optional(), // A: Approvers (users or teams)
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(), // C: Contributors (users or teams)
  informed: z.array(z.union([zid('users'), zid('teams')])).optional(), // I: Informed (users or teams)
  requestor: z.union([zid('people'), zid('teams')]).optional(), // Requestor: The person or team who requested this task
  updated_at: z.number().optional(),
  completed_at: z.number().optional(),
  completed_notes: z.string().optional()
});

// Schema for task creation
export const CreateTaskInput = z.object({
  name: z.string(),
  short_description: z.string().optional(),
  description: z.string().optional(),
  project_id: zid('projects').optional(),
  decision_id: zid('decisions').optional(),
  due_date: z.number().optional(),
  status: TaskStatusEnum.optional(),
  importance: TaskImportanceEnum.optional(),
  driver: zid('users').optional(),
  approver: z.array(z.union([zid('users'), zid('teams')])).optional(),
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
  informed: z.array(z.union([zid('users'), zid('teams')])).optional(),
  requestor: z.union([zid('people'), zid('teams')]).optional()
});

// Schema for bulk task creation
export const CreateMultipleTasksInput = z.array(CreateTaskInput);

// Schema for updating tasks - all fields optional
export const UpdateTaskInput = CreateTaskInput.partial();

// Schema for bulk updates
export const BulkTaskUpdateSchema = z.object({
  id: zid('tasks').optional(),
  ids: z.array(zid('tasks')).optional(),
  updates: UpdateTaskInput
});

// Query filter schema
export const TaskFilterSchema = z
  .object({
    name: z.string().optional(),
    project_id: zid('projects').optional(),
    decision_id: zid('decisions').optional(),
    status: TaskStatusEnum.optional(),
    importance: TaskImportanceEnum.optional(),
    driver: zid('users').optional(),
    approver: z.array(z.union([zid('users'), zid('teams')])).optional(),
    contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
    informed: z.array(z.union([zid('users'), zid('teams')])).optional(),
    requestor: z.union([zid('people'), zid('teams')]).optional(),
    due_date_before: z.number().optional(),
    due_date_after: z.number().optional(),
    updated_at_before: z.number().optional(), // Added
    updated_at_after: z.number().optional() // Added
  })
  .optional();

// Pagination schema
export const PaginationSchema = z
  .object({
    sortBy: z
      .enum(['_creationTime', 'name', 'due_date', 'updated_at', 'importance'])
      .optional(),
    sortDirection: z.enum(['asc', 'desc']).optional(),
    cursor: z.any().optional(),
    limit: z.number().optional()
  })
  .optional();
