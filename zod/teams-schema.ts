import { z } from "zod";
import { zid } from "convex-helpers/server/zod";

/** Schema for filtering teams */
export const TeamFilterSchema = z.object({
  searchText: z.string().optional(),
  slug: z.string().optional(),
});

/** Base team schema */
export const Team = z.object({
  name: z.string(),
  slug: z.string().optional(),
  description: z.string().optional(),
});

/** Schema for creating a team */
export const CreateTeamSchema = Team;

/** Schema for updating a team */
export const UpdateTeamSchema = z.object({
  name: z.string().optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
});

/** Schema for bulk team updates */
export const BulkTeamUpdateSchema = z.object({
  teamId: zid("teams"),
  data: UpdateTeamSchema
});

/** Schema for the updateTeams mutation input */
export const UpdateTeamsSchema = z.object({
  updates: z.array(BulkTeamUpdateSchema).min(1)
});

// Add new schema for team output format
export const TeamOutputSchema = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  name: z.string(),
  slug: z.string().optional(),
  description: z.string().optional(),
});

// Add type export for easier reuse
export type TeamOutput = z.infer<typeof TeamOutputSchema>;

// Add new schema for team member output
export const TeamMemberOutputSchema = z.object({
  _id: z.string(),
  name: z.string().optional(),
  email: z.string(),
  image: z.string().optional(),
  role: z.string().optional(),
});

// Add new schema for team with members output
export const TeamWithMembersOutputSchema = TeamOutputSchema.extend({
  members: z.array(TeamMemberOutputSchema)
});

// Add type export for team with members output
export type TeamWithMembersOutput = z.infer<typeof TeamWithMembersOutputSchema>; 