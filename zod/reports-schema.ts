import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Date filter options
export const DATE_FILTER_OPTIONS = {
  LAST_30_DAYS: 'last30days',
  LAST_60_DAYS: 'last60days',
  LAST_90_DAYS: 'last90days',
  YEAR_TO_DATE: 'ytd',
  Q1: 'q1',
  Q2: 'q2',
  Q3: 'q3',
  Q4: 'q4',
  CUSTOM: 'custom'
} as const;

// Date filter type schema - simplified using nativeEnum
export const DateQuickFilterSchema = z
  .nativeEnum(DATE_FILTER_OPTIONS)
  .nullable();

// Base date range schema for reuse
export const DateRangeBaseSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional()
});

// Extended date range schema with string/number support for API interactions
export const DateRangeSchema = z.object({
  startDate: z.union([z.string(), z.number(), z.date()]),
  endDate: z.union([z.string(), z.number(), z.date()])
});

// Filter state schema
export const FilterStateSchema = DateRangeBaseSchema.extend({
  dateFilter: DateQuickFilterSchema,
  params: z.instanceof(URLSearchParams)
});

// Define the SpendingRow type first to help with recursive typing
export type SpendingRowType = {
  id: string | number | null;
  name: string;
  amount: number;
  transactionCount: number;
  trend: 'up' | 'down' | 'stable';
  parentId: string | number | null;
  percentage?: number;
  isUncategorized?: boolean;
  subCategories?: SpendingRowType[];
};

// Spending row schema - properly typed recursive definition
export const SpendingRowSchema: z.ZodType<SpendingRowType> = z.lazy(() =>
  z.object({
    id: z.union([z.string(), z.number(), z.null()]),
    name: z.string(),
    amount: z.number(),
    transactionCount: z.number(),
    trend: z.enum(['up', 'down', 'stable']),
    parentId: z.union([z.string(), z.number(), z.null()]),
    percentage: z.number().optional(),
    isUncategorized: z.boolean().optional(),
    subCategories: z.array(SpendingRowSchema).optional()
  })
);

// Spending summary schema
export const SpendingSummarySchema = z.object({
  totalSpending: z.number(),
  avgTransaction: z.number(),
  transactionCount: z.number(),
  uncategorizedAmount: z.number(),
  uncategorizedCount: z.number()
});

// Spending overview table props schema
export const SpendingOverviewTablePropsSchema = z.object({
  data: z.array(SpendingRowSchema),
  summary: SpendingSummarySchema.optional()
});

// Spending stats schema
export const SpendingStatsSchema = z.object({
  totalSpending: z.number(),
  avgTransactionSize: z.number(),
  totalTransactions: z.number(),
  spendingTrend: z.object({
    percentage: z.number(),
    previousTotal: z.number(),
    currentTotal: z.number(),
    periodLabel: z.string()
  }),
  topCategory: z.string(),
  topCategoryPercentage: z.number().optional()
});

// Spending overview stats props schema - reusing DateRangeBaseSchema
export const SpendingOverviewStatsPropsSchema = DateRangeBaseSchema.extend({
  dateFilter: z.string()
});

// Report fetch args schema
export const ReportFetchArgsSchema = z.object({
  dateRange: DateRangeSchema,
  reportType: z.enum(['overview', 'detailed'])
});

// Export types using z.infer
export type DateQuickFilterType = z.infer<typeof DateQuickFilterSchema>;
export type FilterState = z.infer<typeof FilterStateSchema>;
export type SpendingRow = SpendingRowType; // Use the already defined type
export type SpendingSummary = z.infer<typeof SpendingSummarySchema>;
export type SpendingOverviewTableProps = z.infer<
  typeof SpendingOverviewTablePropsSchema
>;
export type SpendingStats = z.infer<typeof SpendingStatsSchema>;
export type SpendingOverviewStatsProps = z.infer<
  typeof SpendingOverviewStatsPropsSchema
>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export type DateRangeBase = z.infer<typeof DateRangeBaseSchema>;
export type ReportFetchArgs = z.infer<typeof ReportFetchArgsSchema>;
