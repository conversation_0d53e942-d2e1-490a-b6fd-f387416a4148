import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { Id } from '../convex/_generated/dataModel';

// Define the BillStatus type using Zod enum
export const billStatusValues = [
  'PAID',
  'UNPAID',
  'PARTIALLY_PAID',
  'SCHEDULED',
  'IN_PROCESS',
  'UNDEFINED'
] as const;

// Create a Zod enum for bill status
export const BillStatusSchema = z.enum(billStatusValues);
export type BillStatus = z.infer<typeof BillStatusSchema>;

// Define the UI payment status type
export const paymentStatusValues = ['paid', 'pending', 'overdue'] as const;
export const PaymentStatusSchema = z.enum(paymentStatusValues);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

// Define the bill type enum
export const BillTypeSchema = z.enum(['BILL', 'CREDIT_CARD']);
export type BillType = z.infer<typeof BillTypeSchema>;

// Base Bill Schema - common fields for both input and output
export const BaseBillSchema = z.object({
  type: BillTypeSchema,
  billNo: z.string(),
  billDate: z.number().refine(Number.isFinite, {
    message: 'billDate must be a finite number (timestamp)'
  }),
  vendor_id: zid('organizations'),
  amount: z.number().refine(Number.isFinite, {
    message: 'amount must be a finite number'
  }),
  dueDate: z.number().refine(Number.isFinite, {
    message: 'dueDate must be a finite number (timestamp)'
  }).optional(),
  memo: z.string().optional(),
  documentStorageId: zid('_storage').optional(),
  documentFilename: z.string().optional(),
  billStatus: BillStatusSchema.optional(),
  category: z.string().optional(),
  isHighValue: z.boolean().optional(),
  isUnusual: z.boolean().optional(),
  billComId: z.string().optional(),
  billComArchived: z.boolean().optional(),
  billComCreatedTime: z.number().optional(),
  billComUpdatedTime: z.number().optional(),
  percentageChange: z.number().refine(Number.isFinite, {
    message: 'percentageChange must be a finite number'
  }).optional(),
  previousAmount: z.number().refine(Number.isFinite, {
    message: 'previousAmount must be a finite number'
  }).optional(),
  updated_at: z.number().refine(Number.isFinite, {
    message: 'updated_at must be a finite number (timestamp)'
  }).optional()
});

// System fields for Bill schema
export const BillSystemFieldsSchema = z.object({
  _id: zid('bills'),
  _creationTime: z.number()
});

// Complete Bill schema including system fields
export const BillSchema = z.object({
  _id: zid('bills'),
  _creationTime: z.number(),
  amount: z.number(),
  billDate: z.number(),
  billNo: z.string(),
  dueDate: z.optional(z.number()),
  memo: z.optional(z.string()),
  type: BillTypeSchema,
  updated_at: z.number(),
  vendor_id: zid('organizations'),
  billComId: z.optional(z.string()),
  billComArchived: z.optional(z.boolean()),
  billComCreatedTime: z.optional(z.number()),
  billComUpdatedTime: z.optional(z.number()),
  billStatus: BillStatusSchema.optional()
});
export type Bill = z.infer<typeof BillSchema>;

// Input schema for creating a bill
export const BillInputSchema = BaseBillSchema;
export type BillInput = z.infer<typeof BillInputSchema>;

// UI-specific Bill that extends the base Bill with UI-only fields
export interface UIBill extends Bill {
  vendor: string; // This maps to vendor_id in the database but we display the name in UI
  documentData?: string; // For document preview in UI, not stored in schema directly
  category?: string; // Category of the bill
  percentageChange?: number; // Percentage change in amount from previous bill
  previousAmount?: number; // Previous bill amount for comparison
}

// Priority Bill interface used by the PriorityBills component
export interface PriorityBill {
  _id: Id<'bills'>;
  dueDate: Date; // Note: UI uses Date objects rather than timestamps
  billDate?: Date;
  amount: number;
  vendor: string; // Display name, not the ID
  billStatus?: BillStatus;
  category?: string;
  type: BillType;
  isHighValue?: boolean;
  isUnusual?: boolean;
  percentageChange?: number;
  previousAmount?: number;
  billNo?: string;
  memo?: string;
}

// Props for the BillsTable component
export interface BillsTableProps {
  bills: UIBill[];
  selectedBills: Id<'bills'>[];
  onSelectionChange: (selectedBills: Id<'bills'>[]) => void;
  onOpenCreateModal: () => void;
  onOpenEditModal: (billId: Id<'bills'>) => void;
  onDeleteBills?: (billIds: Id<'bills'>[]) => Promise<void>;
  onUpdateBillStatus?: (billId: Id<'bills'>, status: BillStatus) => Promise<void>;
}

export const BillValidatorSchema = z.object({
  _id: zid('bills'),
  _creationTime: z.number(),
  amount: z.number(),
  billDate: z.number(),
  billNo: z.string(),
  documentFilename: z.string().optional(),
  documentStorageId: zid('_storage').optional(),
  dueDate: z.number().optional(),
  memo: z.string().optional(),
  type: BillTypeSchema,
  updated_at: z.number(),
  vendor_id: zid('organizations'),
  status: BillStatusSchema.optional(),
  billStatus: BillStatusSchema.optional(),
  category: z.string().optional(),
  isHighValue: z.boolean().optional(),
  isUnusual: z.boolean().optional(),
  billComId: z.string().optional(),
  billComArchived: z.boolean().optional(),
  billComCreatedTime: z.number().optional(),
  billComUpdatedTime: z.number().optional(),
  percentageChange: z.number().optional(),
  previousAmount: z.number().optional()
});
export type BillValidator = z.infer<typeof BillValidatorSchema>;

// Schema for updating a bill
// This matches the billUpdateFields from bills.ts
export const BillUpdateSchema = BaseBillSchema.partial();
export type BillUpdate = z.infer<typeof BillUpdateSchema>;

// Re-export BillUpdateSchema as BillUpdateFieldsSchema for compatibility
export const BillUpdateFieldsSchema = BillUpdateSchema;
export type BillUpdateFields = BillUpdate;

// Schema for bill document storage
export const StoreBillDocumentSchema = z.object({
  storageId: zid('_storage'),
  billId: zid('bills'),
  filename: z.string()
});
export type StoreBillDocument = z.infer<typeof StoreBillDocumentSchema>;

// Schema for listing bills with filters
export const BillQueryFiltersSchema = z.object({
  type: BillTypeSchema.optional(),
  startDate: z.number().optional(),
  endDate: z.number().optional(),
  vendor_id: zid('organizations').optional(),
  billStatus: BillStatusSchema.optional()
});
export type BillQueryFilters = z.infer<typeof BillQueryFiltersSchema>;

// Schema for pagination options
export const PaginationOptsSchema = z.object({
  numItems: z.number(),
  cursor: z.string().nullable().optional()
});
export type PaginationOpts = z.infer<typeof PaginationOptsSchema>;

// Schema for vendor bills query
export const VendorBillsQuerySchema = z.object({
  vendor_id: zid('organizations'),
  paginationOpts: PaginationOptsSchema,
  billStatus: BillStatusSchema.optional()
});
export type VendorBillsQuery = z.infer<typeof VendorBillsQuerySchema>;

// Schema for vendor total query
export const VendorTotalQuerySchema = z.object({
  vendor_id: zid('organizations'),
  startDate: z.number().optional(),
  endDate: z.number().optional(),
  billStatus: BillStatusSchema.optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  query: z.enum(['asc', 'desc']).optional()
});
export type VendorTotalQuery = z.infer<typeof VendorTotalQuerySchema>;

// Schema for pagination results
export const PaginationResultSchema = z.object({
  page: z.array(BillSchema),
  isDone: z.boolean(),
  continueCursor: z.string().nullable()
});
export type PaginationResult = z.infer<typeof PaginationResultSchema>;

// Schema for vendor total results
export const VendorTotalResultSchema = z.object({
  total: z.number(),
  count: z.number()
});
export type VendorTotalResult = z.infer<typeof VendorTotalResultSchema>;

// Schema for bulk bill updates
export const BulkBillUpdateSchema = z.object({
  ids: z.array(zid('bills')).optional(),
  id: zid('bills').optional(),
  updates: BillUpdateSchema
});
export type BulkBillUpdate = z.infer<typeof BulkBillUpdateSchema>;

// Schema for bulk bill deletion
export const BulkBillRemoveSchema = z.object({
  ids: z.array(zid('bills'))
});
export type BulkBillRemove = z.infer<typeof BulkBillRemoveSchema>;

// Schema for document URL query
export const GetDocumentUrlSchema = z.object({
  billId: zid('bills')
});
export type GetDocumentUrl = z.infer<typeof GetDocumentUrlSchema>;

// Define sortable fields as a Zod enum
export const SortableFieldsSchema = z.enum([
  'billDate',
  'dueDate',
  'amount',
  'vendor_id',
  'type',
  'billStatus',
  'billNo',
  'category'
]);
export type SortableField = z.infer<typeof SortableFieldsSchema>;

// Sort order enum (already defined in the schema, but making it explicit)
export const SortOrderSchema = z.enum(['asc', 'desc']);
export type SortOrder = z.infer<typeof SortOrderSchema>;

// Schema for unified bill queries with pagination, sorting, and filtering
export const UnifiedBillQuerySchema = z
  .object({
    // Filter parameters
    vendor_id: zid('organizations').optional(),
    type: BillTypeSchema.optional(),
    startDate: z.number().optional(),
    endDate: z.number().optional(),
    billStatus: BillStatusSchema.optional(),

    // Sorting parameters
    sortBy: SortableFieldsSchema.optional(),
    query: SortOrderSchema.optional(),

    // Pagination parameters
    paginationOpts: z
      .object({
        numItems: z.number(),
        cursor: z.string().nullable().optional()
      })
      .optional()
    // Add a refinement to ensure query is provided when sortBy is specified
  })
  .refine(
    (data) => {
      // If sortBy is provided, query must also be provided
      return data.sortBy === undefined || data.query !== undefined;
    },
    {
      message: 'query must be provided when sortBy is specified',
      path: ['query']
    }
  );
export type UnifiedBillQuery = z.infer<typeof UnifiedBillQuerySchema>;

// Schema for listBills query output
export const BillsListResponseSchema = z.union([
  // Response format for paginated results
  z.object({
    page: z.array(BillSchema),
    continuationToken: z.string().nullable().optional(),
    isDone: z.boolean()
  }),
  // Response format for non-paginated results
  z.array(BillSchema)
]);
export type BillsListResponse = z.infer<typeof BillsListResponseSchema>;

// Schema for delete bills operation
export const DeleteBillsSchema = z.object({
  // Either a single ID or an array of IDs
  id: zid('bills').optional(),
  ids: z.array(zid('bills')).optional()
});
export type DeleteBills = z.infer<typeof DeleteBillsSchema>;

// Schema for a single line item
// Defines the structure of an individual bill line item
export const LineItemSchema = z.object({
  bill_id: zid('bills'),
  amount: z.number(),
  billComLineItemId: z.string().optional(),
  description: z.string(),
  post_date: z.number(),
  merchant_name: z.string(),
  spending_category: zid('tags').optional(),
  vendor_id: zid('organizations'),
  updated_at: z.number()
});
export type LineItem = z.infer<typeof LineItemSchema>;

// Schema for line item updates
export const LineItemUpdateSchema = z.object({
  amount: z.number().optional(),
  billComLineItemId: z.string().optional(),
  description: z.string().optional(),
  post_date: z.number().optional(),
  merchant_name: z.string().optional(),
  spending_category: zid('tags').optional(),
  vendor_id: zid('organizations').optional(),
  updated_at: z.number().optional()
});
export type LineItemUpdate = z.infer<typeof LineItemUpdateSchema>;

// Schema for bulk line item operations
// Used when working with multiple line items at once (batch operations)
export const BulkLineItemSchema = z.object({
  items: z.array(LineItemSchema)
});
export type BulkLineItem = z.infer<typeof BulkLineItemSchema>;

// Schema for bulk line item updates
export const BulkLineItemUpdateSchema = z.object({
  id: zid('lineItems').optional(),
  ids: z.array(zid('lineItems')).optional(),
  updates: LineItemUpdateSchema
});
export type BulkLineItemUpdate = z.infer<typeof BulkLineItemUpdateSchema>;
