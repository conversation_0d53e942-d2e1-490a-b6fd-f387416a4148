import { z } from 'zod';

/**
 * Input schema for the analyzeUpload action.
 * Requires a storageId to identify the file to analyze.
 */
export const AnalyzeUploadInputSchema = z.object({
  storageId: z.string().describe('The ID of the uploaded file in Convex storage.'),
});

/**
 * Output schema for the analyzeUpload action.
 * Defines the structure of the analysis results.
 */
export const AnalyzeUploadOutputSchema = z.object({
  documentType: z.string().describe('One of the document types from document_definitions.name or "none".'),
  summary: z.string().describe('A high-level summary of the document.'),
  analysis: z.string().describe('Notable things for a private wealth manager.'),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  children: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  assets: z.array(z.object({
    name: z.string().optional(),
    value: z.number().optional(),
    type: z.string().optional(),
    description: z.string().optional()
  })).optional()
});
