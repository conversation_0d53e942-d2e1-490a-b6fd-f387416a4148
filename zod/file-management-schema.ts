import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { AnalyzeUploadOutputSchema } from "./actions-schema";

// Constants
export const ALLOWED_TYPES = ['application/pdf', 'image/png', 'image/jpeg'] as const;
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Base schemas for common fields
const BaseFileSchema = z.object({
  name: z.string(),
  contentType: z.string(),
  size: z.number().max(MAX_FILE_SIZE, "File size must be less than 10MB"),
  updated_at: z.number(),
});

// Schema for file upload URL generation
export const GenerateUploadUrlSchema = z.object({
  contentType: z.string().optional()
});

// Schema for storing documents
export const StoreDocumentSchema = z.object({
  storageId: zid('_storage'),
  name: z.string(),
  type: z.enum(["DOCUMENT", "CONTRACT", "KNOWLEDGE_BASE", "MEETING_NOTES", "BILL"]),
  size: z.number().max(MAX_FILE_SIZE, "File size must be less than 10MB"),
  category: z.string().optional(),
  parentEntityId: z.union([
    zid('tasks'),
    zid('projects'),
    zid('decisions'),
    zid('organizations'),
    zid('users'),
    zid('people'),
    zid('bills')
  ]).optional()
});

// Schema for file URL retrieval
export const GetFileUrlSchema = z.object({
  storageId: zid('_storage')
});

// Schema for file deletion
export const DeleteFileSchema = z.object({
  fileId: zid('files'),
  storageId: zid('_storage').optional()
});

// Output schemas for different file types
export const AttachmentSchema = BaseFileSchema.extend({
  fileId: zid('files'),
  storageId: zid('_storage'),
  filename: z.string(),
  category: z.string().optional(),
  uploadedAt: z.number()
});

export const ContractSchema = BaseFileSchema.extend({
  fileId: zid('files'),
  storageId: zid('_storage'),
  filename: z.string(),
  category: z.string().optional(),
  uploadedAt: z.number(),
  currentVersionNumber: z.number()
});

export const FileSchema = z.object({
  name: z.string(),
  docType: z.literal('BILL'),
  parentEntityId: zid('bills'),
  fileFilename: z.string(),
  storageId: zid('_storage'),
  fileSize: z.number(),
  uploadStatus: z.literal('completed'),
  updated_at: z.number()
});

// Schema for updating document and checklist
export const UpdateDocumentAndChecklistSchema = z.object({
  fileId: zid('files'),
  analysisResult: AnalyzeUploadOutputSchema,
  userId: zid('users')
});

// Schema for user document checklist
export const UserDocumentChecklistSchema = z.object({
  document_definition_id: zid('document_definitions'),
  user_id: zid('users'),
  status: z.enum(['completed', 'incomplete', 'in_progress']),
  updated_at: z.number()
});

// Schema for scheduling document analysis
export const ScheduleDocumentAnalysisSchema = z.object({
  storageId: zid('_storage'),
  userId: zid('users')
});

// Schema for document definition
export const DocumentDefinitionSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  category: z.string().optional(),
  updated_at: z.number().optional()
});

// Schema for AI processing result
export const AIProcessingResultSchema = z.object({
  summary: z.string().optional(),
  analysis: z.string().optional(),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  children: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  assets: z.array(z.object({
    name: z.string().optional(),
    value: z.number().optional(),
    type: z.string().optional(),
    description: z.string().optional(),
  })).optional()
});

// Type exports
export type GenerateUploadUrlArgs = z.infer<typeof GenerateUploadUrlSchema>;
export type StoreDocumentArgs = z.infer<typeof StoreDocumentSchema>;
export type GetFileUrlArgs = z.infer<typeof GetFileUrlSchema>;
export type DeleteFileArgs = z.infer<typeof DeleteFileSchema>;
export type Attachment = z.infer<typeof AttachmentSchema>;
export type Contract = z.infer<typeof ContractSchema>;
export type File = z.infer<typeof FileSchema>;
export type UpdateDocumentAndChecklistArgs = z.infer<typeof UpdateDocumentAndChecklistSchema>;
export type UserDocumentChecklist = z.infer<typeof UserDocumentChecklistSchema>;
export type ScheduleDocumentAnalysisArgs = z.infer<typeof ScheduleDocumentAnalysisSchema>;
export type DocumentDefinition = z.infer<typeof DocumentDefinitionSchema>;
