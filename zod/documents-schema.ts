import { z } from "zod";
import { zid } from "convex-helpers/server/zod";

// Schema for parsed data from AI analysis
export const ParsedDataSchema = z.object({
  documentType: z.string(),
  summary: z.string(),
  analysis: z.string(),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  children: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  assets: z.array(z.object({
    name: z.string().optional(),
    value: z.number().optional(),
    type: z.string().optional(),
    description: z.string().optional()
  })).optional()
});

// Document Definition Schema
export const DocumentDefinitionSchema = z.object({
  _id: zid("document_definitions"),
  name: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  updated_at: z.number().optional(),
});

export type DocumentDefinition = z.infer<typeof DocumentDefinitionSchema>;

// User Document Checklist Schema
export const UserChecklistItemSchema = z.object({
  _id: zid("user_document_checklist"),
  user_id: zid("users"),
  document_definition_id: zid("document_definitions"),
  status: z.string(),
  updated_at: z.number().optional(),
  definition: DocumentDefinitionSchema,
});

export type UserChecklistItem = z.infer<typeof UserChecklistItemSchema>;

// Checklist Item Schema (for UI)
export const ChecklistItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  documentTypes: z.array(z.string()),
  completed: z.boolean(),
  documentId: z.string().optional(),
  description: z.string().optional(),
});

export type ChecklistItem = z.infer<typeof ChecklistItemSchema>;

// Document Schema
export const DocumentSchema = z.object({
  _id: zid("documents"),
  name: z.string(),
  type: z.string(),
  uploadedAt: z.date(),
  status: z.enum(['uploading', 'processing', 'completed', 'error']),
  parsedData: ParsedDataSchema.optional(),
  // Convex IDs to track the file in the database
  fileId: zid("files").optional(),
  storageId: zid("_storage").optional(),
});

export type Document = z.infer<typeof DocumentSchema>;
