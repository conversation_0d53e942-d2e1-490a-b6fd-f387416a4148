/**
 * Document Schema
 * 
 * This file defines the Zod schemas for document-related data.
 */

import { z } from 'zod';
import { Id } from '../convex/_generated/dataModel';

// Schema for document content
export const documentContentSchema = z.object({
  type: z.literal('doc'),
  content: z.array(z.any()).optional(),
});

// Type for document content
export type DocumentContent = z.infer<typeof documentContentSchema>;

// Schema for document metadata
export const documentMetadataSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  entityType: z.enum(['task', 'project', 'decision', 'template', 'note']).optional(),
  entityId: z.string().optional(),
  lastModified: z.number().optional(),
  createdBy: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// Type for document metadata
export type DocumentMetadata = z.infer<typeof documentMetadataSchema>;

// Schema for document with ID
export const documentWithIdSchema = z.object({
  id: z.string(),
  content: documentContentSchema,
  metadata: documentMetadataSchema.optional(),
});

// Type for document with ID
export type DocumentWithId = z.infer<typeof documentWithIdSchema>;

// Schema for creating a new document
export const createDocumentSchema = z.object({
  content: documentContentSchema,
  metadata: documentMetadataSchema.optional(),
});

// Type for creating a new document
export type CreateDocument = z.infer<typeof createDocumentSchema>;

// Schema for updating a document
export const updateDocumentSchema = z.object({
  id: z.string(),
  content: documentContentSchema.optional(),
  metadata: documentMetadataSchema.partial().optional(),
});

// Type for updating a document
export type UpdateDocument = z.infer<typeof updateDocumentSchema>;

// Schema for document ID
export const documentIdSchema = z.object({
  id: z.string(),
});

// Type for document ID
export type DocumentId = z.infer<typeof documentIdSchema>;
