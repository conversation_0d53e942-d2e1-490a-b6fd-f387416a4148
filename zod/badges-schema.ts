import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { Id } from "../convex/_generated/dataModel";

// Define the domain types that a badge can belong to
export const BadgeDomainEnum = z.enum([
  'task',
  'project',
  'decision',
  'bills',
  'organization',
  'users',
  'people',
  'general'
]);

// Schema for filtering badges
export const BadgeFilterSchema = z.object({
  searchText: z.string().optional(),
  badgeDomain: BadgeDomainEnum.optional(),
  sortBy: z.enum(['name', '_creationTime']).optional(),
  sortDirection: z.enum(['asc', 'desc']).optional().default('asc')
});

// Schema for pagination options
export const PaginationOptionsSchema = z.object({
  cursor: z.any().optional(),
  numItems: z.number().optional().default(50)
});

// Schema representing a badge
export const BadgeSchema = z.object({
  _id: zid('badges'),
  _creationTime: z.number(),
  name: z.string(),
  color: z.string(),
  description: z.string().optional(),
  icon: z.string().optional(),
  badgeDomain: BadgeDomainEnum.optional()
});

// Schema for paginated results
export const PaginatedResultSchema = z.object({
  page: z.array(BadgeSchema),
  isDone: z.boolean(),
  continueCursor: z.any().nullable()
});

// Type exports
export type BadgeFilter = z.infer<typeof BadgeFilterSchema>;
export type PaginationOptions = z.infer<typeof PaginationOptionsSchema>;
export type Badge = z.infer<typeof BadgeSchema>;
export type PaginatedResult = z.infer<typeof PaginatedResultSchema>; 