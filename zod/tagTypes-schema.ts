// zod/tagTypes-schema.ts
import { z } from "zod";
import { zid } from "convex-helpers/server/zod";

/**
 * ----------------------------------------------------------------
 * TagTypeEnum - Represents the type field for tags
 * ----------------------------------------------------------------
 * Since tag types are dynamically defined in the database,
 * we use a string schema rather than a fixed enum.
 * This allows any string value while maintaining type safety.
 */
export const TagTypeEnum = z.string();

/**
 * ----------------------------------------------------------------
 * Base fields that match your `tag_types` table schema in Convex:
 *   defineTable({ name, immutable_slug, description, color, updated_at })
 * ----------------------------------------------------------------
 * We do NOT include Convex system fields (_id, _creationTime) here.
 */
export const TagTypeBaseSchema = z.object({
  name: z.string().min(1, "Tag type name is required"),
  immutable_slug: z.string().optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  updated_at: z.number(), // This is often set automatically on the server
});

/**
 * ----------------------------------------------------------------
 * The full schema for a TagType document, including system fields:
 * ----------------------------------------------------------------
 */
export const TagTypeSchema = TagTypeBaseSchema.extend({
  /** The Convex-generated ID for this document */
  _id: zid("tag_types"),
  /** Convex's creation time (milliseconds since epoch) */
  _creationTime: z.number(),
});

/**
 * ---------------------------------------------------------
 * TypeScript types for convenience in the rest of the app:
 * ---------------------------------------------------------
 */
export type TagType = z.infer<typeof TagTypeSchema>;
export type TagTypeInsert = z.infer<typeof TagTypeBaseSchema>;
export type TagTypeEnumType = z.infer<typeof TagTypeEnum>; // Type for tag type values

/**
 * ----------------------------------------------------------------
 * Combined schema for creating OR updating a tag type:
 * ----------------------------------------------------------------
 *  - If `id` is omitted => we're creating a new doc
 *  - If `id` is present => we're updating that doc
 *  - All other fields (name, slug, etc.) are optional in update
 *    contexts, but required for create. For simplicity, we keep
 *    `name` as .min(1) to enforce a required name always.
 */
export const SaveTagTypeSchema = z.object({
  id: zid("tag_types").optional(),
  name: z.string().min(1, "Tag type name is required"),
  description: z.string().optional(),
  color: z.string().optional(),
  immutable_slug: z.string().optional(),
});

/**
 * ----------------------------------------------------------------
 * Schema for deleting a tag type (just needs an ID).
 * ----------------------------------------------------------------
 */
export const DeleteTagTypeArgsSchema = z.object({
  id: zid("tag_types"),
});
export type DeleteTagTypeArgs = z.infer<typeof DeleteTagTypeArgsSchema>;

/**
 * ----------------------------------------------------------------
 * Schema for fetching tag types:
 * ----------------------------------------------------------------
 * We combine "get by ID" and "list with optional search" by:
 *  - `id` (optional) => fetch a single record if provided
 *  - `searchText` (optional) => free-text search across name/description
 * If both are omitted => returns the full list.
 * If both are present => returns either the single record or filter
 *   (you can define your own rule).
 */
export const FetchTagTypesArgsSchema = z.object({
  id: zid("tag_types").optional(),
  searchText: z.string().optional(),
});

/**
 * ----------------------------------------------------------------
 * Example of a stats schema if you want to return distribution, etc.
 * ----------------------------------------------------------------
 */
export const TagTypeStatsSchema = z.object({
  total: z.number(),
  byColor: z.record(z.string(), z.number()),
});
export type TagTypeStats = z.infer<typeof TagTypeStatsSchema>;
