import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Define decision status enum (kept for backward compatibility, but now accepts any string)
export const DecisionStatusEnum = z.string();

// Legacy status values (for reference only, not enforced)
export const LEGACY_STATUS_VALUES = [
  'draft',
  'in_review',
  'approved',
  'rejected',
  'escalated',
  'cancelled',
  'on_hold'
];

// Define priority enum
export const PriorityEnum = z.enum([
  'low',
  'medium',
  'high',
  'urgent'
]);

export const ImportanceEnum = z.enum(['low', 'medium', 'high', 'urgent']);

// Define amount type enum
export const AmountTypeEnum = z.enum([
  'CURRENCY',
  'PERCENTAGE',
  'APPROVAL',
  'OPTIONCOUNT',
  'SCORE',
  'DURATION',
  'DATE',
  'THRESHOLD',
  'LEVEL'
]);

// Define decision type enum
export const DecisionTypeEnum = z.enum([
  'approval',
  'selection'
]);

// Define time period enum
export const TimePeriodEnum = z.enum([
  'One-time',
  'Monthly',
  'Quarterly',
  'Yearly',
  'Multi-year'
]);

// Define subtable type enum
export const SubtableTypeEnum = z.enum([
  'general_decisions',
  'investment_decisions',
  'decision_custom_data'
]);

// Complete Decision schema (includes all fields from the core decisions table)
export const Decision = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  title: z.string().optional(),
  decision_category_id: zid('decision_categories'),
  short_description: z.string().optional(),
  summary: z.string().optional(),
  project_ids: z.array(zid('projects')).optional(),
  decision_date: z.number().optional(),
  due_date: z.number().optional(),
  status: DecisionStatusEnum.optional(),
  priority: PriorityEnum.optional(),
  approver_id: zid('users').optional(),
  badges: z.array(zid('badges')).optional(),
  amount: z.number().optional(),
  importance: ImportanceEnum.optional(),
  amountType: AmountTypeEnum.optional(),
  currencyCode: z.string().optional(),
  updated_at: z.number().optional(),
  // DCI Fields
  driver: zid('users').optional(),
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
  informed: z.array(z.union([zid('users'), zid('teams')])).optional(),
  
  // Decision Type Component Fields
  decision_type: DecisionTypeEnum.optional(),
  
  // Financial implications fields
  has_financial_implications: z.boolean().optional(),
  financial_amount: z.number().optional(),
  financial_currency: z.string().optional(),
  financial_time_period: TimePeriodEnum.optional(),
  financial_years: z.number().optional(),
  
  // Option implications
  yes_means: z.string().optional(),
  no_means: z.string().optional()
});

// General Decision schema (for the general_decisions sub-table)
export const GeneralDecision = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  decision_id: zid('decisions'),
  description: z.string().optional(),
  rationale: z.string().optional(),
  alternatives_considered: z.string().optional(),
  impact_assessment: z.string().optional(),
  approver_id: zid('users').optional(),
  approval_date: z.number().optional(),
  effectiveness_rating: z.number().optional(),
  implementation_notes: z.string().optional(),
  review_date: z.number().optional(),
  updated_at: z.number()
});

// Kanban column schema for investment decisions
export const KanbanColumn = z.object({
  name: z.string(),
  order: z.number(),
  statuses: z.array(z.string()),
  color: z.string().optional()
});

// Investment Decision schema (for the investment_decisions sub-table)
export const InvestmentDecision = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  decision_id: zid('decisions'),
  description: z.string().optional(),
  is_active: z.boolean(),
  proposed_investment: z.string().optional(),
  total_fundraise: z.number().optional(),
  asset_class: z.string().optional(),
  structure: z.string().optional(),
  investment_entity: z.string().optional(),
  market: z.string().optional(),
  geography: z.string().optional(),
  target_close_date: z.number().optional(),
  close_timing_log: z.string().optional(),
  firm_name: z.string().optional(),
  notes: z.string().optional(),
  due_source: z.string().optional(),
  gdrive_folder: z.string().optional(),
  stage_involvements: z.string().optional(),
  mentioned_by_pm: z.string().optional(),
  research: z.string().optional(),
  tagtypeid: zid('tag_types').optional(),
  updated_at: z.number()
});

// Decision Category schema (for the decision_categories table)
export const DecisionCategory = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  name: z.string(),
  description: z.string().optional(),
  subtable_type: SubtableTypeEnum,
  tag_type_id: zid('tag_types').optional(),
  statuses: z.array(
    z.object({
      value: z.string(),
      label: z.string().optional(),
      description: z.string().optional()
    })
  ),
  fields: z.array(
    z.object({
      key: z.string(),
      name: z.string(),
      type: z.string(),
      required: z.boolean().optional(),
      options: z.array(z.string()).optional(),
      description: z.string().optional(),
      order: z.number(),
      default_value: z.string().optional()
    })
  ),
  kanban_columns: z.array(KanbanColumn),
  updated_at: z.number()
});

// Decision Custom Data schema (for the decision_custom_data table)
export const DecisionCustomData = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  decision_id: zid('decisions'),
  field_key: z.string(),
  field_value: z.string(),
  updated_at: z.number()
});

// Schema for decision creation
export const CreateDecisionInput = z.object({
  title: z.string(),
  decision_category_id: zid('decision_categories'),
  short_description: z.string().optional(),
  summary: z.string().optional(),
  project_ids: z.preprocess(
    (value) => Array.isArray(value) ? value : [value],
    z.array(zid('projects'))
  ).optional().describe('Single values are automatically converted to arrays'),
  decision_date: z.number().optional(),
  due_date: z.number().optional(),
  status: DecisionStatusEnum.optional(),
  priority: PriorityEnum.optional(),
  approver_id: zid('users').optional(),
  badges: z.array(zid('badges')).optional(),
  amount: z.number().optional(),
  amountType: AmountTypeEnum.optional(),
  currencyCode: z.string().optional(),
  updated_at: z.number().optional(),
  // DCI Fields
  driver: zid('users').optional(),
  contributors: z.array(z.union([zid('users'), zid('teams')])).optional(),
  informed: z.array(z.union([zid('users'), zid('teams')])).optional(),
  
  // Decision Type Component Fields
  decision_type: DecisionTypeEnum.optional(),
  
  // Financial implications fields
  has_financial_implications: z.boolean().optional(),
  financial_amount: z.number().optional(),
  financial_currency: z.string().optional(),
  financial_time_period: TimePeriodEnum.optional(),
  financial_years: z.number().optional(),
  
  // Option implications
  yes_means: z.string().optional(),
  no_means: z.string().optional()
});

// Schema for general decision creation
export const CreateGeneralDecisionInput = z.object({
  decision_id: zid('decisions'),
  description: z.string().optional(),
  rationale: z.string().optional(),
  alternatives_considered: z.string().optional(),
  impact_assessment: z.string().optional(),
  approver_id: zid('users').optional(),
  approval_date: z.number().optional(),
  effectiveness_rating: z.number().optional(),
  implementation_notes: z.string().optional(),
  review_date: z.number().optional()
});

// Schema for investment decision creation
export const CreateInvestmentDecisionInput = z.object({
  decision_id: zid('decisions'),
  description: z.string().optional(),
  is_active: z.boolean(),
  proposed_investment: z.string().optional(),
  total_fundraise: z.number().optional(),
  asset_class: z.string().optional(),
  structure: z.string().optional(),
  investment_entity: z.string().optional(),
  market: z.string().optional(),
  geography: z.string().optional(),
  target_close_date: z.number().optional(),
  close_timing_log: z.string().optional(),
  firm_name: z.string().optional(),
  notes: z.string().optional(),
  due_source: z.string().optional(),
  gdrive_folder: z.string().optional(),
  stage_involvements: z.string().optional(),
  mentioned_by_pm: z.string().optional(),
  research: z.string().optional(),
  tagtypeid: zid('tag_types').optional()
});

// Schema for decision category creation
export const CreateDecisionCategoryInput = z.object({
  name: z.string(),
  description: z.string().optional(),
  subtable_type: SubtableTypeEnum,
  tag_type_id: zid('tag_types').optional(),
  statuses: z.array(
    z.object({
      value: z.string(),
      label: z.string().optional(),
      description: z.string().optional()
    })
  ),
  fields: z.array(
    z.object({
      key: z.string(),
      name: z.string(),
      type: z.string(),
      required: z.boolean().optional(),
      options: z.array(z.string()).optional(),
      description: z.string().optional(),
      order: z.number(),
      default_value: z.string().optional()
    })
  ),
  kanban_columns: z.array(KanbanColumn)
});

// Schema for decision custom data creation
export const CreateDecisionCustomDataInput = z.object({
  decision_id: zid('decisions'),
  field_key: z.string(),
  field_value: z.string()
});

// Schema for updating decision categories - all fields optional
export const UpdateDecisionCategoryInput = CreateDecisionCategoryInput.partial();

// Schema for updating decision custom data - all fields optional
export const UpdateDecisionCustomDataInput = CreateDecisionCustomDataInput.partial();

// Schema for bulk decision creation
export const CreateMultipleDecisionsInput = z.array(CreateDecisionInput);

// Schema for updating decisions - all fields optional
export const UpdateDecisionInput = CreateDecisionInput.partial();

// Schema for updating general decisions - all fields optional
export const UpdateGeneralDecisionInput = CreateGeneralDecisionInput.partial();

// Schema for updating investment decisions - all fields optional
export const UpdateInvestmentDecisionInput = CreateInvestmentDecisionInput.partial();

// Schema for bulk updates
export const BulkDecisionUpdateSchema = z.object({
  id: zid('decisions').optional(),
  ids: z.array(zid('decisions')).optional(),
  updates: UpdateDecisionInput
});

// Query filter schema
export const DecisionFilterSchema = z
  .object({
    title: z.string().optional(),
    decision_category_id: zid('decision_categories').optional(),
    project_ids: z.preprocess(
      (value) => Array.isArray(value) ? value : [value],
      z.array(zid('projects'))
    ).optional().describe('Single values are automatically converted to arrays'),
    approver_id: zid('users').optional(),
    driver: zid('users').optional(),
    status: DecisionStatusEnum.optional(),
    priority: PriorityEnum.optional(),
    due_date_before: z.number().optional(),
    due_date_after: z.number().optional(),
    decision_date_before: z.number().optional(),
    decision_date_after: z.number().optional()
  })
  .optional();

// Pagination schema
export const PaginationSchema = z
  .object({
    sortBy: z
      .enum([
        '_creationTime',
        'title',
        'due_date',
        'decision_date',
        'updated_at'
      ])
      .optional(),
    sortDirection: z.enum(['asc', 'desc']).optional(),
    cursor: z.any().optional(),
    limit: z.number().optional()
  })
  .optional();
