/**
 * File Schema Definitions using Zod
 * 
 * This file defines Zod schemas that align with the Convex data model defined in schema.ts.
 * These schemas are used for:
 * 1. Input validation for API requests
 * 2. Type checking for document operations
 * 3. Runtime validation of document data
 * 4. TypeScript type inference for document-related operations
 * 
 * The schemas in this file are carefully designed to match the Convex schema structure,
 * ensuring that all field types, optionality, and nested structures are consistent.
 * 
 * Key components:
 * - FileSchema: Matches the 'files' table in Convex
 * - File sub-type schemas: Match the corresponding sub-tables in Convex
 * - Status and type enums: Match the literal values in the Convex schema
 * - DocumentDefinitionSchema: Matches the 'document_definitions' table in Convex
 * - UserDocumentChecklistSchema: Matches the 'user_document_checklist' table in Convex
 * 
 * Note: schema.ts is considered the source of truth for the data model.
 * Any changes to the Convex schema should be reflected here.
 */

import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { Id } from "../convex/_generated/dataModel";

/**
 * Define the document type union once for reuse
 * This ensures consistency across all schemas and matches exactly
 * with the document types defined in the Convex schema.ts file
 */
const docTypeUnion = z.union([
  z.literal('KNOWLEDGE_BASE'),
  z.literal('MEETING_NOTES'),
  z.literal('CONTRACT'),
  z.literal('DOCUMENT'),
  z.literal('BILL')
]);

/**
 * Document type enum - exported for use in other files
 * Using the same union type definition for consistency
 */
export const DocumentTypeEnum = docTypeUnion;

/**
 * DocumentTypeSchema is provided for backward compatibility
 * and consistent naming with other schemas
 */
export const DocumentTypeSchema = DocumentTypeEnum;

/**
 * Schema for AI processing results
 * This matches exactly with the parsedData object structure in schema.ts
 */
export const AIProcessingResultSchema = z.object({
  summary: z.string().optional(),
  analysis: z.string().optional(),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  children: z.array(z.object({
    name: z.string().optional(),
    dateOfBirth: z.string().optional(),
  })).optional(),
  assets: z.array(z.object({
    name: z.string().optional(),
    value: z.number().optional(),
    type: z.string().optional(),
    description: z.string().optional(),
  })).optional()
});

export type AIProcessingResult = z.infer<typeof AIProcessingResultSchema>;

/**
 * Define the upload status enum
 * This ensures the status values are consistent with the Convex schema
 */
export const UploadStatusEnum = z.enum(['processing', 'completed', 'failed']);

/**
 * Base file schema that matches the files table in Convex schema.ts
 * All field types and optionality match exactly with the Convex schema
 */
export const FileSchema = z.object({
  _id: zid('files'),
  name: z.string(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  docType: docTypeUnion,
  updated_at: z.number().optional(),
  ownerId: zid('users').optional(),
  parentEntityId: z.union([
    zid('tasks'),
    zid('projects'),
    zid('decisions'),
    zid('organizations'),
    zid('users'),
    zid('people'),
    zid('bills')
  ]).optional(),
  uploadStatus: UploadStatusEnum.optional(),
  error: z.string().optional(),
  parsedData: AIProcessingResultSchema.optional(),
  // File metadata
  fileFilename: z.string().optional(),
  fileStorageId: zid('_storage').optional(),
  fileSize: z.number().optional(),
  fileExtension: z.string().optional(),
  fileHash: z.string().optional(),
});

/**
 * Base file upload schema
 * Used for validating file upload requests
 */
export const FileUploadSchema = z.object({
  name: z.string().min(1, 'File name is required'),
  docType: docTypeUnion,
  size: z.number().min(0, 'File size must be positive'),
  category: z.string().optional(),
  description: z.string().optional(),
  ownerId: zid('users').optional(),
  parentEntityId: z.union([
    zid('tasks'),
    zid('projects'),
    zid('decisions'),
    zid('organizations'),
    zid('users'),
    zid('people'),
    zid('bills')
  ]).optional()
});

/**
 * Schema for file URL requests
 * Used for validating file URL requests
 */
export const FileUrlRequestSchema = z.object({
  fileId: zid('_storage')
});

/**
 * Schema for file deletion requests
 * Used for validating file deletion requests
 */
export const FileDeleteRequestSchema = z.object({
  fileId: zid("_storage"),
  documentId: zid("files").optional(),
});

/**
 * Base interface for all file types
 * Matches the core fields from the files table in schema.ts
 */
export interface FileBase {
  _id: Id<"files">;
  name: string;
  description?: string;
  docType: z.infer<typeof DocumentTypeSchema>;
  updated_at?: number;
  ownerId?: Id<"users">;
  parentEntityId?: Id<"tasks" | "projects" | "decisions" | "organizations" | "users" | "people" | "bills">;
  fileStorageId?: Id<"_storage">;
  fileSize?: number;
  fileFilename?: string;
  uploadStatus?: z.infer<typeof UploadStatusEnum>;
 }

/**
 * Interface combining fields from files table and knowledge_base sub-table
 * Matches the structure defined in schema.ts
 */
export interface KnowledgeBaseData extends FileBase {
  docType: 'KNOWLEDGE_BASE';
  category?: string;
  content?: string;
  updated_at: number;
}

/**
 * Interface combining fields from files table and contract sub-table
 * Matches the structure defined in schema.ts
 */
export interface ContractData extends FileBase {
  docType: 'CONTRACT';
  contentType?: string;
  category?: string;
  contractNumber?: string;
  effectiveDate?: number;
  expirationDate?: number;
  parties?: Array<Id<"organizations"> | Id<"people">>;
  value?: number;
  updated_at: number;
  currentVersionNumber?: number;
}

/**
 * Interface combining fields from files table and meeting_notes sub-table
 * Matches the structure defined in schema.ts
 */
export interface MeetingNotesData extends FileBase {
  docType: 'MEETING_NOTES';
  category?: string;
  content?: string;
  transcript?: string;
  meetingDate?: number;
  attendees?: Array<Id<"organizations"> | Id<"people">>;
  updated_at: number;
}

/**
 * Interface combining fields from files table and document sub-table
 * Matches the structure defined in schema.ts
 */
export interface DocumentData extends FileBase {
  docType: 'DOCUMENT';
  category?: string;
  currentVersionNumber?: number;
  updated_at: number;
}

/**
 * Schema for document definitions
 * Matches the document_definitions table in schema.ts
 */
export const DocumentDefinitionSchema = z.object({
  _id: zid('document_definitions'),
  name: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  updated_at: z.number().optional()
});

/**
 * Schema for user document checklist
 * Matches the user_document_checklist table in schema.ts
 */
export const UserDocumentChecklistSchema = z.object({
  _id: zid('user_document_checklist'),
  document_definition_id: zid('document_definitions'),
  user_id: zid('users'),
  status: z.string().optional(),
  updated_at: z.number().optional()
});

/**
 * Schema for creating a user document checklist entry
 */
export const CreateUserDocumentChecklistSchema = z.object({
  document_definition_id: zid('document_definitions'),
  user_id: zid('users'),
  status: z.string().optional()
});

/**
 * Schema for updating a user document checklist entry
 */
export const UpdateUserDocumentChecklistSchema = z.object({
  document_definition_id: zid('document_definitions'),
  user_id: zid('users'),
  status: z.string()
});

// Define the subject types as a const array for better type inference
const SUBJECT_TYPES = [
  "task",
  "project",
  "decision",
  "organization",
  "bills",
  "users",
  "people",
  "files",
  "tags"
] as const;

// Helper schema for validating relationship links using a discriminated union
// Ensures the subject_id type matches the subject_type literal
export const RelationshipLinkSchemaDiscriminated = z.discriminatedUnion("subject_type", [
  z.object({ subject_type: z.literal(SUBJECT_TYPES[0]), subject_id: zid("tasks") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[1]), subject_id: zid("projects") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[2]), subject_id: zid("decisions") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[3]), subject_id: zid("organizations") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[4]), subject_id: zid("bills") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[5]), subject_id: zid("users") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[6]), subject_id: zid("people") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[7]), subject_id: zid("files") }),
  z.object({ subject_type: z.literal(SUBJECT_TYPES[8]), subject_id: zid("tags") }),
]);

// Export the subject type for use in other files
export type SubjectType = typeof SUBJECT_TYPES[number];

// Schema for createKnowledgeBaseArticle mutation arguments
export const CreateKnowledgeBaseArticleArgsSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().optional(), // Keeping as string per user request
  ownerId: zid('users'),
  initialRelationships: z.array(RelationshipLinkSchemaDiscriminated).optional(),
});

// Schema for updateKnowledgeBaseArticle mutation arguments
export const UpdateKnowledgeBaseArticleArgsSchema = z.object({
  fileId: zid('files'),
  title: z.string().min(1, "Title cannot be empty").optional(),
  content: z.string().optional(), // Keeping as string
  relationshipsToAdd: z.array(RelationshipLinkSchemaDiscriminated).optional(),
  relationshipsToRemove: z.array(RelationshipLinkSchemaDiscriminated).optional(), // To identify which links to remove
});

// Schema for deleteKnowledgeBaseArticle mutation arguments
export const DeleteKnowledgeBaseArticleArgsSchema = z.object({
  fileId: zid('files'),
});

// --- End Schemas for Knowledge Base Mutations ---

/**
 * Schema for file metadata when saving uploaded files
 */
export const SaveFileMetadataArgsSchema = z.object({
  name: z.string(), // Could be renamed to title in the future
  docType: docTypeUnion,
  size: z.number(),
  description: z.string().optional(),
  ownerId: zid("users").optional(),
  parentEntityId: z.union([
    zid("tasks"), 
    zid("projects"), 
    zid("decisions"), 
    zid("organizations"), 
    zid("users"), 
    zid("people"), 
    zid("bills")
  ]).optional(),
  fileStorageId: zid("_storage").optional(),
  fileFilename: z.string().optional(),
  fileExtension: z.string().optional(),
  fileHash: z.string().optional()
});

/**
 * Schema for parsed data updates
 */
export const ParsedDataSchema = z.object({
  summary: z.string(),
  analysis: z.string(),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(
    z.object({
      name: z.string().optional(),
      dateOfBirth: z.string().optional(),
    })
  ).optional(),
  children: z.array(
    z.object({
      name: z.string().optional(),
      dateOfBirth: z.string().optional(),
    })
  ).optional(),
  assets: z.array(
    z.object({
      name: z.string().optional(),
      value: z.number().optional(),
      type: z.string().optional(),
      description: z.string().optional()
    })
  ).optional()
});

/**
 * Schema for updating file fields
 */
export const UpdateFileFieldArgsSchema = z.object({
  id: zid("files"),
  field: z.string(),
  value: z.any()
});

/**
 * Success response schema
 */
export const SuccessResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional()
});

/**
 * File response schema with ID
 */
export const FileResponseSchema = z.object({
  fileId: zid("files")
});

/**
 * Types inferred from schemas
 */
export type File = z.infer<typeof FileSchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type DocumentType = z.infer<typeof DocumentTypeEnum>;
// Add inferred types for KB mutation args
export type CreateKnowledgeBaseArticleArgs = z.infer<typeof CreateKnowledgeBaseArticleArgsSchema>;
export type UpdateKnowledgeBaseArticleArgs = z.infer<typeof UpdateKnowledgeBaseArticleArgsSchema>;
export type DeleteKnowledgeBaseArticleArgs = z.infer<typeof DeleteKnowledgeBaseArticleArgsSchema>;
// Add inferred type for the relationship link helper schema
export type RelationshipLink = z.infer<typeof RelationshipLinkSchemaDiscriminated>;
export type UploadStatus = z.infer<typeof UploadStatusEnum>;
export type DocumentDefinition = z.infer<typeof DocumentDefinitionSchema>;
export type UserDocumentChecklist = z.infer<typeof UserDocumentChecklistSchema>;
export type CreateUserDocumentChecklist = z.infer<typeof CreateUserDocumentChecklistSchema>;
export type UpdateUserDocumentChecklist = z.infer<typeof UpdateUserDocumentChecklistSchema>;
// Export inferred types for the new schemas
export type SaveFileMetadataArgs = z.infer<typeof SaveFileMetadataArgsSchema>;
export type ParsedData = z.infer<typeof ParsedDataSchema>;
export type UpdateFileFieldArgs = z.infer<typeof UpdateFileFieldArgsSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type FileResponse = z.infer<typeof FileResponseSchema>;
