import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// Common filter parameters schema
export const LineItemFilterSchema = z.object({
  startDate: z.number().optional(),
  endDate: z.number().optional(),
  category: zid('tags').optional(),
  categorySlug: z.string().optional(),
  searchText: z.string().optional(),
  minAmount: z.number().optional(),
  maxAmount: z.number().optional(),
  onlyUncategorized: z.boolean().optional(),
  limit: z.number().optional(),
  sortBy: z.enum(['date', 'amount', 'merchant']).optional(),
  sortDirection: z.enum(['asc', 'desc']).optional()
});

// Pagination options schema
export const PaginationOptionsSchema = z.object({
  numItems: z.number(),
  cursor: z.any().optional()
});

// Query results pagination schema
export const PaginatedResultSchema = z.object({
  page: z.array(z.any()),
  isDone: z.boolean(),
  continueCursor: z.string().nullable()
});

// Line item object schema
export const LineItemSchema = z.object({
  _id: zid('lineItems'),
  _creationTime: z.number(),
  bill_id: zid('bills'),
  amount: z.number(),
  post_date: z.number(),
  merchant_name: z.string(),
  description: z.string(),
  spending_category: zid('tags').optional(),
  vendor_id: zid('organizations'),
  updated_at: z.number()
});

// Stats output schema - keeping for backward compatibility
export const LineItemStatsSchema = z.object({
  totalSpending: z.number(),
  averageSpending: z.number(),
  spendingTrend: z.object({
    currentPeriod: z.number(),
    previousPeriod: z.number(),
    percentageChange: z.number()
  }),
  topMerchants: z.array(
    z.object({
      merchant_name: z.string(),
      total_amount: z.number()
    })
  ),
  transactionCount: z.number()
});

// Schema for creating a new line item
export const LineItemInputSchema = z.object({
  bill_id: zid('bills'),
  amount: z.number(),
  description: z.string(),
  post_date: z.number(),
  merchant_name: z.string(),
  spending_category: zid('tags').optional(),
  vendor_id: zid('organizations'),
  updated_at: z.number()
});

// Schema for updating a line item
export const LineItemUpdateSchema = z.object({
  amount: z.number().optional(),
  vendor_id: zid('organizations').optional(),
  description: z.string().optional(),
  post_date: z.number().optional(),
  merchant_name: z.string().optional(),
  // Allow null values for spending_category to support uncategorized items
  // This is needed because JSON can't serialize undefined, so we use null during transmission
  // and convert it to undefined server-side
  spending_category: z.union([zid('tags'), z.null()]).optional()
});

// Schema for bulk updates
export const LineItemBulkUpdateSchema = z.array(
  z.object({
    _id: zid('lineItems'),
    updates: LineItemUpdateSchema
  })
);

// Schema for bulk update results
export const BulkUpdateResultSchema = z.object({
  updated: z.array(z.string()),
  failed: z.array(z.string()),
  errors: z.array(
    z.object({
      _id: zid('lineItems'),
      error: z.string()
    })
  )
});

// Schema for getLineItemsByBill function
export const GetByBillSchema = z.object({
  bill_id: zid('bills')
});

// Schema for monthly spending timeline
export const MonthlySpendingItemSchema = z.object({
  month: z.string(),
  year: z.number(),
  timestamp: z.number(),
  spending: z.number(),
  transactionCount: z.number(),
  projected: z.number().optional()
});

// Schema for vendor trends
export const VendorTrendSchema = z.object({
  vendor: z.string(),
  monthlySpend: z.array(
    z.object({
      month: z.string(),
      amount: z.number()
    })
  ),
  trend: z.enum(['increasing', 'decreasing', 'stable']),
  averageSpend: z.number(),
  isNew: z.boolean()
});

// Schema for AI insights
export const AIInsightSchema = z.object({
  insightType: z.enum([
    'category_increase',
    'category_decrease',
    'new_merchant',
    'spending_trend',
    'unusual_transaction'
  ]),
  title: z.string(),
  description: z.string(),
  category: z
    .object({
      id: zid('tags').optional(),
      name: z.string(),
      percentage: z.number(),
      amount: z.number(),
      previousAmount: z.number().optional(),
      percentageChange: z.number().optional()
    })
    .optional(),
  merchant: z
    .object({
      name: z.string(),
      amount: z.number(),
      transactionCount: z.number()
    })
    .optional(),
  actionUrl: z.string().optional(),
  actionLabel: z.string().optional()
});

// Schema for spending insights
export const SpendingInsightSchema = z.object({
  type: z.enum(['trend', 'anomaly', 'suggestion']),
  title: z.string(),
  description: z.string(),
  impact: z.enum(['positive', 'negative', 'neutral']),
  value: z.number().optional()
});

// Schema for tag spending data
export const TagSpendingDataSchema = z.object({
  categories: z.array(
    z.object({
      id: zid('tags').optional(), // Allow undefined for uncategorized
      name: z.string(),
      amount: z.number(),
      transactionCount: z.number(),
      percentage: z.number(),
      trend: z.enum(['up', 'down', 'stable']),
      parentId: zid('tags').optional(),
      subCategories: z.array(
        z.object({
          id: zid('tags'),
          name: z.string(),
          amount: z.number(),
          transactionCount: z.number(),
          percentage: z.number(),
          trend: z.enum(['up', 'down', 'stable']),
          parentId: zid('tags')
        })
      ),
      isUncategorized: z.boolean().optional()
    })
  ),
  summary: z.object({
    totalSpending: z.number(),
    avgTransaction: z.number(),
    transactionCount: z.number(),
    uncategorizedAmount: z.number(),
    uncategorizedCount: z.number()
  })
});

// Schema for a single category's aggregated spending
export const SpendingCategorySchema = z.object({
  id: zid("tags"),
  name: z.string(),
  amount: z.number(),
  count: z.number(),
  color: z.string().optional()
});

// Schema for the complete aggregated spending response
export const SpendingAggregationSchema = z.object({
  categories: z.array(SpendingCategorySchema),
  totalSpent: z.number()
});

// Infer TypeScript types from Zod schemas
export type LineItemFilter = z.infer<typeof LineItemFilterSchema>;
export type LineItem = z.infer<typeof LineItemSchema>;
export type LineItemStats = z.infer<typeof LineItemStatsSchema>;
export type LineItemInput = z.infer<typeof LineItemInputSchema>;
export type LineItemUpdate = z.infer<typeof LineItemUpdateSchema>;
export type LineItemBulkUpdate = z.infer<typeof LineItemBulkUpdateSchema>;
export type BulkUpdateResult = z.infer<typeof BulkUpdateResultSchema>;
export type MonthlySpendingItem = z.infer<typeof MonthlySpendingItemSchema>;
export type VendorTrend = z.infer<typeof VendorTrendSchema>;
export type AIInsight = z.infer<typeof AIInsightSchema>;
export type SpendingInsight = z.infer<typeof SpendingInsightSchema>;
export type TagSpendingData = z.infer<typeof TagSpendingDataSchema>;