import { z } from 'zod';

/**
 * Standard pagination schema for use across all paginated queries.
 * This schema is compatible with both the Convex paginate API (numItems)
 * and the standardized pagination pattern from tasks-decisions-pagination.md (limit).
 *
 * @example
 * import { PaginationSchema } from '../zod/pagination-schema';
 *
 * export const myQuery = zQuery({
 *   args: {
 *     searchQuery: z.string(),
 *     pagination: PaginationSchema
 *   },
 *   handler: async (ctx, args) => {
 *     // Use args.pagination directly with .paginate()
 *     return await query.paginate({
 *       cursor: args.pagination.cursor,
 *       numItems: args.pagination.numItems || args.pagination.limit || 10
 *     });
 *   }
 * });
 */
export const PaginationSchema = z.object({
  // Main pagination fields (compatible with Convex paginate)
  numItems: z.number().optional(), // Support numItems for backward compatibility
  limit: z.number().optional(),    // Support limit for newer code
  cursor: z.any().nullable().default(null),
  // Optional sorting fields
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).optional(),
});

/**
 * Type for pagination options extracted from the schema
 */
export type PaginationOpts = z.infer<typeof PaginationSchema>;

/**
 * Type for pagination results
 */
export type PaginationResult<T> = {
  page: T[];
  continueCursor: string | null;
  isDone: boolean;
};
