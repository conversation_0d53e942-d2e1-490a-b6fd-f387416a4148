import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";
import typography from "@tailwindcss/typography";
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx,css,scss}",
    "./components/**/*.{js,ts,jsx,tsx,mdx,css,scss}",
    "./app/**/*.{js,ts,jsx,tsx,mdx,css,scss}",
    "*.{js,ts,jsx,tsx,mdx,css,scss}",
  ],
  theme: {
    extend: {
      boxShadow: {
        'deep': '0 -8px 20px -4px rgba(0,0,0,0.1), 0 20px 35px -5px rgba(0,0,0,0.2), 0 0 0 1px rgba(0,0,0,0.05), 0 1px 0 0 rgba(255,255,255,0.1) inset',
        'deep-hover': '0 -12px 30px -6px rgba(0,0,0,0.15), 0 30px 55px -7px rgba(0,0,0,0.3), 0 0 0 1px rgba(0,0,0,0.05), 0 1px 0 0 rgba(255,255,255,0.1) inset',
        'medium': '3px 0 20px -4px rgba(0,0,0,0.15), 0 0 0 1px rgba(0,0,0,0.05), 0 1px 0 0 rgba(255,255,255,0.1) inset',
        'medium-hover': '6px 0 35px -4px rgba(0,0,0,0.35), 0 0 0 1px rgba(0,0,0,0.08), 0 1px 0 0 rgba(255,255,255,0.15) inset',
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar))",
          foreground: "hsl(var(--sidebar-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
        },
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
          '6': 'hsl(var(--chart-6))',
          '7': 'hsl(var(--chart-7))',
          '8': 'hsl(var(--chart-8))',
          '9': 'hsl(var(--chart-9))',
          '10': 'hsl(var(--chart-10))',
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        float: {
          '0%': { transform: 'translate(0, 0) rotate(0deg)' },
          '25%': { transform: 'translate(10px, 10px) rotate(2deg)' },
          '50%': { transform: 'translate(-5px, 15px) rotate(-1deg)' },
          '75%': { transform: 'translate(-15px, 5px) rotate(-3deg)' },
          '100%': { transform: 'translate(0, 0) rotate(0deg)' },
        },
        'float-delayed': {
          '0%': { transform: 'translate(-10px, -10px) rotate(-2deg)' },
          '25%': { transform: 'translate(5px, -15px) rotate(1deg)' },
          '50%': { transform: 'translate(15px, -5px) rotate(3deg)' },
          '75%': { transform: 'translate(0, 0) rotate(0deg)' },
          '100%': { transform: 'translate(-10px, -10px) rotate(-2deg)' },
        },
        'float-slow': {
          '0%': { transform: 'translate(5px, 5px) rotate(1deg)' },
          '50%': { transform: 'translate(-5px, -5px) rotate(-1deg)' },
          '100%': { transform: 'translate(5px, 5px) rotate(1deg)' },
        },
        'subtle-float': {
          '0%': { transform: 'translate(0, 0)' },
          '50%': { transform: 'translate(0, 15px)' },
          '100%': { transform: 'translate(0, 0)' },
        },
        'subtle-float-delayed': {
          '0%': { transform: 'translate(0, 15px)' },
          '50%': { transform: 'translate(0, 0)' },
          '100%': { transform: 'translate(0, 15px)' },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        float: 'float 20s ease-in-out infinite',
        'float-delayed': 'float-delayed 25s ease-in-out infinite',
        'float-slow': 'float-slow 30s ease-in-out infinite',
        'subtle-float': 'subtle-float 40s ease-in-out infinite',
        'subtle-float-delayed': 'subtle-float-delayed 45s ease-in-out infinite',
      },
    },
  },
  plugins: [tailwindcssAnimate, typography],
} satisfies Config;